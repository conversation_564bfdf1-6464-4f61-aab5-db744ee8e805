{"printWidth": 100, "tabWidth": 2, "useTabs": false, "semi": true, "singleQuote": true, "quoteProps": "as-needed", "jsxSingleQuote": false, "trailingComma": "all", "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "always", "requirePragma": false, "insertPragma": false, "proseWrap": "preserve", "htmlWhitespaceSensitivity": "css", "vueIndentScriptAndStyle": false, "endOfLine": "lf", "embeddedLanguageFormatting": "auto", "singleAttributePerLine": true, "importOrder": ["^react$", "^react-dom$", "^react-router-dom$", "^@reduxjs/toolkit$", "^react-redux$", "^redux", "^@?\\w", "^(@/)(.*)", "^[./]"], "importOrderSeparation": true, "importOrderSortSpecifiers": true, "importOrderGroupNamespaceSpecifiers": true, "importOrderCaseInsensitive": true}
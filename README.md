# React Messenger - Auto Trimitra

Aplikasi messenger berbasis web untuk platform Auto Trimitra yang memungkinkan admin mengelola komunikasi dengan klien, administrasi leads, dan berbagai fitur otomotif lainnya.

## Deskripsi Proyek

React Messenger Auto Trimitra adalah aplikasi frontend yang dibangun menggunakan React.js dan TypeScript untuk mengelola komunikasi customer service dengan klien di platform Trimitra. Aplikasi ini menyediakan berbagai fitur seperti chat real-time, manajemen data klien, simulasi kredit, dan integrasi dengan berbagai layanan Trimitra.

## Teknologi Utama

- **React** 18.3.1 - Library UI utama
- **TypeScript** 5.6.3 - Type safety
- **Redux Toolkit** 2.3.0 - State management
- **Firebase** 11.0.2 - Real-time database dan authentication
- **Semantic UI React** 2.1.5 - Komponen UI
- **Tailwind CSS** 3.4.15 - Utility-first CSS framework
- **CRACO** 7.1.0 - Custom React App Configuration
- **Apisauce** 3.0.0 - Client HTTP untuk API
- **date-fns** 4.1.0 - Manipulasi tanggal modern
- **Moment.js** 2.30.1 - Parsing, validasi, manipulasi, dan pemformatan tanggal
- **Yup** 1.6.1 - Validasi skema

## Fitur Utama

### 💬 Chat & Komunikasi
- Real-time messaging dengan Firebase
- Template pesan yang dapat dikustomisasi
- Upload dan preview media (gambar, video, dokumen)
- Balas dan teruskan pesan
- Alat debugging obrolan

### 👥 Manajemen Klien
- Profil klien lengkap dengan informasi pribadi
- Riwayat komunikasi dan transaksi
- Unggah dan verifikasi dokumen (KTP, KK, selfie, dll)
- Manajemen data kendaraan yang dimiliki
- Pelacakan status prospek dan akuisisi

### 🚗 Fitur Otomotif
- Katalog kendaraan dan daftar harga
- Simulasi kredit dan perhitungan cicilan
- Pengecekan stok kendaraan
- Integrasi dengan platform Trimobi untuk iklan
- Manajemen kode promo dan penawaran khusus

### 📊 Administrasi
- Dasbor admin dengan berbagai metrik
- Manajemen template pesan
- Sistem peran dan izin
- Ekspor data dan laporan
- Otomatisasi alur percakapan

### 🔧 Integrasi Eksternal
- **Trimitra API** - Data kendaraan dan harga
- **Firebase** - Real-time database dan storage
- **OCR Services** - Ekstraksi data dari dokumen
- **Payment Gateway** - Pembayaran dan verifikasi
- **Logistic Services** - Pelacakan pengiriman

## Scripts

### Development
```bash
# Jalankan aplikasi dalam mode development
npm start
# Buka http://localhost:3000 di browser
```

### Testing
```bash
# Jalankan test runner dalam interactive watch mode
npm test
```

### Production Build
```bash
# Build aplikasi untuk production ke folder 'build'
npm run build
```

### Code Formatting
```bash
# Format code menggunakan Prettier
npm run format

# Check format tanpa mengubah file
npm run format:check
```

### Advanced Configuration
```bash
# Eject konfigurasi (PERINGATAN: tidak bisa diundur!)
npm run eject
```

## Struktur Folder

```
src/
├── Components/           # Komponen React reusable
│   ├── ChatBox/         # Komponen chat utama
│   ├── ContactList/     # Daftar kontak klien
│   ├── Header/          # Header aplikasi
│   ├── Login/           # Autentikasi
│   ├── MainChat/        # Container chat utama
│   └── ...              # Komponen lainnya
├── redux/               # State management
├── services/            # API services
├── helpers/             # Utility functions
├── entities/            # TypeScript interfaces
├── contexts/            # React contexts
└── config/              # Konfigurasi aplikasi
```

## Environment Setup

1. **Clone repository**
   ```bash
   git clone [repository-url]
   cd react-messenger.auto.trimitra.biz
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Setup environment variables**
   - Salin `.env.example` ke `.env`
   - Isi konfigurasi Firebase dan API keys

4. **Start development server**
   ```bash
   npm start
   ```

## Deployment

Aplikasi ini di-deploy menggunakan Firebase Hosting dengan GitHub Actions untuk CI/CD otomatis.

### Manual Deployment
```bash
# Build production
npm run build

# Deploy ke Firebase
firebase deploy
```

### Auto Deployment
- Push ke branch `main` akan otomatis trigger deployment
- Pull request akan membuat preview deployment

## Browser Support

- Chrome (terbaru)
- Firefox (terbaru) 
- Safari (terbaru)
- Edge (terbaru)

## Kontribusi

1. Fork repository
2. Buat feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit perubahan (`git commit -m 'Add some AmazingFeature'`)
4. Push ke branch (`git push origin feature/AmazingFeature`)
5. Buat Pull Request

## Tim Pengembang

- **Frontend Development**: React.js, TypeScript, Redux
- **Backend Integration**: Firebase, REST APIs
- **UI/UX**: Semantic UI, Tailwind CSS
- **DevOps**: Firebase Hosting, GitHub Actions

---

**Auto Trimitra** © 2024 - Platform digital untuk solusi otomotif terpercaya
{"name": "ideal-trimitra-sample-frontend", "version": "1.0.0", "private": true, "dependencies": {"@date-fns/upgrade": "^1.0.3", "@reduxjs/toolkit": "^2.3.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "@types/react-datepicker": "^7.0.0", "apisauce": "3.0.0", "clipboard-copy": "^4.0.1", "collect.js": "^4.36.1", "date-fns": "^4.1.0", "firebase": "^11.0.2", "framer-motion": "^12.1.0", "html-react-parser": "^5.1.18", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "moment": "^2.30.1", "query-string": "^9.1.1", "react": "^18.3.1", "react-confirm": "^0.3.0-7", "react-cropper": "^2.3.3", "react-datepicker": "^7.5.0", "react-dom": "^18.3.1", "react-icons": "^5.4.0", "react-idle-timer": "^5.7.2", "react-lazyload": "^3.2.1", "react-number-format": "^5.4.2", "react-player": "^2.16.0", "react-redux": "^9.1.2", "react-responsive": "^10.0.0", "react-responsive-masonry": "^2.4.1", "react-router-dom": "^5.3.0", "react-scripts": "5.0.1", "react-semantic-ui-datepickers": "^2.17.2", "react-textarea-autosize": "^8.5.7", "redux": "^5.0.1", "redux-thunk": "^3.1.0", "sanitize-html": "^2.13.1", "semantic-ui-css": "^2.5.0", "semantic-ui-react": "^2.1.5", "tailwindcss": "^3.4.15", "typescript": "^5.6.3", "uuid": "^11.0.3", "web-vitals": "^4.2.4", "yet-another-react-lightbox": "^3.21.7", "yup": "^1.6.1"}, "scripts": {"start": "craco start", "build": "craco build", "test": "craco test", "eject": "craco eject", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,scss,md}\"", "format:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx,json,css,scss,md}\""}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@craco/craco": "^7.1.0", "@testing-library/dom": "^10.4.0", "@trivago/prettier-plugin-sort-imports": "5.2.2", "@types/jest": "^29.5.14", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.15", "@types/mocha": "^10.0.9", "@types/node": "^22.9.0", "@types/react": "^18.3.12", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dom": "^18.3.1", "@types/react-lazyload": "^3.2.3", "@types/react-redux": "^7.1.34", "@types/react-responsive-masonry": "^2.1.3", "@types/react-router-dom": "^5.3.3", "@types/sanitize-html": "^2.13.0", "@types/uuid": "^10.0.0", "mocha": "^10.8.2", "prettier": "3.5.1"}}
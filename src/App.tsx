import React from 'react';
import WrappedMainChat from './Components/MainChat/MainChat';
import { Route, Router, Switch } from 'react-router-dom';
import Login from './Components/Login/Login';
import { myHistory } from './helpers/history';
import ImageLightbox from './Components/ImageLightbox/ImageLightbox';
import { ScrollContainerProvider } from './contexts/ScrollContainerContext';

class App extends React.Component {
  render() {
    return (
      <ScrollContainerProvider>
        <Router history={myHistory}>
          <Switch>
            <Route
              exact={true}
              path={'/'}
            >
              <WrappedMainChat />
            </Route>
            <Route path={'/login'}>
              <Login />
            </Route>
          </Switch>
          <ImageLightbox />
        </Router>
      </ScrollContainerProvider>
    );
  }
}

export default App;

import React, { Component } from 'react';
import AdminDocument from '../../../entities/AdminDocument';
import moment from 'moment';
import { mainApiServices } from '../../../services/MainApiServices';
import { mainStore } from '../../../redux/reducers';
import { fetchAdminListThunk } from '../../../redux/modal-list-admin/modalListAdmin.slice';
import confirmDialog from '../../callableDialog/confirmDialog';
import { FiMail, FiUser, FiShield, FiBriefcase, FiGrid, FiCheck, FiX } from 'react-icons/fi';

interface Props {
  admin: AdminDocument;
}

interface States {
  loading: boolean;
}

class AdminListItem extends Component<Props, States> {
  private _isMounted: boolean;

  constructor(props: Props) {
    super(props);

    this.state = {
      loading: false,
    };

    this._isMounted = false;
  }

  componentDidMount() {
    this._isMounted = true;
  }

  componentWillUnmount() {
    this._isMounted = true;
  }

  inactivateAdminStatusButton = () => {
    const onClick = async () => {
      if (this._isMounted) {
        this.setState({
          loading: true,
        });
      }

      try {
        await mainApiServices.updateAdminStatus({
          status: 'disable',
          adminPath: this.props.admin.ref.path,
        });
        mainStore.dispatch(fetchAdminListThunk() as any);
      } catch (e) {
        confirmDialog({
          title: 'Tidak Dapat Dinonaktifkan',
          content: 'Terjadi error pada server sehingga admin gagal dinonaktifkan',
          cancelButton: false,
        });
      }

      if (this._isMounted) {
        this.setState({
          loading: false,
        });
      }
    };
    return (
      <button
        onClick={onClick}
        disabled={this.state.loading}
        className="flex items-center gap-2 px-3 py-1.5 text-sm text-red-600 bg-red-50 rounded-lg hover:bg-red-100 transition-colors disabled:opacity-50"
      >
        <FiX className="w-4 h-4" />
        Nonaktifkan
      </button>
    );
  };

  activateAdminStatusButton = () => {
    const onClick = async () => {
      if (this._isMounted) {
        this.setState({
          loading: true,
        });
      }
      try {
        await mainApiServices.updateAdminStatus({
          status: 'enable',
          adminPath: this.props.admin.ref.path,
        });
        mainStore.dispatch(fetchAdminListThunk() as any);
      } catch (e) {
        confirmDialog({
          title: 'Tidak Dapat Diaktifkan',
          content: 'Terjadi error pada server sehingga admin gagal diaktifkan',
          cancelButton: false,
        });
      }

      if (this._isMounted) {
        this.setState({
          loading: false,
        });
      }
    };

    return (
      <button
        onClick={onClick}
        disabled={this.state.loading}
        className="flex items-center gap-2 px-3 py-1.5 text-sm text-green-600 bg-green-50 rounded-lg hover:bg-green-100 transition-colors disabled:opacity-50"
      >
        <FiCheck className="w-4 h-4" />
        Aktifkan
      </button>
    );
  };

  render() {
    const r = this.props.admin;

    return (
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
        <div className="p-4 border-b border-gray-100">
          <div className="flex items-start justify-between">
            <div>
              <div className="flex items-center gap-2 mb-1">
                <FiMail className="w-4 h-4 text-gray-500" />
                <h3 className="font-medium text-gray-900">{r.email}</h3>
              </div>
              <p className="text-sm text-gray-500">
                {moment(r.created_time.toDate()).format('DD MMM YYYY')}
              </p>
            </div>
            <div
              className={`px-2.5 py-1 text-xs font-medium rounded-full ${r.active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}
            >
              {r.active ? 'Aktif' : 'Nonaktif'}
            </div>
          </div>
        </div>

        <div className="p-4 space-y-3">
          <div className="flex items-center gap-3">
            <FiUser className="w-4 h-4 text-gray-400 shrink-0" />
            <div>
              <p className="text-xs text-gray-500">Nama</p>
              <p className="text-sm text-gray-900">{r.name}</p>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <FiShield className="w-4 h-4 text-gray-400 shrink-0" />
            <div>
              <p className="text-xs text-gray-500">Level</p>
              <p className="text-sm text-gray-900">{r.level}</p>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <FiBriefcase className="w-4 h-4 text-gray-400 shrink-0" />
            <div>
              <p className="text-xs text-gray-500">Project</p>
              <p className="text-sm text-gray-900">
                {r.project.name} - {r.project.provider}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <FiGrid className="w-4 h-4 text-gray-400 shrink-0" />
            <div>
              <p className="text-xs text-gray-500">Department</p>
              <p className="text-sm text-gray-900">{r.department?.name || '-'}</p>
            </div>
          </div>
        </div>

        <div className="px-4 py-3 bg-gray-50 border-t border-gray-100 rounded-b-lg">
          {r.active ? this.inactivateAdminStatusButton() : this.activateAdminStatusButton()}
        </div>
      </div>
    );
  }
}

export default AdminListItem;

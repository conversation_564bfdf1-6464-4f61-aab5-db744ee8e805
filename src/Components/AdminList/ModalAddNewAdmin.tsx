import React, { useEffect, useCallback } from 'react';
import { Button, Form, Input, Message, Modal, Select } from 'semantic-ui-react';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { useDispatch, useSelector } from 'react-redux';
import modalAddNewAdminSlice, {
  modalAddNewAdminFetchAvailableDepartment,
  modalAddNewAdminFetchAvailableProjects,
} from '../../redux/modal-list-admin/modalAddNewAdmin.slice';
import { DropdownProps } from 'semantic-ui-react/dist/commonjs/modules/Dropdown/Dropdown';
import { InputOnChangeData } from 'semantic-ui-react/dist/commonjs/elements/Input/Input';
import { mainApiServices } from '../../services/MainApiServices';
import { collection, doc } from 'firebase/firestore';
import { firestoreIdealVer9 } from '../../services/myFirebase';
import { AxiosError } from 'axios';
import { fetchAdminListThunk } from '../../redux/modal-list-admin/modalListAdmin.slice';
import confirmDialog from '../callableDialog/confirmDialog';

const ModalAddNewAdmin: React.FC = () => {
  const dispatch = useDispatch();
  const {
    open,
    name,
    email,
    password,
    role,
    selectedProjectId,
    availableProjects,
    availableDepartments,
    selectedDepartmentId,
    errorMessage,
    loading,
  } = useSelector((state: TMainReduxStates) => state.modalAddAdmin);

  // Fetch projects on mount (componentDidMount)
  useEffect(() => {
    dispatch(modalAddNewAdminFetchAvailableProjects() as any);
  }, [dispatch]);

  const onProjectChange = useCallback(
    (e: React.SyntheticEvent<HTMLElement, Event>, d: DropdownProps) => {
      const projectId = d.value as string;
      dispatch(modalAddNewAdminSlice.actions.setSelectedProject(projectId));
      // Keeping setTimeout for parity with previous behavior,
      // consider moving this into a useEffect watching projectId if needed.
      setTimeout(() => {
        dispatch(modalAddNewAdminFetchAvailableDepartment(projectId) as any);
      }, 100);
    },
    [dispatch],
  );

  const onDepartmentChange = useCallback(
    (e: React.SyntheticEvent<HTMLElement, Event>, d: DropdownProps) => {
      dispatch(modalAddNewAdminSlice.actions.setSelectedDepartment(d.value as string));
    },
    [dispatch],
  );

  const onRoleChange = useCallback(
    (e: React.SyntheticEvent<HTMLElement, Event>, d: DropdownProps) => {
      dispatch(modalAddNewAdminSlice.actions.setRole(d.value as string));
    },
    [dispatch],
  );

  const onNameChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>, d: InputOnChangeData) => {
      dispatch(modalAddNewAdminSlice.actions.setName(d.value));
    },
    [dispatch],
  );

  const onEmailChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>, d: InputOnChangeData) => {
      dispatch(modalAddNewAdminSlice.actions.setEmail(d.value));
    },
    [dispatch],
  );

  const onPasswordChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>, d: InputOnChangeData) => {
      dispatch(modalAddNewAdminSlice.actions.setPassword(d.value));
    },
    [dispatch],
  );

  const onClose = useCallback(() => {
    dispatch(modalAddNewAdminSlice.actions.close());
  }, [dispatch]);

  const onSubmit = useCallback(async () => {
    dispatch(modalAddNewAdminSlice.actions.setLoading(true));
    dispatch(modalAddNewAdminSlice.actions.setErrorMessage(''));

    if (!selectedProjectId) {
      dispatch(modalAddNewAdminSlice.actions.setLoading(false));
      return;
    }

    const projectCollection = collection(firestoreIdealVer9, 'projects');
    const projectDoc = doc(projectCollection, selectedProjectId);
    const departmentCollection = collection(projectDoc, 'departments');

    const departmentDoc = selectedDepartmentId
      ? doc(departmentCollection, selectedDepartmentId)
      : null;

    try {
      await mainApiServices.addNewAdmin({
        name,
        email,
        password,
        role,
        projectPath: projectDoc.path,
        departmentPath: departmentDoc?.path || null,
      });

      dispatch(modalAddNewAdminSlice.actions.close());
      dispatch(fetchAdminListThunk() as any);

      confirmDialog({
        title: 'Sukses',
        content: 'Admin baru berhasil dibuat',
        cancelButton: false,
      });
    } catch (e) {
      const axiosError = e as AxiosError<{ error: any }>;
      const error = axiosError.response?.data.error;
      let errMsg: string;
      if (error && error.type === 'UNPROCESSABLE_ENTITY') {
        const errorFields: any[] = Object.values(error.messages);
        errMsg = errorFields[0].msg;
      } else {
        console.error(error);
        errMsg = error?.messages || 'Terjadi kesalahan.';
      }

      dispatch(modalAddNewAdminSlice.actions.setErrorMessage(errMsg));
    }

    dispatch(modalAddNewAdminSlice.actions.setLoading(false));
  }, [dispatch, name, email, password, role, selectedProjectId, selectedDepartmentId]);

  return (
    <Modal
      open={open}
      onClose={onClose}
      className="!w-[90%] max-w-2xl"
    >
      <Modal.Header className="!flex !items-center !justify-between !p-6 !border-b !border-gray-200">
        <span className="text-2xl font-semibold text-gray-800">Tambah Admin Baru</span>
      </Modal.Header>

      <Modal.Content className="!p-6">
        <Form className="space-y-4">
          <Form.Field className="!mb-4">
            <label className="!block !text-sm !font-medium !text-gray-700 !mb-1">Nama</label>
            <Input
              fluid
              placeholder="Masukkan nama"
              onChange={onNameChange}
              value={name}
              className="!w-full"
            />
          </Form.Field>

          <Form.Field className="!mb-4">
            <label className="!block !text-sm !font-medium !text-gray-700 !mb-1">Email</label>
            <Input
              fluid
              placeholder="Masukkan email"
              onChange={onEmailChange}
              value={email}
              className="!w-full"
            />
          </Form.Field>

          <Form.Field className="!mb-4">
            <label className="!block !text-sm !font-medium !text-gray-700 !mb-1">Password</label>
            <Input
              fluid
              type="password"
              placeholder="Masukkan password"
              onChange={onPasswordChange}
              value={password}
              className="!w-full"
            />
          </Form.Field>

          <Form.Field className="!mb-4">
            <label className="!block !text-sm !font-medium !text-gray-700 !mb-1">Role</label>
            <Select
              fluid
              options={[
                { text: 'admin', value: 'admin' },
                { text: 'owner', value: 'owner' },
              ]}
              value={role}
              onChange={onRoleChange}
              className="!w-full !bg-white"
            />
          </Form.Field>

          <Form.Field className="!mb-4">
            <label className="!block !text-sm !font-medium !text-gray-700 !mb-1">
              Project / Provider
            </label>
            <Select
              fluid
              options={availableProjects.map((p) => ({
                text: `${p.provider} - ${p.legal_name}`,
                value: p.ref.id,
              }))}
              value={selectedProjectId || ''}
              onChange={onProjectChange}
              className="!w-full !bg-white"
            />
          </Form.Field>

          {role === 'admin' && (
            <Form.Field className="!mb-4">
              <label className="!block !text-sm !font-medium !text-gray-700 !mb-1">
                Department
              </label>
              <Select
                fluid
                options={availableDepartments.map((d) => ({
                  text: d.name,
                  value: d.ref.id,
                }))}
                clearable={true}
                onChange={onDepartmentChange}
                value={selectedDepartmentId || ''}
                className="!w-full !bg-white"
              />
            </Form.Field>
          )}
        </Form>

        {errorMessage && (
          <Message
            negative
            className="!mt-4"
          >
            {errorMessage}
          </Message>
        )}
      </Modal.Content>

      <Modal.Actions className="!flex !items-center !justify-end !gap-3 !p-6 !bg-gray-50 !border-t !border-gray-200">
        <Button
          onClick={onClose}
          className="!bg-white !text-gray-700 !border !border-gray-300"
        >
          Batal
        </Button>
        <Button
          primary
          onClick={onSubmit}
          loading={loading}
          className="!bg-blue-600 !text-white hover:!bg-blue-700"
        >
          Tambah Baru
        </Button>
      </Modal.Actions>
    </Modal>
  );
};

export default ModalAddNewAdmin;

import React, { useCallback, useEffect, useMemo } from 'react';
import { Button, Form, Input, Modal } from 'semantic-ui-react';
import { useDispatch, useSelector } from 'react-redux';
import { TMainReduxStates } from '../../redux/types/redux-types';
import modalListAdminSlice, {
  fetchAdminListThunk,
} from '../../redux/modal-list-admin/modalListAdmin.slice';
import { InputOnChangeData } from 'semantic-ui-react/dist/commonjs/elements/Input/Input';
import modalAddNewAdminSlice from '../../redux/modal-list-admin/modalAddNewAdmin.slice';
import AdminListItem from './Components/AdminListItem';
import { FiSearch, FiUserPlus } from 'react-icons/fi';
import ModalAddNewAdmin from './ModalAddNewAdmin';
import { debounce } from 'lodash';

const ModalListAdmin: React.FC = () => {
  const dispatch = useDispatch();
  const modal = useSelector((state: TMainReduxStates) => state.modalAdminList);

  // Gunakan lodash debounce untuk menghindari render ulang yang tidak perlu
  const debouncedFilter = useMemo(
    () =>
      debounce(() => {
        dispatch(modalListAdminSlice.actions.filterClient());
      }, 500),
    [dispatch],
  );

  // Cancel debounce saat komponen unmount untuk menghindari side-effect
  useEffect(() => {
    return () => {
      debouncedFilter.cancel();
    };
  }, [debouncedFilter]);

  // Ambil daftar admin setiap kali modal terbuka
  useEffect(() => {
    if (modal.open) {
      dispatch(fetchAdminListThunk() as any);
    }
  }, [dispatch, modal.open]);

  const onChangeEmailFilter = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>, data: InputOnChangeData) => {
      dispatch(modalListAdminSlice.actions.setFilterEmail(data.value));
      debouncedFilter();
    },
    [dispatch, debouncedFilter],
  );

  const onClose = () => {
    dispatch(modalListAdminSlice.actions.close());
  };

  const onAddNewAdminClick = () => {
    dispatch(modalAddNewAdminSlice.actions.open());
  };

  return (
    <>
      <Modal
        open={modal.open}
        onClose={onClose}
        closeOnDimmerClick={true}
        className="!w-[90%] max-w-5xl"
      >
        <Modal.Header className="!flex !items-center !justify-between !p-6 !border-b !border-gray-200">
          <span className="text-2xl font-semibold text-gray-800">List Admin</span>
        </Modal.Header>

        <Modal.Content className="!p-6 !border-b">
          <Form>
            <Form.Field className="!m-0">
              <div className="relative">
                <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 z-10" />
                <Input
                  fluid
                  placeholder="Cari Nama atau Email"
                  onChange={onChangeEmailFilter}
                  value={modal.filter.emailOrName}
                  className="!w-full"
                  style={{ paddingLeft: '2.5rem' }}
                />
              </div>
            </Form.Field>
          </Form>
        </Modal.Content>

        <Modal.Content
          scrolling
          className="!p-6 !overflow-y-auto"
        >
          {modal.filteredAdminList.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <span>Data tidak ada / tidak ditemukan</span>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {modal.filteredAdminList.map((r) => (
              <AdminListItem
                admin={r}
                key={r.email}
              />
            ))}
          </div>
        </Modal.Content>

        <Modal.Actions className="!flex !items-center !justify-end !gap-3 !p-6 !bg-gray-50 !border-t !border-gray-200">
          <Button
            onClick={onClose}
            className="!bg-white !text-gray-700 !border !border-gray-300"
          >
            Tutup
          </Button>
          <Button
            primary
            onClick={onAddNewAdminClick}
            className="!flex !items-center !gap-2 !bg-blue-600 !text-white hover:!bg-blue-700"
          >
            <FiUserPlus className="w-5 h-5" />
            Tambah Baru
          </Button>
        </Modal.Actions>
      </Modal>
      <ModalAddNewAdmin />
    </>
  );
};

export default ModalListAdmin;

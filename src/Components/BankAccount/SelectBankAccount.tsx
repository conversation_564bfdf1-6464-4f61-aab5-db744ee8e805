import apisauce from 'apisauce';
import { Select } from 'semantic-ui-react';
import { useEffect, useState } from 'react';

export interface BankInfo {
  Code2: string;
  Name: string;
  PK: string;
  Popular: string;
  SK: string;
  ShortName: string;
  StockTicker: string;
  Swift: string;
}

const fetchBank = async () => {
  const fullUrl = 'https://43lvjrbbtj.execute-api.ap-southeast-1.amazonaws.com';

  const apisauceInstance = apisauce.create({
    baseURL: fullUrl,
  });

  const get = await apisauceInstance.get<{ data: BankInfo[] }>('/util/id/get-bank-list');
  return get.data?.data || [];
};

interface Props {
  onChange?: (info: BankInfo | null) => void;
  value?: string;
}

const SelectBankAccount = (props: Props) => {
  const [banks, setBanks] = useState<BankInfo[]>([]);
  const [loading, setLoading] = useState(true);

  const fetch = async () => {
    setLoading(true);
    const _fetch = await fetchBank();
    setBanks(_fetch);
    setLoading(false);
  };

  useEffect(() => {
    fetch().then();
  }, []);

  const onSelect = (id: string) => {
    const bank = banks.find((b) => b.SK === id) || null;

    props.onChange?.(bank);
  };

  return (
    <Select
      options={banks.map((b) => ({
        value: b.SK,
        label: b.ShortName,
        text: b.Name,
      }))}
      onChange={(event, data) => onSelect(data.value as string)}
      value={props.value || ''}
      loading={loading}
      placeholder="Pilih Bank"
    />
  );
};

export default SelectBankAccount;

import React, { Component } from 'react';
import { Button } from 'semantic-ui-react';
import { updateDoc } from 'firebase/firestore';
import { TMainReduxStates } from '../redux/types/redux-types';
import { connect } from 'react-redux';
import { mainStore } from '../redux/reducers';
import { fetchCustomerThunk } from '../redux/customerInfo/customerInfoSlice';

interface Props {
  urlGmap: string;
  customer: TMainReduxStates['customerReducer'];
  geo: { lat: number; long: number };
}

interface States {
  processing: boolean;
  successMessage: string;
}

class ButtonSetChatAsSurveyLocation extends Component<Props, States> {
  constructor(props: Props) {
    super(props);

    this.state = {
      processing: false,
      successMessage: '',
    };
  }

  onClick = async () => {
    const ref = this.props.customer.ref;
    if (!ref) return;

    this.setState({
      processing: true,
      successMessage: '',
    });

    try {
      await updateDoc(ref, {
        'survey.credit_scheme.surveyGmapUrl': this.props.urlGmap,
        'survey.credit_scheme.surveyGeo': this.props.geo,
      });
      this.setState({
        processing: false,
        successMessage: 'Berhasil dijadikan lokasi survey!',
      });

      mainStore.dispatch(
        fetchCustomerThunk({
          clientDocRef: this.props.customer.ref!,
        }) as any,
      );
    } catch (e) {
      this.setState({
        processing: false,
      });
    }
  };

  render() {
    return (
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <Button
          onClick={this.onClick}
          loading={this.state.processing}
          size="tiny"
        >
          Jadikan Lokasi Survey
        </Button>
        {this.state.successMessage && (
          <div
            style={{
              fontSize: '12px',
              padding: '4px 0',
              color: 'green',
            }}
          >
            {this.state.successMessage}
          </div>
        )}
      </div>
    );
  }
}

const mapStateToProps = (state: TMainReduxStates) => {
  return {
    customer: state.customerReducer,
  };
};

export default connect(mapStateToProps)(ButtonSetChatAsSurveyLocation);

import React from 'react';
import { Icon, SemanticICONS } from 'semantic-ui-react';

interface ChatContentWrapperProps {
  children: React.ReactNode;
  icon?: SemanticICONS;
  action?: React.ReactNode;
  isOutbound?: boolean;
}

const ChatContentWrapper: React.FC<ChatContentWrapperProps> = ({
  children,
  icon,
  action,
  isOutbound = false,
}) => {
  const baseClasses = 'flex flex-col rounded-lg mb-3';
  const inboundClasses = 'bg-gray-50 border border-gray-200';
  const outboundClasses = 'bg-white border border-blue-100';

  return (
    <div className={`${baseClasses} ${isOutbound ? outboundClasses : inboundClasses}`}>
      <div className="flex items-start gap-3 p-2">
        {icon && (
          <div className="flex-shrink-0 mt-0.5">
            <Icon
              name={icon}
              size="small"
              className={isOutbound ? 'text-blue-500' : 'text-gray-500'}
            />
          </div>
        )}
        <div className="flex-1 min-w-0">{children}</div>
      </div>
      {action && (
        <div
          className={`px-2 py-1.5 border-t ${isOutbound ? 'border-blue-100' : 'border-gray-200'}`}
        >
          {action}
        </div>
      )}
    </div>
  );
};

export default ChatContentWrapper;

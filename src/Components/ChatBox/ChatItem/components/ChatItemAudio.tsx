import React from 'react';
import ReactPlayer from 'react-player';
import { mainApiServices } from '../../../../services/MainApiServices';
import ChatContentWrapper from './ChatContentWrapper';
import MessageEntity from '../../../../entities/MessageEntity';

interface ChatItemAudioProps {
  message: MessageEntity;
  isOutbound?: boolean;
}

const ChatItemAudio: React.FC<ChatItemAudioProps> = ({ message, isOutbound = false }) => {
  if (message.message.type !== 'audio' || !message.message.audio) {
    return null;
  }

  const audio = message.message.audio;
  const audioUrl =
    audio.link || mainApiServices.getMediaUrl(message.ref, audio.filename || 'audio.mp3');

  return (
    <div className="mb-3">
      <div className="rounded-lg overflow-hidden border border-gray-200">
        <div className="p-3">
          <ReactPlayer
            url={audioUrl}
            controls={true}
            width="350px"
            height="50px"
            config={{
              file: {
                attributes: {
                  controlsList: 'nodownload',
                },
              },
            }}
            fallback={
              <div
                className={`text-sm p-4 text-center ${isOutbound ? 'text-white' : 'text-gray-400'}`}
              >
                Audio gagal dimuat
              </div>
            }
          />
        </div>
      </div>
    </div>
  );
};

export default ChatItemAudio;

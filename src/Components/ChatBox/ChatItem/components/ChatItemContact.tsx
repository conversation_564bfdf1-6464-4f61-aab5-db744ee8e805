import React from 'react';
import { Button } from 'semantic-ui-react';
import { useDispatch } from 'react-redux';
import { useSelector } from 'react-redux';
import { TMainReduxStates } from '../../../../redux/types/redux-types';
import modalSetPhoneNumberAsSlice from '../../../../redux/modal-update-phone-number/modalSetPhoneNumberAs.slice';
import ChatContentWrapper from './ChatContentWrapper';
import MessageEntity from '../../../../entities/MessageEntity';

interface ChatItemContactProps {
  message: MessageEntity;
  isOutbound?: boolean;
}

const ChatItemContact: React.FC<ChatItemContactProps> = ({ message, isOutbound = false }) => {
  const dispatch = useDispatch();
  const { admin } = useSelector((state: TMainReduxStates) => state.reducerAdmin);

  if (message.message.type !== 'contacts' || !message.message.contacts?.[0]) {
    return null;
  }

  const contact = message.message.contacts[0];
  const phoneNumber = contact.phones?.[0].wa_id;
  const name = contact.name.formatted_name;

  const handleClick = () => {
    dispatch(
      modalSetPhoneNumberAsSlice.actions.open({
        phoneNumber: phoneNumber,
      }),
    );
  };

  return (
    <ChatContentWrapper
      icon="user circle"
      isOutbound={isOutbound}
      action={
        <Button
          size="small"
          onClick={handleClick}
          className={`${
            isOutbound ? '!bg-white/10 hover:!bg-white/20' : '!bg-blue-600 hover:!bg-blue-700'
          } !text-white`}
        >
          Jadikan kontak sebagai...
        </Button>
      }
    >
      <div className={`${isOutbound ? 'text-white' : 'text-gray-700'}`}>
        <div className="font-medium">{name || 'Tidak Ada Nama'}</div>
        {admin?.admin_rank === 1 ? (
          <div className="text-sm mt-0.5">{phoneNumber || <i>Tidak ada nomor</i>}</div>
        ) : (
          <div className={`text-sm mt-0.5 ${isOutbound ? 'text-white/80' : 'text-gray-500'}`}>
            Nomor telepon kami sembunyikan
          </div>
        )}
      </div>
    </ChatContentWrapper>
  );
};

export default ChatItemContact;

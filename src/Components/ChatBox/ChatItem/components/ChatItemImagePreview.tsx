/*
  ChatItemImagePreview Component
  - Displays an image preview within a chat item with lazy loading support.
  - Handles image loading state, error state, and provides dropdown actions for additional options (e.g., setting image as document, saving to Google Bucket).
*/

import { useState, useMemo, useCallback, useRef, useEffect } from 'react';
import LazyLoad from 'react-lazyload';
import { FaImage, FaFileAlt, FaSpinner, FaEllipsisH } from 'react-icons/fa';
import { SiGooglecloud } from 'react-icons/si';
import { useDispatch } from 'react-redux';
import { mainApiServices } from '../../../../services/MainApiServices';
import modalSetImageAsSlice from '../../../../redux/modal-capture-image/modalSetImageAs.slice';
import MessageEntity from '../../../../entities/MessageEntity';
import successDialog from '../../../callableDialog/successDialog';
import errorDialog from '../../../callableDialog/errorDialog';
import { openLightbox } from '../../../../redux/lightbox/lightbox.slice';

// Define props interface
interface ChatItemImagePreviewProps {
  message: MessageEntity;
}

const IMAGE_HEIGHT = 384; // Constant height for images and placeholders

const ChatItemImagePreview = ({ message }: ChatItemImagePreviewProps) => {
  // Destructure the message object for easier access
  const { message: msg, ref } = message;
  const { type, image, direction } = msg;

  const dispatch = useDispatch();
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setDropdownOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Memoize the URL for the image based on message type and saved link
  const urlImage = useMemo(() => {
    if (type === 'image') {
      return image?.saved_link || mainApiServices.getImageUrl(ref);
    }
    return null;
  }, [type, image, ref]);

  // Handle click on the image to open it in a lightbox
  const handleImageClick = useCallback(() => {
    if (urlImage) {
      dispatch(openLightbox(urlImage));
    }
  }, [urlImage, dispatch]);

  // Handle saving the image to Google Bucket
  const saveImageToGoogleBucket = useCallback(async () => {
    try {
      await mainApiServices.saveImageToGoogleBucket(ref.path);
      successDialog({
        content: 'Berhasil menyimpan gambar ke Google Bucket',
        title: 'Berhasil',
        cancelButton: false,
      });
    } catch (error) {
      errorDialog({
        content: 'Gagal menyimpan gambar ke Google Bucket',
        title: 'Gagal',
        cancelButton: false,
      });
      console.error(error);
    }
  }, [ref.path]);

  // Render a placeholder with an icon when image is loading or fails
  const renderPlaceholder = useCallback(
    (icon: React.ReactNode) => (
      <div
        className="flex items-center justify-center text-gray-400 py-4"
        style={{ height: IMAGE_HEIGHT }}
      >
        {icon}
      </div>
    ),
    [],
  );

  // Memoized function to render the Dropdown for image actions
  const renderDropdown = useCallback(
    () => (
      <div
        className="absolute top-2 right-2 z-10"
        ref={dropdownRef}
      >
        <button
          type="button"
          onClick={() => setDropdownOpen((prev) => !prev)}
          className="p-2 bg-black bg-opacity-50 rounded-full text-white hover:bg-opacity-75 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-white"
        >
          <span className="sr-only">Open options</span>
          <FaEllipsisH />
        </button>
        {dropdownOpen && (
          <div className="origin-top-right absolute right-0 mt-2 w-64 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 py-1 focus:outline-none">
            <button
              type="button"
              onClick={() => {
                dispatch(
                  modalSetImageAsSlice.actions.open({
                    urlImageToSet: urlImage || undefined,
                    type: 'SET',
                  }),
                );
                setDropdownOpen(false);
              }}
              className="w-full text-left block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
            >
              <div className="flex items-center gap-2">
                <FaFileAlt className="align-middle" />
                <span className="leading-none">Jadikan Sebagai Dokumen</span>
              </div>
            </button>
            {!image?.saved_link && (
              <button
                type="button"
                onClick={() => {
                  saveImageToGoogleBucket();
                  setDropdownOpen(false);
                }}
                className="w-full text-left block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                <div className="flex items-center gap-2">
                  <SiGooglecloud className="align-middle" />
                  <span className="leading-none">Google Bucket</span>
                </div>
              </button>
            )}
          </div>
        )}
      </div>
    ),
    [dispatch, urlImage, saveImageToGoogleBucket, image, dropdownOpen],
  );

  return (
    <LazyLoad
      once
      scrollContainer=".chat-box"
      className="mb-2"
      placeholder={renderPlaceholder(
        <FaImage
          size={48}
          className="animate-pulse"
        />,
      )}
    >
      <div className="w-full flex justify-center relative">
        {/* Render the dropdown for incoming messages when image is loaded and no error */}
        {!imageError && imageLoaded && direction === 'IN' && renderDropdown()}
        {imageError ? (
          renderPlaceholder(<FaImage size={48} />)
        ) : (
          <>
            {/* Show spinner until the image loads */}
            {!imageLoaded &&
              renderPlaceholder(
                <FaSpinner
                  size={32}
                  className="animate-spin text-gray-400"
                />,
              )}
            {/* Render the image once loaded */}
            <img
              onLoad={() => setImageLoaded(true)}
              onClick={handleImageClick}
              src={urlImage || undefined}
              className={`rounded-lg cursor-pointer hover:opacity-90 transition-opacity object-cover w-full ${!imageLoaded ? 'hidden' : ''}`}
              alt="Gambar"
              onError={() => setImageError(true)}
              style={{
                height: IMAGE_HEIGHT,
                objectPosition: 'center',
              }}
            />
          </>
        )}
      </div>
    </LazyLoad>
  );
};

export default ChatItemImagePreview;

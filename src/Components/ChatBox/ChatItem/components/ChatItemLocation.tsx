import React from 'react';
import { Icon } from 'semantic-ui-react';
import ButtonSetChatAsSurveyLocation from '../../../ButtonSetChatAsSurveyLocation';
import ChatContentWrapper from './ChatContentWrapper';
import MessageEntity from '../../../../entities/MessageEntity';

interface ChatItemLocationProps {
  message: MessageEntity;
  isOutbound?: boolean;
}

const ChatItemLocation: React.FC<ChatItemLocationProps> = ({ message, isOutbound = false }) => {
  if (!message.message.location) {
    return null;
  }

  const location = message.message.location;
  const urlGmap = `https://www.google.com/maps/search/?api=1&query=${location.latitude},${location.longitude}`;

  return (
    <ChatContentWrapper
      icon="location arrow"
      isOutbound={isOutbound}
      action={
        <div className="space-y-2">
          <a
            href={urlGmap}
            target="_blank"
            rel="noreferrer"
            className={`${
              isOutbound ? 'text-blue-600 hover:text-blue-800' : 'text-blue-600 hover:text-blue-800'
            } text-sm inline-flex items-center gap-2`}
          >
            <Icon
              name="map marker alternate"
              size="small"
            />
            Buka di Google Maps
          </a>
          <ButtonSetChatAsSurveyLocation
            urlGmap={urlGmap}
            geo={{
              lat: location.latitude,
              long: location.longitude,
            }}
          />
        </div>
      }
    >
      <div
        className={`text-sm flex items-center gap-2 ${isOutbound ? 'text-gray-700' : 'text-gray-700'}`}
      >
        <Icon
          name="map pin"
          size="small"
          className={isOutbound ? 'text-gray-400' : 'text-gray-400'}
        />
        {location.latitude}, {location.longitude}
      </div>
    </ChatContentWrapper>
  );
};

export default ChatItemLocation;

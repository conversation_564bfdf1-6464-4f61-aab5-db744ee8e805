import React from 'react';
import { useSelector } from 'react-redux';
import { TMainReduxStates } from '../../../../redux/types/redux-types';
import MessageEntity from '../../../../entities/MessageEntity';
import HTMLReactParser from 'html-react-parser';

// Definisikan tipe untuk context
interface IMessageContext {
  from: string;
  id: string;
}

interface IProps {
  context: IMessageContext;
}

const ChatItemReplyContext: React.FC<IProps> = ({ context }) => {
  // Ambil semua pesan dari state Redux
  const messages = useSelector(
    (state: TMainReduxStates) => state.reducerConversation?.conversations || [],
  );

  // Cari pesan yang dibalas berdasarkan context.id
  const repliedMessage = messages.find((msg) => msg.message.id === context.id);

  if (!repliedMessage) {
    return (
      <div className="mb-2 p-2 rounded-lg bg-gray-100 border-l-4 border-gray-400">
        <p className="text-sm font-semibold text-gray-600">Pesan tidak ditemukan</p>
        <p className="text-xs text-gray-500 italic">Pesan asli mungkin sudah lama.</p>
      </div>
    );
  }

  const textBody = () => {
    const { message: m } = repliedMessage;
    let textBody: string = '';

    switch (m.type) {
      case 'document':
        textBody = m.document?.caption ?? '';
        break;
      case 'image':
        textBody = m.image?.caption ?? '';
        break;
      case 'video':
        textBody = m.video?.caption ?? '';
        break;
      case 'text':
        textBody = m.text?.body ?? '';
        break;
      case 'button':
        textBody = m.button?.text ?? '';
        break;
      case 'interactive':
        textBody = m.interactive?.body?.text ?? '';
        break;
    }
    return HTMLReactParser(textBody);
  };

  const renderMessagePreview = (message: MessageEntity['message']) => {
    switch (message.type) {
      case 'text':
        return <p className="text-sm text-gray-600 line-clamp-3">{textBody()}</p>;
      case 'image':
        return <p className="text-sm text-gray-600">📷 {textBody()}</p>;
      case 'video':
        return <p className="text-sm text-gray-600">🎥 {textBody()}</p>;
      case 'document':
        return <p className="text-sm text-gray-600">📄 {textBody()}</p>;
      case 'location':
        return <p className="text-sm text-gray-600">📍 {textBody()}</p>;
      case 'contacts':
        return (
          <p className="text-sm text-gray-600">👤 {message.contacts?.[0]?.name?.formatted_name}</p>
        );
      case 'interactive':
        return <p className="text-sm text-gray-600">💬 {textBody()}</p>;
      default:
        return <p className="text-sm text-gray-500 italic">Pesan</p>;
    }
  };

  const isIncoming = repliedMessage.message.direction === 'IN';

  const bgColorClass = isIncoming
    ? 'bg-blue-100/70 hover:bg-blue-200/70'
    : 'bg-green-100/70 hover:bg-green-200/70';
  const borderColorClass = isIncoming ? 'border-blue-400' : 'border-green-400';
  const textColorClass = isIncoming ? 'text-blue-600' : 'text-green-600';

  return (
    <div
      className={`mb-2 p-2 rounded-lg border-l-4 cursor-pointer transition-colors duration-200 ${bgColorClass} ${borderColorClass}`}
      onClick={() => {
        const targetId = `chat-item-row-${context.id}`;
        const targetElement = document.getElementById(targetId);
        if (targetElement) {
          targetElement.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
          });
          setTimeout(() => {
            targetElement.classList.add('highlight-message');
          }, 300); // Delay to allow scroll to finish

          setTimeout(() => {
            targetElement.classList.remove('highlight-message');
          }, 1800); // Total duration: 300ms delay + 1500ms animation
        }
      }}
    >
      <p className={`text-sm font-semibold ${textColorClass}`}>
        {repliedMessage.origin.display_name || repliedMessage.origin.id}
      </p>
      <div className="flex items-center">{renderMessagePreview(repliedMessage.message)}</div>
    </div>
  );
};

export default ChatItemReplyContext;

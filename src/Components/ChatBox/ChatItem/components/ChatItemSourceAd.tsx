import React from 'react';
import { Icon } from 'semantic-ui-react';
import ChatContentWrapper from './ChatContentWrapper';
import MessageEntity from '../../../../entities/MessageEntity';

interface ChatItemSourceAdProps {
  message: MessageEntity;
  isOutbound?: boolean;
}

const ChatItemSourceAd: React.FC<ChatItemSourceAdProps> = ({ message, isOutbound = false }) => {
  if (!message.message.referral) {
    return null;
  }

  const ref = message.message.referral;

  return (
    <ChatContentWrapper
      icon="bullhorn"
      isOutbound={isOutbound}
      action={
        <a
          href={ref.source_url}
          target="_blank"
          rel="noreferrer"
          className={`${
            isOutbound ? 'text-blue-600 hover:text-blue-800' : 'text-blue-600 hover:text-blue-800'
          } text-sm inline-flex items-center gap-2`}
        >
          <Icon
            name="external"
            size="small"
          />
          Lihat Iklan
        </a>
      }
    >
      <div className={`${isOutbound ? 'text-gray-700' : 'text-gray-700'}`}>
        <div className="font-medium">{ref.headline}</div>
        <div className="text-sm mt-1">{ref.body}</div>
        <div
          className={`text-xs mt-2 flex items-center gap-1 ${isOutbound ? 'text-gray-500' : 'text-gray-500'}`}
        >
          <Icon
            name="id card outline"
            size="small"
          />
          {ref.source_id}
        </div>
      </div>
    </ChatContentWrapper>
  );
};

export default ChatItemSourceAd;

import React from 'react';
import ChatContentWrapper from './ChatContentWrapper';
import MessageEntity from '../../../../entities/MessageEntity';

interface ChatItemUnsupportedProps {
  message: MessageEntity;
  isOutbound?: boolean;
}

const SUPPORTED_TYPES = [
  'text',
  'image',
  'interactive',
  'contacts',
  'location',
  'button',
  'document',
  'video',
  'audio',
];

const ChatItemUnsupported: React.FC<ChatItemUnsupportedProps> = ({
  message,
  isOutbound = false,
}) => {
  if (SUPPORTED_TYPES.includes(message.message.type)) {
    return null;
  }

  return (
    <ChatContentWrapper
      icon="warning sign"
      isOutbound={isOutbound}
    >
      <div className={`text-sm ${isOutbound ? 'text-gray-700' : 'text-gray-700'}`}>
        <div>Tipe pesan ini belum didukung</div>
        <div className={`text-xs mt-1 ${isOutbound ? 'text-gray-500' : 'text-gray-500'}`}>
          Tipe: {message.message.type}
        </div>
      </div>
    </ChatContentWrapper>
  );
};

export default ChatItemUnsupported;

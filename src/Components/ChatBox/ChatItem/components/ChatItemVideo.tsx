import React from 'react';
import ReactPlayer from 'react-player';
import { mainApiServices } from '../../../../services/MainApiServices';
import ChatContentWrapper from './ChatContentWrapper';
import MessageEntity from '../../../../entities/MessageEntity';

interface ChatItemVideoProps {
  message: MessageEntity;
  isOutbound?: boolean;
}

const ChatItemVideo: React.FC<ChatItemVideoProps> = ({ message, isOutbound = false }) => {
  if (message.message.type !== 'video' || !message.message.video) {
    return null;
  }

  const video = message.message.video;
  const videoUrl =
    video.link || mainApiServices.getMediaUrl(message.ref, video.filename || 'video.mp4');

  return (
    <div className="mb-3">
      <div className="rounded-lg overflow-hidden">
        <ReactPlayer
          url={videoUrl}
          controls={true}
          width="100%"
          height="384px"
          config={{
            file: {
              attributes: {
                controlsList: 'nodownload',
              },
            },
          }}
          fallback={
            <div
              className={`text-sm p-4 text-center ${isOutbound ? 'text-white' : 'text-gray-400'}`}
            >
              Video gagal dimuat
            </div>
          }
        />
      </div>
    </div>
  );
};

export default ChatItemVideo;

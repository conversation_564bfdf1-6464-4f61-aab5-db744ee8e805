import React, { useRef, useCallback } from 'react';
import { Button, Dropdown, Message } from 'semantic-ui-react';
import { FaLock, FaRobot } from 'react-icons/fa';
import { IChatTextInputProps } from '../types/chat-text-input-types';
import { useDispatch, useSelector } from 'react-redux';
import { TMainReduxStates } from '../../../redux/types/redux-types';
import ModalSelectCatalogue from '../../ModalSelectCatalogue/ModalSelectCatalogue';
import { mainApiServices } from '../../../services/MainApiServices';
import MessageTemplateEntity from '../../../entities/MessageTemplateEntity';
import TemplateModal from '../../Template/TemplateModal';
import moment, { Moment } from 'moment';
import { Timestamp } from 'firebase/firestore';
import { mainStore } from '../../../redux/reducers';
import modalInitMessageSlice from '../../../redux/modal-init-message/modalInitMessageSlice';
import ModalSendPromo from '../../ModalPromo/ModalSendPromo';
import { AxiosError } from 'axios';
import modalPriceListSlice from '../../../redux/modal-price-list/modalPriceListSlice';
import modalPriceListSliceSimple from '../../../redux/modal-price-list-simple/modalPriceListSliceSimple';
import { openModal } from '../../../redux/modal-amarta-art/modalAmartaArt.slice';
import chatTextInputSlice, { MediaType } from '../../../redux/chat-text-input/chatTextInputSlice';
import { IMessageSendV2Params } from '../../../services/types/mainApiService/mainApiService.types';
import modalSelectCatalogueSlice from '../../../redux/modal-select-catalogue/modalSelectCatalogueSlice';
import FilePreview from './FilePreview';
import modalSendPromoSlice from '../../../redux/modal-send-promo/modalSendPromoSlice';
import modalDealerDataSlice from '../../../redux/modal-dealer-data/modalDealerDataSlice';
import ModalDealerData from '../../ModalDealerData/ModalDealerData';
import ChatTextInputTextArea from './ChatTextInputTextArea';
import { BsPlus } from 'react-icons/bs';
import { IoSendSharp } from 'react-icons/io5';
import {
  FaBoxOpen,
  FaDollarSign,
  FaFile,
  FaPalette,
  FaRegFileAlt,
  FaTable,
  FaThList,
  FaTicketAlt,
  FaBuilding,
} from 'react-icons/fa';
import { BiErrorCircle } from 'react-icons/bi';
import { HiOutlineClock } from 'react-icons/hi';
import { useScrollContainer } from '../../../contexts/ScrollContainerContext';

const ChatTextInput: React.FC<IChatTextInputProps> = () => {
  const dispatch = useDispatch();
  const inputFileRef = useRef<HTMLInputElement>(null);
  const { scrollContainerRef } = useScrollContainer();

  // Redux state selectors
  const conversation = useSelector((state: TMainReduxStates) => state.reducerConversation);
  const agentAiReply = conversation.chatRoom?.agent_ai_reply ?? false;
  const project = useSelector((state: TMainReduxStates) => state.reducerProject);
  const admin = useSelector((state: TMainReduxStates) => state.reducerAdmin);
  const chatTextInput = useSelector((state: TMainReduxStates) => state.chatTextInput);
  const modalSelectCatalogue = useSelector((state: TMainReduxStates) => state.modalSelectCatalogue);
  const customer = useSelector((state: TMainReduxStates) => state.customerReducer);

  const onTemplateClick = async (template: MessageTemplateEntity) => {
    if (chatTextInput.sending) return;

    mainStore.dispatch(chatTextInputSlice.actions.setHtmlContentEditable(template.text));
    await onSendTemplate(template.ref.path);
  };

  const templateModal = {
    onOpen: () => mainStore.dispatch(chatTextInputSlice.actions.setTemplateModalVisibility(true)),
    onClose: () => mainStore.dispatch(chatTextInputSlice.actions.setTemplateModalVisibility(false)),
    onSend: async (template: MessageTemplateEntity) => {
      mainStore.dispatch(chatTextInputSlice.actions.setTemplateModalVisibility(false));
      await onTemplateClick(template);
    },
  };

  const templateModalProvider = () => {
    mainStore.dispatch(
      modalInitMessageSlice.actions.open({
        open: true,
        preContact: {
          name: conversation.chatRoom?.headers.title ?? '',
          phoneNumber: conversation.chatRoom?.contacts?.[0] ?? '',
        },
      }),
    );
  };

  const onSendTemplate = async (templatePath: string) => {
    if (chatTextInput.sending) return false;

    mainStore.dispatch(chatTextInputSlice.actions.setSending(true));
    mainStore.dispatch(chatTextInputSlice.actions.setErrorMessage(''));

    const send = await mainApiServices.sendMessageTemplateInternal({
      roomPath: conversation.chatRoom!.ref!.path,
      templatePath: templatePath,
      adminSessionPath: admin.adminSession!.ref.path,
      phoneNumber: conversation.chatRoom?.contacts[0] ?? '',
    });

    if (send.ok) {
      mainStore.dispatch(chatTextInputSlice.actions.resetState());
    } else {
      const error: AxiosError<any> = send.originalError;
      mainStore.dispatch(
        chatTextInputSlice.actions.setErrorMessage(
          error.response?.data.messages || 'Error pada server',
        ),
      );
    }
  };

  const onSend = async () => {
    if (chatTextInput.sending) return;

    const currentScrollRecentChatPosition = scrollContainerRef.current?.scrollTop;

    const text = chatTextInput.htmlContentEditable.trim();
    if (text === '' && !chatTextInput.file) return;

    mainStore.dispatch(chatTextInputSlice.actions.setSending(true));
    mainStore.dispatch(chatTextInputSlice.actions.setErrorMessage(''));

    const payload: IMessageSendV2Params = {
      roomPath: conversation.chatRoom!.ref!.path,
      phoneNumber: conversation.chatRoom?.contacts[0] ?? '',
      adminSessionPath: admin.adminSession!.ref.path,
      text: chatTextInput.htmlContentEditable,
    };

    switch (chatTextInput.file?.source) {
      case 'local':
        payload.media = chatTextInput.file.local?.file;
        payload.filename = chatTextInput.file.local?.name;
        payload.fileType = chatTextInput.file.local?.type;
        break;
      case 'url':
        payload.fileUrl = chatTextInput.file.url?.url;
        payload.filename = chatTextInput.file.url?.name;
        payload.fileType = chatTextInput.file.url?.type;
        break;
      case 'whatsappMediaId':
        payload.fileMediaId = chatTextInput.file.whatsappMediaId?.id;
        payload.fileType = chatTextInput.file.whatsappMediaId?.type;
        break;
    }

    const send = await mainApiServices.sendMessageV2(payload);

    if (send.ok) {
      mainStore.dispatch(chatTextInputSlice.actions.resetState());

      setTimeout(() => {
        scrollContainerRef.current?.scrollTo({
          top: currentScrollRecentChatPosition,
        });
      }, 80);
    } else {
      const error: AxiosError<any> = send.originalError;
      mainStore.dispatch(
        chatTextInputSlice.actions.setErrorMessage(
          error.response?.data.messages || 'Error pada server',
        ),
      );
    }

    mainStore.dispatch(chatTextInputSlice.actions.setSending(false));
  };

  const onFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files![0];

    let mediaType: MediaType;
    if (file.type.startsWith('image/')) {
      mediaType = 'image';
    } else if (file.type.startsWith('video/')) {
      mediaType = 'video';
    } else if (file.type.startsWith('audio/')) {
      mediaType = 'audio';
    } else {
      mediaType = 'document';
    }

    mainStore.dispatch(
      chatTextInputSlice.actions.setFile({
        source: 'local',
        local: {
          file: file,
          name: file.name,
          type: mediaType,
        },
      }),
    );
    event.target.value = '';
  };

  const selectFile = () => {
    inputFileRef.current?.click();
  };

  const onModalSendPromoCodeClick = () => {
    mainStore.dispatch(modalSendPromoSlice.actions.open());
  };

  const catalogueModal = {
    onCataloguePressed: () => {
      mainStore.dispatch(modalSelectCatalogueSlice.actions.setOpen());
    },
  };

  const onModalAmartaArtClick = () => {
    mainStore.dispatch(openModal());
  };

  const onModalDealerDataClick = () => {
    mainStore.dispatch(modalDealerDataSlice.actions.open());
  };

  // Handle sending dealer data to chat
  const handleSendDealerData = useCallback((send: { text: string; image?: string }) => {
    if (send.image) {
      const filename = send.image?.split('/').pop()?.split('?')[0];
      // Save image to chat state
      mainStore.dispatch(
        chatTextInputSlice.actions.setFile({
          source: 'url',
          url: {
            url: send.image || '',
            name: filename || '',
            type: 'image',
          },
        }),
      );
    } else {
      mainStore.dispatch(chatTextInputSlice.actions.setFile(null));
    }

    // Set dealer data text to chat input
    mainStore.dispatch(chatTextInputSlice.actions.setHtmlContentEditable(send.text));
    mainStore.dispatch(modalDealerDataSlice.actions.close());
  }, []);

  const isExpired = () => {
    const { conversations } = conversation;
    const filterInbound = conversations.filter((m) => m.message.direction === 'IN');

    let isExpired = false;
    let lastInboundMessages: Moment | undefined;

    if (filterInbound.length === 0) return isExpired;

    for (const messageEntity of filterInbound) {
      if (!lastInboundMessages) {
        lastInboundMessages = moment(
          (messageEntity.message.timestamp as any as Timestamp).toDate(),
        );
      } else {
        const currentCreatedAt = moment(
          (messageEntity.message.timestamp as any as Timestamp).toDate(),
        );
        if (currentCreatedAt.isAfter(lastInboundMessages)) lastInboundMessages = currentCreatedAt;
      }
    }

    if (!lastInboundMessages) return isExpired;

    const last24Hours = lastInboundMessages.clone().add(24, 'h');

    if (moment().isAfter(last24Hours)) {
      isExpired = true;
    }

    return isExpired;
  };

  if (!admin.admin?.doc_department) {
    if (admin?.admin?.level === 'admin' && !project.project?.null_department_reply_message) {
      return <div />;
    }
  }

  const currentAdmin = admin.admin!;
  const chatRoom = conversation.chatRoom!;

  const AccessRestrictedMessage = ({
    icon,
    message,
    actionButton,
  }: {
    icon: React.ReactNode;
    message: React.ReactNode;
    actionButton?: React.ReactNode;
  }) => (
    <div className={'!px-3 !mt-2'}>
      <Message
        size={'mini'}
        color={'orange'}
        className="!w-full !rounded-md !shadow-sm"
      >
        <div className="flex items-center">
          <span className="mr-2 text-lg">{icon}</span>
          <span className="flex-1">{message}</span>
          {actionButton}
        </div>
      </Message>
    </div>
  );

  if (chatRoom.exclusive_admin?.email && chatRoom.exclusive_admin.email !== currentAdmin.email) {
    return (
      <AccessRestrictedMessage
        icon={<FaLock className="text-gray-500" />}
        message={
          <>
            Hanya admin <strong>{chatRoom.exclusive_admin.email}</strong> yang bisa membalas
            percakapan ini.
          </>
        }
      />
    );
  }

  const onDeactivateAiReply = async () => {
    await mainApiServices.toggleAgentAiReply({
      chatRoomRefPath: conversation.chatRoom!.ref!.path,
      status: false,
      source: 'admin',
    });
  };

  if (chatRoom.agent_ai_reply) {
    let disabled = false;
    const totalInbound = conversation.conversations.filter(
      (m) => m.message.direction === 'IN',
    ).length;
    if (totalInbound < 10) {
      disabled = true;
    }
    return (
      <AccessRestrictedMessage
        icon={<FaRobot className="text-gray-500" />}
        message="Percakapan ini sedang ditangani oleh AI. Anda tidak dapat mengirim pesan saat ini."
        actionButton={
          <Button
            size="mini"
            color="red"
            onClick={onDeactivateAiReply}
            disabled={disabled}
          >
            Nonaktifkan AI
          </Button>
        }
      />
    );
  }

  const { file } = chatTextInput;
  let previewUrl: string | null = null;
  let previewName: string = '';
  let source: string = '';

  if (file) {
    if (file.source === 'local' && file.local) {
      if (file.local.file.type.startsWith('image/') || file.local.file.type.startsWith('video/')) {
        previewUrl = URL.createObjectURL(file.local.file);
        previewName = file.local.name;
        source = 'local';
      } else {
        previewName = file.local.name;
        source = 'local';
      }
    } else if (file.source === 'url' && file.url) {
      if (file.url.type === 'image' || file.url.type === 'video') {
        previewUrl = file.url.url;
      }
      previewName = file.url.name;
      source = 'url';
    } else if (file.source === 'whatsappMediaId' && file.whatsappMediaId) {
      previewUrl = file.whatsappMediaId.previewUrl || null;
      previewName = file.whatsappMediaId.id || '';
      source = 'whatsappMediaId';
    }
  }

  return (
    <div className="chat-text-input-container sticky bottom-0 left-0 right-0 bg-white shadow-sm rounded-lg p-3 border-t border-gray-100">
      <React.Fragment>
        {(previewUrl || previewName) && (
          <div className="mb-3">
            <FilePreview
              previewUrl={previewUrl}
              previewName={previewName}
              source={source}
            />
          </div>
        )}

        {chatTextInput.errorMessage && (
          <Message
            negative
            size="mini"
            className="mb-3 rounded-md shadow-sm [animation:fadeIn_0.3s_ease-in-out]"
          >
            <div className="flex items-center">
              <BiErrorCircle className="mr-2 text-lg" />
              <span>Gagal mengirim pesan: {chatTextInput.errorMessage}</span>
            </div>
          </Message>
        )}

        <div className="flex gap-2 items-center">
          <Dropdown
            icon={<BsPlus />}
            button
            className="icon !rounded-full flex items-center justify-center hover:!bg-gray-100 transition-colors !m-0"
          >
            <Dropdown.Menu className="!mt-2 !rounded-lg !shadow-lg !border-gray-100">
              <Dropdown.Item
                onClick={templateModal.onOpen}
                className="!text-gray-700 hover:!bg-gray-50"
                disabled={isExpired()}
              >
                <div className="flex items-center">
                  <FaRegFileAlt className="mr-2 text-lg" />
                  <span>Kirim Template Internal</span>
                </div>
              </Dropdown.Item>
              <Dropdown.Item
                onClick={templateModalProvider}
                className="!text-gray-700 hover:!bg-gray-50"
              >
                <div className="flex items-center">
                  <FaThList className="mr-2 text-lg" />
                  <span>Kirim Template Provider</span>
                </div>
              </Dropdown.Item>
              <Dropdown.Divider />
              <Dropdown.Item
                onClick={catalogueModal.onCataloguePressed}
                className="!text-gray-700 hover:!bg-gray-50"
                disabled={isExpired()}
              >
                <div className="flex items-center">
                  <FaBoxOpen className="mr-2 text-lg" />
                  <span>Kirim Katalog</span>
                </div>
              </Dropdown.Item>
              <Dropdown.Item
                onClick={onModalSendPromoCodeClick}
                className="!text-gray-700 hover:!bg-gray-50"
                disabled={isExpired()}
              >
                <div className="flex items-center">
                  <FaTicketAlt className="mr-2 text-lg" />
                  <span>Kirim Promo</span>
                </div>
              </Dropdown.Item>
              <Dropdown.Divider />
              <Dropdown.Item
                onClick={() => dispatch(modalPriceListSlice.actions.open())}
                className="!text-gray-700 hover:!bg-gray-50"
                disabled={isExpired()}
              >
                <div className="flex items-center">
                  <FaDollarSign className="mr-2 text-lg" />
                  <span>Kirim Price List</span>
                </div>
              </Dropdown.Item>
              <Dropdown.Item
                onClick={() => dispatch(modalPriceListSliceSimple.actions.open())}
                className="!text-gray-700 hover:!bg-gray-50"
                disabled={isExpired()}
              >
                <div className="flex items-center">
                  <FaTable className="mr-2 text-lg" />
                  <span>Kirim Price List Table</span>
                </div>
              </Dropdown.Item>
              <Dropdown.Divider />
              {admin.admin?.level === 'owner' && (
                <Dropdown.Item
                  disabled={true}
                  onClick={selectFile}
                  className="!text-gray-400"
                >
                  <div className="flex items-center">
                    <FaFile className="mr-2 text-lg" />
                    <span>Kirim File</span>
                  </div>
                </Dropdown.Item>
              )}
              <Dropdown.Item
                onClick={onModalAmartaArtClick}
                className="!text-gray-700 hover:!bg-gray-50"
                disabled={isExpired()}
              >
                <div className="flex items-center">
                  <FaPalette className="mr-2 text-lg" />
                  <span>Kirim Media Art</span>
                </div>
              </Dropdown.Item>
              <Dropdown.Item
                onClick={onModalDealerDataClick}
                className="!text-gray-700 hover:!bg-gray-50"
                disabled={isExpired()}
              >
                <div className="flex items-center">
                  <FaBuilding className="mr-2 text-lg" />
                  <span>Kirim Data Dealer</span>
                </div>
              </Dropdown.Item>
            </Dropdown.Menu>
          </Dropdown>

          {isExpired() ? (
            <Message
              size={'mini'}
              color={'red'}
              negative={true}
              className="!m-0 !w-full !rounded-md !shadow-sm"
            >
              <div className="flex items-center">
                <HiOutlineClock className="mr-2 text-lg" />
                <span>Sesi chat sudah expired!</span>
              </div>
            </Message>
          ) : (
            <div className="flex-1">
              <ChatTextInputTextArea
                send={onSend}
                disabledByAI={agentAiReply}
              />
            </div>
          )}

          {!isExpired() && (
            <Button
              icon={<IoSendSharp />}
              disabled={chatTextInput.sending || agentAiReply}
              loading={chatTextInput.sending}
              onClick={() => onSend()}
              className="!rounded-full flex items-center justify-center !bg-blue-500 hover:!bg-blue-600 !text-white transition-colors"
            />
          )}
        </div>

        <input
          accept={'image/*,video/*,audio/*'}
          type={'file'}
          hidden={true}
          ref={inputFileRef}
          onChange={onFileChange}
        />
        {modalSelectCatalogue.visible && <ModalSelectCatalogue />}
        <TemplateModal
          open={chatTextInput.templateModalVisibility}
          onClose={templateModal.onClose}
          onSendTemplate={templateModal.onSend}
        />
        <ModalSendPromo
          model={customer!.client?.dream_vehicle?.model_name ?? ''}
          cityGroup={customer!.client?.profile.area?.text ?? ''}
        />
        <ModalDealerData onClick={handleSendDealerData} />
      </React.Fragment>
    </div>
  );
};

export default ChatTextInput;

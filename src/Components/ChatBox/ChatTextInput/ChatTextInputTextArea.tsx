import React, { useEffect, useRef } from 'react';
import TextareaAutosize from 'react-textarea-autosize';
import { useDispatch, useSelector } from 'react-redux';
import { TMainReduxStates } from '../../../redux/types/redux-types';
import chatTextInputSlice from '../../../redux/chat-text-input/chatTextInputSlice';

interface Props {
  send: () => Promise<void>;
  disabledByAI: boolean;
}

const ChatTextInputTextArea = (props: Props) => {
  const dispatch = useDispatch();
  const chatTextInput = useSelector((state: TMainReduxStates) => state.chatTextInput);
  const conversation = useSelector((state: TMainReduxStates) => state.reducerConversation);

  // Buat ref untuk textarea
  const textAreaRef = useRef<HTMLTextAreaElement>(null);

  const handleChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    dispatch(chatTextInputSlice.actions.setHtmlContentEditable(event.target.value));
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // Jika Shift+Enter, biarkan default behavior untuk membuat baris baru
    if (event.key === 'Enter' && event.shiftKey) {
      return;
    }

    // Jika hanya Enter, kirim pesan
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault(); // Mencegah line break
      onSend().catch(() => {}); // Panggil onSend dan handle error jika ada
    }
  };

  const onSend = async () => {
    console.log('Sending...');
    try {
      await props.send();
      setTimeout(() => {
        textAreaRef.current?.focus();
      }, 200);
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  useEffect(() => {
    setTimeout(() => {
      textAreaRef.current?.focus();
    }, 200);
  }, [conversation.chatRoom?.ref.id]);

  return (
    <TextareaAutosize
      ref={textAreaRef}
      minRows={1}
      maxRows={6}
      placeholder="Balas Pesan..."
      value={chatTextInput.htmlContentEditable}
      onChange={handleChange}
      onKeyDown={handleKeyDown}
      className="w-full px-4 py-2.5 bg-gray-50 border border-gray-200 rounded-3xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none text-sm transition-all hover:bg-gray-100 placeholder-gray-400"
      disabled={chatTextInput.sending || props.disabledByAI}
    />
  );
};

export default ChatTextInputTextArea;

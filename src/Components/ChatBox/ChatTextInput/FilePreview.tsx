import React from 'react';
import { <PERSON><PERSON>, Icon } from 'semantic-ui-react';
import { useDispatch, useSelector } from 'react-redux';
import { TMainReduxStates } from '../../../redux/types/redux-types';
import chatTextInputSlice from '../../../redux/chat-text-input/chatTextInputSlice';

type FilePreviewProps = {
  previewUrl: string | null;
  previewName: string;
  source: string;
};

const FilePreview: React.FC<FilePreviewProps> = ({ previewUrl, previewName, source }) => {
  const dispatch = useDispatch();
  const sending = useSelector((state: TMainReduxStates) => state.chatTextInput.sending);

  const handleDelete = () => {
    dispatch(chatTextInputSlice.actions.setFile(null));
  };

  const isVideo = previewUrl?.match(/\.(mp4|mov|avi)$/i);

  return (
    <div className="relative inline-block bg-gray-50 rounded-lg overflow-hidden shadow-sm border border-gray-100 transition-all hover:shadow-md">
      <div className="w-[140px]">
        {previewUrl ? (
          <div className="relative w-[140px] h-[140px]">
            {isVideo ? (
              <video
                src={previewUrl}
                className="w-full h-full object-cover"
                muted
                loop
                playsInline
              />
            ) : (
              <>
                <img
                  src={previewUrl}
                  alt="Preview"
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 transition-opacity hover:bg-opacity-10" />
              </>
            )}
          </div>
        ) : (
          <div className="w-[140px] h-[140px] flex justify-center items-center bg-gray-50">
            <Icon
              name="file"
              size="big"
              className="!text-gray-400"
            />
          </div>
        )}

        <Button
          icon="trash"
          size="mini"
          circular
          title="Hapus file"
          onClick={handleDelete}
          disabled={sending}
          className="!absolute !top-2 !right-2 !bg-white !shadow-sm hover:!bg-red-50 !text-red-500 !transition-colors"
        />

        <div className="p-2 text-center bg-white">
          <div className="text-xs font-medium text-gray-600 mb-1">{source}</div>
          <div className="text-xs text-gray-400 truncate max-w-full px-2">
            {previewName || 'File Attachment'}
          </div>
          {isVideo && (
            <div className="absolute bottom-8 left-2 bg-black/50 text-white text-xs px-2 py-1 rounded">
              🎥 Video
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FilePreview;

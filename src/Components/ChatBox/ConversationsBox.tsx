import React, { useEffect, useMemo, useRef } from 'react';
import { Icon, Ref } from 'semantic-ui-react';
import ChatItem from './ChatItem/ChatItem';
import { useSelector } from 'react-redux';
import { TMainReduxStates } from '../../redux/types/redux-types';
import moment, { Moment } from 'moment';
import isSameDay from '../../helpers/isSameday';

// Pisahkan komponen tanggal untuk memoization
const DateLabel = React.memo(({ date }: { date: Moment }) => (
  <div className="w-full text-center my-5">
    <div className="bg-gray-500 text-white px-4 py-2 rounded-full text-sm font-medium shadow-sm inline-flex items-center gap-2">
      <Icon name="calendar" />
      {date.format('DD MMM, YYYY')}
    </div>
  </div>
));

const ConversationsBox = () => {
  const box = useRef<HTMLElement>(null);
  const isFirstRender = useRef(true);
  const activeDayRef = useRef<Moment | null>(null); // Gunakan ref untuk tracking hari aktif

  const { conversations } = useSelector((states: TMainReduxStates) => states.reducerConversation);
  const { file } = useSelector((states: TMainReduxStates) => states.chatTextInput);

  const scrollDown = useRef(() => {
    box.current?.scrollTo({
      top: box.current.scrollHeight,
      behavior: 'instant',
    });
  }).current; // Gunakan ref untuk fungsi stabil

  useEffect(() => {
    const timer = setTimeout(() => {
      scrollDown();
      isFirstRender.current = false;
    }, 300);
    return () => clearTimeout(timer);
  }, [scrollDown]);

  useEffect(() => {
    if (!isFirstRender.current) scrollDown();
  }, [conversations.length, file, scrollDown]);

  const renderConversations = useMemo(() => {
    activeDayRef.current = null; // Reset hari aktif sebelum render

    return conversations.map((conversation, index) => {
      const currentMoment = moment.unix(conversation.message.unixtime);
      const show = !activeDayRef.current || !isSameDay(currentMoment, activeDayRef.current);
      if (show) activeDayRef.current = currentMoment;

      return (
        <React.Fragment key={conversation.message.id}>
          {show && activeDayRef.current && <DateLabel date={activeDayRef.current} />}
          <ChatItem message={conversation} />
        </React.Fragment>
      );
    });
  }, [conversations]);

  return (
    <Ref innerRef={box}>
      <div className="chat-box flex-1 min-h-0 relative">
        {' '}
        {/* tambahkan relative */}
        <div className="conversation-box h-full">{renderConversations}</div>
      </div>
    </Ref>
  );
};

export default React.memo(ConversationsBox);

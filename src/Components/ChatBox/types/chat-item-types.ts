import MessageEntity from '../../../entities/MessageEntity';
import { TMainReduxStates } from '../../../redux/types/redux-types';

export interface IChatItemProps {
  message: MessageEntity;
  admin: TMainReduxStates['reducerAdmin'];
  conversation: TMainReduxStates['reducerConversation'];
}

export interface IChatItemStates {
  imageErrorFetch: boolean;
  dropdownOpen: boolean;
  isHovered: boolean;
}

import React from 'react';
import { But<PERSON>, Container } from 'semantic-ui-react';
import { mainStore } from '../../redux/reducers';
import customerFinalDecisionSlice from '../../redux/customer-final-decission/customerFinalDecision.slice';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { useSelector } from 'react-redux';
import moment from 'moment';
import { MdUpdate, MdDescription, MdSchedule } from 'react-icons/md';
import { IoCheckmarkCircle } from 'react-icons/io5';

interface InfoItemProps {
  icon: React.ReactNode;
  label: string;
  value: React.ReactNode;
}

const InfoItem: React.FC<InfoItemProps> = ({ icon, label, value }) => (
  <div className="flex items-center p-3 bg-white rounded-lg border border-gray-100 hover:border-gray-200 transition-all">
    <div className="text-xl text-gray-400 mr-3">{icon}</div>
    <div className="flex-grow">
      <div className="text-sm text-gray-500 font-medium">{label}</div>
      <div className="text-gray-700 mt-0.5">{value}</div>
    </div>
  </div>
);

const ClientFinalDecision: React.FC = () => {
  const customer = useSelector((state: TMainReduxStates) => state.customerReducer);

  const onModalOpenClick = () => {
    let followUpDate = customer.client?.finalDecision?.followUp?.dateTime
      ? moment(customer.client.finalDecision.followUp.dateTime.toDate())
      : null;

    mainStore.dispatch(
      customerFinalDecisionSlice.actions.modalOpen({
        finalDecision: (customer.client?.finalDecision?.result || null) as any,
        followUp: customer.client?.finalDecision?.followUp
          ? {
              date: followUpDate?.toDate() || null,
              time: followUpDate?.format('HH:mm') || '',
              message: {
                template: null,
                _template: customer.client.finalDecision.followUp.message.template || '',
                variables: customer.client.finalDecision.followUp.message.variables || [],
              },
            }
          : null,
      }),
    );
  };

  const isButtonDisabled = () => {
    if (customer.client?.finalDecision?.result === 'needToFollowUp') {
      const sendAt = moment(customer.client.finalDecision.followUp!.dateTime.toDate());
      if (moment().isBefore(sendAt)) {
        return true;
      }
    }
    return false;
  };

  return (
    <Container>
      <div className="space-y-3">
        <InfoItem
          icon={<IoCheckmarkCircle />}
          label="Hasil"
          value={
            customer.client?.finalDecision?.result || <i className="text-gray-400">Belum Ada</i>
          }
        />

        <InfoItem
          icon={<MdUpdate />}
          label="Tanggal Update"
          value={
            customer.client?.finalDecision ? (
              moment(customer.client.finalDecision.updatedAt.toDate()).format('DD MMM YYYY HH:mm')
            ) : (
              <i className="text-gray-400">Belum Ada</i>
            )
          }
        />

        {customer.client?.finalDecision?.followUp?.message.template && (
          <InfoItem
            icon={<MdDescription />}
            label="Template"
            value={customer.client.finalDecision.followUp.message.template}
          />
        )}

        {customer.client?.finalDecision?.followUp?.dateTime && (
          <InfoItem
            icon={<MdSchedule />}
            label="Jadwal Kirim"
            value={moment(customer.client.finalDecision.followUp.dateTime.toDate()).format(
              'DD MMM YYYY HH:mm',
            )}
          />
        )}

        <div className="pt-3">
          <Button
            disabled={isButtonDisabled()}
            onClick={onModalOpenClick}
            className={`
                            !bg-blue-500 !text-white !font-medium
                            hover:!bg-blue-600 disabled:!bg-gray-300
                            !transition-colors !rounded-lg !px-6
                        `}
          >
            Update
          </Button>
        </div>
      </div>
    </Container>
  );
};

export default ClientFinalDecision;

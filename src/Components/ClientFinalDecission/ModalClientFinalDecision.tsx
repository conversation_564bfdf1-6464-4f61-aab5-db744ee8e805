import React, { Component } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  Container,
  Form,
  Header,
  Input,
  Modal,
  Segment,
  Select,
} from 'semantic-ui-react';
import DatePicker from 'react-datepicker';
import { connect } from 'react-redux';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { DropdownProps } from 'semantic-ui-react/dist/commonjs/modules/Dropdown/Dropdown';
import { mainStore } from '../../redux/reducers';
import customerFinalDecisionSlice, {
  fetchTemplateFinalDecision,
} from '../../redux/customer-final-decission/customerFinalDecision.slice';
import { mainApiServices } from '../../services/MainApiServices';
import moment from 'moment';
import { AxiosError } from 'axios';
import { fetchCustomerThunk } from '../../redux/customerInfo/customerInfoSlice';

interface IModalClientFinalDecision {
  modal: TMainReduxStates['modalUpdateCustomerFinalDecision'];
  customer: TMainReduxStates['customerReducer'];
}

class ModalClientFinalDecision extends Component<IModalClientFinalDecision> {
  onResultChange = (e: React.SyntheticEvent<HTMLElement, Event>, d: DropdownProps) => {
    mainStore.dispatch(customerFinalDecisionSlice.actions.changeFinalDecision(d.value as any));
  };

  onDateChange = (date: Date | null) => {
    if (date) {
      mainStore.dispatch(customerFinalDecisionSlice.actions.changeFollowUpDate(date));
    } else {
      mainStore.dispatch(customerFinalDecisionSlice.actions.changeFollowUpDate(null));
    }
  };

  onTimeChange = (e: React.SyntheticEvent<HTMLElement, Event>, d: DropdownProps) => {
    mainStore.dispatch(customerFinalDecisionSlice.actions.changeFollowUpTime(d.value as string));
  };

  onTemplateChange = (e: React.SyntheticEvent<HTMLElement, Event>, d: DropdownProps) => {
    const templates = this.props.modal.availableTemplates;
    const selectedTemplate = templates.find((t) => t.id === d.value);
    if (selectedTemplate) {
      mainStore.dispatch(
        customerFinalDecisionSlice.actions.changeFollowUpTemplateMessage(selectedTemplate),
      );
    }
  };

  onCancel = () => {
    mainStore.dispatch(customerFinalDecisionSlice.actions.close());
  };

  submit = async () => {
    if (!this.props.modal.finalDecision) return;

    mainStore.dispatch(customerFinalDecisionSlice.actions.setLoading(true));

    try {
      await mainApiServices.updateCustomerDecision({
        followUp: {
          date: moment(this.props.modal.followUp.date).format('YYYY-MM-DD'),
          time: this.props.modal.followUp.time,
          message: {
            template: this.props.modal.followUp.message.template?.id || '',
            templateRef: this.props.modal.followUp.message.template?.ref.path || '',
            variables: this.props.modal.followUp.message.variables,
          },
        },
        clientRefPath: this.props.customer.ref?.path || '',
        finalResult: this.props.modal.finalDecision,
      });

      mainStore.dispatch(
        fetchCustomerThunk({
          clientDocRef: this.props.customer.ref!,
          cb: () => {
            mainStore.dispatch(customerFinalDecisionSlice.actions.close());
          },
        }) as any,
      );
    } catch (e) {
      const axiosError = e as AxiosError<{ error: any }>;
      const error = axiosError.response?.data.error;
      let errorMessage: string;
      if (error.type === 'UNPROCESSABLE_ENTITY') {
        const errorFields: any[] = Object.values(error.messages);
        errorMessage = errorFields[0].msg;
      } else {
        console.log(error);
        errorMessage = error.messages;
      }
      mainStore.dispatch(customerFinalDecisionSlice.actions.setErrorMessage(errorMessage));
    }

    mainStore.dispatch(customerFinalDecisionSlice.actions.setLoading(false));
  };

  componentDidMount() {
    mainStore.dispatch(fetchTemplateFinalDecision() as any);
  }

  render() {
    return (
      <Modal open={true}>
        <Modal.Header>Hasil Akhir Customer</Modal.Header>
        <Modal.Content>
          <Form>
            <Form.Field required={true}>
              <label>Keputusan Customer</label>
              <Select
                value={this.props.modal.finalDecision || ''}
                options={[
                  {
                    value: 'noResult',
                    text: 'Tidak ada',
                  },
                  {
                    value: 'order',
                    text: 'Pesan Kendaraan',
                  },
                  {
                    value: 'needToFollowUp',
                    text: 'Perlu Follow Up',
                  },
                ]}
                onChange={this.onResultChange}
              />
            </Form.Field>
            {this.props.modal.finalDecision === 'needToFollowUp' && (
              <div>
                <Form.Group>
                  <Form.Field required={true}>
                    <label>Tanggal Follow Up</label>
                    <DatePicker
                      onChange={this.onDateChange}
                      minDate={new Date()}
                      selected={this.props.modal.followUp.date}
                      dateFormat={'dd MMM yyyy'}
                    />
                  </Form.Field>
                  <Form.Field required={true}>
                    <label>Jam Follow Up</label>
                    <Select
                      options={[
                        {
                          text: 'Jam 08:00 Pagi',
                          value: '07:00',
                        },
                        {
                          text: 'Jam 13:00 Siang',
                          value: '13:00',
                        },
                        {
                          text: 'Jam 17:00 Sore',
                          value: '17:00',
                        },
                      ]}
                      value={this.props.modal.followUp.time}
                      onChange={this.onTimeChange}
                    />
                  </Form.Field>
                </Form.Group>
                <Form.Field required={true}>
                  <label>Template Follow Up</label>
                  <Select
                    options={this.props.modal.availableTemplates.map((r) => {
                      return {
                        text: r.id,
                        value: r.id,
                      };
                    })}
                    onChange={this.onTemplateChange}
                    value={this.props.modal.followUp.message.template?.id}
                  />
                </Form.Field>
                {this.props.modal.followUp.message.template && (
                  <Card>
                    <Card.Content>{this.props.modal.followUp.message.template.body}</Card.Content>
                  </Card>
                )}
                {this.props.modal.followUp.message.template?.variables &&
                  this.props.modal.followUp.message.template?.variables.length > 0 && (
                    <Segment>
                      <Header>Variable</Header>
                      {this.props.modal.followUp.message.template.variables.map((v, i) => {
                        return (
                          <Form.Field
                            key={i.toString()}
                            required={true}
                          >
                            <label>{v.name.toUpperCase()}</label>
                            <Input
                              value={this.props.modal.followUp.message.variables[i] || ''}
                              onChange={(event, data) => {
                                mainStore.dispatch(
                                  mainStore.dispatch(
                                    customerFinalDecisionSlice.actions.changeFollowUpTemplateVariable(
                                      {
                                        value: data.value,
                                        index: i,
                                      },
                                    ),
                                  ),
                                );
                              }}
                            />
                          </Form.Field>
                        );
                      })}
                    </Segment>
                  )}
              </div>
            )}
          </Form>

          {this.props.modal.errorSubmittingMessage && (
            <Container style={{ marginTop: '32px', color: 'red' }}>
              {this.props.modal.errorSubmittingMessage}
            </Container>
          )}
        </Modal.Content>
        <Modal.Actions>
          <Button onClick={this.onCancel}>Batal</Button>
          <Button
            primary={true}
            onClick={this.submit}
            loading={this.props.modal.submitting}
          >
            Submit
          </Button>
        </Modal.Actions>
      </Modal>
    );
  }
}

const mapStateToProps = (states: TMainReduxStates) => {
  return {
    modal: states.modalUpdateCustomerFinalDecision,
    customer: states.customerReducer,
  };
};

export default connect(mapStateToProps)(ModalClientFinalDecision);

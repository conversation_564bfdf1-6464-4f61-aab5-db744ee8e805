import React, { ChangeEvent, SyntheticEvent, useState, useEffect, useCallback } from 'react';
import { Button, Form, Input, Select } from 'semantic-ui-react';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { useSelector } from 'react-redux';
import { VariantProduct } from '../../services/types/catalaogueTypes';
import { catalogueServices } from '../../services/catalogue/catalogueServices';
import currencyFormat from '../../helpers/currencyFormat';
import { mainApiServices } from '../../services/MainApiServices';
import DatePicker from 'react-datepicker';
import moment from 'moment';
import { motion, AnimatePresence } from 'framer-motion';
import { IPriceListJsonData } from '../../services/types/TabularPriceList.types';
import b2bServices from '../../services/b2b/b2bServices';
import collect from 'collect.js';
import { DropdownProps } from 'semantic-ui-react/dist/commonjs/modules/Dropdown/Dropdown';
import ModalConfirmSendSurvey from '../ModalConfirmSendSurvey/ModalConfirmSendSurvey';
import getDataDealCode, { IGetDataDealCodeHelper } from '../../helpers/getDataDealCode';
import { AxiosError } from 'axios';
import {
  IoLocationSharp,
  IoSend,
  IoRefresh,
  IoClose,
  IoCheckmark,
  IoBicycle,
} from 'react-icons/io5';

export interface CreditSchemeProps {}

interface PriceListTenorFromDealCode {
  tenor: number;
  installment: number[];
}

interface CreditSchemeState {
  selectedTenor: number | null;
  selectedInstallment: number | null;
  selectedDownPayment: number | null;
  discountTenor: number | null;
  discountInstallment: number | null;
}

interface PriceListInfoState {
  priceList: IPriceListJsonData | null;
  fetchingPriceList: boolean;
  priceListExistence: 'available' | 'none' | 'notAvailable';
  priceListSource: 'b2b' | 'dealCode' | null;
  dealCode: string;
  selectedDealCode: IGetDataDealCodeHelper | null;
  selectedLeasingCode: string | null;
}

const CreditScheme: React.FC<CreditSchemeProps> = () => {
  const conversation = useSelector((state: TMainReduxStates) => state.reducerConversation);
  const customer = useSelector((state: TMainReduxStates) => state.customerReducer);
  const admin = useSelector((state: TMainReduxStates) => state.reducerAdmin.admin);
  const project = useSelector((state: TMainReduxStates) => state.reducerProject.project);

  // Group related states
  const [creditScheme, setCreditScheme] = useState<CreditSchemeState>({
    selectedTenor: 0,
    selectedInstallment: 0,
    selectedDownPayment: 0,
    discountTenor: 0,
    discountInstallment: 0,
  });

  const [vehicleInfo, setVehicleInfo] = useState({
    product: null as VariantProduct | null,
    area: null as string | null,
  });

  const [uiState, setUiState] = useState({
    loading: false,
    editing: false,
    updating: false,
    success: false,
    confirmation: false,
    showSuccessIndicatorSendSurvey: false,
    errorMessage: '',
  });

  const [surveyInfo, setSurveyInfo] = useState({
    surveyDateTime: null as Date | null,
    urlMapGmap: '',
    lastSendSurvey: null as Date | null,
    lastOfferCode: null as string | null,
  });

  const [priceListInfo, setPriceListInfo] = useState<PriceListInfoState>({
    priceList: null,
    fetchingPriceList: false,
    priceListExistence: 'none',
    priceListSource: null,
    dealCode: '',
    selectedDealCode: null,
    selectedLeasingCode: null,
  });

  // Validation functions
  const validateProjectAccess = useCallback(() => {
    if (!project || project.group !== 'amartamotor') {
      return {
        isValid: false,
        error: (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
            <h3 className="text-red-800 font-medium mb-2">Tidak Dapat Mengakses Menu Ini</h3>
            <p className="text-red-700 text-sm">
              Menu ini hanya tersedia untuk project Amarta Motor.
            </p>
          </div>
        ),
      };
    }
    return { isValid: true };
  }, [project]);

  const validateRequiredData = useCallback(() => {
    if (!customer.client || !vehicleInfo.area || !vehicleInfo.product?.color_code) {
      return {
        isValid: false,
        error: (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
            <h3 className="text-red-800 font-medium mb-2">Tidak Dapat Menentukan Skema Kredit</h3>
            <ul className="list-disc pl-5 text-red-700 text-sm">
              <li>
                Pastikan <strong>Area</strong> sudah ditentukan.
              </li>
              <li>
                Pastikan <strong>Kendaraan yang Diinginkan</strong> sudah ditentukan hingga ke warna
                kendaraan.
              </li>
            </ul>
          </div>
        ),
      };
    }
    return { isValid: true };
  }, [customer.client, vehicleInfo.area, vehicleInfo.product]);

  // Memoized handlers
  const sendSurvey = useCallback(() => {
    setUiState((prev) => ({ ...prev, confirmation: true }));
  }, []);

  const onCancel = useCallback(() => {
    setUiState((prev) => ({ ...prev, confirmation: false }));
  }, []);

  const fetchPriceListB2b = useCallback(async () => {
    if (!vehicleInfo.product && !vehicleInfo.area) {
      return;
    }

    setPriceListInfo((prev) => ({ ...prev, fetchingPriceList: true }));

    try {
      const fetch = await b2bServices.getPriceList({
        variantCode: vehicleInfo.product?.variant_code || '',
        modelName: vehicleInfo.product?.model_name || '',
        cityGroup: vehicleInfo.area || '',
      });
      setPriceListInfo((prev) => ({
        ...prev,
        priceList: fetch.data,
        priceListExistence: 'available',
        selectedDealCode: null,
        dealCode: '',
        selectedLeasingCode: null,
      }));
    } catch (e) {
      setPriceListInfo((prev) => ({
        ...prev,
        priceList: null,
        priceListExistence: 'notAvailable',
      }));
    }

    setPriceListInfo((prev) => ({ ...prev, fetchingPriceList: false }));
  }, [vehicleInfo.product, vehicleInfo.area]);

  // Function to load data
  const load = async (callback?: () => void) => {
    setUiState((prev) => ({ ...prev, loading: true }));
    setUiState((prev) => ({ ...prev, confirmation: false }));

    const clientDocRef = conversation.chatRoom?.clients[0];
    if (!clientDocRef || !customer.client) {
      setUiState((prev) => ({ ...prev, loading: false }));
      return;
    }

    const phase2 = async () => {
      if (!customer.client) {
        setUiState((prev) => ({ ...prev, loading: false }));
        return;
      }

      const area = customer.client.profile?.area?.text;
      const variantCode = customer.client.dream_vehicle?.variant_code;

      if (area && variantCode) {
        setVehicleInfo((prev) => ({ ...prev, area }));
        try {
          const getProduct = await catalogueServices.getVariantByAreaAMH({
            area,
            variantCode,
          });
          setVehicleInfo((prev) => ({
            ...prev,
            product: (getProduct?.data[0] as VariantProduct) ?? null,
          }));
        } catch (e: any) {
          setVehicleInfo((prev) => ({ ...prev, product: null }));
        }
      }

      const survey = customer.client.survey;
      if (survey?.credit_scheme) {
        const creditScheme = survey.credit_scheme;
        setCreditScheme((prev) => ({
          ...prev,
          selectedDownPayment: creditScheme.down_payment || 0,
          selectedTenor: creditScheme.tenor || 0,
          selectedInstallment: creditScheme.installment || 0,
          discountTenor: creditScheme.discountTenor || 0,
          discountInstallment: creditScheme.discountInstallment || 0,
        }));

        setSurveyInfo((prev) => ({
          ...prev,
          surveyDateTime: creditScheme.surveyTime?.toDate() || null,
          urlMapGmap: creditScheme.surveyGmapUrl || '',
          lastSendSurvey: survey.last_send?.toDate() || null,
          lastOfferCode: survey.offer_code || null,
        }));

        setPriceListInfo((prev) => ({
          ...prev,
          dealCode: creditScheme.dealCode || '',
          priceListSource: (creditScheme.priceListSource || 'b2b') as 'b2b' | 'dealCode',
          selectedLeasingCode: creditScheme.selectedLeasingCode || null,
        }));
      }

      setUiState((prev) => ({ ...prev, loading: false }));
      callback?.();
    };

    await phase2();
  };

  // URL Map Google Maps handlers
  const urlMapGmapHandlers = {
    onChange: (params: ChangeEvent<HTMLInputElement>) => {
      if (params) {
        setSurveyInfo((prev) => ({ ...prev, urlMapGmap: params.target.value }));
      }
    },
  };

  // Leasing handlers
  const leasingHandlers = {
    availableLeasing: () => {
      let leasingCodes: string[] = [];
      if (
        priceListInfo.selectedDealCode &&
        priceListInfo.priceListSource === 'dealCode' &&
        !priceListInfo.selectedDealCode.fincoId
      ) {
        const filter = priceListInfo.selectedDealCode.originalCreditScheme.filter((c) => {
          return (
            c.dp_amount === creditScheme.selectedDownPayment &&
            parseInt(c.tenor[0]) === creditScheme.selectedTenor &&
            c.installment_amount === creditScheme.selectedInstallment
          );
        });

        filter.forEach((c) => leasingCodes.push(c.finco_code));
      }
      return leasingCodes;
    },
    onChange: (event: React.SyntheticEvent<HTMLElement>, data: DropdownProps) => {
      if (data.value) {
        setPriceListInfo((prev) => ({
          ...prev,
          selectedLeasingCode: data.value as string,
        }));
      }
    },
  };

  // Down payment handlers
  const downPaymentHandlers = {
    availableDownPayment: () => {
      let downPayments: number[] = [];

      if (priceListInfo.priceList && priceListInfo.priceListSource === 'b2b') {
        const groupDp = collect(priceListInfo.priceList.formatted).groupBy('dp');
        groupDp.keys().each((currentItem) => {
          downPayments.push(parseInt(currentItem));
        });
      }

      if (priceListInfo.selectedDealCode && priceListInfo.priceListSource === 'dealCode') {
        downPayments = priceListInfo.selectedDealCode.downPayments.map((dp) => dp.downPayment);
      }

      return downPayments;
    },
    onChange: (event: React.SyntheticEvent<HTMLElement>, data: DropdownProps) => {
      if (data.value) {
        setCreditScheme((prev) => ({
          ...prev,
          selectedDownPayment: data.value as number,
        }));
        setCreditScheme((prev) => ({ ...prev, selectedTenor: null }));
        setCreditScheme((prev) => ({ ...prev, selectedInstallment: null }));
        setPriceListInfo((prev) => ({ ...prev, selectedLeasingCode: null }));
      }
    },
  };

  // Tenor handlers
  const tenorHandlers = {
    availableTenor: () => {
      let tenors: number[] = [];

      if (priceListInfo.priceList && priceListInfo.priceListSource === 'b2b') {
        const groupTenor = collect(priceListInfo.priceList.formatted)
          .filter((item) => item.dp === creditScheme.selectedDownPayment)
          .groupBy('tenor');
        groupTenor.keys().each((currentItem) => {
          tenors.push(parseInt(currentItem));
        });
      }

      if (priceListInfo.selectedDealCode && priceListInfo.priceListSource === 'dealCode') {
        const downPayment = priceListInfo.selectedDealCode.downPayments.find(
          (dp) => dp.downPayment === creditScheme.selectedDownPayment,
        );

        tenors = downPayment?.tenors.map((t) => t.tenor) || [];
      }

      return tenors;
    },
    onChange: (event: React.SyntheticEvent<HTMLElement>, data: DropdownProps) => {
      if (data.value) {
        setCreditScheme((prev) => ({
          ...prev,
          selectedTenor: data.value as number,
        }));
        setCreditScheme((prev) => ({ ...prev, selectedInstallment: null }));
        setPriceListInfo((prev) => ({ ...prev, selectedLeasingCode: null }));
      }
    },
    onChangeDiscountTenor: (params: ChangeEvent<HTMLInputElement>) => {
      setCreditScheme((prev) => ({
        ...prev,
        discountTenor: params.target.value ? parseInt(params.target.value) : null,
      }));
    },
  };

  // Installment handlers
  const installmentHandlers = {
    availableInstallment: () => {
      let installments: number[] = [];

      if (priceListInfo.priceList && priceListInfo.priceListSource === 'b2b') {
        const getInstallments = collect(priceListInfo.priceList.formatted).filter(
          (item) =>
            item.dp === creditScheme.selectedDownPayment &&
            item.tenor === creditScheme.selectedTenor,
        );
        getInstallments.each((currentItem) => {
          installments.push(currentItem.installment);
        });
      }

      if (priceListInfo.selectedDealCode && priceListInfo.priceListSource === 'dealCode') {
        const downPayment = priceListInfo.selectedDealCode.downPayments.find(
          (dp) => dp.downPayment === creditScheme.selectedDownPayment,
        );

        if (downPayment) {
          const tenor = downPayment.tenors.filter((t) => t.tenor === creditScheme.selectedTenor);
          installments = tenor.map((t) => t.installment);
        }
      }

      return installments;
    },
    onChange: (event: React.SyntheticEvent<HTMLElement>, data: DropdownProps) => {
      if (data.value) {
        setCreditScheme((prev) => ({
          ...prev,
          selectedInstallment: data.value as number,
        }));
        setPriceListInfo((prev) => ({ ...prev, selectedLeasingCode: null }));
      }
    },
    onChangeDiscountInstallment: (params: ChangeEvent<HTMLInputElement>) => {
      setCreditScheme((prev) => ({
        ...prev,
        discountInstallment: params.target.value ? parseInt(params.target.value) : null,
      }));
    },
  };

  // Deal code handlers
  const dealCodeHandlers = {
    onChange: (params: ChangeEvent<HTMLInputElement>) => {
      if (params) {
        setPriceListInfo((prev) => ({
          ...prev,
          dealCode: params.target.value,
        }));
      }
    },
  };

  // Update handlers
  const onUpdateClick = () => {
    setUiState((prev) => ({ ...prev, editing: true }));
    if (priceListInfo.priceListSource === 'b2b') {
      fetchPriceListB2b();
    } else if (priceListInfo.priceListSource === 'dealCode') {
      onApplyDealCodeClick();
    }
  };

  const onCancelUpdateClick = async () => {
    setUiState((prev) => ({ ...prev, editing: false }));

    if (!customer.client?.survey?.credit_scheme) {
      return;
    }

    const { credit_scheme } = customer.client.survey;

    setCreditScheme((prev) => ({
      ...prev,
      selectedDownPayment: credit_scheme.down_payment || 0,
      selectedTenor: credit_scheme.tenor || 0,
      selectedInstallment: credit_scheme.installment || 0,
      discountTenor: credit_scheme.discountTenor || 0,
      discountInstallment: credit_scheme.discountInstallment || 0,
    }));

    setSurveyInfo((prev) => ({
      ...prev,
      surveyDateTime: credit_scheme.surveyTime?.toDate() || null,
      urlMapGmap: credit_scheme.surveyGmapUrl || '',
    }));

    setPriceListInfo((prev) => ({
      ...prev,
      priceList: null,
      priceListExistence: 'none',
      priceListSource: (credit_scheme.priceListSource || 'b2b') as 'b2b' | 'dealCode',
      dealCode: credit_scheme.dealCode || '',
      selectedLeasingCode: credit_scheme.selectedLeasingCode || '',
    }));
  };

  const onSubmitClick = async () => {
    if (!customer.ref || !priceListInfo.priceListSource) {
      setUiState((prev) => ({ ...prev, errorMessage: 'Data tidak lengkap' }));
      return;
    }

    if (
      priceListInfo.selectedDealCode?.variantCodes.indexOf(
        customer.client?.dream_vehicle?.variant_code || '',
      ) === -1 &&
      priceListInfo.selectedDealCode
    ) {
      setUiState((prev) => ({
        ...prev,
        errorMessage: 'Kode deal tidak sesuai dengan kendaraan yang dipilih',
      }));
      return;
    }

    if (!surveyInfo.surveyDateTime) {
      setUiState((prev) => ({
        ...prev,
        errorMessage: 'Tanggal survey harus diisi',
      }));
      return;
    }

    if (!moment(surveyInfo.surveyDateTime).isValid()) {
      setUiState((prev) => ({
        ...prev,
        errorMessage: 'Format tanggal survey tidak valid',
      }));
      return;
    }

    if (!surveyInfo.urlMapGmap) {
      setUiState((prev) => ({
        ...prev,
        errorMessage: 'URL Google Maps harus diisi',
      }));
      return;
    }

    if (
      !creditScheme.selectedTenor ||
      !creditScheme.selectedInstallment ||
      !creditScheme.selectedDownPayment
    ) {
      setUiState((prev) => ({
        ...prev,
        errorMessage: 'Informasi kredit harus lengkap',
      }));
      return;
    }

    setUiState((prev) => ({ ...prev, updating: true }));
    setUiState((prev) => ({ ...prev, errorMessage: '' }));

    try {
      await mainApiServices.updateCreditScheme(
        {
          downPayment: creditScheme.selectedDownPayment,
          tenor: creditScheme.selectedTenor,
          installment: creditScheme.selectedInstallment,
          surveyGmapUrl: surveyInfo.urlMapGmap,
          surveyTime: moment(surveyInfo.surveyDateTime).format('YYYY-MM-DD HH:mm'),
          discountTenor: creditScheme.discountTenor || 0,
          discountInstallment: creditScheme.discountInstallment || 0,
          priceListSource: priceListInfo.priceListSource,
          dealCode: priceListInfo.dealCode,
          selectedLeasingCode: priceListInfo.selectedLeasingCode || '',
          leasingAdminId: priceListInfo.selectedDealCode?.fincoId || '',
        },
        customer.ref,
      );

      setUiState((prev) => ({ ...prev, success: true }));
      setUiState((prev) => ({ ...prev, editing: false }));
      setUiState((prev) => ({ ...prev, updating: false }));

      load();

      setTimeout(() => {
        setUiState((prev) => ({ ...prev, success: false }));
      }, 5000);
    } catch (e: any) {
      const error = e as AxiosError<any>;
      let newErrorMessage = '';

      if (error.response?.data?.error.type === 'UNPROCESSABLE_ENTITY') {
        const errorResponseData = error.response?.data.error;
        for (const key of Object.keys(errorResponseData.messages)) {
          newErrorMessage += errorResponseData.messages[key].msg;
        }
      } else {
        newErrorMessage = 'Terjadi kesalahan ketika update data kredit.';
      }

      setUiState((prev) => ({ ...prev, errorMessage: newErrorMessage }));
      setUiState((prev) => ({ ...prev, updating: false }));
    }
  };

  // Memoized calculations
  const calculateDownPaymentInPercent = () => {
    const downPaymentCredit = creditScheme.selectedDownPayment ?? 0;
    const otr = vehicleInfo.product?.price ?? 0;
    const result = (downPaymentCredit / otr) * 100;
    return isNaN(result) ? '0' : result.toFixed(2);
  };

  const changeDateTimeSurvey = (date: Date | null) => {
    setSurveyInfo((prev) => ({ ...prev, surveyDateTime: date }));
  };

  const fetchWrap = () => {
    load();
  };

  const onSuccessCreateSurveyOrder = () => {
    setUiState((prev) => ({ ...prev, showSuccessIndicatorSendSurvey: true }));
    fetchWrap();
  };

  const onApplyDealCodeClick = async () => {
    setPriceListInfo((prev) => ({ ...prev, fetchingPriceList: true }));
    try {
      const get = await getDataDealCode(priceListInfo.dealCode);
      setPriceListInfo((prev) => ({ ...prev, selectedDealCode: get }));
      setPriceListInfo((prev) => ({
        ...prev,
        priceListSource: 'dealCode' as 'b2b' | 'dealCode',
      }));
      setPriceListInfo((prev) => ({
        ...prev,
        priceListExistence: 'available',
      }));
    } catch (e) {
      setPriceListInfo((prev) => ({
        ...prev,
        fetchingPriceList: false,
        priceListExistence: 'notAvailable',
      }));
    }
    setPriceListInfo((prev) => ({ ...prev, fetchingPriceList: false }));
  };

  const onDetachDealCode = () => {
    setPriceListInfo((prev) => ({ ...prev, selectedDealCode: null }));
    setCreditScheme((prev) => ({
      ...prev,
      selectedDownPayment: customer.client?.survey?.credit_scheme?.down_payment || 0,
    }));
    setCreditScheme((prev) => ({
      ...prev,
      selectedTenor: customer.client?.survey?.credit_scheme?.tenor || 0,
    }));
    setCreditScheme((prev) => ({
      ...prev,
      selectedInstallment: customer.client?.survey?.credit_scheme?.installment || 0,
    }));
    setPriceListInfo((prev) => ({
      ...prev,
      selectedLeasingCode: customer.client?.survey?.credit_scheme?.selectedLeasingCode || '',
    }));
    setPriceListInfo((prev) => ({ ...prev, priceListExistence: 'none' }));
  };

  const onPriceListType = async (params: SyntheticEvent, data: DropdownProps) => {
    setPriceListInfo((prev) => ({
      ...prev,
      priceListSource: data.value as any,
    }));
    setCreditScheme((prev) => ({ ...prev, selectedDownPayment: 0 }));
    setCreditScheme((prev) => ({ ...prev, selectedTenor: 0 }));
    setCreditScheme((prev) => ({ ...prev, selectedInstallment: 0 }));
    setPriceListInfo((prev) => ({ ...prev, selectedLeasingCode: null }));
    setPriceListInfo((prev) => ({ ...prev, priceListExistence: 'none' }));

    if (data.value === 'b2b') {
      fetchPriceListB2b();
    } else if (data.value === 'dealCode') {
      setPriceListInfo((prev) => ({ ...prev, priceList: null }));
    }
  };

  useEffect(() => {
    fetchWrap();
    return () => {
      // Cleanup
      setUiState((prev) => ({
        ...prev,
        success: false,
        errorMessage: '',
        showSuccessIndicatorSendSurvey: false,
      }));
    };
  }, []);

  // Validation checks
  const projectAccessValidation = validateProjectAccess();
  if (!projectAccessValidation.isValid) {
    return projectAccessValidation.error;
  }

  const requiredDataValidation = validateRequiredData();
  if (!requiredDataValidation.isValid) {
    return requiredDataValidation.error;
  }

  return (
    <div className="space-y-6">
      {/* Vehicle Information Card */}
      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <div className="flex items-start gap-4">
          <div className="p-3 bg-blue-50 rounded-lg">
            <IoBicycle className="text-2xl text-blue-600" />
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-medium text-gray-900 mb-2">Kendaraan yang diinginkan</h3>
            <div className="space-y-2">
              <p className="text-gray-700">{vehicleInfo.product?.variant_name}</p>
              <p className="text-gray-600 text-sm">
                {`${vehicleInfo.product?.color_code} ${vehicleInfo.product?.color_name}`.toUpperCase()}
              </p>
              <p className="text-gray-600 text-sm">
                Tahun: {customer.client?.dream_vehicle?.year || '-'}
              </p>
              <p className="text-lg font-semibold text-blue-600">
                {currencyFormat(vehicleInfo.product?.price ?? 0)}
              </p>
              {admin?.amartaVip && (
                <div className="mt-4 p-3 bg-green-50 rounded-lg space-y-1">
                  <p className="text-sm text-green-700">
                    <span className="font-medium">Kode Agen:</span> {admin.amartaVip.mediatorCode}
                  </p>
                  <p className="text-sm text-green-700">
                    <span className="font-medium">Nama Agen:</span> {admin.amartaVip.mediatorName}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      <AnimatePresence mode="wait">
        {uiState.editing ? (
          <motion.div
            key="edit"
            initial={{ opacity: 0, x: 100 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -100 }}
            className="bg-white rounded-xl shadow-sm p-6 border border-gray-100"
          >
            <Form className="max-w-2xl mx-auto space-y-8">
              {/* Price List Source Selection */}
              <Form.Field className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Sumber Pricelist</label>
                <Select
                  options={[
                    { value: 'b2b', text: 'B2B' },
                    { value: 'dealCode', text: 'Deal Code' },
                  ]}
                  placeholder="Pilih sumber pricelist"
                  value={priceListInfo.priceListSource as any}
                  onChange={onPriceListType}
                  className="w-full transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                />
              </Form.Field>

              {/* Deal Code Section */}
              {priceListInfo.priceListSource === 'dealCode' && (
                <Form.Field className="p-6 bg-gray-50 rounded-xl space-y-4">
                  <h4 className="font-medium text-gray-800">Gunakan DealCode sebagai Pricelist</h4>
                  <div className="flex gap-3">
                    <Input
                      placeholder="Masukan DealCode"
                      value={priceListInfo.dealCode}
                      readOnly={!!priceListInfo.selectedDealCode?.dealCode}
                      onChange={dealCodeHandlers.onChange}
                      className="flex-1 transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                    />
                    {!priceListInfo.selectedDealCode ? (
                      <Button
                        onClick={onApplyDealCodeClick}
                        disabled={priceListInfo.fetchingPriceList}
                        primary
                        className="!bg-blue-600 hover:!bg-blue-700 transition-colors disabled:!bg-blue-300"
                      >
                        {priceListInfo.fetchingPriceList ? 'Loading...' : 'Terapkan'}
                      </Button>
                    ) : (
                      <Button
                        onClick={onDetachDealCode}
                        negative
                        className="!bg-red-600 hover:!bg-red-700 transition-colors"
                      >
                        Lepas
                      </Button>
                    )}
                  </div>
                </Form.Field>
              )}

              {/* Credit Scheme Form */}
              <div className="space-y-6">
                <div className="pb-6 border-b border-gray-100">
                  <h3 className="text-lg font-medium text-gray-900 mb-6">Informasi Kredit</h3>
                  <div className="space-y-6">
                    <Form.Field>
                      <label className="text-sm font-medium text-gray-700">Uang Muka</label>
                      <Select
                        placeholder="Pilih Uang Muka"
                        value={creditScheme.selectedDownPayment || ''}
                        options={downPaymentHandlers.availableDownPayment().map((value) => ({
                          value,
                          text: currencyFormat(value),
                        }))}
                        onChange={downPaymentHandlers.onChange}
                        className="w-full mt-1 transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                      />
                    </Form.Field>

                    <Form.Field>
                      <label className="text-sm font-medium text-gray-700">Tenor</label>
                      <Select
                        placeholder="Pilih Tenor"
                        value={creditScheme.selectedTenor || ''}
                        options={tenorHandlers.availableTenor().map((value) => ({
                          value,
                          text: `${value} kali`,
                        }))}
                        onChange={tenorHandlers.onChange}
                        className="w-full mt-1 transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                      />
                    </Form.Field>

                    <Form.Field>
                      <label className="text-sm font-medium text-gray-700">Potongan Tenor</label>
                      <Input
                        placeholder="Masukan Potongan Tenor"
                        type="number"
                        value={creditScheme.discountTenor || ''}
                        onChange={tenorHandlers.onChangeDiscountTenor}
                        className="w-full mt-1 transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                      />
                    </Form.Field>

                    <Form.Field>
                      <label className="text-sm font-medium text-gray-700">Angsuran</label>
                      <Select
                        placeholder="Pilih Angsuran"
                        value={creditScheme.selectedInstallment || ''}
                        options={installmentHandlers.availableInstallment().map((value) => ({
                          value,
                          text: currencyFormat(value),
                        }))}
                        onChange={installmentHandlers.onChange}
                        className="w-full mt-1 transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                      />
                    </Form.Field>

                    <Form.Field>
                      <label className="text-sm font-medium text-gray-700">Potongan Angsuran</label>
                      <Input
                        placeholder="Masukan Potongan Angsuran"
                        type="number"
                        value={creditScheme.discountInstallment || ''}
                        onChange={installmentHandlers.onChangeDiscountInstallment}
                        className="w-full mt-1 transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                      />
                    </Form.Field>

                    {priceListInfo.selectedDealCode && !priceListInfo.selectedDealCode.fincoId && (
                      <Form.Field>
                        <label className="text-sm font-medium text-gray-700">Leasing</label>
                        <Select
                          placeholder="Pilih Leasing"
                          value={priceListInfo.selectedLeasingCode || ''}
                          options={leasingHandlers.availableLeasing().map((value) => ({
                            value,
                            text: value,
                          }))}
                          onChange={leasingHandlers.onChange}
                          className="w-full mt-1 transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                        />
                      </Form.Field>
                    )}
                  </div>
                </div>

                {/* Survey Information */}
                <div className="space-y-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-6">Informasi Survey</h3>
                  <Form.Field>
                    <label className="text-sm font-medium text-gray-700">
                      Tanggal dan Jam Survey
                    </label>
                    <DatePicker
                      timeFormat="HH:mm"
                      selected={surveyInfo.surveyDateTime}
                      showTimeSelect={true}
                      onChange={changeDateTimeSurvey}
                      dateFormat="dd MMM yyyy HH:mm"
                      minDate={new Date()}
                      className="w-full mt-1 p-2.5 border border-gray-300 rounded-lg transition-all duration-200 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </Form.Field>

                  <Form.Field>
                    <label className="text-sm font-medium text-gray-700">
                      URL Google Map Alamat Survey
                    </label>
                    <Input
                      onChange={urlMapGmapHandlers.onChange}
                      value={surveyInfo.urlMapGmap}
                      placeholder="Masukkan URL Google Maps"
                      className="w-full mt-1 transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                    />
                  </Form.Field>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3 pt-6 border-t border-gray-100">
                <Button
                  onClick={onCancelUpdateClick}
                  basic
                  className="!border-gray-300 hover:!bg-gray-50 transition-colors"
                >
                  <div className="flex items-center gap-2">
                    <IoClose className="text-lg" />
                    <span>Batal</span>
                  </div>
                </Button>
                <Button
                  onClick={onSubmitClick}
                  disabled={uiState.updating}
                  primary
                  className="flex-1 !bg-blue-600 hover:!bg-blue-700 transition-colors disabled:!bg-blue-300"
                >
                  <div className="flex items-center justify-center gap-2">
                    <IoCheckmark className="text-lg" />
                    <span>{uiState.updating ? 'Menyimpan...' : 'Simpan'}</span>
                  </div>
                </Button>
              </div>
            </Form>
          </motion.div>
        ) : (
          <motion.div
            key="view"
            initial={{ opacity: 0, x: -100 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 100 }}
            className="bg-white rounded-xl shadow-sm p-6 border border-gray-100 space-y-6"
          >
            {/* View Mode Content */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-500">Terakhir Update</label>
                  <p className="text-gray-900">
                    {customer.client?.survey?.credit_scheme?.lastUpdate
                      ? moment(customer.client.survey.credit_scheme.lastUpdate.toDate()).format(
                          'DD MMM YYYY HH:mm',
                        )
                      : 'Belum ada'}
                  </p>
                </div>

                {priceListInfo.priceListSource === 'dealCode' && (
                  <>
                    <div>
                      <label className="block text-sm font-medium text-gray-500">Deal Code</label>
                      <p className="text-gray-900">{priceListInfo.dealCode || '-'}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-500">Leasing</label>
                      <p className="text-gray-900">
                        {customer.client?.survey?.credit_scheme?.selectedLeasingCode || 'Tidak Ada'}
                      </p>
                    </div>
                  </>
                )}

                <div>
                  <label className="block text-sm font-medium text-gray-500">Uang Muka</label>
                  <p className="text-gray-900">
                    {currencyFormat(creditScheme.selectedDownPayment || 0)}
                    <span className="ml-2 text-sm text-gray-600">
                      ({calculateDownPaymentInPercent()}%)
                    </span>
                  </p>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-500">Tenor</label>
                  <p className="text-gray-900">{creditScheme.selectedTenor || '-'}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-500">Potongan Tenor</label>
                  <p className="text-gray-900">{creditScheme.discountTenor || '-'}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-500">Angsuran</label>
                  <p className="text-gray-900">
                    {currencyFormat(creditScheme.selectedInstallment || 0)}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-500">
                    Potongan Angsuran
                  </label>
                  <p className="text-gray-900">
                    {currencyFormat(creditScheme.discountInstallment || 0)}
                  </p>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-500">
                  Tanggal dan Jam Survey
                </label>
                <p className="text-gray-900">
                  {surveyInfo.surveyDateTime
                    ? moment(surveyInfo.surveyDateTime).format('DD MMM YYYY HH:mm')
                    : 'Belum ada'}
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-500">
                  URL Google Map Alamat Survey
                </label>
                {surveyInfo.urlMapGmap ? (
                  <a
                    href={surveyInfo.urlMapGmap}
                    target="_blank"
                    rel="noreferrer"
                    className="text-blue-600 hover:text-blue-700 font-medium inline-flex items-center gap-1"
                  >
                    <IoLocationSharp />
                    <span>Buka di Google Maps</span>
                  </a>
                ) : (
                  <p className="text-gray-500 italic">Belum ada</p>
                )}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3">
              <button
                onClick={onUpdateClick}
                className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
              >
                <div className="flex items-center gap-2">
                  <IoRefresh className="text-lg" />
                  <span>Perbarui</span>
                </div>
              </button>
              <button
                onClick={sendSurvey}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex-1"
              >
                <div className="flex items-center justify-center gap-2">
                  <IoSend className="text-lg" />
                  <span>Kirim Survey</span>
                </div>
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Status Messages */}
      {uiState.success && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-green-700">
          Berhasil memperbarui skema kredit
        </div>
      )}

      {uiState.errorMessage && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-red-700">
          {uiState.errorMessage}
        </div>
      )}

      {/* Survey History */}
      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100 space-y-4">
        {uiState.showSuccessIndicatorSendSurvey && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-green-700">
            Berhasil mengirim survey order
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-500 mb-1">
              Terakhir Kirim Survey
            </label>
            <p className="text-gray-900">
              {surveyInfo.lastSendSurvey ? (
                moment(surveyInfo.lastSendSurvey).format('DD MMM YYYY HH:mm')
              ) : (
                <span className="text-gray-500 italic">Belum Pernah</span>
              )}
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-500 mb-1">
              Kode Offer Kredit Terakhir
            </label>
            <p className="text-gray-900">
              {surveyInfo.lastOfferCode || <span className="text-gray-500 italic">Belum Ada</span>}
            </p>
          </div>
        </div>
      </div>

      {uiState.confirmation && (
        <ModalConfirmSendSurvey
          onSuccess={onSuccessCreateSurveyOrder}
          onCancel={onCancel}
        />
      )}
    </div>
  );
};

export default CreditScheme;

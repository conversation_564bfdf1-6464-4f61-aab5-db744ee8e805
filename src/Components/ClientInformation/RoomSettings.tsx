import React, { useState, useEffect } from 'react';
import { Button, Form, Select } from 'semantic-ui-react';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { useSelector } from 'react-redux';
import { DropdownProps } from 'semantic-ui-react/dist/commonjs/modules/Dropdown/Dropdown';
import LabelDropdown from '../LabelDropdown/LabelDropdown';
import { collection, doc, updateDoc } from 'firebase/firestore';
import { mainStore } from '../../redux/reducers';
import modalBlockPhoneNumberSlice from '../../redux/modalBlockPhoneNumber/modalBlockPhoneNumber.slice';
import ModalBlock from '../ModalBlock/ModalBlock';
import {
  IoBusinessOutline,
  IoPricetagOutline,
  IoShieldOutline,
  IoPersonOutline,
  IoDownloadOutline,
  IoHardwareChipOutline,
} from 'react-icons/io5';
import moment from 'moment';
import DepartmentEntity from '../../entities/DeparmentEntity';
import { mainApiServices } from '../../services/MainApiServices';

interface ISettingFields {
  department: DepartmentEntity | null;
  label: null | string;
}

const RoomSettings: React.FC = () => {
  // Redux state selectors
  const admin = useSelector((state: TMainReduxStates) => state.reducerAdmin);
  const departmentState = useSelector(
    (state: TMainReduxStates) => state.reducerAvailableDepartment,
  );
  const conversation = useSelector((state: TMainReduxStates) => state.reducerConversation);
  const customer = useSelector((state: TMainReduxStates) => state.customerReducer);

  // Local state
  const [updating, setUpdating] = useState(false);
  const [settings, setSettings] = useState<ISettingFields>({
    department: null,
    label: null,
  });
  const [blocked, setBlocked] = useState(conversation.chatRoom?.blocked ?? true);
  const [agentAiReply, setAgentAiReply] = useState(conversation.chatRoom?.agent_ai_reply ?? false);

  const handleDepartmentChange = async (
    event: React.SyntheticEvent<HTMLElement>,
    data: DropdownProps,
  ) => {
    const department = departmentState.departments.find((value1) => value1.ref.id === data.value);

    setUpdating(true);
    setSettings((prev) => ({ ...prev, department: department || null }));

    if (conversation.chatRoom) {
      await conversation.chatRoom.updateDepartment(department?.ref || null);
    }
    setUpdating(false);
  };

  const handleLabelChange = async (
    event: React.SyntheticEvent<HTMLElement>,
    data: DropdownProps,
  ) => {
    const label = data.value === 'NO_LABEL' ? null : (data.value as string);
    setUpdating(true);
    setSettings((prev) => ({ ...prev, label }));

    const chatRoom = conversation.chatRoom;
    const parent = chatRoom?.ref?.parent?.parent;

    if (label && parent) {
      const labelCollection = collection(parent, 'labels');
      await chatRoom!.updateLabel(doc(labelCollection, label));
    } else {
      await chatRoom!.updateLabel(false);
    }

    setUpdating(false);
  };

  const handleBlockToggle = async () => {
    if (blocked) {
      setBlocked(false);
      const chatRoomRef = conversation.chatRoom?.ref;
      if (chatRoomRef) {
        await updateDoc(chatRoomRef, {
          blocked: false,
          blockReason: null,
        });
      }
    } else {
      mainStore.dispatch(modalBlockPhoneNumberSlice.actions.open());
      setBlocked(true);
    }
  };

  const handleAgentAiReplyToggle = async () => {
    if (!conversation.chatRoom) return;
    const newStatus = !agentAiReply;
    setAgentAiReply(newStatus);

    try {
      // Call API to toggle agent AI reply
      await mainApiServices.toggleAgentAiReply({
        chatRoomRefPath: conversation.chatRoom.ref.path,
        status: newStatus,
        source: 'admin',
      });
    } catch (error) {
      console.error('Error toggling agent AI reply:', error);
      // Revert state on error
      setAgentAiReply(!newStatus);
    }
  };

  const handleExportChat = () => {
    const histories = conversation.conversations;
    const clientName = customer.client?.profile.name || '';
    const phoneNumber = customer.client?.contacts.whatsapp || '';

    const header = `Client Name: ${clientName}\nPhone Number: ${phoneNumber}\n\n`;

    const chatHistories = histories.map((history) => {
      const date = moment(history.message.timestamp.toDate()).format('YYYY-MM-DD HH:mm:ss');
      const direction = history.message.direction;
      const senderName = history.origin.display_name;
      let type = '';
      let message = '';

      switch (history.message.type) {
        case 'image':
          type = 'image';
          message = history.message.image?.caption ?? '_NO_TEXT_';
          break;
        case 'video':
          type = 'video';
          message = history.message.video?.caption ?? '_NO_TEXT_';
          break;
        case 'audio':
          type = 'audio';
          message = history.message.audio?.caption ?? '_NO_TEXT_';
          break;
        case 'location':
          type = 'location';
          message = `${history.message.location?.latitude ?? ''},${
            history.message.location?.longitude ?? ''
          }`;
          break;
        case 'document':
          type = 'document';
          message = history.message.document?.caption ?? '_NO_TEXT_';
          break;
        case 'sticker':
          type = 'sticker';
          break;
        case 'button':
          type = 'button';
          message = history.message.button?.text ?? '_NO_TEXT_';
          break;
        default:
          type = 'text';
          message = history.message.text?.body || '_NO_TEXT_';
          break;
      }
      return `[${date}][${direction}][${senderName}][${type}] ${message}`;
    });

    const downloadText = header + chatHistories.join('\n');
    const blob = new Blob([downloadText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `chat_history_${clientName}_${phoneNumber}.txt`;
    a.click();
    URL.revokeObjectURL(url);
  };

  useEffect(() => {
    const fetchInitialData = async () => {
      // Fetch department
      const currentChatRoom = conversation.chatRoom;
      if (currentChatRoom?.doc_department) {
        const department =
          departmentState.departments.find(
            (value1) => value1?.ref?.id === currentChatRoom.doc_department?.id,
          ) ?? null;
        if (department) {
          setSettings((prev) => ({ ...prev, department }));
        }
      }

      // Fetch label
      const label = currentChatRoom?.label;
      if (label && 'id' in label) {
        setSettings((prev) => ({ ...prev, label: label.id }));
      }
      setAgentAiReply(conversation.chatRoom?.agent_ai_reply ?? false);
    };

    fetchInitialData();
  }, [conversation.chatRoom, departmentState.departments]);

  const chatRoom = conversation.chatRoom!;

  return (
    <React.Fragment>
      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <Form className="space-y-8">
          {/* Department Section */}
          <Form.Field className="space-y-2">
            <div className="flex items-center gap-2 mb-2">
              <IoBusinessOutline className="text-lg text-gray-500" />
              <label className="text-sm font-medium text-gray-700">Departemen</label>
            </div>
            <Select
              clearable
              options={departmentState.departments.map((department) => ({
                text: department.name,
                key: department.ref.id,
                value: department.ref.id,
              }))}
              value={settings.department?.ref.id ?? ''}
              disabled={updating || admin.admin?.level !== 'owner'}
              loading={updating}
              onChange={handleDepartmentChange}
              className="w-full transition-all duration-200 focus:ring-2 focus:ring-blue-500"
              placeholder="Pilih Departemen"
            />
          </Form.Field>

          {/* Label Section */}
          <Form.Field className="space-y-2">
            <div className="flex items-center gap-2 mb-2">
              <IoPricetagOutline className="text-lg text-gray-500" />
              <label className="text-sm font-medium text-gray-700">Label Percakapan</label>
            </div>
            <LabelDropdown
              value={settings.label || ''}
              disabled={updating}
              loading={updating}
              onChange={handleLabelChange}
            />
          </Form.Field>

          {/* Block Status Section */}
          {admin?.admin?.level === 'owner' && (
            <Form.Field className="space-y-2">
              <div className="flex items-center gap-2 mb-2">
                <IoShieldOutline className="text-lg text-gray-500" />
                <label className="text-sm font-medium text-gray-700">Status Blokir</label>
              </div>
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="text-gray-700">{blocked ? 'Diblokir' : 'Tidak di Blokir'}</div>
                <Button
                  color={blocked ? 'blue' : 'red'}
                  onClick={handleBlockToggle}
                  size="small"
                  className={`${
                    blocked ? '!bg-blue-600 hover:!bg-blue-700' : '!bg-red-600 hover:!bg-red-700'
                  } !text-white transition-colors`}
                >
                  {blocked ? 'Unblock' : 'Block'}
                </Button>
              </div>
            </Form.Field>
          )}

          {/* Agent AI Reply Section */}
          {admin?.admin?.level === 'owner' && (
            <Form.Field className="space-y-2">
              <div className="flex items-center gap-2 mb-2">
                <IoHardwareChipOutline className="text-lg text-gray-500" />
                <label className="text-sm font-medium text-gray-700">Agent AI Reply</label>
              </div>
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="text-gray-700">{agentAiReply ? 'Aktif' : 'Tidak Aktif'}</div>
                <Button
                  color={agentAiReply ? 'red' : 'green'}
                  onClick={handleAgentAiReplyToggle}
                  size="small"
                  // disabled={!agentAiReply}
                  // className={`${
                  //   agentAiReply
                  //     ? '!bg-red-600 hover:!bg-red-700'
                  //     : '!bg-gray-400'
                  // } !text-white transition-colors`}
                >
                  {agentAiReply ? 'Nonaktifkan' : 'Aktifkan'}
                </Button>
              </div>
            </Form.Field>
          )}

          {/* Exclusive Admin Section */}
          {chatRoom.exclusive_admin?.email && (
            <Form.Field className="space-y-2">
              <div className="flex items-center gap-2 mb-2">
                <IoPersonOutline className="text-lg text-gray-500" />
                <label className="text-sm font-medium text-gray-700">Admin Exclusive</label>
              </div>
              <div className="p-4 bg-gray-50 rounded-lg">
                <span className="text-gray-700">{chatRoom.exclusive_admin.email}</span>
              </div>
            </Form.Field>
          )}

          {/* Export Chat Section */}
          <Form.Field className="space-y-2">
            <div className="flex items-center gap-2 mb-2">
              <IoDownloadOutline className="text-lg text-gray-500" />
              <label className="text-sm font-medium text-gray-700">Export Chat</label>
            </div>
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="text-gray-700">Export seluruh percakapan dalam format TXT</div>
              <Button
                color="green"
                onClick={handleExportChat}
                size="small"
                className="!bg-green-600 hover:!bg-green-700 !text-white transition-colors"
              >
                Export
              </Button>
            </div>
          </Form.Field>
        </Form>
      </div>
      <ModalBlock />
    </React.Fragment>
  );
};

export default RoomSettings;

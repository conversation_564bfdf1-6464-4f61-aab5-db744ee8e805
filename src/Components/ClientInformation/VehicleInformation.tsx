import React, { ChangeEvent, useEffect, useState, useCallback, useMemo } from 'react';
import { Form, Input, Select, DropdownProps, Dropdown } from 'semantic-ui-react';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { useSelector, useDispatch } from 'react-redux';
import { Model, VariantProduct } from '../../services/types/catalaogueTypes';
import SelectVehicleFromCatalogue from '../SelectVehicle/SelectVehicleFromCatalogue';
import currencyFormat from '../../helpers/currencyFormat';
import moment from 'moment';
import { mainApiServices } from '../../services/MainApiServices';
import { fetchCustomerThunk } from '../../redux/customerInfo/customerInfoSlice';
import ModalStockCheck from '../ModalStockCheck/ModalStockCheck';
import stockCheckSlice from '../../redux/stock-check/stock-check.slice';
import SelectVehicleFromAutoTrimitra from '../SelectVehicle/SelectVehicleFromAutoTrimitra';
import { ApiErrorResponse } from 'apisauce';
import HTMLReactParser from 'html-react-parser';
import { IoCalendarOutline, IoSaveOutline, IoSearchOutline } from 'react-icons/io5';

export interface VehicleInformationStates {
  cityGroup: string;
  licensePlate: string;
  brand: {
    name: string;
    uuid: string;
  } | null;
  model: {
    name: string;
    uuid: string;
    category: string;
  } | null;
  variant: null | {
    name: string;
    code: string;
    uuid: string;
  };
  variantFreeText: string;
  color: null | { color_name: string; color_code: string };
  otr: number;
  year: string;
  mileage: string;

  condition: 'new' | 'used' | null;

  negativeMessage: string | null;
  success: boolean;
  loading: boolean;
}

interface VehicleInformationProps {}

const VehicleInformation: React.FC<VehicleInformationProps> = () => {
  const { conversation, customer, project } = useSelector((state: TMainReduxStates) => ({
    conversation: state.reducerConversation,
    customer: state.customerReducer,
    project: state.reducerProject,
  }));

  // State management
  const [cityGroup, setCityGroup] = useState('');
  const [licensePlate, setLicensePlate] = useState('');
  const [brand, setBrand] = useState<{ name: string; uuid: string } | null>(null);
  const [model, setModel] = useState<{
    name: string;
    uuid: string;
    category: string;
  } | null>(null);
  const [variant, setVariant] = useState<{
    name: string;
    code: string;
    uuid: string;
  } | null>(null);
  const [variantFreeText, setVariantFreeText] = useState('');
  const [color, setColor] = useState<{
    color_name: string;
    color_code: string;
  } | null>(null);
  const [otr, setOtr] = useState(0);
  const [year, setYear] = useState(moment().year().toString());
  const [mileage, setMileage] = useState('');
  const [condition, setCondition] = useState<'new' | 'used' | null>(null);
  const [negativeMessage, setNegativeMessage] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [loading, setLoading] = useState(false);

  const dispatch = useDispatch();

  const projectGroup = project.project?.group;

  if (projectGroup !== 'amartamotor') {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
        <h3 className="text-red-800 font-medium mb-2">Project ini tidak bisa mengakses menu ini</h3>
        <p className="text-red-700 text-sm">Menu ini hanya tersedia untuk project Amarta Motor.</p>
      </div>
    );
  }

  const load = useCallback(async () => {
    const client = customer.client;
    if (client) {
      if (client.profile.area) {
        setCityGroup(client.profile.area.text);
      }
      if (client.dream_vehicle) {
        setCondition(client.dream_vehicle.condition || null);
        setLicensePlate(client.dream_vehicle.license_plate || '');
        setBrand({
          name: client.dream_vehicle.brand_name || '',
          uuid: client.dream_vehicle.brand_uuid || '',
        });
        setModel({
          name: client.dream_vehicle.model_name || '',
          category: client.dream_vehicle.model_category || '',
          uuid: client.dream_vehicle.model_uuid || '',
        });
        setVariant({
          code: client.dream_vehicle.variant_code || '',
          name: client.dream_vehicle.variant_name?.toUpperCase() || '',
          uuid: client.dream_vehicle.variant_uuid || '',
        });
        setVariantFreeText(client.dream_vehicle.variant_free_text || '');
        setOtr(client.dream_vehicle.price || 0);
        setColor({
          color_code: client.dream_vehicle.color_code || '',
          color_name: client.dream_vehicle.color_name || '',
        });
        setMileage(client.dream_vehicle.mileage || '');
        setYear(client.dream_vehicle?.year || moment().format('YYYY'));
      }
    }
  }, [customer.client]);

  useEffect(() => {
    load();
  }, [load]);

  // Optimasi 1: Gunakan useMemo untuk options dropdown tahun
  const yearOptions = useMemo(
    () =>
      Array.from({ length: new Date().getFullYear() - 1990 + 1 }, (_, i) => ({
        key: 1990 + i,
        text: (1990 + i).toString(),
        value: (1990 + i).toString(),
      })).reverse(),
    [],
  );

  // Optimasi 2: Destructuring untuk customer.client.dream_vehicle
  const { dream_vehicle } = customer.client || {};
  const lastUpdated = dream_vehicle?.updated_at
    ? moment(dream_vehicle.updated_at.toDate()).format('DD MMM YYYY HH:mm:ss')
    : 'Belum ada';

  // Optimasi 3: Gunakan useCallback untuk handler functions
  const handleSubmit = useCallback(async () => {
    setLoading(true);
    setSuccess(false);
    setNegativeMessage(null);

    const clientRef = conversation.chatRoom?.clients[0];
    const chatRoomRef = conversation.chatRoom?.ref;

    if (clientRef && model && chatRoomRef) {
      try {
        await mainApiServices.updateDreamVehicle(
          {
            condition: condition || 'new',
            license_plate: licensePlate,
            brand_name: brand?.name?.toUpperCase() || '',
            brand_uuid: brand?.uuid || '',
            model_name: model?.name.toUpperCase(),
            model_uuid: model?.uuid || '',
            model_category: model?.category?.toUpperCase() || '',
            variant_code: variant?.code?.toUpperCase() || '',
            variant_name: variant?.name?.toUpperCase() || '',
            variant_uuid: variant?.uuid || '',
            variant_free_text: variantFreeText,
            color_name: color?.color_name.toUpperCase() ?? '',
            color_code: color?.color_code.toUpperCase() ?? '',
            year: year ?? null,
            area: cityGroup || '',
            price: otr ?? 0,
            mileage: mileage,
          },
          clientRef,
          chatRoomRef,
        );

        setSuccess(true);
        if (clientRef) {
          dispatch(fetchCustomerThunk({ clientDocRef: clientRef }) as any);
        }
      } catch (e: any) {
        const error = e as ApiErrorResponse<{ error: any }>;
        let messages!: string;

        if ('type' in error.data?.error && error.data?.error.type === 'UNPROCESSABLE_ENTITY') {
          messages = "<ul class='list-disc'>";
          for (let [key, value] of Object.entries(error.data!.error.messages)) {
            messages += `<li>${(value as { msg: string }).msg} (${key})</li>`;
          }
          messages += '</ul>';
        } else {
          messages = error.data?.error.messages || 'Gagal memperbarui kendaraan';
        }

        setNegativeMessage(messages);
      } finally {
        setLoading(false);
      }
    }
  }, [
    condition,
    licensePlate,
    brand,
    model,
    variant,
    variantFreeText,
    color,
    year,
    cityGroup,
    otr,
    mileage,
    conversation.chatRoom,
  ]);

  // Optimasi 4: Memoize condition options
  const conditionOptions = useMemo(
    () => [
      { text: 'Baru', value: 'new' },
      { text: 'Bekas', value: 'used' },
    ],
    [],
  );

  // Handler functions
  const handleConditionChange = (e: any, data: DropdownProps) => {
    const value = data.value as 'new' | 'used';
    setCondition(value);
    // Reset related states
    setLicensePlate('');
    setBrand(null);
    setModel(null);
    setVariant(null);
    setVariantFreeText('');
    setColor(null);
    setOtr(0);
    setYear(moment().year().toString());
    setMileage('');
    setSuccess(false);

    if (value === customer.client?.dream_vehicle?.condition) {
      load();
    }
  };

  const handleBrandChangeFromAutotrimitra = (brand: { name: string; uuid: string }) => {
    setBrand(brand);
    setModel(null);
    setVariant(null);
    setColor(null);
  };

  const handleMileageChange = (params: ChangeEvent<HTMLInputElement>) => {
    setMileage(params.target.value);
  };

  const handleLicensePlateChange = (params: ChangeEvent<HTMLInputElement>) => {
    setLicensePlate(params.target.value);
  };

  const handleYearChange = (value: string) => {
    setYear(value);
  };

  const handleModelChangeFromCatalogue = (model: Model) => {
    setModel({
      uuid: '',
      name: model.model_name.toLowerCase(),
      category: '',
    });
    setVariant(null);
    setColor(null);
  };

  const handleModelChangeFromAutotrimitra = (model: {
    name: string;
    uuid: string;
    category: string;
  }) => {
    setModel({
      uuid: model.uuid,
      name: model.name.toLowerCase(),
      category: model.category,
    });
    setVariant(null);
    setColor(null);
  };

  const handleVariantChangeFromCatalogue = (variant: VariantProduct) => {
    setVariant({
      name: variant.variant_name,
      code: variant.variant_code,
      uuid: '',
    });
    setColor(null);
  };

  const handleVariantChangeFromAutotrimitra = (variant: {
    name: string;
    code: string;
    uuid: string;
  }) => {
    setVariant({
      name: variant.name,
      uuid: variant.uuid,
      code: variant.code,
    });
  };

  const handleVariantChangeFromAutotrimitraFreeText = (variant: string) => {
    setVariantFreeText(variant);
  };

  const handleColorChangeFromCatalogue = (variantColor: VariantProduct) => {
    setColor({
      color_code: variantColor.color_code,
      color_name: variantColor.color_name,
    });
    setOtr(variantColor.price);
  };

  const onClickOpenCheckStockModal = () => {
    if (!color || !variant || !year || !cityGroup) return;

    dispatch(
      stockCheckSlice.actions.open({
        color: {
          name: color.color_name,
          code: color.color_code,
        },
        variant: variant,
        year: year,
        cityGroup: cityGroup.toUpperCase(),
      }),
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-pulse text-gray-600">Mohon Tunggu...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {!cityGroup && condition === 'new' && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h3 className="text-red-800 font-medium mb-2">Tidak Dapat Diakses</h3>
          <p className="text-red-700 text-sm">
            Jika ingin memilih kendaraan yang diinginkan <strong>City Group</strong> harus di
            tentukan terlebih dahulu di menu <strong>Profile {'>'} City Group</strong>
          </p>
        </div>
      )}

      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <Form>
          <div className="space-y-6">
            <Form.Field required>
              <label className="block text-sm font-medium text-gray-700 mb-1">Kondisi</label>
              <Select
                options={conditionOptions}
                placeholder="Kondisi Kendaraan"
                value={condition || ''}
                onChange={handleConditionChange}
                className="w-full"
              />
            </Form.Field>

            {condition === 'new' && (
              <Form.Field>
                <label className="block text-sm font-medium text-gray-700 mb-1">City Group</label>
                <div className="text-gray-900 p-2">
                  {cityGroup || <span className="italic text-gray-500">Belum ada</span>}
                </div>
              </Form.Field>
            )}

            {condition === 'new' ? (
              <Form.Field>
                <SelectVehicleFromCatalogue
                  cityGroup={cityGroup}
                  variant={{
                    selected: variant ?? undefined,
                    onChange: handleVariantChangeFromCatalogue,
                  }}
                  model={{
                    selected: model?.name ?? undefined,
                    onChange: handleModelChangeFromCatalogue,
                  }}
                  color={{
                    selected: color
                      ? {
                          code: color.color_code,
                          name: color.color_name,
                        }
                      : undefined,
                    onChange: handleColorChangeFromCatalogue,
                  }}
                />
              </Form.Field>
            ) : (
              condition === 'used' && (
                <div className="space-y-6">
                  <Form.Field>
                    <SelectVehicleFromAutoTrimitra
                      useFreeTextVariant={true}
                      brand={{
                        name: brand?.name || '',
                        uuid: brand?.uuid || '',
                      }}
                      model={{
                        name: model?.name || '',
                        uuid: model?.uuid || '',
                        category: model?.category || '',
                      }}
                      variant={{
                        uuid: variant?.uuid || '',
                        name: variant?.name || '',
                        code: variant?.code || '',
                      }}
                      variantFreeText={variantFreeText}
                      onBrandChange={handleBrandChangeFromAutotrimitra}
                      onModelChange={handleModelChangeFromAutotrimitra}
                      onVariantChange={handleVariantChangeFromAutotrimitra}
                      onVariantFreeTextChange={handleVariantChangeFromAutotrimitraFreeText}
                    />
                  </Form.Field>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Form.Field required>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Plat Nomor
                      </label>
                      <Input
                        placeholder="Masukan Plat Nomor"
                        onChange={handleLicensePlateChange}
                        value={licensePlate}
                        className="w-full"
                      />
                    </Form.Field>
                    <Form.Field required>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Kilometer Kendaraan
                      </label>
                      <Input
                        placeholder="Masukan Kilometer Kendaraan"
                        onChange={handleMileageChange}
                        value={mileage}
                        className="w-full"
                      />
                    </Form.Field>
                  </div>
                </div>
              )
            )}

            {condition && (
              <Form.Field required>
                <label className="block text-sm font-medium text-gray-700 mb-1">Tahun</label>
                <Dropdown
                  placeholder="Pilih Tahun"
                  fluid
                  selection
                  options={yearOptions}
                  value={year || ''}
                  onChange={(e, data: DropdownProps) => handleYearChange(data.value as string)}
                  className="w-full"
                />
              </Form.Field>
            )}

            <Form.Field>
              <button
                type="button"
                onClick={handleSubmit}
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:bg-blue-300 flex items-center gap-2"
              >
                <IoSaveOutline className="text-lg" />
                <span>{loading ? 'Menyimpan...' : 'Perbarui Sekarang'}</span>
              </button>
            </Form.Field>

            {negativeMessage && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="text-red-700">{HTMLReactParser(negativeMessage)}</div>
              </div>
            )}

            {success && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <p className="text-green-700">Berhasil memperbarui kendaraan.</p>
              </div>
            )}
          </div>
        </Form>
      </div>

      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <div className="flex items-center gap-2 text-gray-600">
          <IoCalendarOutline className="text-lg" />
          <span className="text-sm">Terakhir di update:</span>
          <span className="font-medium">{lastUpdated}</span>
        </div>
      </div>

      {condition === 'new' && (
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100 space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <h3 className="text-md font-medium text-gray-900">OTR Kendaraan</h3>
              <p className="text-xl font-semibold text-blue-600">{currencyFormat(otr ?? 0)}</p>
            </div>
            <button
              onClick={onClickOpenCheckStockModal}
              className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors flex items-center gap-2"
            >
              <IoSearchOutline className="text-lg" />
              <span>Periksa Stok</span>
            </button>
          </div>
          <ModalStockCheck />
        </div>
      )}
    </div>
  );
};

export default VehicleInformation;

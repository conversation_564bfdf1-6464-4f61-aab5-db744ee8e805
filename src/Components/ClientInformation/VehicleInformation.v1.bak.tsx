import React, { ChangeEvent, Component } from 'react';
import { <PERSON><PERSON>, Divider, Form, Message, Segment } from 'semantic-ui-react';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { connect } from 'react-redux';
import { Model, VariantProduct } from '../../services/types/catalaogueTypes';
import SelectVehicleFromCatalogue from '../SelectVehicle/SelectVehicleFromCatalogue';
import currencyFormat from '../../helpers/currencyFormat';
import moment from 'moment';
import { mainApiServices } from '../../services/MainApiServices';
import { mainStore } from '../../redux/reducers';
import { fetchCustomerThunk } from '../../redux/customerInfo/customerInfoSlice';
import { catalogueServices } from '../../services/catalogue/catalogueServices';
import ModalStockCheck from '../ModalStockCheck/ModalStockCheck';
import stockCheckSlice from '../../redux/stock-check/stock-check.slice';

export interface VehicleInformationStates {
  cityGroup: null | { value: string; text: string };
  model: string | null;
  variant: null | {
    name: string;
    code: string;
  };
  color: null | VariantProduct;
  year: string;

  buyTime: null | Date;

  negativeMessage: string | null;
  success: boolean;
  loading: boolean;
}

export interface VehicleInformationProps {
  target: 'vehicleDream';
  conversation: TMainReduxStates['reducerConversation'];
  customer: TMainReduxStates['customerReducer'];
}

class VehicleInformation extends Component<VehicleInformationProps, VehicleInformationStates> {
  constructor(props: VehicleInformationProps) {
    super(props);
    this.state = {
      loading: false,

      cityGroup: null,
      model: null,
      variant: null,
      color: null,
      year: moment().year().toString(),

      buyTime: moment().toDate(),

      negativeMessage: null,
      success: false,
    };
  }

  private year = {
    onChange: (params: ChangeEvent<HTMLInputElement>) => {
      this.setState({
        year: params.target.value,
      });
    },
  };

  load = async () => {
    const currentState: VehicleInformationStates = { ...this.state };

    const client = this.props.customer.client;
    if (client) {
      if (client.profile.area) {
        currentState.cityGroup = { ...client.profile.area };
      }
      if (client.dream_vehicle) {
        currentState.model = client.dream_vehicle.model_name ?? '';
        currentState.variant =
          !!client.dream_vehicle.variant_code && !!client.dream_vehicle.variant_code
            ? {
                code: client.dream_vehicle.variant_code,
                name: client.dream_vehicle.variant_name,
              }
            : null;
        currentState.color = client.dream_vehicle.color_code
          ? ({
              variant_code: client.dream_vehicle.variant_code,
              model_name: client.dream_vehicle.model_name,
              color_code: client.dream_vehicle.color_code,
              color_name: client.dream_vehicle.color_name,
            } as any)
          : null;
      }

      if (client.dream_vehicle?.color_code) {
        currentState.year = !client.dream_vehicle.year
          ? moment().year().toString()
          : client.dream_vehicle.year;
        const getVehicle = await catalogueServices.getVariantByAreaAMH({
          area: client.profile.area?.text || '',
          variantCode: client.dream_vehicle.variant_code,
        });
        if (getVehicle?.data && getVehicle?.data.length > 0) {
          currentState.color =
            getVehicle.data.find((v) => v.color_code === client.dream_vehicle?.color_code) || null;
        }
      }
    }

    this.setState({
      ...currentState,
    });
  };

  private model = {
    onChange: (model: Model) => {
      this.setState({
        model: model.model_name.toLowerCase(),
        variant: null,
        color: null,
      });
    },
  };

  private variant = {
    onChange: (variant: VariantProduct) => {
      this.setState({
        variant: {
          name: variant.variant_name,
          code: variant.variant_code,
        },
        color: null,
      });
    },
  };

  private color = {
    onChange: (variantColor: VariantProduct) => {
      this.setState({
        color: variantColor,
      });
    },
  };

  onSubmit = async () => {
    this.setState(
      {
        loading: true,
      },
      async () => {
        let currentState: VehicleInformationStates = { ...this.state };
        const clientRef = this.props.conversation.chatRoom?.clients[0];
        const chatRoomRef = this.props.conversation.chatRoom?.ref;

        if (clientRef && this.state.variant && this.state.model && chatRoomRef) {
          try {
            await mainApiServices.updateDreamVehicle(
              {
                mileage: '',
                condition: 'new',
                license_plate: '',
                variant_free_text: '',
                brand_name: '',
                brand_uuid: '',
                model_name: this.state.model.toUpperCase(),
                model_uuid: '',
                model_category: '',
                variant_code: this.state.variant.code.toUpperCase(),
                variant_name: this.state.variant.name.toUpperCase(),
                variant_uuid: '',
                color_name: this.state.color?.color_name.toUpperCase() ?? '',
                color_code: this.state.color?.color_code.toUpperCase() ?? '',
                year: this.state.year ?? null,
                area: this.state.cityGroup?.value ?? '',
                price: this.state.color?.price ?? 0,
              },
              clientRef,
              chatRoomRef,
            );

            currentState.success = true;
          } catch (e: any) {}
        }
        currentState.loading = false;

        this.setState(
          {
            ...currentState,
          },
          () => {
            const client = this.props.conversation.chatRoom?.clients[0];
            if (client) {
              mainStore.dispatch(
                fetchCustomerThunk({
                  clientDocRef: client,
                }) as any,
              );
            }
          },
        );
      },
    );
  };

  onClickOpenCheckStockModal = () => {
    if (!this.state.color || !this.state.variant || !this.state.year || !this.state.cityGroup)
      return;

    mainStore.dispatch(
      stockCheckSlice.actions.open({
        color: {
          name: this.state.color.color_name,
          code: this.state.color.color_code,
        },
        variant: this.state.variant,
        year: this.state.year,
        cityGroup: this.state.cityGroup.text.toUpperCase(),
      }),
    );
  };

  componentDidMount() {
    this.load().then();
  }

  render() {
    if (!this.state.cityGroup?.text) {
      return (
        <Message error={true}>
          <Message.Header>Tidak Dapat Diakses</Message.Header>
          <p>
            Jika ingin memilih kendaraan yang diinginkan <strong>City Group</strong> harus di
            tentukan terlebih dahulu di menu <strong>Profile {'>'} City Group</strong>
          </p>
        </Message>
      );
    }

    return (
      <Segment basic>
        <Form>
          <Form.Field>
            <label>City Group</label>
            <span>{this.state.cityGroup?.text ?? <i>Belum ada</i>}</span>
          </Form.Field>
          <SelectVehicleFromCatalogue
            cityGroup={this.state.cityGroup.text}
            variant={{
              selected: this.state.variant ?? undefined,
              onChange: this.variant.onChange,
            }}
            model={{
              selected: this.state.model ?? undefined,
              onChange: this.model.onChange,
            }}
            color={{
              selected: this.state.color
                ? {
                    code: this.state.color.color_code,
                    name: this.state.color.color_name,
                  }
                : undefined,
              onChange: this.color.onChange,
            }}
          />
          <Form.Field>
            <input
              placeholder={'Tahun'}
              value={this.state.year}
              onChange={this.year.onChange}
            />
          </Form.Field>

          {this.state.negativeMessage && (
            <Message negative>
              <Message.Header>Tidak dapat memperbarui kendaraan</Message.Header>
              <p>{this.state.negativeMessage}</p>
            </Message>
          )}

          {this.state.success && (
            <Message positive>
              <p>Berhasil memperbarui kendaraan.</p>
            </Message>
          )}

          <Form.Group>
            <Form.Field>
              <Button
                onClick={this.onSubmit}
                disabled={this.state.loading}
                loading={this.state.loading}
              >
                Perbarui Sekarang
              </Button>
            </Form.Field>
          </Form.Group>
        </Form>
        <div style={{ marginTop: '16px' }}>
          OTR Kendaraan : {currencyFormat(this.state.color?.price ?? 0)}
        </div>
        <Divider className={'my-5'} />
        <div>
          <Button onClick={this.onClickOpenCheckStockModal}>Periksa Stok</Button>
        </div>
        <ModalStockCheck />
      </Segment>
    );
  }
}

const mapStateToProps = (states: TMainReduxStates) => ({
  conversation: states.reducerConversation,
  customer: states.customerReducer,
});

export default connect(mapStateToProps)(VehicleInformation);

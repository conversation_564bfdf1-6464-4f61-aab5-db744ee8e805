import React, { Component } from 'react';
import moment from 'moment';
import { Icon, List } from 'semantic-ui-react';
import { IContactItemProps } from './types/contact-item-types';
import isSameDay from '../../helpers/isSameday';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { connect } from 'react-redux';
import { maskPhoneNumbers } from '../../helpers/maskPhoneNumber/maskSanitizedPhoneNumber';
import { BsCheck, BsCheckAll } from 'react-icons/bs';
import { MdPerson } from 'react-icons/md';
import { IoHardwareChipOutline } from 'react-icons/io5';

class ContactItem extends Component<IContactItemProps> {
  dateRender = () => {
    const chatMoment = moment.unix(this.props.chat.recent_chat.unixtime);

    if (isSameDay(chatMoment)) {
      return chatMoment.format('HH:mm');
    } else {
      return chatMoment.format('D MMM YYYY');
    }
  };

  onClick = () => {
    this.props.onClick(this.props.chat);
  };

  labelRender = () => {
    let bgColor = 'bg-gray-500';
    const labelGroup = this.props.labels.labels.find(
      (value) => value.ref?.id === this.props.chat.label?.id,
    );

    if (labelGroup) {
      switch (labelGroup.group) {
        case 'after_sales':
          bgColor = 'bg-blue-500';
          break;
        case 'sales':
          bgColor = 'bg-orange-500';
          break;
        case 'pre_sales':
          bgColor = 'bg-green-500';
          break;
        case 'finco':
          bgColor = 'bg-yellow-500';
          break;
        case 'blacklist':
          bgColor = 'bg-black';
          break;
      }

      return (
        <div
          className={`${bgColor} text-white px-2 py-0.5 rounded-full text-xs font-medium shadow-sm whitespace-nowrap`}
        >
          {
            this.props.labels.labels.find((value) => value.ref?.id === this.props.chat.label?.id)
              ?.name
          }
        </div>
      );
    } else return;
  };

  renderLabelExclusive = () => {
    if (
      this.props.chat.exclusive_admin?.email &&
      this.props.admin.admin?.email !== this.props.chat.exclusive_admin?.email
    ) {
      return (
        <div className="bg-gray-100 text-gray-700 px-2 py-0.5 rounded-full text-xs font-medium border border-gray-300 flex items-center gap-1 whitespace-nowrap">
          <Icon name="dont" />
          EXCLUSIVE ADMIN
        </div>
      );
    }

    if (
      this.props.chat.exclusive_admin?.email &&
      this.props.admin.admin?.email === this.props.chat.exclusive_admin?.email
    ) {
      return (
        <div className="bg-violet-100 text-violet-700 px-2 py-0.5 rounded-full text-xs font-medium border border-violet-300 whitespace-nowrap">
          EXCLUSIVE
        </div>
      );
    }
  };

  renderDepartment = () => {
    const department = this.props.departments.departments.find(
      (value) => value.ref.id === this.props.chat.doc_department?.id,
    );
    if (department) {
      return (
        <div className="bg-gray-100 text-gray-700 px-2 py-0.5 rounded-full text-xs font-medium border border-gray-300 whitespace-nowrap">
          {department.name}
        </div>
      );
    }
  };

  renderMessageStatus = () => {
    const statuses = this.props.chat.recent_chat.statuses;
    if (!statuses || this.props.chat.recent_chat.direction === 'IN') {
      return null;
    }

    if (statuses.read) {
      return (
        <BsCheckAll
          className="text-green-500"
          size={16}
        />
      );
    } else if (statuses.delivered) {
      return (
        <BsCheckAll
          className="text-blue-500"
          size={16}
        />
      );
    } else if (statuses.sent) {
      return (
        <BsCheck
          className="text-gray-400"
          size={16}
        />
      );
    }

    return null;
  };

  renderAvatar = () => {
    const title = this.props.chat.headers.title.replace(/[^a-zA-Z0-9]/g, '');
    const initial = title.charAt(0).toUpperCase().trim();
    const isIncoming = this.props.chat.recent_chat.direction === 'IN';

    return (
      <div
        className={`
                w-10 h-10 rounded-full flex items-center justify-center
                ${isIncoming ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'}
                transition-colors duration-200
            `}
      >
        {initial ? (
          <span className="text-lg font-semibold">{initial}</span>
        ) : (
          <MdPerson className="text-xl" />
        )}
      </div>
    );
  };

  render() {
    const recentChatDirection = this.props.chat.recent_chat.direction;
    const isIncoming = recentChatDirection === 'IN';

    return (
      <List.Item
        active={this.props.active}
        onClick={this.onClick}
        className={`hover:bg-gray-50 transition-all duration-200 px-5 py-3.5 ${this.props.active ? 'bg-blue-50/70' : ''} ${isIncoming ? 'bg-blue-50/30' : ''}`}
      >
        <div className="flex gap-3">
          {this.renderAvatar()}

          <div className="flex-1 min-w-0">
            <div className="flex justify-between items-start mb-1.5">
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 flex-wrap">
                  <span
                    className={`text-base truncate ${isIncoming ? 'font-semibold' : 'font-medium'} ${isIncoming ? 'text-blue-900' : 'text-gray-900'}`}
                  >
                    {this.props.chat.headers.title}
                  </span>
                  {this.props.chat.cityGroup && (
                    <span className="text-xs font-medium px-2 py-0.5 rounded-full bg-gray-100 text-gray-600 whitespace-nowrap">
                      {this.props.chat.cityGroup}
                    </span>
                  )}
                  {this.props.chat.dream_vehicle?.model_name && (
                    <span className="text-xs font-medium px-2 py-0.5 rounded-full bg-blue-50 text-blue-600 whitespace-nowrap">
                      {this.props.chat.dream_vehicle.model_name}
                    </span>
                  )}
                  {this.props.chat.organization && (
                    <span className="text-xs font-medium px-2 py-0.5 rounded-full bg-blue-50 text-orange-600 whitespace-nowrap">
                      {this.props.chat.organization}
                    </span>
                  )}
                </div>
              </div>
              <div className="flex items-center gap-2.5 ml-2 shrink-0">
                <span
                  className={`text-xs whitespace-nowrap ${isIncoming ? 'text-blue-600 font-medium' : 'text-gray-500'}`}
                >
                  {this.dateRender()}
                </span>
                {isIncoming ? (
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 rounded-full bg-blue-600 animate-pulse shadow-sm"></div>
                  </div>
                ) : (
                  <div className="flex items-center">{this.renderMessageStatus()}</div>
                )}
              </div>
            </div>

            <div
              className={`text-sm mb-2.5 line-clamp-1 ${isIncoming ? 'text-blue-800' : 'text-gray-600'}`}
            >
              {isIncoming ? (
                <span className="font-medium truncate">
                  {this.renderText(this.props.chat.recent_chat.text)}
                </span>
              ) : (
                <div className="flex items-center gap-1">
                  <span className="truncate">
                    <span className="text-gray-700 font-medium">
                      {this.props.chat.recent_chat.display_name}:
                    </span>
                    &nbsp;
                    {this.renderText(this.props.chat.recent_chat.text)}
                  </span>
                </div>
              )}
            </div>

            <div className="flex flex-wrap items-center gap-2">
              {this.labelRender()}
              {this.renderDepartment()}
              {this.renderLabelExclusive()}
              {this.props.chat.blocked && (
                <div className="bg-red-500 text-white px-2 py-0.5 rounded-full text-xs font-medium shadow-sm whitespace-nowrap">
                  Blocked
                </div>
              )}
              {this.props.chat.agent_ai_reply && (
                <div className="bg-gradient-to-r from-teal-500 to-blue-500 text-white px-2 py-0.5 rounded-full flex items-center gap-1 text-xs font-medium shadow-sm">
                  <IoHardwareChipOutline className="text-xs" />
                  <span>AI Aktif</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </List.Item>
    );
  }

  private renderText = (text: string) => {
    let textChat = text || '';
    textChat = maskPhoneNumbers(textChat);
    return textChat;
  };
}

const mapStateToProps = (states: TMainReduxStates) => ({
  labels: states.reducerAvailableLabels,
  departments: states.reducerAvailableDepartment,
  admin: states.reducerAdmin,
});

export default connect(mapStateToProps)(ContactItem);

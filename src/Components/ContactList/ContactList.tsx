/**
 * ContactList Component
 *
 * A component that displays a list of chat contacts/rooms with filtering capabilities.
 * Features include:
 * - Search by phone number
 * - Filter by labels
 * - Filter by departments (for admin/owner)
 * - Real-time chat room loading
 * - Pagination support
 *
 * @component
 */
import React, { useCallback, useEffect, useMemo } from 'react';
import { Button, Dropdown, Icon, List } from 'semantic-ui-react';
import ContactItem from './ContactItem';
import { IContactListProps } from './types/contact-list-types';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { useDispatch, useSelector } from 'react-redux';
import ChatRoomEntity from '../../entities/ChatRoomEntity';
import LabelDropdown from '../LabelDropdown/LabelDropdown';
import SearchPhoneNumber from './SearchPhoneNumber';
import { changeListenerChatRoomMessages } from '../../redux/conversations/chatRoomSlice';
import recentChatSlice, {
  refreshListenChatRoomList,
  startListenChatRoomList,
  stopListenChatRoomList,
} from '../../redux/recent-chats/recentChatSlice';
import ProjectInfo from './ProjectInfo';
import { AppDispatch } from '../../redux/store';
import { useScrollContainer } from '../../contexts/ScrollContainerContext';

const ContactList: React.FC<IContactListProps> = () => {
  const dispatch = useDispatch<AppDispatch>();

  const { scrollContainerRef } = useScrollContainer();

  // Redux state selectors
  const recentChats = useSelector((state: TMainReduxStates) => state.recentChat);
  const conversations = useSelector((state: TMainReduxStates) => state.reducerConversation);
  const admin = useSelector((state: TMainReduxStates) => state.reducerAdmin);
  const department = useSelector((state: TMainReduxStates) => state.reducerAvailableDepartment);
  const label = useSelector((state: TMainReduxStates) => state.reducerAvailableLabels);

  /**
   * Fetches all chat rooms from the server
   */
  const fetchChatRooms = useCallback(() => {
    dispatch(startListenChatRoomList() as any);
  }, []);

  /**
   * Handles click event on a chat room item
   */
  const onItemClick = useCallback((chatRoom: ChatRoomEntity) => {
    dispatch(changeListenerChatRoomMessages(chatRoom) as any);
  }, []);

  /**
   * Handles department filter changes
   * @param departmentId - ID of the selected department or special values (ALL_DEPARTMENTS/NO_DEPARTMENT)
   */
  const onDepartmentChange = useCallback((departmentId: string) => {
    if (['ALL_DEPARTMENTS', 'NO_DEPARTMENT'].includes(departmentId)) {
      dispatch(
        recentChatSlice.actions.setFilterDepartment(
          departmentId as 'ALL_DEPARTMENTS' | 'NO_DEPARTMENT',
        ),
      );
    } else {
      const foundDepartment = department.departments.find((d) => d.ref.id === departmentId);
      if (foundDepartment) {
        dispatch(recentChatSlice.actions.setFilterDepartment(foundDepartment));
      } else {
        dispatch(recentChatSlice.actions.setFilterDepartment(undefined));
      }
    }
  }, []);

  /**
   * Handles label filter changes
   * @param value - ID of the selected label or special values (ALL_LABELS/NO_LABEL)
   */
  const onFilterLabelChange = useCallback((value: string) => {
    if (['ALL_LABELS', 'NO_LABEL'].includes(value)) {
      dispatch(recentChatSlice.actions.setFilterLabel(value as 'ALL_LABELS' | 'NO_LABEL'));
    } else {
      const foundLabel = label.labels.find((l) => l.ref?.id === value);
      if (foundLabel) {
        dispatch(recentChatSlice.actions.setFilterLabel(foundLabel));
      } else {
        dispatch(recentChatSlice.actions.setFilterLabel(undefined));
      }
    }
  }, []);

  // Compute the current label value for the dropdown
  const labelValue = useMemo(() => {
    if (recentChats.filters.label === 'ALL_LABELS') {
      return 'ALL_LABELS';
    } else if (recentChats.filters.label === 'NO_LABEL') {
      return 'NO_LABEL';
    } else {
      return recentChats.filters.label?.ref?.id;
    }
  }, [recentChats.filters.label]);

  // Compute the current department value for the dropdown
  const departmentValue = useMemo(() => {
    if (recentChats.filters.department === 'ALL_DEPARTMENTS') {
      return 'ALL_DEPARTMENTS';
    } else if (recentChats.filters.department === 'NO_DEPARTMENT') {
      return 'NO_DEPARTMENT';
    } else {
      return recentChats.filters.department?.ref?.id;
    }
  }, [recentChats.filters.department]);

  /**
   * Handles loading more chat rooms (pagination)
   */
  const viewMore = useCallback(() => {
    dispatch(recentChatSlice.actions.addFilterLimit(50));
  }, []);

  useEffect(() => {
    fetchChatRooms();

    return () => {
      dispatch(stopListenChatRoomList() as any);
    };
  }, []);

  useEffect(() => {
    dispatch(refreshListenChatRoomList() as any);
  }, [recentChats.filters.label, recentChats.filters.department, recentChats?.filters.limit]);

  /**
   * Renders the filter section including search, label, and department filters
   */
  const renderFilters = useCallback(() => {
    // Check if phone number filter is active to disable other filters
    const isPhoneNumberFilterActive = Boolean(
      recentChats.filters.phoneNumber && recentChats.filters.phoneNumber.trim() !== '',
    );

    return (
      <div className="px-3 py-4 bg-white border-b border-gray-100 shadow-sm">
        <div className="space-y-3">
          <SearchPhoneNumber />

          <div className="flex gap-3">
            <div className="flex-1">
              <LabelDropdown
                noLabelText="Tanpa Label"
                onChange={(_, data) => onFilterLabelChange(data.value as string)}
                value={labelValue}
                disabled={isPhoneNumberFilterActive}
              />
            </div>

            {/* Department filter - only shown for owner level admin */}
            {admin?.admin?.level === 'owner' ? (
              <div className="flex-1">
                <Dropdown
                  fluid
                  clearable
                  selection
                  placeholder="Departemen"
                  disabled={isPhoneNumberFilterActive}
                  className="!text-sm !border !border-gray-200 !rounded-lg hover:!border-blue-400
									  focus:!ring-2 focus:!ring-blue-500 focus:!border-blue-500 !bg-white !shadow-sm
									  [&_.menu]:!mt-2 [&_.menu]:!py-1.5 [&_.menu]:!rounded-lg [&_.item]:!px-3 [&_.item]:!py-2
									  [&_.item]:!text-sm [&_.text]:!truncate"
                  onChange={(event, data) => onDepartmentChange(data.value as string)}
                  options={[
                    {
                      text: 'Belum Ada Departemen',
                      value: 'NO_DEPARTMENT',
                      key: 'NO_DEPARTMENT',
                    },
                    ...department.departments.map((dep) => ({
                      text: dep.name,
                      key: dep.ref.id,
                      value: dep.ref.id,
                    })),
                  ]}
                  value={departmentValue || ''}
                />
              </div>
            ) : (
              // Read-only department display for non-owner users
              <div className="flex-1 px-4 py-2.5 bg-gray-50 rounded-lg border border-gray-200">
                <span className="text-sm text-gray-600 font-medium truncate">
                  {typeof recentChats.filters.department !== 'string'
                    ? recentChats.filters.department?.name
                    : 'Belum Ada Departemen'}
                </span>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }, [
    admin,
    department.departments,
    onDepartmentChange,
    onFilterLabelChange,
    departmentValue,
    labelValue,
    recentChats.filters.phoneNumber,
  ]);

  /**
   * Renders the main content section including loading states and chat room list
   */
  const renderContent = useCallback(() => {
    // Show loading state
    if (recentChats.fetching) {
      return (
        <div className="flex flex-col items-center justify-center py-16 bg-gray-50/50">
          <Icon
            loading
            name="spinner"
            size="large"
            className="text-blue-500 mb-4"
          />
          <span className="text-gray-600 font-medium">Tunggu sebentar...</span>
        </div>
      );
    }

    // Show empty state
    if (recentChats.fetchedRoomsFromServer.length === 0 && !recentChats.fetching) {
      return (
        <div className="flex flex-col items-center justify-center py-16 bg-gray-50/50">
          <div className="w-14 h-14 bg-gray-100 rounded-full flex items-center justify-center mb-4 shadow-sm">
            <Icon
              name="search"
              size="large"
              className="text-gray-400"
            />
          </div>
          <span className="text-gray-600 font-medium">Tidak ada pesan</span>
        </div>
      );
    }

    // Show chat room list
    return (
      <List
        selection
        className="!border-none divide-y divide-gray-100"
      >
        {recentChats.fetchedRoomsFromServer.map((value) => (
          <ContactItem
            active={conversations.chatRoom?.ref!.id === value.ref!.id}
            onClick={onItemClick}
            key={value.ref.id}
            chat={value}
          />
        ))}

        {/* Show "Load More" button if not filtering by phone number */}
        {!recentChats.filters.phoneNumber && (
          <div className="p-4 bg-gray-50/50">
            <Button
              fluid
              basic
              loading={recentChats.fetching}
              onClick={viewMore}
              className="!bg-white hover:!bg-gray-50 !text-blue-600 !border-gray-200 !shadow-sm !font-medium transition-all duration-200"
            >
              Lebih Banyak
            </Button>
          </div>
        )}
      </List>
    );
  }, [recentChats, conversations.chatRoom, onItemClick, viewMore]);

  return (
    <div className="w-full h-full flex flex-col bg-white">
      <ProjectInfo />
      {renderFilters()}
      <div
        className="flex-1 overflow-y-auto bg-white"
        ref={scrollContainerRef}
      >
        {renderContent()}
      </div>
    </div>
  );
};

export default ContactList;

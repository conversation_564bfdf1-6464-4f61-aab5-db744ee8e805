import React from 'react';
import { useMediaQuery } from 'react-responsive';
import { Container, Dropdown, Grid } from 'semantic-ui-react';
import LabelDropdown from '../LabelDropdown/LabelDropdown';
import ChatRoomList from './ContactList';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { ThunkDispatch } from 'redux-thunk';
import { compose } from 'redux';
import { connect } from 'react-redux';

export interface IContactWrapperProps {
  department: TMainReduxStates['reducerAvailableDepartment'];
}

const ContactWrapper = (props: IContactWrapperProps) => {
  useMediaQuery({
    query: '(min-width: 1224px)',
  });
  const isTabletOrMobile = useMediaQuery({ query: '(max-width: 1224px)' });

  return (
    <Grid.Column width={isTabletOrMobile ? 12 : 4}>
      <Container textAlign={'center'}>
        <div>
          <Dropdown
            placeholder={'Departemen'}
            inline={true}
            options={[
              {
                text: 'Semua Departemen',
                value: 'all',
                key: 'all',
              },
              {
                text: 'Belum Ada Departemen',
                value: 'none',
                key: 'none',
              },
              ...props.department.departments.map((department) => ({
                text: department.name,
                key: department.ref.id,
                value: department.ref.id,
              })),
            ]}
          />
        </div>
        <div>
          <LabelDropdown noLabelText={'Semua Label'} />
        </div>
      </Container>
      <ChatRoomList />
    </Grid.Column>
  );
};

const mapStateToProps = (states: TMainReduxStates) => ({});

const mapDispatchToProps = (dispatch: ThunkDispatch<TMainReduxStates, void, any>) => ({});

export default compose<React.ComponentType>(connect(mapStateToProps, mapDispatchToProps))(
  ContactWrapper,
);

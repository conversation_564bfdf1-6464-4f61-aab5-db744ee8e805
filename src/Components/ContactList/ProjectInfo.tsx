import React from 'react';
import { useSelector } from 'react-redux';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { HiOfficeBuilding } from 'react-icons/hi';
import { FaWhatsapp } from 'react-icons/fa';
import { MdBusinessCenter } from 'react-icons/md';

const ProjectInfo: React.FC = () => {
  // Access project information from the redux store.
  const projectState = useSelector((state: TMainReduxStates) => state.reducerProject);

  // Show loading message if project info is being fetched.
  if (projectState.fetching) {
    return (
      <div className="animate-pulse px-6 py-3 bg-white border-b border-gray-100">
        <div className="h-5 bg-gray-100 rounded w-2/3 mb-2"></div>
        <div className="h-4 bg-gray-100 rounded w-1/3"></div>
      </div>
    );
  }

  // If no project is set, display a default message.
  if (!projectState.project) {
    return (
      <div className="px-6 py-3 bg-white border-b border-gray-100">
        <p className="text-gray-500 text-sm">No project information available</p>
      </div>
    );
  }

  // Display the project title and description.
  return (
    <div className="h-[60px] px-4 py-3 bg-white border-b border-gray-200 flex flex-col justify-center">
      {/* Project Name */}
      <div className="mb-1">
        <div className="flex items-center gap-2">
          <HiOfficeBuilding className="w-4 h-4 text-blue-500" />
          <h1 className="text-[15px] font-semibold text-gray-800">
            {projectState.project.legal_name || 'Not specified'}
          </h1>
        </div>
      </div>

      {/* Secondary Info */}
      <div className="flex items-center gap-4 text-xs text-gray-500">
        <div className="flex items-center gap-1.5">
          {['qiscus', 'meta'].includes(projectState.project.provider) ? (
            <FaWhatsapp className="w-3.5 h-3.5 text-green-500" />
          ) : (
            <MdBusinessCenter className="w-3.5 h-3.5 text-gray-400" />
          )}
          <span>{projectState.project.phone_number || 'Not specified'}</span>
        </div>
        <div className="flex items-center gap-1.5">
          <MdBusinessCenter className="w-3.5 h-3.5 text-gray-400" />
          <span>{projectState.project.provider || 'Not specified'}</span>
        </div>
      </div>
    </div>
  );
};

export default ProjectInfo;

import { ChangeEvent, useCallback, useRef, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import recentChatSlice, {
  refreshListenChatRoomList,
  startListenChatRoomList,
} from '../../redux/recent-chats/recentChatSlice';
import { TMainReduxStates } from '../../redux/types/redux-types';

const SearchPhoneNumber = () => {
  const dispatch = useDispatch();
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const filter = useSelector((state: TMainReduxStates) => state.recentChat.filters);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const handleChange = useCallback(
    (ev: ChangeEvent<HTMLInputElement>) => {
      const value = ev.target.value;
      if (timeoutRef.current) clearTimeout(timeoutRef.current);
      dispatch(recentChatSlice.actions.setFilterPhoneNumber(value));

      timeoutRef.current = setTimeout(() => {
        dispatch(refreshListenChatRoomList() as any);
      }, 500);
    },
    [dispatch],
  );

  const handleClear = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    dispatch(recentChatSlice.actions.setFilterPhoneNumber(''));
    dispatch(refreshListenChatRoomList() as any);
  }, [dispatch]);

  return (
    <div className="relative">
      <input
        type="text"
        placeholder="Cari nomor telepon..."
        value={filter.phoneNumber || ''}
        onChange={handleChange}
        className="w-full px-4 py-2.5 pr-10 text-sm bg-white border border-gray-200 rounded-lg
						  focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none
						  hover:border-blue-400 transition-all duration-200 shadow-sm"
      />
      {filter.phoneNumber ? (
        <button
          type="button"
          onClick={handleClear}
          className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      ) : (
        <div className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>
      )}
    </div>
  );
};

export default SearchPhoneNumber;

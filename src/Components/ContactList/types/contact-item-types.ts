import ChatRoomEntity from '../../../entities/ChatRoomEntity';
import { TMainReduxStates } from '../../../redux/types/redux-types';

export interface IContactItemProps {
  labels: TMainReduxStates['reducerAvailableLabels'];
  departments: TMainReduxStates['reducerAvailableDepartment'];
  chat: ChatRoomEntity;
  onClick: (chatRoom: ChatRoomEntity) => void;
  active?: boolean;
  admin: TMainReduxStates['reducerAdmin'];
}

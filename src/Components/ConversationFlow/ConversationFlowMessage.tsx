import React from 'react';
import { <PERSON><PERSON>, <PERSON>, CardContent, Form, Input, Select, TextArea } from 'semantic-ui-react';
import { useSelector } from 'react-redux';
import { mainStore } from '../../redux/reducers';
import addConversationFlowSlice, {
  IConversationFlowMessage,
} from '../../redux/add-conversation-flow/addConversationFlow.slice';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { DropdownProps } from 'semantic-ui-react/dist/commonjs/modules/Dropdown/Dropdown';
import range from '../../helpers/range';
import updateConversationFlowSlice from '../../redux/add-conversation-flow/updateConversationFlow.slice';
import { InputOnChangeData } from 'semantic-ui-react/dist/commonjs/elements/Input/Input';
import { FiTrash2, FiMessageSquare } from 'react-icons/fi';

interface ConversationFlowMessageProps {
  index: number;
  mode: 'update' | 'create';
}

const ConversationFlowMessage: React.FC<ConversationFlowMessageProps> = ({ index, mode }) => {
  // Mengambil data dari store menggunakan useSelector
  const modalAdd = useSelector((state: TMainReduxStates) => state.modalAddConversationFlow);
  const modalUpdate = useSelector((state: TMainReduxStates) => state.modalUpdateConversationFlow);

  // Helper untuk mengambil data pesan sesuai mode
  const v = () => {
    return mode === 'create' ? modalAdd.messages[index] : modalUpdate.messages[index];
  };

  // Fungsi untuk menghapus baris pesan
  const deleteRow = () => {
    if (mode === 'create') {
      mainStore.dispatch(addConversationFlowSlice.actions.deleteRow({ index }));
    } else {
      mainStore.dispatch(updateConversationFlowSlice.actions.deleteRow({ index }));
    }
  };

  // Fungsi untuk mengubah tipe pesan
  const onMessageTypeChange = (e: React.SyntheticEvent<HTMLElement, Event>, d: DropdownProps) => {
    let data: IConversationFlowMessage['type'] = (d.value as any) || null;
    if (mode === 'create') {
      mainStore.dispatch(
        addConversationFlowSlice.actions.changeMessageType({
          index,
          value: data,
        }),
      );
    } else {
      mainStore.dispatch(
        updateConversationFlowSlice.actions.changeMessageType({
          index,
          value: data,
        }),
      );
    }
  };

  // Fungsi untuk mengubah template internal
  const onIdInternalTemplateChange = (e: any, d: InputOnChangeData) => {
    const text = d.value;
    if (mode === 'create') {
      mainStore.dispatch(
        addConversationFlowSlice.actions.changeInternalTemplateText({
          index,
          text,
        }),
      );
    } else {
      mainStore.dispatch(
        updateConversationFlowSlice.actions.changeInternalTemplateText({
          index,
          text,
        }),
      );
    }
  };

  // Fungsi untuk mengubah konten pesan teks
  const onMessageTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const text = e.target.value;
    if (mode === 'create') {
      mainStore.dispatch(
        addConversationFlowSlice.actions.changeMessageTextType({
          text,
          index,
        }),
      );
    } else {
      mainStore.dispatch(
        updateConversationFlowSlice.actions.changeMessageTextType({
          text,
          index,
        }),
      );
    }
  };

  // Fungsi untuk menentukan nilai text body berdasarkan tipe pesan
  const textBody = () => {
    if (v().type === 'text') {
      return v().text.body;
    } else if (v().type === 'button') {
      return v().button.text;
    } else if (v().type === 'priceList') {
      return v().priceList.text;
    } else if (v().type === 'mediaTrimitraArt') {
      return v().mediaTrimitraArt.text;
    }
  };

  // Mengambil key dari tombol
  const valueButtonKey = (indexButton: number) => {
    return v().button.buttons[indexButton].id;
  };
  ConversationFlowMessage;
  // Mengubah key pada tombol
  const onChangeButtonKey = (key: string, indexButton: number) => {
    if (mode === 'create') {
      mainStore.dispatch(
        addConversationFlowSlice.actions.onChangeButtonInteractiveKey({
          key,
          index,
          indexButton,
        }),
      );
    } else {
      mainStore.dispatch(
        updateConversationFlowSlice.actions.onChangeButtonInteractiveKey({
          key,
          index,
          indexButton,
        }),
      );
    }
  };

  // Mengambil text pada tombol
  const valueButtonText = (indexButton: number) => {
    return v().button.buttons[indexButton].text;
  };

  // Mengubah text pada tombol
  const onChangeButtonText = (text: string, indexButton: number) => {
    if (mode === 'create') {
      mainStore.dispatch(
        addConversationFlowSlice.actions.onChangeButtonInteractiveText({
          text,
          index,
          indexButton,
        }),
      );
    } else {
      mainStore.dispatch(
        updateConversationFlowSlice.actions.onChangeButtonInteractiveText({
          text,
          index,
          indexButton,
        }),
      );
    }
  };

  // Fungsi untuk mengubah kode media
  const onMediaCodeChange = (e: any, d: InputOnChangeData) => {
    const text = d.value;
    if (mode === 'create') {
      mainStore.dispatch(
        addConversationFlowSlice.actions.changeMediatrimitraArt({
          index,
          artCode: text,
          text: v().mediaTrimitraArt.text,
        }),
      );
    } else {
      mainStore.dispatch(
        updateConversationFlowSlice.actions.changeMediatrimitraArt({
          index,
          artCode: text,
          text: v().mediaTrimitraArt.text,
        }),
      );
    }
  };
  return (
    <Card
      fluid
      className="!shadow-sm hover:!shadow-md transition-shadow duration-200"
    >
      <CardContent>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2 text-gray-700">
            <FiMessageSquare className="text-gray-400" />
            <span className="font-medium">Message {index + 1}</span>
          </div>
          <Button
            icon
            size="tiny"
            onClick={deleteRow}
            className="!bg-red-50 !text-red-600 hover:!bg-red-100"
          >
            <FiTrash2 />
          </Button>
        </div>

        <Form className="space-y-4">
          <Form.Field className="!mb-0">
            <label className="block text-sm font-medium text-gray-700 mb-1">Message Type</label>
            <Select
              options={[
                { text: 'Text', value: 'text' },
                { text: 'Button Reply', value: 'button' },
                { text: 'Pricelist', value: 'priceList' },
                { text: 'Internal Template', value: 'internalTemplate' },
                { text: 'Media Trimtra Art', value: 'mediaTrimitraArt' },
              ]}
              onChange={onMessageTypeChange}
              value={v().type as any}
              className="w-full"
            />
          </Form.Field>

          {v().type === 'internalTemplate' && (
            <Form.Field className="!mb-0">
              <label className="block text-sm font-medium text-gray-700 mb-1">Template ID</label>
              <Input
                onChange={onIdInternalTemplateChange}
                value={v().internalTemplate.templateId}
                placeholder="Enter template ID"
                className="w-full"
              />
            </Form.Field>
          )}

          {v().type === 'mediaTrimitraArt' && (
            <Form.Field className="!mb-0">
              <label className="block text-sm font-medium text-gray-700 mb-1">Media Code</label>
              <Input
                onChange={onMediaCodeChange}
                value={v().mediaTrimitraArt.artCode}
                placeholder="Enter media code"
                className="w-full"
              />
            </Form.Field>
          )}

          {['button', 'text', 'mediaTrimitraArt'].indexOf(v().type as any) > -1 && (
            <Form.Field className="!mb-0">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Message Content
              </label>
              <TextArea
                onChange={onMessageTextChange}
                value={textBody()}
                placeholder="Enter your message"
                className="w-full"
                rows={4}
              />
            </Form.Field>
          )}

          {v().type === 'button' && (
            <div className="space-y-4 pt-2">
              {range(3).map((value) => {
                const indexButton = value;
                return (
                  <div
                    key={value.toString()}
                    className="grid grid-cols-1 md:grid-cols-2 gap-4"
                  >
                    <Form.Field className="!mb-0">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Button {value + 1} Key
                      </label>
                      <Input
                        value={valueButtonKey(indexButton)}
                        onChange={(event, data) => {
                          onChangeButtonKey(data.value, indexButton);
                        }}
                        placeholder="Enter button key"
                        className="w-full"
                      />
                    </Form.Field>
                    <Form.Field className="!mb-0">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Button {value + 1} Text
                      </label>
                      <Input
                        value={valueButtonText(indexButton)}
                        onChange={(event, data) => {
                          onChangeButtonText(data.value, indexButton);
                        }}
                        placeholder="Enter button text"
                        className="w-full"
                      />
                    </Form.Field>
                  </div>
                );
              })}
            </div>
          )}
        </Form>
      </CardContent>
    </Card>
  );
};

export default ConversationFlowMessage;

import React from 'react';
import ConversationFlow from '../../entities/ConversationFlow';
import {
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  CardHeader,
  CardMeta,
  Dropdown,
  Table,
  TableBody,
  TableCell,
  TableRow,
} from 'semantic-ui-react';
import moment from 'moment';
import { mainStore } from '../../redux/reducers';
import { getConversationFlowToUpdate } from '../../redux/add-conversation-flow/updateConversationFlow.slice';
import confirmDialog from '../callableDialog/confirmDialog';
import { deleteDoc } from 'firebase/firestore';
import { getConversationFlowThunk } from '../../redux/add-conversation-flow/modalListConversationFlow.slice';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { connect } from 'react-redux';
import addConversationFlowSlice from '../../redux/add-conversation-flow/addConversationFlow.slice';
import { FiEdit2, FiTrash2, FiCopy } from 'react-icons/fi';
import { IoMdTime } from 'react-icons/io';
import { BsListTask } from 'react-icons/bs';

interface Props {
  c: ConversationFlow;
  project: TMainReduxStates['reducerProject'];
}

function ListConversationFlowItem(props: Props) {
  let { c } = props;

  const onDeleteClick = async () => {
    const confirm = await confirmDialog({
      cancelButton: 'Cancel',
      content: 'Are you sure you want to delete this conversation flow?',
      title: 'Delete Conversation Flow',
      okButton: 'Delete',
    });

    if (confirm) {
      await deleteDoc(c.ref);
      mainStore.dispatch(getConversationFlowThunk(props.project.project!.ref) as any);
    }
  };

  return (
    <Card
      fluid
      className="!shadow-md hover:!shadow-lg transition-shadow duration-200"
    >
      <CardContent className="!bg-gray-50 !border-b">
        <div className="flex justify-between items-center">
          <div>
            <CardHeader className="!text-lg !font-semibold !mb-1">{c.startAt}</CardHeader>
            <CardMeta className="!flex !items-center !gap-1 !text-gray-500">
              <IoMdTime className="text-gray-400" />
              {moment(c.createdAt.toDate()).format('DD MMM YYYY • HH:mm')}
            </CardMeta>
          </div>
          <div className="flex gap-2">
            <Button
              icon
              size="tiny"
              onClick={() => {
                mainStore.dispatch(
                  getConversationFlowToUpdate({
                    conversationFlowId: c.ref.id,
                    projectRef: props.project.project!.ref,
                  }) as any,
                );
              }}
              className="!bg-blue-50 !text-blue-600 hover:!bg-blue-100"
            >
              <FiEdit2 />
            </Button>
            {c.startAt === 'referralSourceId' && (
              <>
                <Button
                  icon
                  size="tiny"
                  onClick={() => {
                    mainStore.dispatch(
                      addConversationFlowSlice.actions.duplicate({
                        prefilled: {
                          startAt: props.c.startAt,
                          messages: props.c.messages,
                          referralSourceId: {
                            dealCode: props.c.referralSourceId.dealCode,
                          },
                        },
                      }),
                    );
                  }}
                  className="!bg-green-50 !text-green-600 hover:!bg-green-100"
                >
                  <FiCopy />
                </Button>
                <Button
                  icon
                  size="tiny"
                  onClick={onDeleteClick}
                  className="!bg-red-50 !text-red-600 hover:!bg-red-100"
                >
                  <FiTrash2 />
                </Button>
              </>
            )}
          </div>
        </div>
      </CardContent>
      <CardContent>
        <div className="text-xs text-gray-500 mb-3 font-mono">ID: {c.ref.id}</div>
        <Table
          basic="very"
          compact
          className="!text-sm"
        >
          <TableBody>
            <TableRow>
              <TableCell className="!font-medium !text-gray-600">
                <div className="flex items-center gap-2">
                  <BsListTask />
                  Total Flow
                </div>
              </TableCell>
              <TableCell>{c.messages.length}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell className="!font-medium !text-gray-600">Status</TableCell>
              <TableCell>
                <span className={c.active ? 'text-green-600' : 'text-red-600'}>
                  {c.active ? 'Active' : 'Inactive'}
                </span>
              </TableCell>
            </TableRow>
            {c.startAt === 'referralSourceId' && (
              <>
                <TableRow>
                  <TableCell className="!font-medium !text-gray-600">Ad Source Id</TableCell>
                  <TableCell>{c.referralSourceId.sourceId}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="!font-medium !text-gray-600">Deal Code</TableCell>
                  <TableCell>{c.referralSourceId.dealCode}</TableCell>
                </TableRow>
              </>
            )}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}

const stateToProps = (states: TMainReduxStates) => {
  return {
    project: states.reducerProject,
  };
};

export default connect(stateToProps)(ListConversationFlowItem);

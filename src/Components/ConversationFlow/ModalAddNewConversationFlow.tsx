import React, { Component } from 'react';
import { Button, Container, Form, Input, Modal, ModalActions, Select } from 'semantic-ui-react';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { connect } from 'react-redux';
import { mainStore } from '../../redux/reducers';
import addConversationFlowSlice from '../../redux/add-conversation-flow/addConversationFlow.slice';
import ConversationFlowMessage from './ConversationFlowMessage';
import { mainApiServices } from '../../services/MainApiServices';
import confirmDialog from '../callableDialog/confirmDialog';
import { AxiosError } from 'axios';
import { ThunkDispatch } from 'redux-thunk';
import errorDialog from '../callableDialog/errorDialog';
import successDialog from '../callableDialog/successDialog';
import { IoMdAdd } from 'react-icons/io';
import { getConversationFlowThunk } from '../../redux/add-conversation-flow/modalListConversationFlow.slice';
interface ModalAddNewConversationFlowProps {
  modal: TMainReduxStates['modalAddConversationFlow'];
  changeAdSourceId: (value: string) => void;
  project: TMainReduxStates['reducerProject'];
}

class ModalAddNewConversationFlow extends Component<ModalAddNewConversationFlowProps> {
  onClose = () => {
    mainStore.dispatch(addConversationFlowSlice.actions.close());
  };

  addRow = () => {
    mainStore.dispatch(addConversationFlowSlice.actions.addRow());
  };

  onSubmit = async () => {
    const { modal } = this.props;
    if (!modal.startAt) return;

    mainStore.dispatch(addConversationFlowSlice.actions.setSubmittingStatus(true));

    try {
      await mainApiServices.addConversationFlow({
        startAt: modal.startAt,
        messages: modal.messages,
        topicName: modal.topicName,
        referralSourceId: {
          dealCode: modal.referralSourceId.dealCode,
          sourceId: modal.referralSourceId.sourceId,
        },
        projectId: this.props.project.project!.ref.id,
      });

      await successDialog({
        cancelButton: false,
        title: 'Success',
        content: 'Conversation Flow has been added successfully',
      });

      mainStore.dispatch(addConversationFlowSlice.actions.close());

      mainStore.dispatch(getConversationFlowThunk(this.props.project.project!.ref) as any);
    } catch (e) {
      mainStore.dispatch(addConversationFlowSlice.actions.setSubmittingStatus(false));
      const error = e as AxiosError<any>;
      let messages: string | string[] = '';
      if (error.response?.data?.error.type === 'UNPROCESSABLE_ENTITY') {
        const errorResponseData = error.response?.data.error;
        messages = [];
        for (const key of Object.keys(errorResponseData.messages)) {
          messages.push(errorResponseData.messages[key].msg);
        }
      } else {
        messages = error.response?.data?.error?.messages;
      }
      await errorDialog({
        cancelButton: false,
        title: 'Error',
        content: messages || 'Failed to add Conversation Flow',
      });
    }
  };

  render() {
    return (
      <Modal
        open={this.props.modal.modalOpen}
        onClose={this.onClose}
      >
        <Modal.Header className="bg-gray-50 px-6 py-4 border-b">
          <div className="text-xl font-semibold text-gray-800">Add New Conversation Flow</div>
        </Modal.Header>
        <Modal.Content className="p-6">
          <Form className="space-y-6">
            <Form.Field className="!mb-0">
              <label className="block text-sm font-medium text-gray-700 mb-1">Trigger When</label>
              <Select
                options={[
                  {
                    text: 'Referral Source ID',
                    value: 'referralSourceId',
                  },
                ]}
                onChange={(event, data) => {
                  mainStore.dispatch(
                    addConversationFlowSlice.actions.changeRunAt(data.value as any),
                  );
                }}
                value={this.props.modal.startAt || ''}
                className="w-full"
              />
            </Form.Field>
            {this.props.modal.startAt === 'referralSourceId' && (
              <div className="space-y-4">
                <Form.Field className="!mb-0">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Ad Source ID
                  </label>
                  <Input
                    onChange={(event, data) => {
                      this.props.changeAdSourceId(data.value);
                    }}
                    value={this.props.modal.referralSourceId.sourceId}
                    placeholder="Enter Ad Source ID"
                    className="w-full"
                  />
                </Form.Field>
                <Form.Field className="!mb-0">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Deal Code</label>
                  <Input
                    onChange={(event, data) => {
                      mainStore.dispatch(
                        addConversationFlowSlice.actions.changeDealCodeReferralSource(data.value),
                      );
                    }}
                    value={this.props.modal.referralSourceId.dealCode}
                    placeholder="Enter Deal Code"
                    className="w-full"
                  />
                </Form.Field>
              </div>
            )}
          </Form>

          <div className="mt-8">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Flow Messages</h3>
              <Button
                primary
                size="small"
                onClick={this.addRow}
                className="!flex !items-center"
              >
                <IoMdAdd className="mr-2 -ml-1" />
                Add Row
              </Button>
            </div>
            <Container>
              {this.props.modal.messages.map((value, index) => {
                return (
                  <ConversationFlowMessage
                    mode={'create'}
                    index={index}
                    key={index}
                  />
                );
              })}
            </Container>
          </div>
        </Modal.Content>
        <Modal.Actions className="bg-gray-50 px-6 py-4 border-t">
          <Button onClick={this.onClose}>Cancel</Button>
          <Button
            primary
            onClick={this.onSubmit}
            loading={this.props.modal.modalSubmitting}
          >
            Submit
          </Button>
        </Modal.Actions>
      </Modal>
    );
  }
}

const mapStateToProps = (states: TMainReduxStates) => {
  return {
    modal: states.modalAddConversationFlow,
    project: states.reducerProject,
  };
};

const mapDispatchToProps = (dispatch: ThunkDispatch<any, any, any>) => {
  return {
    changeAdSourceId: (value: string) =>
      dispatch(addConversationFlowSlice.actions.changeReferralSourceId(value)),
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(ModalAddNewConversationFlow);

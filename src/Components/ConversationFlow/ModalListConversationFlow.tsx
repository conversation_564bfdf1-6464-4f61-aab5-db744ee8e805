import React, { Component } from 'react';
import { <PERSON><PERSON>, Modal } from 'semantic-ui-react';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { connect } from 'react-redux';
import { mainStore } from '../../redux/reducers';
import modalListConversationFlowSlice from '../../redux/add-conversation-flow/modalListConversationFlow.slice';
import addConversationFlowSlice from '../../redux/add-conversation-flow/addConversationFlow.slice';
import ListConversationFlowItem from './ListConversationFlowItem';
import { IoMdAdd } from 'react-icons/io';

interface Props {
  modal: TMainReduxStates['modalListConversationFlow'];
}

class ModalListConversationFlow extends Component<Props> {
  onClose = () => {
    mainStore.dispatch(modalListConversationFlowSlice.actions.close());
  };

  onAddClick = () => {
    mainStore.dispatch(addConversationFlowSlice.actions.open());
  };

  render() {
    return (
      <Modal
        open={this.props.modal.open}
        size="large"
      >
        <Modal.Header className="bg-gray-50 px-6 py-4 border-b">
          <div className="text-xl font-semibold text-gray-800">List Conversation Flow</div>
        </Modal.Header>
        <Modal.Content
          scrolling={true}
          className="p-6"
        >
          {this.props.modal.conversationFlows.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12 text-gray-500">
              <div className="text-lg mb-2">No conversation flows available</div>
              <Button
                primary
                onClick={this.onAddClick}
                className="mt-4"
              >
                <IoMdAdd className="mr-2 -ml-1" />
                Add New Flow
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {this.props.modal.conversationFlows.map((c) => (
                <ListConversationFlowItem
                  c={c}
                  key={c.ref.id}
                />
              ))}
            </div>
          )}
        </Modal.Content>
        <Modal.Actions className="bg-gray-50 px-6 py-4 border-t">
          <Button onClick={this.onClose}>Close</Button>
          {this.props.modal.conversationFlows.length > 0 && (
            <Button
              positive
              onClick={this.onAddClick}
              className={'!bg-blue-500'}
            >
              Add New
            </Button>
          )}
        </Modal.Actions>
      </Modal>
    );
  }
}

const mapStateToProps = (states: TMainReduxStates) => {
  return {
    modal: states.modalListConversationFlow,
  };
};

export default connect(mapStateToProps)(ModalListConversationFlow);

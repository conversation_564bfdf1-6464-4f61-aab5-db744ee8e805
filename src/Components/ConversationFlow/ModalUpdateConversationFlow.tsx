import { Component } from 'react';
import { Button, Checkbox, Container, Form, Input, Modal, Select } from 'semantic-ui-react';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { connect } from 'react-redux';
import { mainStore } from '../../redux/reducers';
import ConversationFlowMessage from './ConversationFlowMessage';
import updateConversationFlowSlice from '../../redux/add-conversation-flow/updateConversationFlow.slice';
import { mainApiServices } from '../../services/MainApiServices';
import { getConversationFlowThunk } from '../../redux/add-conversation-flow/modalListConversationFlow.slice';
import { AxiosError } from 'axios';
import errorDialog from '../callableDialog/errorDialog';
import successDialog from '../callableDialog/successDialog';
import { IoMdAdd } from 'react-icons/io';
import { FiCheck } from 'react-icons/fi';

interface ModalUpdateConversationFlowProps {
  modal: TMainReduxStates['modalUpdateConversationFlow'];
  project: TMainReduxStates['reducerProject'];
}

class ModalUpdateConversationFlow extends Component<ModalUpdateConversationFlowProps> {
  onClose = () => {
    mainStore.dispatch(updateConversationFlowSlice.actions.close());
  };

  addRow = () => {
    mainStore.dispatch(updateConversationFlowSlice.actions.addRow());
  };

  onSubmit = async () => {
    const { modal } = this.props;
    if (!modal.startAt) return;

    try {
      await mainApiServices.updateConversationFlow({
        pathConversationFlow: modal.ref?.path || '',
        messages: modal.messages,
        active: modal.active,
        referralSourceId: {
          sourceId: modal.referralSourceId.sourceId,
          dealCode: modal.referralSourceId.dealCode,
        },
        projectId: this.props.project.project!.ref.id,
      });

      await successDialog({
        cancelButton: false,
        title: 'Success',
        content: 'Conversation Flow has been updated successfully',
      });

      mainStore.dispatch(updateConversationFlowSlice.actions.close());

      mainStore.dispatch(getConversationFlowThunk(this.props.project.project!.ref) as any);
    } catch (e) {
      const error = e as AxiosError<any>;
      let errorMessage: string | string[] = '';
      if (error.response?.data?.error.type === 'UNPROCESSABLE_ENTITY') {
        const errorResponseData = error.response?.data.error;
        errorMessage = [];
        for (const key of Object.keys(errorResponseData.messages)) {
          errorMessage.push(errorResponseData.messages[key].msg);
        }
      } else {
        errorMessage = error.response?.data?.error?.messages;
      }

      await errorDialog({
        cancelButton: false,
        title: 'Error',
        content: errorMessage || 'Failed to update Conversation Flow',
      });
    }
  };

  render() {
    return (
      <Modal
        open={this.props.modal.modalOpen}
        onClose={this.onClose}
        size="large"
      >
        <Modal.Header className="bg-gray-50 px-6 py-4 border-b">
          <div className="text-xl font-semibold text-gray-800">Update Conversation Flow</div>
        </Modal.Header>
        <Modal.Content className="p-6">
          <Form className="space-y-6">
            <Form.Field className="!mb-0">
              <div className="flex items-center space-x-2">
                <Checkbox
                  checked={this.props.modal.active}
                  onChange={(event, data) => {
                    mainStore.dispatch(
                      updateConversationFlowSlice.actions.changeActiveCheckbox(
                        data.checked || false,
                      ),
                    );
                  }}
                  className="!mr-2"
                />
                <label className="text-sm font-medium text-gray-700">Active</label>
              </div>
            </Form.Field>

            <Form.Field className="!mb-0">
              <label className="block text-sm font-medium text-gray-700 mb-1">Trigger When</label>
              <Select
                readOnly
                disabled={true}
                options={[
                  {
                    text: 'Referral Source ID',
                    value: 'referralSourceId',
                  },
                ]}
                value={this.props.modal.startAt || ''}
                className="w-full"
              />
            </Form.Field>

            {this.props.modal.startAt === 'referralSourceId' && (
              <div className="space-y-4">
                <Form.Field className="!mb-0">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Ad Source ID
                  </label>
                  <Input
                    onChange={(event, data) => {
                      mainStore.dispatch(
                        updateConversationFlowSlice.actions.changeReferralSourceId(data.value),
                      );
                    }}
                    value={this.props.modal.referralSourceId.sourceId}
                    placeholder="Enter Ad Source ID"
                    className="w-full"
                  />
                </Form.Field>
                <Form.Field className="!mb-0">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Deal Code</label>
                  <Input
                    onChange={(event, data) => {
                      mainStore.dispatch(
                        updateConversationFlowSlice.actions.changeDealCodeReferralSource(
                          data.value,
                        ),
                      );
                    }}
                    value={this.props.modal.referralSourceId.dealCode}
                    placeholder="Enter Deal Code"
                    className="w-full"
                  />
                </Form.Field>
              </div>
            )}
          </Form>

          <div className="mt-8">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Flow Messages</h3>
              <Button
                primary
                size="small"
                onClick={this.addRow}
                className="!flex !items-center"
              >
                <IoMdAdd className="mr-2 -ml-1" />
                Add Row
              </Button>
            </div>
            <Container className="space-y-4">
              {this.props.modal.messages.map((value, index) => (
                <ConversationFlowMessage
                  mode="update"
                  index={index}
                  key={index}
                />
              ))}
            </Container>
          </div>
        </Modal.Content>
        <Modal.Actions className="bg-gray-50 px-6 py-4 border-t">
          <Button onClick={this.onClose}>Cancel</Button>
          <Button
            primary
            onClick={this.onSubmit}
            className="flex items-center"
          >
            Update
          </Button>
        </Modal.Actions>
      </Modal>
    );
  }
}

const mapStateToProps = (states: TMainReduxStates) => {
  return {
    modal: states.modalUpdateConversationFlow,
    project: states.reducerProject,
  };
};

export default connect(mapStateToProps)(ModalUpdateConversationFlow);

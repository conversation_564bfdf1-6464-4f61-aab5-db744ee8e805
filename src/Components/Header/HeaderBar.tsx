import React, { Component } from 'react';
import { Button, Container, Dropdown, Image, Menu } from 'semantic-ui-react';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { IHeaderBarProps, IHeaderBarStates } from './types/header-bar-types';
import { connect } from 'react-redux';
import { compose, Dispatch } from 'redux';
import ModalChangePassword from './ModalChangePassword';
import { onLogout } from '../../helpers/history';
import MediaQuery from 'react-responsive';
import { mainStore } from '../../redux/reducers';
import modalInitMessageSlice from '../../redux/modal-init-message/modalInitMessageSlice';
import modalSettingsTemplateSlice from '../../redux/modal-settings-template/modalSettingsTemplateSlice';
import modalListAdminSlice from '../../redux/modal-list-admin/modalListAdmin.slice';
import modalListConversationFlowSlice, {
  getConversationFlowThunk,
} from '../../redux/add-conversation-flow/modalListConversationFlow.slice';
import modalPaymentStatusSlice from '../../redux/modal-trimobi/modalPaymentStatus.slice';
import ModalProfile from './ModalProfile';
import modalProfileSlice from '../../redux/modal-profile/modalProfile.slice';

// Add react-icons imports
import { IoSendSharp } from 'react-icons/io5';
import {
  FaUserCircle,
  FaKey,
  FaPaintBrush,
  FaComments,
  FaUsers,
  FaCar,
  FaSignOutAlt,
} from 'react-icons/fa';

class HeaderBar extends Component<IHeaderBarProps, IHeaderBarStates> {
  constructor(props: IHeaderBarProps) {
    super(props);

    this.state = {
      modalChangePasswordOpen: false,
    };
  }

  onChangePasswordClick = () => {
    this.setState({
      modalChangePasswordOpen: true,
    });
  };

  onCancelChangePassword = () => {
    this.setState({
      modalChangePasswordOpen: false,
    });
  };

  onInitMessageClick = () => {
    mainStore.dispatch(
      modalInitMessageSlice.actions.open({
        open: true,
      }),
    );
  };

  onSettingTemplateClick = () => {
    mainStore.dispatch(modalSettingsTemplateSlice.actions.open());
  };

  onAddConversationClick = () => {
    mainStore.dispatch(modalListConversationFlowSlice.actions.open());
    mainStore.dispatch(getConversationFlowThunk(this.props.admin.admin!.doc_project) as any);
    mainStore.dispatch(getConversationFlowThunk(this.props.admin.admin!.doc_project) as /**/ any);
  };

  onListAdminClick = () => {
    mainStore.dispatch(modalListAdminSlice.actions.open());
  };

  onPaymentStatusClick = () => {
    mainStore.dispatch(modalPaymentStatusSlice.actions.setIsOpen(true));
  };

  onProfileClick = () => {
    mainStore.dispatch(modalProfileSlice.actions.setIsOpen(true));
  };

  render() {
    const { admin } = this.props;
    const userName = admin.admin?.name || 'User';

    return (
      <div className="bg-gray-900 shadow-sm">
        <Container fluid>
          <div className="flex items-center justify-between h-16 px-4">
            {/* Logo Section */}
            <div className="flex items-center space-x-3">
              <Image
                size="mini"
                src={process.env.PUBLIC_URL + '/favicon.ico'}
                className="w-8 h-8"
              />
              <div>
                <div className="text-sm font-semibold text-gray-100">Autotrimitra Ideal</div>
                <div className="text-xs text-gray-300">Ver {process.env.REACT_APP_WEB_VERSION}</div>
              </div>
            </div>

            {/* Right Section */}
            <div className="flex items-center space-x-4">
              {/* Init Message Button */}
              <button
                onClick={this.onInitMessageClick}
                className="p-2 text-gray-300 hover:text-blue-400 transition-colors"
                title="Init Message"
              >
                <IoSendSharp className="w-5 h-5" />
              </button>

              {/* User Dropdown */}
              <Dropdown
                trigger={
                  <div className="flex items-center space-x-2 cursor-pointer">
                    <div className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center">
                      <span className="text-sm font-medium text-gray-100">
                        {userName[0].toUpperCase()}
                      </span>
                    </div>
                    <span className="text-sm font-medium text-gray-100">{userName}</span>
                  </div>
                }
                icon={null}
                pointing="top right"
                className="!border-0"
              >
                <Dropdown.Menu className="!mt-2 !rounded-lg !shadow-lg !border-gray-700 !bg-gray-800">
                  <Dropdown.Item
                    onClick={this.onProfileClick}
                    className="!p-3 hover:!bg-gray-700"
                  >
                    <div className="flex items-center space-x-3">
                      <FaUserCircle className="w-5 h-5 text-gray-400" />
                      <span className="text-sm text-gray-100">Profile Saya</span>
                    </div>
                  </Dropdown.Item>

                  <Dropdown.Item
                    onClick={this.onChangePasswordClick}
                    className="!p-3 hover:!bg-gray-700"
                  >
                    <div className="flex items-center space-x-3">
                      <FaKey className="w-5 h-5 text-gray-400" />
                      <span className="text-sm text-gray-100">Ganti Password</span>
                    </div>
                  </Dropdown.Item>

                  <Dropdown.Item
                    onClick={this.onSettingTemplateClick}
                    className="!p-3 hover:!bg-gray-700"
                  >
                    <div className="flex items-center space-x-3">
                      <FaPaintBrush className="w-5 h-5 text-gray-400" />
                      <span className="text-sm text-gray-100">Atur Template</span>
                    </div>
                  </Dropdown.Item>

                  <Dropdown.Item
                    onClick={this.onAddConversationClick}
                    className="!p-3 hover:!bg-gray-700"
                  >
                    <div className="flex items-center space-x-3">
                      <FaComments className="w-5 h-5 text-gray-400" />
                      <span className="text-sm text-gray-100">Flow Percakapan</span>
                    </div>
                  </Dropdown.Item>

                  {admin.admin?.level === 'owner' && (
                    <Dropdown.Item
                      onClick={this.onListAdminClick}
                      className="!p-3 hover:!bg-gray-700"
                    >
                      <div className="flex items-center space-x-3">
                        <FaUsers className="w-5 h-5 text-gray-400" />
                        <span className="text-sm text-gray-100">List Admin</span>
                      </div>
                    </Dropdown.Item>
                  )}

                  <Dropdown.Item
                    onClick={this.onPaymentStatusClick}
                    className="!p-3 hover:!bg-gray-700"
                  >
                    <div className="flex items-center space-x-3">
                      <FaCar className="w-5 h-5 text-gray-400" />
                      <span className="text-sm text-gray-100">Trimobi</span>
                    </div>
                  </Dropdown.Item>

                  <Dropdown.Item
                    onClick={onLogout}
                    className="!p-3 hover:!bg-gray-700"
                  >
                    <div className="flex items-center space-x-3">
                      <FaSignOutAlt className="w-5 h-5 text-gray-400" />
                      <span className="text-sm text-gray-100">Keluar</span>
                    </div>
                  </Dropdown.Item>
                </Dropdown.Menu>
              </Dropdown>
            </div>
          </div>
        </Container>

        <ModalChangePassword
          onCancel={this.onCancelChangePassword}
          open={this.state.modalChangePasswordOpen}
        />
        <ModalProfile open={this.props.modalProfile.isOpen} />
      </div>
    );
  }
}

const mapStateToProps = (states: TMainReduxStates) => ({
  admin: states.reducerAdmin,
  modalProfile: states.modalProfile,
});

const mapDispatchToProps = (dispatch: Dispatch) => ({});

export default compose<React.ComponentType>(connect(mapStateToProps, mapDispatchToProps))(
  HeaderBar,
);

import React, { Component } from 'react';
import { Button, Form, Message, Modal } from 'semantic-ui-react';
import {
  IModalChangePasswordProps,
  IModalChangePasswordStates,
} from './types/modal-change-password-types';
import authService from '../../services/firebase/AuthServices';
import { withRouter } from 'react-router-dom';
import { compose, Dispatch } from 'redux';
import { connect } from 'react-redux';
import { onLogout } from '../../helpers/history';

class ModalChangePassword extends Component<IModalChangePasswordProps, IModalChangePasswordStates> {
  constructor(props: IModalChangePasswordProps) {
    super(props);
    this.state = {
      newPassword: null,
      oldPassword: null,
      updating: false,
      errorMessage: false,
    };
  }

  onFieldChange = <T extends keyof IModalChangePasswordStates>(target: T, value: string) => {
    let currentState = { ...this.state } as IModalChangePasswordStates;

    switch (target) {
      case 'oldPassword':
        currentState.oldPassword = value;
        break;
      case 'newPassword':
        currentState.newPassword = value;
        break;
    }

    this.setState({
      ...currentState,
    });
  };

  onSubmit = async () => {
    if (!this.state.newPassword) return false;

    this.setState({
      updating: true,
    });

    const update = await authService.changePassword(
      this.state.oldPassword!,
      this.state.newPassword,
    );

    if (update.success) {
      onLogout().then();
    } else {
      this.setState({
        updating: false,
        errorMessage: update.message ?? false,
      });
    }
  };

  render() {
    return (
      <Modal
        open={this.props.open}
        size={'tiny'}
      >
        <Modal.Header>Ganti Password</Modal.Header>
        <Modal.Content>
          <Form>
            <Form.Field>
              <label>Masukan Password Sekarang</label>
              <input
                type={'password'}
                onChange={(event) => this.onFieldChange('oldPassword', event.target.value)}
                value={this.state.oldPassword ?? ''}
                autoComplete={'new-password'}
              />
            </Form.Field>
            <Form.Field>
              <label>Masukan Password Baru</label>
              <input
                type={'password'}
                onChange={(event) => this.onFieldChange('newPassword', event.target.value)}
                value={this.state.newPassword ?? ''}
              />
            </Form.Field>
          </Form>
          {this.state.errorMessage && (
            <Message
              error={true}
              size={'tiny'}
            >
              <p>{this.state.errorMessage}</p>
            </Message>
          )}
          <Message
            info={true}
            size={'tiny'}
          >
            <p>
              Jika berhasil mengganti password anda akan diarahkan ke halaman login untuk melakukan
              login sekali lagi.
            </p>
          </Message>
        </Modal.Content>
        <Modal.Actions>
          <Button
            disabled={this.state.updating}
            onClick={this.props.onCancel}
          >
            Batal
          </Button>
          <Button
            loading={this.state.updating}
            primary={true}
            onClick={this.onSubmit}
          >
            Submit
          </Button>
        </Modal.Actions>
      </Modal>
    );
  }
}

const mapStateToProps = () => ({});

const mapDispatchToProps = (dispatch: Dispatch) => ({});

export default compose<React.ComponentType<Pick<IModalChangePasswordProps, 'open' | 'onCancel'>>>(
  withRouter,
  connect(mapStateToProps, mapDispatchToProps),
)(ModalChangePassword);

import React from 'react';
import { Mo<PERSON>, Grid, Icon, Segment, Header, Divider } from 'semantic-ui-react';
import { useDispatch, useSelector } from 'react-redux';
import { TMainReduxStates } from '../../redux/types/redux-types';
import moment from 'moment';
import modalProfileSlice from '../../redux/modal-profile/modalProfile.slice';

const ModalProfile: React.FC<{ open: boolean }> = ({ open }) => {
  const dispatch = useDispatch();
  const admin = useSelector((state: TMainReduxStates) => state.reducerAdmin.admin);
  const actions = modalProfileSlice.actions;

  const onClose = () => {
    dispatch(actions.setIsOpen(false));
  };

  if (!admin) return null;

  return (
    <Modal
      open={open}
      onClose={onClose}
      size="small"
    >
      <Modal.Header className="flex items-center bg-blue-50">
        <Icon
          name="user circle"
          size="large"
          className="mr-2 text-blue-600"
        />
        <span className="text-blue-800 font-semibold">Profile Saya</span>
      </Modal.Header>
      <Modal.Content>
        <Grid>
          <Grid.Row>
            <Grid.Column width={16}>
              {/* Main Info */}
              <Segment raised>
                <Header
                  as="h4"
                  className="text-blue-700 mb-4"
                >
                  <Icon name="id badge" />
                  <Header.Content>
                    Informasi Utama
                    <Header.Subheader className="mt-1 text-gray-500">
                      Detail informasi akun Anda
                    </Header.Subheader>
                  </Header.Content>
                </Header>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <div className="text-gray-500 text-sm mb-1">Nama</div>
                    <div className="text-lg font-medium">{admin.name}</div>
                  </div>
                  <div>
                    <div className="text-gray-500 text-sm mb-1">Email</div>
                    <div className="text-lg">{admin.email}</div>
                  </div>
                </div>
              </Segment>

              {/* Role Info */}
              <Segment raised>
                <Header
                  as="h4"
                  className="text-blue-700 mb-4"
                >
                  <Icon name="shield" />
                  <Header.Content>
                    Peran & Akses
                    <Header.Subheader className="mt-1 text-gray-500">
                      Level akses dan departemen
                    </Header.Subheader>
                  </Header.Content>
                </Header>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <div className="text-gray-500 text-sm mb-1">Level</div>
                    <div className="text-lg font-medium capitalize">{admin.level}</div>
                  </div>
                  {admin.department && (
                    <div>
                      <div className="text-gray-500 text-sm mb-1">Departemen</div>
                      <div className="text-lg">{admin.department.name}</div>
                    </div>
                  )}
                  {admin.project && (
                    <div className="col-span-2">
                      <div className="text-gray-500 text-sm mb-1">Project</div>
                      <div className="text-lg">{admin.project.name}</div>
                    </div>
                  )}
                </div>
              </Segment>

              {/* Amarta VIP Info */}
              {admin.amartaVip && (
                <Segment
                  raised
                  color="blue"
                >
                  <Header
                    as="h4"
                    className="text-blue-700 mb-4"
                  >
                    <Icon name="star" />
                    <Header.Content>
                      Amarta VIP
                      <Header.Subheader className="mt-1 text-gray-500">
                        Informasi mediator Amarta
                      </Header.Subheader>
                    </Header.Content>
                  </Header>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <div className="text-gray-500 text-sm mb-1">Nama Mediator</div>
                      <div className="text-lg font-medium">{admin.amartaVip.mediatorName}</div>
                    </div>
                    <div>
                      <div className="text-gray-500 text-sm mb-1">Kode Mediator</div>
                      <div className="text-lg">{admin.amartaVip.mediatorCode}</div>
                    </div>
                  </div>
                </Segment>
              )}

              {/* Additional Info */}
              <Segment raised>
                <Header
                  as="h4"
                  className="text-blue-700 mb-4"
                >
                  <Icon name="info circle" />
                  <Header.Content>
                    Informasi Tambahan
                    <Header.Subheader className="mt-1 text-gray-500">
                      Detail lainnya
                    </Header.Subheader>
                  </Header.Content>
                </Header>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <div className="text-gray-500 text-sm mb-1">Terdaftar Sejak</div>
                    <div className="text-lg">
                      {moment(admin.created_time.toDate()).format('DD MMMM YYYY')}
                    </div>
                  </div>
                  <div>
                    <div className="text-gray-500 text-sm mb-1">Status</div>
                    <div className="text-lg">
                      {admin.active ? (
                        <span className="text-green-600 font-medium">
                          <Icon name="check circle" /> Aktif
                        </span>
                      ) : (
                        <span className="text-red-600 font-medium">
                          <Icon name="times circle" /> Tidak Aktif
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </Segment>
            </Grid.Column>
          </Grid.Row>
        </Grid>
      </Modal.Content>
      <Modal.Actions className="bg-gray-50">
        <button
          onClick={onClose}
          className="ui button bg-blue-500 text-white hover:bg-blue-600 transition-colors px-4 py-2 rounded-lg"
        >
          <Icon name="times" /> Tutup
        </button>
      </Modal.Actions>
    </Modal>
  );
};

export default ModalProfile;

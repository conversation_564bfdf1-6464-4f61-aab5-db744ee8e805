import { TMainReduxStates } from '../../../redux/types/redux-types';
import { IAdminStates } from '../../../redux/admin/admin.slice';
import { IModalProfileState } from '../../../redux/modal-profile/modalProfile.slice';

export interface IHeaderBarReduxStateProps {
  admin: TMainReduxStates['reducerAdmin'];
}

export interface IHeaderBarReduxDispatchProps {}

export interface IHeaderBarProps extends IHeaderBarReduxStateProps, IHeaderBarReduxDispatchProps {
  admin: IAdminStates;
  modalProfile: IModalProfileState;
}

export interface IHeaderBarStates {
  modalChangePasswordOpen: boolean;
}

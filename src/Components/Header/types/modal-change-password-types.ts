import { RouteComponentProps } from 'react-router-dom';

export interface IModalChangePasswordFields {
  oldPassword: string | null;
  newPassword: string | null;
}

export interface IModalChangePasswordStates extends IModalChangePasswordFields {
  updating: boolean;
  errorMessage: false | string;
}

export interface IModalChangePasswordReduxDispatch {}

export interface IModalChangePasswordProps
  extends IModalChangePasswordReduxDispatch,
    RouteComponentProps {
  open: boolean;
  onCancel: () => void;
}

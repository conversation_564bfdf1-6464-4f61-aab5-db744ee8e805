import React, { Component } from 'react';
import { Button, Form, Message, Select } from 'semantic-ui-react';
import { EBusinessDocument } from './types/business-document-types';
import BusinessDocumentHelper from '../../helpers/businessDocumentHelper';
import { DropdownProps } from 'semantic-ui-react/dist/commonjs/modules/Dropdown/Dropdown';
import { SemanticDatepickerProps } from 'react-semantic-ui-datepickers/dist/types';
import { ReactCropperElement } from 'react-cropper';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { connect } from 'react-redux';
import DatePicker from 'react-semantic-ui-datepickers';
import 'react-semantic-ui-datepickers/dist/react-semantic-ui-datepickers.css';
import { mainApiServices } from '../../services/MainApiServices';
import moment from 'moment';
import { AxiosError } from 'axios';
import ClientEntity from '../../entities/ClientEntity';
import { profileServices } from '../../services/profileServices';
import { getDoc } from 'firebase/firestore';

export interface IBusinessDocumentFields {
  documentTypes: EBusinessDocument | null;
  documentNumber: string;
  expiredDate: Date | null;
  image: Blob | null;
}

export interface BusinessDocumentCaptureStates extends IBusinessDocumentFields {
  creating: boolean;
  loading: boolean;

  errorMessage: string | null;
  successMessage: string | null;

  currentImage: string | null;

  allowed: boolean;
}

export interface BusinessDocumentCaptureProps {
  cropCanvas: React.RefObject<ReactCropperElement>;
  conversation: TMainReduxStates['reducerConversation'];
}

class BusinessDocumentCapture extends Component<
  BusinessDocumentCaptureProps,
  BusinessDocumentCaptureStates
> {
  constructor(props: BusinessDocumentCaptureProps) {
    super(props);

    this.state = {
      currentImage: null,

      allowed: false,

      documentTypes: null,
      documentNumber: '',
      expiredDate: null,
      image: null,

      creating: false,
      loading: false,
      successMessage: null,
      errorMessage: null,
    };
  }

  private businessDocumentTypes = {
    options: Object.keys(EBusinessDocument).map((value) => {
      return {
        value: value,
        text: BusinessDocumentHelper.toIndonesianFormatter(value as keyof EBusinessDocument),
      };
    }),

    onChange: (event: React.SyntheticEvent<HTMLElement>, data: DropdownProps) => {
      this.setState({
        documentTypes: data.value as EBusinessDocument,
      });
    },
  };

  private documentNumber = {
    onChange: (e: React.ChangeEvent<HTMLInputElement>) =>
      this.setState({
        documentNumber: e.target.value,
      }),
  };

  private expiredDate = {
    onChange: (event: React.SyntheticEvent | undefined, data: SemanticDatepickerProps) => {
      this.setState({
        expiredDate: (data.value as Date | null) ?? null,
      });
    },
  };

  onSubmit = async () => {
    if (this.state.creating) return;

    await this.setState({
      creating: true,
      errorMessage: null,
      successMessage: null,
    });

    let currentState: BusinessDocumentCaptureStates = { ...this.state };

    const blob = await new Promise<Blob | null>((resolve) => {
      if (this.props.cropCanvas.current) {
        this.props.cropCanvas.current?.cropper
          .crop()
          .getCroppedCanvas()
          .toBlob((blob) => {
            if (blob) resolve(blob);
          });
      } else {
        resolve(null);
      }
    });

    try {
      await mainApiServices.setBusinessDocument(
        {
          image: blob,
          documentNumber: this.state.documentNumber,
          documentTypes: this.state.documentTypes,
          expiredDate: this.state.expiredDate,
          imageUrl: this.state.currentImage ?? undefined,
        },
        this.props.conversation.chatRoom!.clients[0],
      );
      currentState.successMessage =
        'Berhasil memperbarui Dokumen Bisnis ' + moment().format('LLLL');
    } catch (e: any) {
      const error: AxiosError = e as any;
      currentState.errorMessage = error.message;
    }

    currentState.creating = false;
    await this.setState({
      ...currentState,
    });
  };

  load = async () => {
    await this.setState({
      loading: true,
    });

    let currentState: BusinessDocumentCaptureStates = { ...this.state };

    const client = this.props.conversation.chatRoom?.clients[0];

    if (!client) throw new Error('Client Ref is Empty');
    const getClient = await getDoc(client.withConverter(ClientEntity.converter));

    if (getClient.exists()) {
      const client = getClient!.data()!;

      if (client.details?.owner_phone_number) {
        const getProfile = await profileServices.getProfile(client.details.owner_phone_number);

        if (getProfile?.length === 1) {
          currentState.allowed = true;
          if (getProfile[0].BusinessDocument) {
            const businessDocument = getProfile[0].BusinessDocument;
            currentState = {
              ...currentState,
              currentImage: businessDocument?.BusinessDocumentImage ?? null,

              documentTypes: businessDocument?.DocumentTypes,
              documentNumber: businessDocument?.DocumentNumber,
              expiredDate: businessDocument?.ExpiredDate
                ? new Date(businessDocument?.ExpiredDate)
                : null,
            };
          }
        }
      }
    }

    await this.setState({
      ...currentState,
      loading: false,
    });
  };

  componentDidMount() {
    this.load();
  }

  render() {
    if (this.state.loading) return <div>Memuat...</div>;
    else if (!this.state.allowed)
      return <Message negative={true}>Isi data KTP Pemilik terlebih dahulu.</Message>;
    return (
      <React.Fragment>
        <Form>
          {this.state.currentImage && (
            <Form.Field>
              <a
                href={this.state.currentImage}
                style={{ fontWeight: 'bolder' }}
                target={'_blank'}
                rel="noreferrer"
              >
                Lihat Foto Sekarang
              </a>
            </Form.Field>
          )}
          <Form.Field>
            <label>Jenis Dokumen</label>
            <Select
              options={this.businessDocumentTypes.options}
              value={this.state.documentTypes ?? undefined}
              onChange={this.businessDocumentTypes.onChange}
            />
          </Form.Field>
          <Form.Field>
            <label>Nomor Dokumen</label>
            <input
              value={this.state.documentNumber}
              onChange={this.documentNumber.onChange}
            />
          </Form.Field>
          <Form.Field>
            <label>Tanggal Kadaluarsa</label>
            <DatePicker
              placeholder={'Tanggal Kadaluarsa'}
              onChange={this.expiredDate.onChange}
              value={this.state.expiredDate}
            />
          </Form.Field>
          <Button
            loading={this.state.creating}
            content="Simpan Dokumen Bisnis"
            labelPosition="right"
            icon="checkmark"
            onClick={this.onSubmit}
            positive
          />
        </Form>
        {this.state.errorMessage && <Message negative={true}>{this.state.errorMessage}</Message>}
        {this.state.successMessage && (
          <Message positive={true}>{this.state.successMessage}</Message>
        )}
      </React.Fragment>
    );
  }
}

const mapStateToProps = (states: TMainReduxStates) => ({
  conversation: states.reducerConversation,
});

export default connect(mapStateToProps)(BusinessDocumentCapture);

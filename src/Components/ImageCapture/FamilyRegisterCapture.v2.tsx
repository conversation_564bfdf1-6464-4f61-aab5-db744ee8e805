import React, { Component } from 'react';
import { Button, Form, Message } from 'semantic-ui-react';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { connect } from 'react-redux';
import { ReactCropperElement } from 'react-cropper';
import { mainApiServices } from '../../services/MainApiServices';
import { AxiosError } from 'axios';
import moment from 'moment';
import ClientEntity from '../../entities/ClientEntity';
import { getDoc } from 'firebase/firestore';

export interface IFamilyRegisterCaptureFields {
  familyRegisterNumber: string;
  image: Blob | null;
  imageUrl: string;
}

export interface IFamilyRegisterCaptureStates extends IFamilyRegisterCaptureFields {
  loading: boolean;
  creating: boolean;

  errorMessage: string | null;
  successMessage: string | null;

  currentImage: null | string;
}

export interface IFamilyRegisterCaptureProps {
  cropCanvas: React.RefObject<ReactCropperElement>;
  conversation: TMainReduxStates['reducerConversation'];
}

class FamilyRegisterCaptureV2 extends Component<
  IFamilyRegisterCaptureProps,
  IFamilyRegisterCaptureStates
> {
  constructor(props: IFamilyRegisterCaptureProps) {
    super(props);

    this.state = {
      imageUrl: '',
      image: null,
      familyRegisterNumber: '',

      currentImage: null,

      successMessage: null,
      errorMessage: null,
      creating: false,
      loading: false,
    };
  }

  private familyRegisterNumber = {
    onChange: (e: React.ChangeEvent<HTMLInputElement>) =>
      this.setState({
        familyRegisterNumber: e.target.value,
      }),
  };

  onSubmit = async () => {
    if (this.state.creating) return;
    this.setState(
      {
        creating: true,
        errorMessage: null,
        successMessage: null,
      },
      async () => {
        let currentState: IFamilyRegisterCaptureStates = { ...this.state };

        const blob = await new Promise<Blob | null>((resolve) => {
          if (this.props.cropCanvas.current) {
            this.props.cropCanvas.current?.cropper
              .crop()
              .getCroppedCanvas()
              .toBlob((blob) => {
                if (blob) resolve(blob);
              });
          } else {
            resolve(null);
          }
        });

        try {
          await mainApiServices.setFamilyRegisterV2(
            {
              image: blob || null,
              familyRegisterNumber: this.state.familyRegisterNumber,
            },
            this.props.conversation.chatRoom!.clients[0],
          );
          currentState.successMessage = 'Berhasil memperbarui KK ' + moment().format('LLLL');
        } catch (e: any) {
          const error: AxiosError<any> = e as any;
          currentState.errorMessage = 'Gagal memperbarui KK: ';
          if (error.response?.data?.error.type === 'UNPROCESSABLE_ENTITY') {
            const messages = error.response?.data.error.messages;
            const msgS: any[] = Object.values(messages);
            for (const msg of msgS) {
              currentState.errorMessage += ` ${msg.msg},`;
            }
          }
        }

        currentState.creating = false;
        this.setState({
          ...currentState,
        });
      },
    );
  };

  load = async () => {
    this.setState(
      {
        loading: true,
      },
      async () => {
        let currentState: IFamilyRegisterCaptureStates = { ...this.state };

        const client = this.props.conversation.chatRoom?.clients[0];

        if (!client) throw new Error('Client ref is empty');

        const getClient = await getDoc(client.withConverter(ClientEntity.converter));
        const dataClient = getClient.data()!;

        const dataFamilyRegister = dataClient.details.familyRegister;
        if (dataFamilyRegister) {
          currentState.currentImage = dataFamilyRegister.familyRegisterImage;
          currentState.familyRegisterNumber = dataFamilyRegister.familyRegisterNumber;
        } else {
          currentState.currentImage = '';
          currentState.familyRegisterNumber = '';
        }

        this.setState({
          ...currentState,
          loading: false,
        });
      },
    );
  };

  componentDidMount() {
    this.load();
  }

  render() {
    if (this.state.loading) return <div>Memuat...</div>;
    return (
      <React.Fragment>
        <Form className={'mb-2'}>
          {this.state.currentImage && (
            <Form.Field>
              <a
                href={this.state.currentImage}
                style={{ fontWeight: 'bolder', color: 'blue' }}
                target={'_blank'}
                rel="noreferrer"
              >
                Lihat Foto Tersimpan
              </a>
            </Form.Field>
          )}
          <Form.Field>
            <input
              placeholder={'Nomor KK'}
              value={this.state.familyRegisterNumber}
              onChange={this.familyRegisterNumber.onChange}
            />
          </Form.Field>
        </Form>

        <Button
          loading={this.state.creating}
          content="Simpan KK"
          labelPosition="right"
          icon="checkmark"
          positive
          onClick={this.onSubmit}
        />

        {this.state.errorMessage && (
          <Message negative={true}>
            <div>{this.state.errorMessage}</div>
          </Message>
        )}
        {this.state.successMessage && (
          <Message positive={true}>{this.state.successMessage}</Message>
        )}
      </React.Fragment>
    );
  }
}

const mapStateToProps = (states: TMainReduxStates) => ({
  conversation: states.reducerConversation,
});

export default connect(mapStateToProps)(FamilyRegisterCaptureV2);

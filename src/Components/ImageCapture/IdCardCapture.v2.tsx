import React, { Component, createRef } from 'react';
import { <PERSON><PERSON>, Divider, Form, Message, Select } from 'semantic-ui-react';
import DatePicker from 'react-semantic-ui-datepickers';
import 'react-semantic-ui-datepickers/dist/react-semantic-ui-datepickers.css';
import { EMaritalStatus } from './types/marital-status-types';
import SelectRegion, { CommonRegionProps } from '../SelectRegion/SelectRegion';
import { DropdownProps } from 'semantic-ui-react/dist/commonjs/modules/Dropdown/Dropdown';
import { SemanticDatepickerProps } from 'react-semantic-ui-datepickers/dist/types';
import { ReactCropperElement } from 'react-cropper';
import { mainApiServices } from '../../services/MainApiServices';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { AxiosError } from 'axios';
import moment from 'moment';
import { connect } from 'react-redux';
import ClientEntity from '../../entities/ClientEntity';
import MaritalStatusHelper from '../../helpers/maritalStatusHelper';

import occupation from '../../config/finalOccupation.json';
import { getDoc } from 'firebase/firestore';
import { EFamilyEducation } from '../../entities/types/family-register-entity-types';
import EducationHelper from '../../helpers/educationHelper';
import ocrServices from '../../services/ocr/ocrServices';
import { mainStore } from '../../redux/reducers';
import { fetchCustomerThunk } from '../../redux/customerInfo/customerInfoSlice';
import { IdCard } from '../../entities/types/client-entity-types';
import occupationHelper from '../../helpers/occupationHelper';

interface IdCardCaptureFields {
  idCardNumber: string;
  fullName: string;
  birthMother: string;
  placeOfBirth: string;
  dateOfBirth: Date | null;
  occupation: string;
  occupationCode: string;

  maritalStatus: string;
  maritalStatusCode: EMaritalStatus | null;

  lastEducation: string;
  lastEducationCode: EFamilyEducation | null;

  fullAddress: string;
  zipCode: string;
  province: CommonRegionProps | null;
  city: CommonRegionProps | null;
  district: CommonRegionProps | null;
  subDistrict: CommonRegionProps | null;
  neighbourhood: string;
  hamlet: string;

  domicileFullAddress: string;
  domicileZipCode: string;
  domicileProvince: CommonRegionProps | null;
  domicileCity: CommonRegionProps | null;
  domicileDistrict: CommonRegionProps | null;
  domicileSubDistrict: CommonRegionProps | null;
  domicileNeighbourhood: string;
  domicileHamlet: string;
}

interface IdCardCaptureStates extends IdCardCaptureFields {
  fetchingOcr: boolean;
  creating: boolean;
  loading: boolean;

  errorMessage: string | null;
  successMessage: string | null;

  currentImage: string | null;
}

interface IdCardCaptureProps {
  modal: TMainReduxStates['modalSetImageAs'];
  cropCanvas: React.RefObject<ReactCropperElement>;
  conversation: TMainReduxStates['reducerConversation'];
}

class IdCardCaptureV2 extends Component<IdCardCaptureProps, IdCardCaptureStates> {
  private domRegionComRef = createRef<SelectRegion>();

  constructor(props: IdCardCaptureProps) {
    super(props);

    this.state = {
      fetchingOcr: false,

      loading: false,
      errorMessage: null,
      successMessage: null,

      idCardNumber: '',
      fullName: '',
      birthMother: '',
      placeOfBirth: '',
      dateOfBirth: null,
      occupation: '',
      occupationCode: '',
      maritalStatus: '',
      maritalStatusCode: null,
      lastEducation: '',
      lastEducationCode: null,

      fullAddress: '',
      province: null,
      city: null,
      district: null,
      subDistrict: null,
      zipCode: '',
      neighbourhood: '',
      hamlet: '',

      domicileFullAddress: '',
      domicileProvince: null,
      domicileCity: null,
      domicileDistrict: null,
      domicileSubDistrict: null,
      domicileZipCode: '',
      domicileNeighbourhood: '',
      domicileHamlet: '',

      currentImage: null,

      creating: false,
    };
  }

  private idCardNumber = {
    onChange: (e: React.ChangeEvent<HTMLInputElement>) =>
      this.setState({
        idCardNumber: e.target.value,
      }),
  };

  private birthDate = {
    onChange: (event: React.SyntheticEvent | undefined, data: SemanticDatepickerProps) => {
      this.setState({
        dateOfBirth: (data.value as Date | null) ?? null,
      });
    },
  };

  private fullName = {
    onChange: (e: React.ChangeEvent<HTMLInputElement>) =>
      this.setState({
        fullName: e.target.value,
      }),
  };

  private birthMother = {
    onChange: (e: React.ChangeEvent<HTMLInputElement>) =>
      this.setState({
        birthMother: e.target.value,
      }),
  };

  private placeOfBirth = {
    onChange: (e: React.ChangeEvent<HTMLInputElement>) =>
      this.setState({
        placeOfBirth: e.target.value,
      }),
  };

  private occupation = {
    onChange: (event: React.SyntheticEvent<HTMLElement>, data: DropdownProps) => {
      if (!data.value) return;
      const occupation = occupationHelper.toIndonesianFormatter(data.value as string);
      if (!occupation) return;
      this.setState({
        occupationCode: occupation.code,
        occupation: occupation.name,
      });
    },
  };

  private maritalStatus = {
    onChange: (event: React.SyntheticEvent<HTMLElement>, data: DropdownProps) => {
      if (!data.value) return;

      const maritalStatus = MaritalStatusHelper.toIndonesianFormatter(data.value as EMaritalStatus);

      if (!maritalStatus) return;

      this.setState({
        maritalStatus: maritalStatus,
        maritalStatusCode: data.value as EMaritalStatus,
      });
    },
  };

  private lastEducation = {
    onChange: (event: React.SyntheticEvent<HTMLElement>, data: DropdownProps) => {
      if (!data.value) return;
      const education = EducationHelper.toIndonesianFormatter(data.value as EFamilyEducation);
      if (!education) return;
      this.setState({
        lastEducation: education,
        lastEducationCode: data.value as EFamilyEducation,
      });
    },
  };

  private fullAddress = {
    onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) =>
      this.setState({
        fullAddress: e.target.value,
      }),
  };

  private domicileFullAddress = {
    onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) =>
      this.setState({
        domicileFullAddress: e.target.value,
      }),
  };

  private region = {
    province: {
      onChange: (params: CommonRegionProps | null) =>
        this.setState({
          province: params,
        }),
    },
    city: {
      onChange: (params: CommonRegionProps | null) =>
        this.setState({
          city: params,
        }),
    },
    district: {
      onChange: (params: CommonRegionProps | null) =>
        this.setState({
          district: params,
        }),
    },
    subDistrict: {
      onChange: (params: CommonRegionProps | null, postalCode: string | null) =>
        this.setState({
          subDistrict: params,
          zipCode: postalCode ?? '',
        }),
    },
    postalCode: {
      onChange: (postalCode: string | null) => {
        if (postalCode && postalCode.length <= 5) {
          this.setState({
            zipCode: postalCode,
          });
        }
      },
    },
  };

  private domicileRegion = {
    domicileProvince: {
      onChange: (params: CommonRegionProps | null) =>
        this.setState({
          domicileProvince: params,
        }),
    },
    domicileCity: {
      onChange: (params: CommonRegionProps | null) =>
        this.setState({
          domicileCity: params,
        }),
    },
    domicileDistrict: {
      onChange: (params: CommonRegionProps | null) =>
        this.setState({
          domicileDistrict: params,
        }),
    },
    domicileSubDistrict: {
      onChange: (params: CommonRegionProps | null, postalCode: string | null) =>
        this.setState({
          domicileSubDistrict: params,
          domicileZipCode: postalCode ?? '',
        }),
    },
    domicilePostalCode: {
      onChange: (postalCode: string | null) => {
        if (postalCode && postalCode.length <= 5) {
          this.setState({
            domicileZipCode: postalCode,
          });
        }
      },
    },
  };

  private neighbourhood = {
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => {
      this.setState({
        neighbourhood: e.target.value,
      });
    },
  };

  private domicileNeighbourhood = {
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => {
      this.setState({
        domicileNeighbourhood: e.target.value,
      });
    },
  };

  private hamlet = {
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => {
      this.setState({
        hamlet: e.target.value,
      });
    },
  };

  private domicileHamlet = {
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => {
      this.setState({
        domicileHamlet: e.target.value,
      });
    },
  };

  onSubmit = async () => {
    if (this.state.creating) return;
    let isSuccess: boolean = false;

    this.setState(
      {
        creating: true,
        errorMessage: null,
        successMessage: null,
      },
      async () => {
        const currentState: IdCardCaptureStates = { ...this.state };
        const blob = await new Promise<Blob | null>((resolve) => {
          if (this.props.cropCanvas.current) {
            this.props.cropCanvas.current?.cropper
              .crop()
              .getCroppedCanvas()
              .toBlob((blob) => {
                if (blob) resolve(blob);
              });
          } else {
            resolve(null);
          }
        });

        const preProcessData = {
          idCardNumber: this.state.idCardNumber,
          fullName: this.state.fullName,
          birthMother: this.state.birthMother,
          placeOfBirth: this.state.placeOfBirth,
          dateOfBirth: this.state.dateOfBirth,
          occupation: this.state.occupation,
          occupationCode: this.state.occupationCode,
          maritalStatus: this.state.maritalStatus,
          maritalStatusCode: this.state.maritalStatusCode,
          lastEducation: this.state.lastEducation,
          lastEducationCode: this.state.lastEducationCode,

          fullAddress: this.state.fullAddress,
          zipCode: this.state.zipCode,
          province: this.state.province,
          city: this.state.city,
          district: this.state.district,
          subDistrict: this.state.subDistrict,
          image: blob ?? undefined,
          imageUrl: this.state.currentImage ?? undefined,
          neighbourhood: this.state.neighbourhood,
          hamlet: this.state.hamlet,

          domicileFullAddress: this.state.domicileFullAddress,
          domicileProvince: this.state.domicileProvince,
          domicileCity: this.state.domicileCity,
          domicileDistrict: this.state.domicileDistrict,
          domicileSubDistrict: this.state.domicileSubDistrict,
          domicileZipCode: this.state.domicileZipCode,
          domicileNeighbourhood: this.state.domicileNeighbourhood,
          domicileHamlet: this.state.domicileHamlet,
        };

        try {
          await mainApiServices.setClientIdCardV2(
            {
              ...preProcessData,
            },
            this.props.modal.documentTarget as any,
            this.props.conversation.chatRoom!.clients[0],
          );

          mainStore.dispatch(
            fetchCustomerThunk({
              clientDocRef: this.props.conversation.chatRoom!.clients[0],
            }) as any,
          );

          currentState.successMessage = 'Berhasil memperbarui KTP ' + moment().format('LLLL');
          isSuccess = true;
        } catch (e: any) {
          const error: AxiosError<any> = e as any;
          let errorMessagesText = '';
          if (error.response?.data?.error.type === 'UNPROCESSABLE_ENTITY') {
            const messages = error.response?.data?.error.messages;
            for (const [, value] of Object.entries(messages)) {
              const _value: any = value;
              errorMessagesText += `${_value.msg} (${_value.path})\n`;
            }
          }
          currentState.errorMessage = errorMessagesText;

          isSuccess = false;
        }

        currentState.creating = false;

        this.setState({
          ...currentState,
        });

        if (isSuccess) {
        }
      },
    );
  };

  load = async () => {
    this.setState({
      loading: true,
    });

    let client: ClientEntity | undefined;
    const clientRef = this.props.conversation.chatRoom?.clients[0];
    if (clientRef) {
      const getClient = await getDoc(clientRef.withConverter(ClientEntity.converter));
      if (getClient.exists()) {
        client = getClient.data();
      }
    }

    if (!client) {
      this.setState({
        loading: false,
      });
      return;
    }

    let idCardData: IdCard | null = null;

    switch (this.props.modal.documentTarget) {
      case 'idCardOwner':
        idCardData = client.details.idCardOwner;
        break;
      case 'idCardOrderMaker':
        idCardData = client.details.idCardOrderMaker;
        break;
      case 'idCardGuarantor':
        idCardData = client.details.idCardGuarantor;
        break;
      case 'idCardGuarantorSpouse':
        idCardData = client.details.idCardGuarantorSpouse;
        break;
    }

    let currentState: IdCardCaptureStates = { ...this.state };

    if (idCardData) {
      currentState.currentImage = idCardData.idCardImage;

      currentState.fullName = idCardData.fullName;
      currentState.idCardNumber = idCardData.idCardNumber;
      currentState.placeOfBirth = idCardData.placeOfBirth;
      currentState.dateOfBirth = idCardData.dateOfBirth.toDate();
      currentState.birthMother = idCardData.birthMother;

      currentState.occupation = idCardData.occupation;
      currentState.occupationCode = idCardData.occupationCode;

      currentState.maritalStatus = idCardData.maritalStatus;
      currentState.maritalStatusCode = idCardData.maritalStatusCode;

      currentState.lastEducation = idCardData.lastEducation;
      currentState.lastEducationCode = idCardData.lastEducationCode;

      currentState.fullAddress = idCardData.fullAddress;
      currentState.province = idCardData.province;
      currentState.city = idCardData.city;
      currentState.district = idCardData.district;
      currentState.subDistrict = idCardData.subDistrict;
      currentState.zipCode = idCardData.zipCode;
      currentState.hamlet = idCardData.hamlet;
      currentState.neighbourhood = idCardData.neighbourhood;

      currentState.domicileFullAddress = idCardData.domicileFullAddress;
      currentState.domicileProvince = idCardData.domicileProvince;
      currentState.domicileCity = idCardData.domicileCity;
      currentState.domicileDistrict = idCardData.domicileDistrict;
      currentState.domicileSubDistrict = idCardData.domicileSubDistrict;
      currentState.domicileZipCode = idCardData.domicileZipCode;
      currentState.domicileHamlet = idCardData.domicileHamlet;
      currentState.domicileNeighbourhood = idCardData.domicileNeighbourhood;
    } else {
      currentState.currentImage = null;

      currentState.fullName = '';
      currentState.idCardNumber = '';
      currentState.placeOfBirth = '';
      currentState.dateOfBirth = null;
      currentState.birthMother = '';

      currentState.occupation = '';
      currentState.occupationCode = '';

      currentState.maritalStatus = '';
      currentState.maritalStatusCode = null;

      currentState.lastEducation = '';
      currentState.lastEducationCode = null;

      currentState.fullAddress = '';
      currentState.province = null;
      currentState.city = null;
      currentState.district = null;
      currentState.subDistrict = null;
      currentState.zipCode = '';
      currentState.hamlet = '';
      currentState.neighbourhood = '';

      currentState.domicileFullAddress = '';
      currentState.domicileProvince = null;
      currentState.domicileCity = null;
      currentState.domicileDistrict = null;
      currentState.domicileSubDistrict = null;
      currentState.domicileZipCode = '';
      currentState.domicileHamlet = '';
      currentState.domicileHamlet = '';
    }

    currentState.loading = false;

    this.setState({ ...currentState });
  };

  fetchOcr = async () => {
    if (!this.props.modal.urlImageToSet) return;
    this.setState(
      {
        fetchingOcr: true,
      },
      () => {
        ocrServices.getDataFromIdCard(this.props.modal.urlImageToSet!).then((value) => {
          if (value.ok) {
            const data = value.data!.data;
            const [neighbourhood, hamlet] = data.neighbourhood_and_hamlet.split('/');
            this.setState({
              idCardNumber: data.id_card_number,
              fullName: data.name,
              fullAddress: data.address,
              placeOfBirth: data.place_of_birth,
              dateOfBirth: moment(data.date_of_birth, 'YYYY-MM-DD').toDate(),
              neighbourhood,
              hamlet,
            });
          }

          this.setState({
            fetchingOcr: false,
          });
        });
      },
    );
  };

  sameAsIdCardAddress = () => {
    this.setState(
      {
        domicileProvince: this.state.province,
        domicileCity: this.state.city,
        domicileDistrict: this.state.district,
        domicileSubDistrict: this.state.subDistrict,
        domicileFullAddress: this.state.fullAddress,
        domicileNeighbourhood: this.state.neighbourhood,
        domicileHamlet: this.state.hamlet,
        domicileZipCode: this.state.zipCode,
      },
      () => {
        this.domRegionComRef.current?.fetchAll();
      },
    );
  };

  componentDidUpdate(
    prevProps: Readonly<IdCardCaptureProps>,
    prevState: Readonly<IdCardCaptureStates>,
    snapshot?: any,
  ) {
    if (prevProps.modal.documentTarget !== this.props.modal.documentTarget) {
      this.load();
    }
  }

  componentDidMount() {
    this.load();
  }

  render() {
    if (this.state.loading) return <div>Memuat...</div>;
    return (
      <React.Fragment>
        {this.props.modal.type === 'SET' && (
          <div
            style={{
              marginBottom: '1rem',
              display: 'flex',
              flexDirection: 'column',
            }}
          >
            <div>
              <Button
                onClick={this.fetchOcr}
                loading={this.state.fetchingOcr}
              >
                Dapatkan data gambar
              </Button>
            </div>
            <div>
              <small>*Data yang dihasilkan tidak selalu akurat, harap periksa kembali.</small>
            </div>
          </div>
        )}

        <Form>
          <Divider horizontal={true}>Data KTP</Divider>
          {this.state.currentImage && (
            <Form.Field>
              <a
                href={this.state.currentImage}
                style={{ fontWeight: 'bolder', color: 'blue' }}
                target={'_blank'}
                rel="noreferrer"
              >
                Lihat Foto Tersimpan
              </a>
            </Form.Field>
          )}
          <Form.Field>
            <input
              placeholder={'No KTP'}
              onChange={this.idCardNumber.onChange}
              value={this.state.idCardNumber}
            />
          </Form.Field>
          <Form.Field>
            <input
              placeholder={'Nama Lengkap'}
              onChange={this.fullName.onChange}
              value={this.state.fullName}
            />
          </Form.Field>
          <Form.Group widths={'equal'}>
            <Form.Field>
              <input
                placeholder={'Tempat Lahir'}
                onChange={this.placeOfBirth.onChange}
                value={this.state.placeOfBirth}
              />
            </Form.Field>
            <Form.Field>
              <DatePicker
                placeholder={'Tanggal Lahir'}
                onChange={this.birthDate.onChange}
                value={this.state.dateOfBirth}
              />
            </Form.Field>
          </Form.Group>
          <Form.Group widths={'equal'}>
            <Form.Field>
              <Select
                options={occupation.map((value) => {
                  return {
                    value: value.code,
                    text: value.name,
                  };
                })}
                value={this.state.occupationCode}
                onChange={this.occupation.onChange}
                search={true}
                placeholder={'Pekerjaan'}
              />
            </Form.Field>
            <Form.Field>
              <Select
                onChange={this.maritalStatus.onChange}
                options={Object.keys(EMaritalStatus).map((value) => {
                  return {
                    value: value as any,
                    text: MaritalStatusHelper.toIndonesianFormatter(value as EMaritalStatus),
                  };
                })}
                placeholder={'Status Kawin'}
                value={this.state.maritalStatusCode ?? undefined}
              />
            </Form.Field>
          </Form.Group>
          <Divider horizontal={true}>Data Lainnya</Divider>
          <Form.Field>
            <input
              placeholder={'Nama Ibu Kandung'}
              onChange={this.birthMother.onChange}
              value={this.state.birthMother}
            />
          </Form.Field>
          <Form.Field>
            <Select
              options={Object.keys(EFamilyEducation).map((v) => {
                return {
                  value: v as any,
                  text: EducationHelper.toIndonesianFormatter(v as EFamilyEducation),
                };
              })}
              onChange={this.lastEducation.onChange}
              value={this.state.lastEducationCode ?? undefined}
              placeholder={'Pendidikan Terakhir'}
            />
          </Form.Field>
          <Divider horizontal={true}>Alamat KTP</Divider>
          <Form.Field>
            <textarea
              onChange={this.fullAddress.onChange}
              placeholder={'Alamat Lengkap'}
              rows={2}
              value={this.state.fullAddress}
            />
          </Form.Field>
          <SelectRegion
            postCode={{
              selectedCode: this.state.zipCode,
              onChange: this.region.postalCode.onChange,
            }}
            province={{
              selectedCode: this.state.province?.code,
              onChange: this.region.province.onChange,
            }}
            city={{
              selectedCode: this.state.city?.code,
              onChange: this.region.city.onChange,
            }}
            district={{
              selectedCode: this.state.district?.code,
              onChange: this.region.district.onChange,
            }}
            subDistrict={{
              selectedCode: this.state.subDistrict?.code,
              onChange: this.region.subDistrict.onChange,
            }}
          />
          <Form.Group widths={'equal'}>
            <Form.Field>
              <input
                placeholder={'RT'}
                onChange={this.neighbourhood.onChange}
                value={this.state.neighbourhood}
              />
            </Form.Field>
            <Form.Field>
              <input
                placeholder={'RW'}
                onChange={this.hamlet.onChange}
                value={this.state.hamlet}
              />
            </Form.Field>
          </Form.Group>

          <Divider horizontal={true}>Alamat Domisili</Divider>

          <Form.Field>
            <Button onClick={this.sameAsIdCardAddress}>Salin Alamat KTP ke Alamat Domisili</Button>
          </Form.Field>

          <Form.Field>
            <textarea
              onChange={this.domicileFullAddress.onChange}
              placeholder={'Alamat Lengkap'}
              rows={2}
              value={this.state.domicileFullAddress}
            />
          </Form.Field>
          <SelectRegion
            ref={this.domRegionComRef}
            postCode={{
              selectedCode: this.state.domicileZipCode,
              onChange: this.domicileRegion.domicilePostalCode.onChange,
            }}
            province={{
              selectedCode: this.state.domicileProvince?.code,
              onChange: this.domicileRegion.domicileProvince.onChange,
            }}
            city={{
              selectedCode: this.state.domicileCity?.code,
              onChange: this.domicileRegion.domicileCity.onChange,
            }}
            district={{
              selectedCode: this.state.domicileDistrict?.code,
              onChange: this.domicileRegion.domicileDistrict.onChange,
            }}
            subDistrict={{
              selectedCode: this.state.domicileSubDistrict?.code,
              onChange: this.domicileRegion.domicileSubDistrict.onChange,
            }}
          />

          <Form.Group widths={'equal'}>
            <Form.Field>
              <input
                placeholder={'RT'}
                onChange={this.domicileNeighbourhood.onChange}
                value={this.state.domicileNeighbourhood}
                type={'number'}
              />
            </Form.Field>
            <Form.Field>
              <input
                placeholder={'RW'}
                onChange={this.domicileHamlet.onChange}
                value={this.state.domicileHamlet}
                type={'number'}
              />
            </Form.Field>
          </Form.Group>

          <Button
            loading={this.state.creating}
            content="Simpan KTP"
            labelPosition="right"
            icon="checkmark"
            onClick={this.onSubmit}
            positive
          />
        </Form>
        {this.state.errorMessage && (
          <Message negative={true}>
            <pre>{this.state.errorMessage}</pre>
          </Message>
        )}
        {this.state.successMessage && (
          <Message positive={true}>{this.state.successMessage}</Message>
        )}
      </React.Fragment>
    );
  }
}

const mapStateToProps = (states: TMainReduxStates) => ({
  conversation: states.reducerConversation,
  modal: states.modalSetImageAs,
});

export default connect(mapStateToProps)(IdCardCaptureV2);

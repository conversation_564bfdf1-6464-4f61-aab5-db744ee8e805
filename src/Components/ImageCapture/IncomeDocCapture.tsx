import React, { Component } from 'react';
import { Button, DropdownItemProps, Form, Message, Select } from 'semantic-ui-react';
import { ReactCropperElement } from 'react-cropper';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { connect } from 'react-redux';
import { mainApiServices } from '../../services/MainApiServices';
import moment from 'moment';
import { AxiosError } from 'axios';
import { EIncomeType } from './types/income_document_types';
import IncomeDocumentHelper from '../../helpers/incomeDocumentHelper';
import { DropdownProps } from 'semantic-ui-react/dist/commonjs/modules/Dropdown/Dropdown';
import { EPrivateDocument } from './types/private-document-types';
import PrivateDocumentHelper from '../../helpers/privateDocumentHelper';
import ClientEntity from '../../entities/ClientEntity';
import { profileServices } from '../../services/profileServices';
import { getDoc } from 'firebase/firestore';

export interface IIncomeDocFields {
  incomeType: EIncomeType | null;
  documentType: EPrivateDocument | null;
  incomeAmount: string;
  additionalInfo: string;
  bankName: string;
  bankAccountNumber: string;
  image: Blob | null;
}

export interface IncomeDocCaptureStates extends IIncomeDocFields {
  loading: boolean;
  creating: boolean;

  errorMessage: string | null;
  successMessage: string | null;

  currentImage: null | string;
  allowed: boolean;
}

export interface IncomeDocCaptureProps {
  cropCanvas: React.RefObject<ReactCropperElement>;
  conversation: TMainReduxStates['reducerConversation'];
}

class IncomeDocCapture extends Component<IncomeDocCaptureProps, IncomeDocCaptureStates> {
  private incomeType = {
    onChange: (event: React.SyntheticEvent<HTMLElement>, data: DropdownProps) => {
      this.setState({
        incomeType: data.value as EIncomeType,
      });
    },
    options: (): DropdownItemProps[] => {
      return Object.keys(EIncomeType).map((value) => {
        return {
          value: value,
          text: IncomeDocumentHelper.toIndonesianFormatter(value as keyof EIncomeType),
        };
      });
    },
  };

  private documentType = {
    onChange: (event: React.SyntheticEvent<HTMLElement>, data: DropdownProps) => {
      this.setState({
        documentType: data.value as EPrivateDocument,
      });
    },
    options: (): DropdownItemProps[] => {
      return Object.keys(EPrivateDocument).map((value) => {
        return {
          value: value,
          text: PrivateDocumentHelper.toIndonesianFormatter(value as keyof EPrivateDocument),
        };
      });
    },
  };

  private incomeAmount = {
    onChange: (e: React.ChangeEvent<HTMLInputElement>) =>
      this.setState({
        incomeAmount: e.target.value,
      }),
  };

  private additionalInfo = {
    onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) =>
      this.setState({
        additionalInfo: e.target.value,
      }),
  };

  private bankName = {
    onChange: (e: React.ChangeEvent<HTMLInputElement>) =>
      this.setState({
        bankName: e.target.value,
      }),
  };

  private bankAccountNumber = {
    onChange: (e: React.ChangeEvent<HTMLInputElement>) =>
      this.setState({
        bankAccountNumber: e.target.value,
      }),
  };

  constructor(props: IncomeDocCaptureProps) {
    super(props);
    this.state = {
      incomeType: null,
      documentType: null,
      incomeAmount: '',
      additionalInfo: '',
      bankName: '',
      bankAccountNumber: '',
      image: null,

      currentImage: null,
      errorMessage: null,
      successMessage: null,
      creating: false,
      loading: false,
      allowed: false,
    };
  }

  onSubmit = async () => {
    if (this.state.creating) return;

    await this.setState({
      creating: true,
      errorMessage: null,
      successMessage: null,
    });

    let currentState: IncomeDocCaptureStates = { ...this.state };

    const blob = await new Promise<Blob | null>((resolve) => {
      if (this.props.cropCanvas.current) {
        this.props.cropCanvas.current?.cropper
          .crop()
          .getCroppedCanvas()
          .toBlob((blob) => {
            if (blob) resolve(blob);
          });
      } else {
        resolve(null);
      }
    });

    try {
      await mainApiServices.setIncomeDocument(
        {
          image: blob,
          imageUrl: this.state.currentImage ?? undefined,
          additionalInfo: this.state.additionalInfo,
          bankAccountNumber: this.state.bankAccountNumber,
          bankName: this.state.bankName,
          incomeAmount: this.state.incomeAmount,
          incomeType: this.state.incomeType,
          documentType: this.state.documentType,
        },
        this.props.conversation.chatRoom!.clients[0],
      );
      currentState.successMessage =
        'Berhasil memperbarui Dokumen Penghasilan ' + moment().format('LLLL');
    } catch (e: any) {
      const error: AxiosError = e as any;
      currentState.errorMessage = error.message;
    }

    currentState.creating = false;
    await this.setState({
      ...currentState,
    });
  };

  load = async () => {
    await this.setState({
      loading: true,
    });

    let currentState: IncomeDocCaptureStates = { ...this.state };

    const clientRef = this.props.conversation.chatRoom?.clients[0];
    if (clientRef) {
      const getClient = await getDoc(clientRef.withConverter(ClientEntity.converter));
      if (getClient.exists()) {
        const client = getClient!.data()!;

        if (client.details?.owner_phone_number) {
          const getProfile = await profileServices.getProfile(client.details.owner_phone_number);

          if (getProfile?.length === 1) {
            currentState.allowed = true;
            if (getProfile[0].IncomeDocument) {
              const incomeDocument = getProfile[0].IncomeDocument;
              currentState = {
                ...currentState,
                incomeType: incomeDocument?.IncomeType,
                currentImage: incomeDocument?.ImageIncomeDocument ?? '',
                documentType: incomeDocument?.DocumentType,
                incomeAmount: incomeDocument?.IncomeAmount,
                additionalInfo: incomeDocument?.AdditionalInfo,
                bankName: incomeDocument?.BankName,
                bankAccountNumber: incomeDocument?.BankAccountNumber,
              };
            }
          }
        }
      }
    }

    await this.setState({
      ...currentState,
      loading: false,
    });
  };

  componentDidMount() {
    this.load();
  }

  render() {
    if (this.state.loading) return <div>Memuat...</div>;
    else if (!this.state.allowed)
      return <Message negative={true}>Isi data KTP Pemilik terlebih dahulu.</Message>;
    return (
      <React.Fragment>
        <Form>
          {this.state.currentImage && (
            <Form.Field>
              <a
                href={this.state.currentImage}
                style={{ fontWeight: 'bolder' }}
                target={'_blank'}
                rel="noreferrer"
              >
                Lihat Foto Sekarang
              </a>
            </Form.Field>
          )}
          <Form.Field>
            <Select
              options={this.incomeType.options()}
              placeholder={'Jenis Penghasilan'}
              onChange={this.incomeType.onChange}
              value={this.state.incomeType as any}
            />
          </Form.Field>
          <Form.Field>
            <Select
              placeholder={'Jenis Dokumen'}
              onChange={this.documentType.onChange}
              value={this.state.documentType as any}
              options={this.documentType.options()}
            />
          </Form.Field>
          <Form.Field>
            <input
              placeholder={'Total Penghasilan'}
              onChange={this.incomeAmount.onChange}
              value={this.state.incomeAmount}
            />
          </Form.Field>
          <Form.Field>
            <textarea
              rows={2}
              placeholder={'Keterangan Tambahan'}
              onChange={this.additionalInfo.onChange}
              value={this.state.additionalInfo}
            />
          </Form.Field>
          <Form.Field>
            <input
              placeholder={'Nama Bank'}
              onChange={this.bankName.onChange}
              value={this.state.bankName}
            />
          </Form.Field>
          <Form.Field>
            <input
              placeholder={'No Rekening'}
              onChange={this.bankAccountNumber.onChange}
              value={this.state.bankAccountNumber}
            />
          </Form.Field>
          <Button
            loading={this.state.creating}
            content="Simpan Dokumen Penghasilan"
            labelPosition="right"
            icon="checkmark"
            onClick={this.onSubmit}
            positive
          />
        </Form>
        {this.state.errorMessage && <Message negative={true}>{this.state.errorMessage}</Message>}
        {this.state.successMessage && (
          <Message positive={true}>{this.state.successMessage}</Message>
        )}
      </React.Fragment>
    );
  }
}

const mapStateToProps = (states: TMainReduxStates) => ({
  conversation: states.reducerConversation,
});

export default connect(mapStateToProps)(IncomeDocCapture);

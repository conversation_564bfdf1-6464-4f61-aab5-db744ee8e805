import React, { Component } from 'react';
import { Button, Form, Message } from 'semantic-ui-react';
import { ReactCropperElement } from 'react-cropper';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { connect } from 'react-redux';
import moment from 'moment';
import { AxiosError } from 'axios';
import { SemanticDatepickerProps } from 'react-semantic-ui-datepickers/dist/types';
import DatePicker from 'react-semantic-ui-datepickers';
import { mainApiServices } from '../../services/MainApiServices';
import ClientEntity from '../../entities/ClientEntity';
import { profileServices } from '../../services/profileServices';
import { getDoc } from 'firebase/firestore';

export interface IOtherDocumentFields {
  documentName: string;
  expiredDate: Date | null;
  notes: string;
  image: Blob | null;
}

export interface OtherDocumentStates extends IOtherDocumentFields {
  creating: boolean;
  loading: boolean;

  errorMessage: string | null;
  successMessage: string | null;

  currentImage: string | null;

  allowed: boolean;
}

export interface OtherDocumentProps {
  cropCanvas: React.RefObject<ReactCropperElement>;
  conversation: TMainReduxStates['reducerConversation'];
}

class OtherDocumentCapture extends Component<OtherDocumentProps, OtherDocumentStates> {
  constructor(props: OtherDocumentProps) {
    super(props);
    this.state = {
      allowed: false,
      expiredDate: null,
      documentName: '',
      notes: '',
      image: null,

      errorMessage: null,
      successMessage: null,
      creating: false,
      loading: false,
      currentImage: null,
    };
  }

  private documentName = {
    onChange: (e: React.ChangeEvent<HTMLInputElement>) =>
      this.setState({
        documentName: e.target.value,
      }),
  };

  private notes = {
    onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) =>
      this.setState({
        notes: e.target.value,
      }),
  };

  private expiredDate = {
    onChange: (event: React.SyntheticEvent | undefined, data: SemanticDatepickerProps) => {
      this.setState({
        expiredDate: (data.value as Date | null) ?? null,
      });
    },
  };

  onSubmit = async () => {
    if (this.state.creating) return;

    await this.setState({
      creating: true,
      errorMessage: null,
      successMessage: null,
    });

    let currentState: OtherDocumentStates = { ...this.state };

    const blob = await new Promise<Blob | null>((resolve) => {
      if (this.props.cropCanvas.current) {
        this.props.cropCanvas.current?.cropper
          .crop()
          .getCroppedCanvas()
          .toBlob((blob) => {
            if (blob) resolve(blob);
          });
      } else {
        resolve(null);
      }
    });

    try {
      await mainApiServices.setOtherDocument(
        {
          imageUrl: this.state.currentImage ?? undefined,
          documentName: this.state.documentName,
          expiredDate: this.state.expiredDate,
          notes: this.state.notes,
          image: blob,
        },
        this.props.conversation.chatRoom!.clients[0],
      );
      currentState.successMessage =
        'Berhasil memperbarui Dokumen Lain Lain ' + moment().format('LLLL');
    } catch (e: any) {
      const error: AxiosError = e as any;
      currentState.errorMessage = error.message;
    }

    currentState.creating = false;
    await this.setState({
      ...currentState,
    });
  };

  load = async () => {
    await this.setState({
      loading: true,
    });

    let currentState: OtherDocumentStates = { ...this.state };

    const clientRef = this.props.conversation.chatRoom?.clients[0];
    if (clientRef) {
      const getClient = await getDoc(clientRef.withConverter(ClientEntity.converter));
      if (getClient.exists()) {
        const client = getClient!.data()!;

        if (client.details?.owner_phone_number) {
          const getProfile = await profileServices.getProfile(client.details.owner_phone_number);

          if (getProfile?.length === 1) {
            currentState.allowed = true;
            if (getProfile[0].OtherDocument) {
              const otherDocument = getProfile[0].OtherDocument;
              currentState = {
                ...currentState,
                expiredDate: otherDocument?.ExpiredDate
                  ? new Date(otherDocument.ExpiredDate)
                  : null,
                documentName: otherDocument?.DocumentName,
                notes: otherDocument?.Notes,
                currentImage: otherDocument?.ImageOtherDocument ?? '',
              };
            }
          }
        }
      }
    }

    await this.setState({
      ...currentState,
      loading: false,
    });
  };

  componentDidMount() {
    this.load();
  }

  render() {
    if (this.state.loading) return <div>Memuat...</div>;
    else if (!this.state.allowed)
      return <Message negative={true}>Isi data KTP Pemilik terlebih dahulu.</Message>;
    return (
      <React.Fragment>
        <Form>
          {this.state.currentImage && (
            <Form.Field>
              <a
                href={this.state.currentImage}
                style={{ fontWeight: 'bolder' }}
                target={'_blank'}
                rel="noreferrer"
              >
                Lihat Foto Sekarang
              </a>
            </Form.Field>
          )}
          <Form.Field>
            <label>Nama Dokumen</label>
            <input
              placeholder={'Nama Dokumen'}
              onChange={this.documentName.onChange}
              value={this.state.documentName}
            />
          </Form.Field>
          <Form.Field>
            <label>Tanggal Kadaluarsa</label>
            <DatePicker
              placeholder={'Tanggal Kadaluarsa'}
              onChange={this.expiredDate.onChange}
              value={this.state.expiredDate}
            />
          </Form.Field>
          <Form.Field>
            <label>Catatan</label>
            <textarea
              rows={2}
              placeholder={'Catatan'}
              onChange={this.notes.onChange}
              value={this.state.notes}
            />
          </Form.Field>
          <Button
            loading={this.state.creating}
            content="Simpan Dokumen"
            labelPosition="right"
            icon="checkmark"
            onClick={this.onSubmit}
            positive
          />
        </Form>
        {this.state.errorMessage && <Message negative={true}>{this.state.errorMessage}</Message>}
        {this.state.successMessage && (
          <Message positive={true}>{this.state.successMessage}</Message>
        )}
      </React.Fragment>
    );
  }
}

const mapStateToProps = (states: TMainReduxStates) => ({
  conversation: states.reducerConversation,
});

export default connect(mapStateToProps)(OtherDocumentCapture);

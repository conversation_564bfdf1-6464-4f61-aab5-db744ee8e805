import React, { Component } from 'react';
import { Button, DropdownItemProps, Form, Message, Select } from 'semantic-ui-react';
import { ReactCropperElement } from 'react-cropper';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { mainApiServices } from '../../services/MainApiServices';
import moment from 'moment';
import { AxiosError } from 'axios';
import { connect } from 'react-redux';
import { EBusinessSize, EBusinessType } from './types/place_of_business_types';
import { DropdownProps } from 'semantic-ui-react/dist/commonjs/modules/Dropdown/Dropdown';
import BusinessSizeHelper from '../../helpers/businessSizeHelper';
import BusinessTypeHelper from '../../helpers/businessTypeHelper';
import ClientEntity from '../../entities/ClientEntity';
import { profileServices } from '../../services/profileServices';
import { getDoc } from 'firebase/firestore';

export interface IPlaceOfBusinessFields {
  fullAddress: string;
  latitude: string;
  longitude: string;
  image: Blob | null;
  businessSize: EBusinessSize | null;
  businessType: EBusinessType | null;
}

export interface PlaceOfBusinessStates extends IPlaceOfBusinessFields {
  creating: boolean;
  loading: boolean;

  errorMessage: string | null;
  successMessage: string | null;

  currentImage: string | null;

  allowed: boolean;
}

export interface PlaceOfBusinessProps {
  cropCanvas: React.RefObject<ReactCropperElement>;
  conversation: TMainReduxStates['reducerConversation'];
}

class PlaceOfBusinessCapture extends Component<PlaceOfBusinessProps, PlaceOfBusinessStates> {
  constructor(props: PlaceOfBusinessProps) {
    super(props);

    this.state = {
      fullAddress: '',
      latitude: '',
      longitude: '',
      image: null,
      businessSize: null,
      businessType: null,

      creating: false,
      loading: false,
      errorMessage: null,
      successMessage: null,

      currentImage: null,

      allowed: false,
    };
  }

  private fullAddress = {
    onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) =>
      this.setState({
        fullAddress: e.target.value,
      }),
  };

  private latitude = {
    onChange: (e: React.ChangeEvent<HTMLInputElement>) =>
      this.setState({
        latitude: e.target.value,
      }),
  };

  private longitude = {
    onChange: (e: React.ChangeEvent<HTMLInputElement>) =>
      this.setState({
        longitude: e.target.value,
      }),
  };

  private businessSize = {
    onChange: (event: React.SyntheticEvent<HTMLElement>, data: DropdownProps) => {
      this.setState({
        businessSize: data.value as EBusinessSize,
      });
    },
    options: (): DropdownItemProps[] => {
      return Object.keys(EBusinessSize).map((value) => {
        return {
          value: value,
          text: BusinessSizeHelper.toIndonesianFormatter(value as keyof EBusinessSize),
        };
      });
    },
  };

  private businessType = {
    onChange: (event: React.SyntheticEvent<HTMLElement>, data: DropdownProps) => {
      this.setState({
        businessType: data.value as EBusinessType,
      });
    },
    options: (): DropdownItemProps[] => {
      return Object.keys(EBusinessType).map((value) => {
        return {
          value: value,
          text: BusinessTypeHelper.toIndonesianFormatter(value as keyof EBusinessType),
        };
      });
    },
  };

  onSubmit = async () => {
    if (this.state.creating) return;

    await this.setState({
      creating: true,
      errorMessage: null,
      successMessage: null,
    });

    let currentState: PlaceOfBusinessStates = { ...this.state };

    const blob = await new Promise<Blob | null>((resolve) => {
      if (this.props.cropCanvas.current) {
        this.props.cropCanvas.current?.cropper
          .crop()
          .getCroppedCanvas()
          .toBlob((blob) => {
            if (blob) resolve(blob);
          });
      } else {
        resolve(null);
      }
    });

    try {
      await mainApiServices.setPlaceOfBusiness(
        {
          imageUrl: this.state.currentImage ?? undefined,
          latitude: this.state.latitude,
          longitude: this.state.longitude,
          fullAddress: this.state.fullAddress,
          image: blob,
          businessSize: this.state.businessSize,
          businessType: this.state.businessType,
        },
        this.props.conversation.chatRoom!.clients[0],
      );
      currentState.successMessage = 'Berhasil memperbarui Tempat Usaha ' + moment().format('LLLL');
    } catch (e: any) {
      const error: AxiosError = e as any;
      currentState.errorMessage = error.message;
    }

    currentState.creating = false;
    await this.setState({
      ...currentState,
    });
  };

  load = async () => {
    await this.setState({
      loading: true,
    });

    let currentState: PlaceOfBusinessStates = { ...this.state };

    const clientRef = this.props.conversation.chatRoom?.clients[0];
    if (clientRef) {
      const getClient = await getDoc(clientRef.withConverter(ClientEntity.converter));
      if (getClient.exists()) {
        const client = getClient!.data()!;

        if (client.details?.owner_phone_number) {
          const getProfile = await profileServices.getProfile(client.details.owner_phone_number);

          if (getProfile?.length === 1) {
            currentState.allowed = true;
            if (getProfile[0].PlaceOfBusiness) {
              const placeOfBusiness = getProfile[0].PlaceOfBusiness;
              currentState = {
                ...currentState,
                fullAddress: placeOfBusiness?.FullAddress,
                latitude: placeOfBusiness?.Latitude,
                longitude: placeOfBusiness?.Longitude,
                currentImage: placeOfBusiness?.PlaceOfBusinessImage ?? '',
                businessSize: placeOfBusiness?.BusinessSize,
                businessType: placeOfBusiness?.BusinessType,
              };
            }
          }
        }
      }
    }

    await this.setState({
      ...currentState,
      loading: false,
    });
  };

  componentDidMount() {
    this.load();
  }

  render() {
    if (this.state.loading) return <div>Memuat...</div>;
    else if (!this.state.allowed)
      return <Message negative={true}>Isi data KTP Pemilik terlebih dahulu.</Message>;
    return (
      <React.Fragment>
        <Form>
          {this.state.currentImage && (
            <Form.Field>
              <a
                href={this.state.currentImage}
                style={{ fontWeight: 'bolder' }}
                target={'_blank'}
                rel="noreferrer"
              >
                Lihat Foto Sekarang
              </a>
            </Form.Field>
          )}
          <Form.Field>
            <label>Ukuran Usaha</label>
            <Select
              onChange={this.businessSize.onChange}
              value={this.state.businessSize as any}
              options={this.businessSize.options()}
            />
          </Form.Field>
          <Form.Field>
            <label>Jenis Usaha</label>
            <Select
              onChange={this.businessType.onChange}
              value={this.state.businessType as any}
              options={this.businessType.options()}
            />
          </Form.Field>
          <Form.Field>
            <label>Alamat</label>
            <textarea
              rows={2}
              onChange={this.fullAddress.onChange}
              value={this.state.fullAddress}
            />
          </Form.Field>
          <Form.Field>
            <label>Latitude</label>
            <input
              onChange={this.latitude.onChange}
              value={this.state.latitude}
            />
          </Form.Field>
          <Form.Field>
            <label>Longitude</label>
            <input
              onChange={this.longitude.onChange}
              value={this.state.longitude}
            />
          </Form.Field>
          <Button
            loading={this.state.creating}
            content="Simpan Selfie"
            labelPosition="right"
            icon="checkmark"
            positive
            onClick={this.onSubmit}
          />
        </Form>

        {this.state.errorMessage && <Message negative={true}>{this.state.errorMessage}</Message>}
        {this.state.successMessage && (
          <Message positive={true}>{this.state.successMessage}</Message>
        )}
      </React.Fragment>
    );
  }
}

const mapStateToProps = (states: TMainReduxStates) => ({
  conversation: states.reducerConversation,
});

export default connect(mapStateToProps)(PlaceOfBusinessCapture);

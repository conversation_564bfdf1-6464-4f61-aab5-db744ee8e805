import React, { Component } from 'react';
import { Button, DropdownItemProps, Form, Message, Select } from 'semantic-ui-react';
import { ReactCropperElement } from 'react-cropper';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { connect } from 'react-redux';
import { mainApiServices } from '../../services/MainApiServices';
import moment from 'moment';
import { AxiosError } from 'axios';
import { DropdownProps } from 'semantic-ui-react/dist/commonjs/modules/Dropdown/Dropdown';
import ClientEntity from '../../entities/ClientEntity';
import { profileServices } from '../../services/profileServices';
import placeToStay from '../../config/finalPlaceToStay.json';
import { getDoc } from 'firebase/firestore';

export interface IPlaceToStayCaptureFields {
  fullAddress: string;
  latitude: string;
  longitude: string;
  image: Blob | null;
  placeToStayStatus: string | null;
}

export interface PlaceToStayCaptureStates extends IPlaceToStayCaptureFields {
  creating: boolean;
  loading: boolean;

  errorMessage: string | null;
  successMessage: string | null;

  currentImage: string | null;

  allowed: boolean;
}

export interface PlaceToStayCaptureProps {
  cropCanvas: React.RefObject<ReactCropperElement>;
  conversation: TMainReduxStates['reducerConversation'];
}

class PlaceToStayCapture extends Component<PlaceToStayCaptureProps, PlaceToStayCaptureStates> {
  constructor(props: PlaceToStayCaptureProps) {
    super(props);

    this.state = {
      fullAddress: '',
      latitude: '',
      longitude: '',
      image: null,
      placeToStayStatus: null,

      currentImage: null,

      loading: false,
      creating: false,

      successMessage: null,
      errorMessage: null,

      allowed: false,
    };
  }

  private fullAddress = {
    onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) =>
      this.setState({
        fullAddress: e.target.value,
      }),
  };

  private latitude = {
    onChange: (e: React.ChangeEvent<HTMLInputElement>) =>
      this.setState({
        latitude: e.target.value,
      }),
  };

  private longitude = {
    onChange: (e: React.ChangeEvent<HTMLInputElement>) =>
      this.setState({
        longitude: e.target.value,
      }),
  };

  private placeToStayStatus = {
    onChange: (event: React.SyntheticEvent<HTMLElement>, data: DropdownProps) => {
      this.setState({
        placeToStayStatus: data.value as string,
      });
    },
    options: (): DropdownItemProps[] => {
      return placeToStay.map((value) => {
        return {
          value: value.code,
          text: value.status_tempat_tinggal,
        };
      });
    },
  };

  onSubmit = async () => {
    if (this.state.creating) return;

    await this.setState({
      creating: true,
      errorMessage: null,
      successMessage: null,
    });

    let currentState: PlaceToStayCaptureStates = { ...this.state };

    const blob = await new Promise<Blob | null>((resolve) => {
      if (this.props.cropCanvas.current) {
        this.props.cropCanvas.current?.cropper
          .crop()
          .getCroppedCanvas()
          .toBlob((blob) => {
            if (blob) resolve(blob);
          });
      } else {
        resolve(null);
      }
    });

    try {
      await mainApiServices.setPlaceToStay(
        {
          placeToStayStatus: this.state.placeToStayStatus,
          latitude: this.state.latitude,
          longitude: this.state.longitude,
          fullAddress: this.state.fullAddress,
          image: blob,
          imageUrl: this.state.currentImage ?? undefined,
        },
        this.props.conversation.chatRoom!.clients[0],
      );
      currentState.successMessage =
        'Berhasil memperbarui Tempat Tinggal ' + moment().format('LLLL');
    } catch (e: any) {
      const error: AxiosError = e as any;
      currentState.errorMessage = error.message;
    }

    currentState.creating = false;
    await this.setState({
      ...currentState,
    });
  };

  load = async () => {
    await this.setState({
      loading: true,
    });

    let currentState: PlaceToStayCaptureStates = { ...this.state };

    const clientRef = this.props.conversation.chatRoom?.clients[0];
    if (clientRef) {
      const getClient = await getDoc(clientRef.withConverter(ClientEntity.converter));

      if (getClient.exists()) {
        const client = getClient!.data()!;

        if (client.details?.owner_phone_number) {
          const getProfile = await profileServices.getProfile(client.details.owner_phone_number);

          if (getProfile?.length === 1) {
            currentState.allowed = true;
            if (getProfile[0].PlaceToStay) {
              const placeToStay = getProfile[0].PlaceToStay;
              currentState = {
                ...currentState,
                currentImage: placeToStay?.PlaceToStayImage ?? null,
                fullAddress: placeToStay?.FullAddress,
                latitude: placeToStay?.Latitude,
                longitude: placeToStay?.Longitude,
                placeToStayStatus: placeToStay?.PlaceToStayStatus,
              };
            }
          }
        }
      }
    }

    await this.setState({
      ...currentState,
      loading: false,
    });
  };

  componentDidMount() {
    this.load();
  }

  render() {
    if (this.state.loading) return <div>Memuat...</div>;
    else if (!this.state.allowed)
      return <Message negative={true}>Isi data KTP Pemilik terlebih dahulu.</Message>;
    return (
      <React.Fragment>
        <Form>
          {this.state.currentImage && (
            <Form.Field>
              <a
                href={this.state.currentImage}
                style={{ fontWeight: 'bolder' }}
                target={'_blank'}
                rel="noreferrer"
              >
                Lihat Foto Sekarang
              </a>
            </Form.Field>
          )}
          <Form.Field>
            <label>Status Tempat Tinggal</label>
            <Select
              onChange={this.placeToStayStatus.onChange}
              value={this.state.placeToStayStatus as any}
              options={this.placeToStayStatus.options()}
            />
          </Form.Field>
          <Form.Field>
            <label>Alamat</label>
            <textarea
              rows={2}
              onChange={this.fullAddress.onChange}
              value={this.state.fullAddress}
            />
          </Form.Field>
          <Form.Field>
            <label>Latitude</label>
            <input
              onChange={this.latitude.onChange}
              value={this.state.latitude}
            />
          </Form.Field>
          <Form.Field>
            <label>Longitude</label>
            <input
              onChange={this.longitude.onChange}
              value={this.state.longitude}
            />
          </Form.Field>

          <Button
            loading={this.state.creating}
            content="Simpan Tempat Tinggal"
            labelPosition="right"
            icon="checkmark"
            positive
            onClick={this.onSubmit}
          />
        </Form>

        {this.state.errorMessage && <Message negative={true}>{this.state.errorMessage}</Message>}
        {this.state.successMessage && (
          <Message positive={true}>{this.state.successMessage}</Message>
        )}
      </React.Fragment>
    );
  }
}

const mapStateToProps = (states: TMainReduxStates) => ({
  conversation: states.reducerConversation,
});

export default connect(mapStateToProps)(PlaceToStayCapture);

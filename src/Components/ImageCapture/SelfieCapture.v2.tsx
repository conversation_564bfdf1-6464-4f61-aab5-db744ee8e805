import React, { Component } from 'react';
import { Button, Form, Message } from 'semantic-ui-react';
import { ReactCropperElement } from 'react-cropper';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { connect } from 'react-redux';
import moment from 'moment';
import { AxiosError } from 'axios';
import { mainApiServices } from '../../services/MainApiServices';
import ClientEntity from '../../entities/ClientEntity';
import { getDoc } from 'firebase/firestore';

interface ISelfieCaptureFields {
  image: Blob | null;
}

interface ISelfieCaptureStates extends ISelfieCaptureFields {
  creating: boolean;
  loading: boolean;

  errorMessage: string | null;
  successMessage: string | null;

  currentImage: string | null;
}

export interface ISelfieCaptureProps {
  cropCanvas: React.RefObject<ReactCropperElement>;
  conversation: TMainReduxStates['reducerConversation'];
}

class SelfieCapture extends Component<ISelfieCaptureProps, ISelfieCaptureStates> {
  constructor(props: ISelfieCaptureProps) {
    super(props);

    this.state = {
      image: null,

      creating: false,
      loading: false,
      errorMessage: null,
      successMessage: null,

      currentImage: null,
    };
  }

  onSubmit = async () => {
    if (this.state.creating) return;
    this.setState(
      {
        creating: true,
        errorMessage: null,
        successMessage: null,
      },
      async () => {
        let currentState: ISelfieCaptureStates = { ...this.state };
        const blob = await new Promise<Blob | null>((resolve) => {
          if (this.props.cropCanvas.current) {
            this.props.cropCanvas.current?.cropper
              .crop()
              .getCroppedCanvas()
              .toBlob((blob) => {
                if (blob) resolve(blob);
              });
          } else {
            resolve(null);
          }
        });
        try {
          await mainApiServices.setSelfieV2(
            {
              image: blob,
            },
            this.props.conversation.chatRoom!.clients[0],
          );
          currentState.successMessage = 'Berhasil memperbarui Selfie ' + moment().format('LLLL');
        } catch (e: any) {
          const error: AxiosError = e as any;
          currentState.errorMessage = error.message;
        }
        currentState.creating = false;

        this.setState({
          ...currentState,
        });
      },
    );
  };

  load = async () => {
    this.setState({
      loading: true,
    });

    let currentState: ISelfieCaptureStates = { ...this.state };

    const clientRef = this.props.conversation.chatRoom?.clients[0];
    if (!clientRef) return;

    const getClient = await getDoc(clientRef.withConverter(ClientEntity.converter));
    const dataClient = getClient.data()!;

    if (dataClient.details.selfie?.selfieImage) {
      currentState.currentImage = dataClient.details.selfie.selfieImage;
    }

    this.setState({
      ...currentState,
      loading: false,
    });
  };

  componentDidMount() {
    this.load();
  }

  render() {
    if (this.state.loading) return <div>Memuat...</div>;

    return (
      <React.Fragment>
        <Form>
          {this.state.currentImage && (
            <Form.Field>
              <a
                href={this.state.currentImage}
                style={{ fontWeight: 'bolder', color: 'blue' }}
                target={'_blank'}
                rel="noreferrer"
              >
                Lihat Foto Tersimpan
              </a>
            </Form.Field>
          )}

          <Button
            loading={this.state.creating}
            content="Simpan Selfie"
            labelPosition="right"
            icon="checkmark"
            positive
            onClick={this.onSubmit}
          />
        </Form>

        {this.state.errorMessage && <Message negative={true}>{this.state.errorMessage}</Message>}
        {this.state.successMessage && (
          <Message positive={true}>{this.state.successMessage}</Message>
        )}
      </React.Fragment>
    );
  }
}

const mapStateToProps = (states: TMainReduxStates) => ({
  conversation: states.reducerConversation,
});

export default connect(mapStateToProps)(SelfieCapture);

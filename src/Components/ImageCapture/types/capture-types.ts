import CaptureDocumentHelper from '../../../helpers/captureDocumentHelper';

export enum ImageCaptureTypes {
  ID_CARD_OWNER = 'ID_CARD_OWNER',
  ID_CARD_GUARANTOR = 'ID_CARD_GUARANTOR',
  ID_CARD_GUARANTOR_SPOUSE = 'ID_CARD_GUARANTOR_SPOUSE',
  ID_CARD_ORDER_MAKER = 'ID_CARD_ORDER_MAKER',
  FAMILY_REGISTER = 'FAMILY_REGISTER',
  INCOME_DOC = 'INCOME_DOC',
  PLACE_TO_STAY = 'PLACE_TO_STAY',
  PLACE_OF_BUSINESS = 'PLACE_OF_BUSINESS',
  BUSINESS_DOCUMENT = 'BUSINESS_DOCUMENT',
  SELFIE = 'SELFIE',
  OTHER_DOCUMENT = 'OTHER_DOCUMENT',
}

export interface DocumentList {
  document_path: string;
  capture_types: ImageCaptureTypes;
  name: string;
}

export const documents: DocumentList[] = [
  {
    document_path: 'id_card_owner',
    capture_types: ImageCaptureTypes.ID_CARD_OWNER,
    name: CaptureDocumentHelper.toIndonesianFormatter(ImageCaptureTypes.ID_CARD_OWNER),
  },
  {
    document_path: 'id_card_guarantor',
    capture_types: ImageCaptureTypes.ID_CARD_GUARANTOR,
    name: CaptureDocumentHelper.toIndonesianFormatter(ImageCaptureTypes.ID_CARD_GUARANTOR),
  },
  {
    document_path: 'id_card_spouse_guarantor',
    capture_types: ImageCaptureTypes.ID_CARD_GUARANTOR_SPOUSE,
    name: CaptureDocumentHelper.toIndonesianFormatter(ImageCaptureTypes.ID_CARD_GUARANTOR_SPOUSE),
  },
  {
    document_path: 'id_card_order_maker',
    capture_types: ImageCaptureTypes.ID_CARD_ORDER_MAKER,
    name: CaptureDocumentHelper.toIndonesianFormatter(ImageCaptureTypes.ID_CARD_ORDER_MAKER),
  },
  {
    document_path: 'family_register',
    capture_types: ImageCaptureTypes.FAMILY_REGISTER,
    name: CaptureDocumentHelper.toIndonesianFormatter(ImageCaptureTypes.FAMILY_REGISTER),
  },
  {
    document_path: 'income_document',
    capture_types: ImageCaptureTypes.INCOME_DOC,
    name: CaptureDocumentHelper.toIndonesianFormatter(ImageCaptureTypes.INCOME_DOC),
  },
  {
    document_path: 'business_document',
    capture_types: ImageCaptureTypes.BUSINESS_DOCUMENT,
    name: CaptureDocumentHelper.toIndonesianFormatter(ImageCaptureTypes.BUSINESS_DOCUMENT),
  },
  {
    document_path: 'place_to_stay',
    capture_types: ImageCaptureTypes.PLACE_TO_STAY,
    name: CaptureDocumentHelper.toIndonesianFormatter(ImageCaptureTypes.PLACE_TO_STAY),
  },
  {
    document_path: 'place_of_business',
    capture_types: ImageCaptureTypes.PLACE_OF_BUSINESS,
    name: CaptureDocumentHelper.toIndonesianFormatter(ImageCaptureTypes.PLACE_OF_BUSINESS),
  },
  {
    document_path: 'selfie',
    capture_types: ImageCaptureTypes.SELFIE,
    name: CaptureDocumentHelper.toIndonesianFormatter(ImageCaptureTypes.SELFIE),
  },
  {
    document_path: 'other_document',
    capture_types: ImageCaptureTypes.OTHER_DOCUMENT,
    name: CaptureDocumentHelper.toIndonesianFormatter(ImageCaptureTypes.OTHER_DOCUMENT),
  },
];

export type TCaptureTarget =
  | 'idCardOwner'
  | 'idCardOrderMaker'
  | 'idCardGuarantor'
  | 'idCardGuarantorSpouse'
  | 'familyRegister'
  | 'selfie';

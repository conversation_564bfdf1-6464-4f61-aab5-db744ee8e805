import Lightbox from 'yet-another-react-lightbox';
import 'yet-another-react-lightbox/styles.css';
import { useSelector, useDispatch } from 'react-redux';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { closeLightbox } from '../../redux/lightbox/lightbox.slice';
import './ImageLightbox.css';

const ImageLightbox = () => {
  const dispatch = useDispatch();
  const { isOpen, imageUrl } = useSelector((state: TMainReduxStates) => state.lightbox);

  const handleClose = () => {
    dispatch(closeLightbox());
  };

  return (
    <div className="custom-lightbox-wrapper">
      <Lightbox
        open={isOpen}
        close={handleClose}
        slides={imageUrl ? [{ src: imageUrl }] : []}
        carousel={{
          finite: false,
        }}
        render={{
          buttonPrev: () => null,
          buttonNext: () => null,
        }}
        styles={{
          container: { backgroundColor: 'rgba(0, 0, 0, 0.9)' },
          root: {
            '--yarl__color_backdrop': 'rgba(0, 0, 0, 0.9)',
            '--yarl__container_z_index': '9999',
          },
        }}
      />
    </div>
  );
};

export default ImageLightbox;

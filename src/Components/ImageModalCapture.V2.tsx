import React, { Component } from 'react';
import {
  Button,
  Container,
  DropdownItemProps,
  Form,
  Grid,
  Message,
  Modal,
  Segment,
  Select,
} from 'semantic-ui-react';

import Cropper, { ReactCropperElement } from 'react-cropper';
import 'cropperjs/dist/cropper.css';
import { DropdownProps } from 'semantic-ui-react/dist/commonjs/modules/Dropdown/Dropdown';
import { TMainReduxStates } from '../redux/types/redux-types';
import { connect } from 'react-redux';
import modalSetImageAsSlice from '../redux/modal-capture-image/modalSetImageAs.slice';
import { mainStore } from '../redux/reducers';
import IdCardCaptureV2 from './ImageCapture/IdCardCapture.v2';
import FamilyRegisterCaptureV2 from './ImageCapture/FamilyRegisterCapture.v2';
import SelfieCaptureV2 from './ImageCapture/SelfieCapture.v2';

export interface IImageModalProps {
  modal: TMainReduxStates['modalSetImageAs'];
}

class ImageModalCaptureV2 extends Component<IImageModalProps> {
  private refCanvas = React.createRef<ReactCropperElement>();

  private optionCaptureTypes = {
    options: (): DropdownItemProps[] => {
      return [
        {
          value: 'idCardOwner',
          text: 'KTP Pemilik',
        },
        {
          value: 'idCardGuarantor',
          text: 'KTP Penjamin',
        },
        {
          value: 'idCardOrderMaker',
          text: 'KTP Pemesan',
        },
        {
          value: 'idCardGuarantorSpouse',
          text: 'KTP Pasangan dari Penjamin',
        },
        {
          value: 'familyRegister',
          text: 'Kartu Keluarga',
        },
        {
          value: 'selfie',
          text: 'Foto Selfie',
        },
      ];
    },
    onChange: async (event: React.SyntheticEvent<HTMLElement>, data: DropdownProps) => {
      mainStore.dispatch(modalSetImageAsSlice.actions.setImageCaptureTypes(data.value as any));
    },

    load: async () => {},
  };

  private cropper = {
    onRotate: (degree: number) => this.refCanvas.current?.cropper.rotate(degree),
  };

  public close = () => {
    mainStore.dispatch(modalSetImageAsSlice.actions.close());
  };

  render() {
    return (
      <Modal open={this.props.modal.open}>
        <Modal.Header>
          {this.props.modal.type === 'SET' ? 'Jadikan Gambar Sebagai Dokumen' : 'Perbarui Dokumen'}
        </Modal.Header>
        <Modal.Content>
          <Grid divided={'vertically'}>
            <Grid.Row>
              {this.props.modal.type === 'SET' && (
                <Grid.Column
                  widescreen={16}
                  largeScreen={16}
                  mobile={16}
                  tablet={16}
                >
                  {this.props.modal.urlImageToSet && (
                    <Segment>
                      <Cropper
                        src={this.props.modal.urlImageToSet}
                        style={{
                          height: 400,
                          width: '100%',
                          marginBottom: '16px',
                        }}
                        initialAspectRatio={16 / 9}
                        guides={false}
                        ref={this.refCanvas}
                        rotatable={true}
                      />

                      <Container
                        textAlign={'center'}
                        fluid={true}
                      >
                        <Button
                          icon={'redo alternate'}
                          onClick={() => this.cropper.onRotate(90)}
                        />
                        <Button
                          icon={'undo alternate'}
                          onClick={() => this.cropper.onRotate(-90)}
                        />
                      </Container>
                    </Segment>
                  )}
                </Grid.Column>
              )}
              <Grid.Column
                widescreen={16}
                largeScreen={16}
                table={16}
                mobile={16}
              >
                <Form>
                  <Form.Field>
                    <label>Ini adalah gambar untuk dokumen</label>
                    <Select
                      placeholder={'Pilih Target Gambar'}
                      options={this.optionCaptureTypes.options()}
                      onChange={this.optionCaptureTypes.onChange}
                      value={this.props.modal.documentTarget || undefined}
                    />
                  </Form.Field>
                </Form>
                <Segment>
                  {!this.props.modal.documentTarget && (
                    <Message color={'yellow'}>Pilih Target Gambar di Atas</Message>
                  )}

                  {this.props.modal.documentTarget &&
                    [
                      'idCardOwner',
                      'idCardGuarantor',
                      'idCardOrderMaker',
                      'idCardGuarantorSpouse',
                    ].indexOf(this.props.modal.documentTarget!) >= 0 && (
                      <IdCardCaptureV2 cropCanvas={this.refCanvas} />
                    )}

                  {this.props.modal.documentTarget === 'familyRegister' && (
                    <FamilyRegisterCaptureV2 cropCanvas={this.refCanvas} />
                  )}

                  {this.props.modal.documentTarget === 'selfie' && (
                    <SelfieCaptureV2 cropCanvas={this.refCanvas} />
                  )}
                </Segment>
              </Grid.Column>
            </Grid.Row>
          </Grid>
        </Modal.Content>
        <Modal.Actions>
          <Button
            color="black"
            onClick={this.close}
          >
            Tutup
          </Button>
        </Modal.Actions>
      </Modal>
    );
  }
}

const mapStateToProps = (reducer: TMainReduxStates) => {
  return {
    modal: reducer.modalSetImageAs,
    conversation: reducer.reducerConversation,
  };
};

export default connect(mapStateToProps)(ImageModalCaptureV2);

import React, { useMemo, useCallback } from 'react';
import { Dropdown, DropdownItemProps } from 'semantic-ui-react';
import { DropdownProps } from 'semantic-ui-react/dist/commonjs/modules/Dropdown/Dropdown';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { useSelector } from 'react-redux';

export interface LabelDropdownProps
  extends Pick<DropdownProps, 'onChange' | 'value' | 'defaultValue' | 'loading' | 'disabled'> {
  noLabelText?: string;
  showAllLabelsOption?: boolean;
}

const LabelDropdown: React.FC<LabelDropdownProps> = ({
  onChange,
  value,
  defaultValue,
  loading,
  disabled,
  noLabelText,
}) => {
  // Ambil data label dari redux menggunakan useSelector
  const availableLabels = useSelector((state: TMainReduxStates) => state.reducerAvailableLabels);

  // Jika tidak diberikan, gunakan teks default "Belum Ada Label"
  const noLabelTextValue = noLabelText || 'Belum Ada Label';

  // Menggunakan useMemo untuk mengoptimalkan pembuatan opsi dropdown
  const dropdownOptions: DropdownItemProps[] = useMemo(() => {
    const options: DropdownItemProps[] = [];

    options.push({
      text: noLabelTextValue,
      value: 'NO_LABEL',
      key: 'NO_LABEL',
    });

    availableLabels.labels.forEach((label) => {
      options.push({
        text: label.name,
        value: label.ref?.id,
        key: label.ref?.id,
      });
    });

    return options;
  }, [availableLabels, noLabelTextValue]);

  // Menggunakan useCallback untuk mendefinisikan handler onChange
  const handleChange = useCallback(
    (event: React.SyntheticEvent<HTMLElement>, data: DropdownProps) => {
      onChange?.(event, data);
    },
    [onChange],
  );

  // Tampilkan pesan loading jika masih fetching label
  if (availableLabels.fetching) return <span>Fetching Labels...</span>;

  return (
    <Dropdown
      options={dropdownOptions}
      value={value || ''}
      defaultValue={defaultValue}
      disabled={disabled}
      loading={loading}
      onChange={handleChange}
      placeholder="Pilih Label"
      search
      selection
      className="!w-full !text-sm !border !border-gray-200 !rounded-lg hover:!border-blue-400 
					  focus:!ring-2 focus:!ring-blue-500 focus:!border-blue-500 !bg-white !shadow-sm
					  transition-all duration-200 [&_.menu]:!mt-2 [&_.menu]:!py-1.5 [&_.menu]:!rounded-lg 
					  [&_.item]:!px-3 [&_.item]:!py-2 [&_.item]:!text-sm [&_.text]:!truncate"
      clearable
      compact
    />
  );
};

export default LabelDropdown;

import { Form, Input } from 'semantic-ui-react';
import LicensePlateSelectCityCode, {
  ILicensePlateRegistrationCode,
} from './LicensePlateSelectCityCode';

interface Props {
  registrationCode?: string;
  registrationCodeOnChange?: (value: ILicensePlateRegistrationCode) => void;

  number?: string;
  numberOnChange?: (text: string) => void;

  series?: string;
  seriesOnChange?: (text: string) => void;
}

const InputLicensePlate = (props: Props) => {
  return (
    <div>
      <label
        className={'font-bold'}
        style={{ fontSize: '.92857143em' }}
      >
        Plat Nomor <span className={'text-red-500'}>*</span>
      </label>
      <Form.Group widths={'equal'}>
        <Form.Field>
          <LicensePlateSelectCityCode
            value={props.registrationCode}
            onChange={(event) => props.registrationCodeOnChange?.(event)}
          />
        </Form.Field>
        <Form.Field>
          <Input
            placeholder={'1234'}
            type={'number'}
            value={props.number}
            onChange={(event) => props.numberOnChange?.(event.target.value)}
          />
        </Form.Field>
        <Form.Field>
          <Input
            placeholder={'XYZ'}
            value={props.series}
            onChange={(event) => props.seriesOnChange?.(event.target.value)}
          />
        </Form.Field>
      </Form.Group>
    </div>
  );
};

export default InputLicensePlate;

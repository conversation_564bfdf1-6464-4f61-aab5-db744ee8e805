import { Select } from 'semantic-ui-react';
import { useEffect, useState } from 'react';
import getLicensePlateCodes from '../../services/getLicensePlateCodes';

interface Props {
  onChange?: (c: ILicensePlateRegistrationCode) => void;
  value?: string;
}

export interface ILicensePlateRegistrationCode {
  code: string;
  location: string;
  location_codes: string[];
}

const LicensePlateSelectCityCode = (props: Props) => {
  const [codes, setCodes] = useState<ILicensePlateRegistrationCode[]>([]);

  const fetch = async () => {
    try {
      const get = await getLicensePlateCodes();
      setCodes(get);
    } catch (error) {
      setCodes([]);
    }
  };

  useEffect(() => {
    fetch();
  }, []);

  const onChange = (v: string) => {
    const find = codes.find((c) => c.code === v);
    if (!find) return;
    props.onChange?.(find || null);
  };

  return (
    <Select
      options={codes.map((c) => {
        return {
          text: `${c.code}`,
          value: c.code,
        };
      })}
      onChange={(event, data) => onChange(data.value as string)}
      value={props.value || undefined}
      placeholder={'B'}
      search={true}
    />
  );
};

export default LicensePlateSelectCityCode;

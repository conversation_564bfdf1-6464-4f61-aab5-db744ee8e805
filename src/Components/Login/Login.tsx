import React, { Component } from 'react';
import { Button, Form, Icon, Message } from 'semantic-ui-react';
import { LoginFields, LoginProps, LoginStates } from './types/login-types';
import authServices from '../../services/firebase/AuthServices';
import { myHistory } from '../../helpers/history';

class Login extends Component<LoginProps, LoginStates> {
  constructor(props: LoginProps) {
    super(props);
    this.state = {
      email: '',
      password: '',
      signing: false,
      errorMessage: null,
      success: false,
    };
  }

  onFieldChange = (target: keyof LoginFields, value: string) => {
    let currentState: LoginStates = { ...this.state };
    switch (target) {
      case 'email':
        currentState.email = value;
        break;
      case 'password':
        currentState.password = value;
        break;
    }
    // Clear error when user types
    currentState.errorMessage = null;
    this.setState({ ...currentState });
  };

  onSubmit = async () => {
    this.setState({ signing: true, errorMessage: null });
    let currentState: LoginStates = { ...this.state };
    const login = await authServices.login(this.state.email, this.state.password);

    if (!login.success) {
      currentState.errorMessage =
        login.message ?? 'Terjadi kesalahan saat login. Silakan coba lagi.';
    } else {
      myHistory.push('/');
    }

    currentState.signing = false;
    this.setState({ ...currentState });
  };

  handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !this.state.signing) {
      this.onSubmit();
    }
  };

  render() {
    const { email, password, signing, errorMessage } = this.state;

    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-blue-100 flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          <div className="bg-white rounded-2xl shadow-xl p-8">
            {/* Header */}
            <div className="text-center mb-8">
              <div className="w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Icon
                  name="comments"
                  size="large"
                  className="text-blue-600"
                />
              </div>
              <h1 className="text-2xl font-bold text-gray-800">Selamat Datang Kembali</h1>
              <p className="text-gray-500 mt-2">Masuk ke akun Anda untuk melanjutkan</p>
            </div>

            {/* Error Message */}
            {errorMessage && !signing && (
              <div className="mb-6 animate-shake">
                <div className="bg-red-50 border-l-4 border-red-500 p-4 rounded-lg">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <Icon
                        name="warning sign"
                        className="text-red-500"
                      />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-red-800">Gagal Masuk</h3>
                      <div className="mt-2 text-sm text-red-700">{errorMessage}</div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Form */}
            <Form
              size="large"
              onKeyPress={this.handleKeyPress}
            >
              <div className="space-y-4">
                <Form.Input
                  fluid
                  icon={{
                    name: 'mail',
                    color: email ? 'blue' : 'grey',
                    className: '!opacity-70',
                  }}
                  iconPosition="left"
                  placeholder="Alamat Email"
                  type="email"
                  value={email}
                  onChange={(e, data) => this.onFieldChange('email', data.value)}
                  className={`
                                        !bg-white !border !rounded-lg transition-all duration-200
                                        ${
                                          errorMessage
                                            ? '!border-red-300 hover:!border-red-400 focus-within:!border-red-500'
                                            : '!border-gray-200 hover:!border-blue-400 focus-within:!border-blue-500'
                                        }
                                    `}
                  error={!!errorMessage}
                  autoComplete="username"
                />

                <Form.Input
                  fluid
                  icon={{
                    name: 'lock',
                    color: password ? 'blue' : 'grey',
                    className: '!opacity-70',
                  }}
                  iconPosition="left"
                  placeholder="Password"
                  type="password"
                  value={password}
                  onChange={(e, data) => this.onFieldChange('password', data.value)}
                  className={`
                                        !bg-white !border !rounded-lg transition-all duration-200
                                        ${
                                          errorMessage
                                            ? '!border-red-300 hover:!border-red-400 focus-within:!border-red-500'
                                            : '!border-gray-200 hover:!border-blue-400 focus-within:!border-blue-500'
                                        }
                                    `}
                  error={!!errorMessage}
                  autoComplete="current-password"
                />

                <Button
                  fluid
                  loading={signing}
                  disabled={!email || !password}
                  onClick={this.onSubmit}
                  className={`
                                        !h-11 !rounded-lg !border-0 transition-all duration-200
                                        ${
                                          !email || !password
                                            ? '!bg-gray-100 !text-gray-400 cursor-not-allowed'
                                            : '!bg-blue-600 hover:!bg-blue-700 !text-white'
                                        }
                                    `}
                >
                  {signing ? 'Sedang Masuk...' : 'Masuk'}
                </Button>
              </div>
            </Form>
          </div>

          {/* Version Info */}
          <div className="text-center mt-6">
            <span className="text-sm text-gray-500">Versi {process.env.REACT_APP_WEB_VERSION}</span>
          </div>
        </div>
      </div>
    );
  }
}

export default Login;

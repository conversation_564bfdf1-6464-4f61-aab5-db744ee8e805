import React, { Component } from 'react';
import { <PERSON><PERSON>, I<PERSON> } from 'semantic-ui-react';
import ClientInformationWrapper from '../NewClientInformation/ClientInformationWrapper';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { ThunkDispatch } from 'redux-thunk';
import { closeInfoSegment } from '../../redux/info-client-segment/action-info-client-segment';
import { compose } from 'redux';
import { connect } from 'react-redux';
import customizeMediaQuery, { CustomizeMediaQuery } from '../hoc/CustomizeMediaQuery';
import { IConversationStates } from '../../redux/types/conversation-type';

interface IActiveClientInfoSegment extends CustomizeMediaQuery {
  conversations: IConversationStates;
  clientInfoSegment: TMainReduxStates['reducerInfoClientSegment'];
  closeInfoSegment: () => void;
}

class ActiveClientInfoSegment extends Component<IActiveClientInfoSegment> {
  renderContent = () => {
    const { fetching, chatRoom } = this.props.conversations;
    if (fetching || !chatRoom) return null;

    return <ClientInformationWrapper />;
  };

  renderTitle = () => (
    <div className="flex items-center">
      <span className="font-semibold text-gray-800">Informasi Klien</span>
    </div>
  );

  renderPanel = (widthClass: string, hasCloseButton: boolean) => (
    <div className={`${widthClass} h-full flex flex-col bg-white border-l border-gray-200`}>
      <div className="bg-white border-b border-gray-200">
        <div className="h-[60px] flex items-center justify-between px-4">
          {this.renderTitle()}
          {hasCloseButton && (
            <Button
              icon
              basic
              size="tiny"
              className="!bg-transparent hover:!bg-gray-50 !p-1.5"
              onClick={this.props.closeInfoSegment}
            >
              <Icon
                name="close"
                className="!m-0"
              />
            </Button>
          )}
        </div>
      </div>
      <div className="flex-1 overflow-y-auto bg-gray-50">{this.renderContent()}</div>
    </div>
  );

  render() {
    const { isTabletOrMobile } = this.props.mediaQueries;
    const { visible } = this.props.clientInfoSegment;
    const { chatRoom } = this.props.conversations;

    if (!chatRoom) return null;

    const shouldShowMobile = isTabletOrMobile && visible;
    const shouldShowDesktop = !isTabletOrMobile;

    return (
      <React.Fragment>
        {shouldShowMobile && this.renderPanel('w-full', true)}
        {shouldShowDesktop && this.renderPanel('w-1/4', false)}
      </React.Fragment>
    );
  }
}

const mapStateToProps = (states: TMainReduxStates) => ({
  conversations: states.reducerConversation,
  clientInfoSegment: states.reducerInfoClientSegment,
});

const mapDispatchToProps = (dispatch: ThunkDispatch<TMainReduxStates, void, any>) => ({
  closeInfoSegment: () => dispatch(closeInfoSegment()),
});

export default compose<React.ComponentType>(
  connect(mapStateToProps, mapDispatchToProps),
  customizeMediaQuery,
)(ActiveClientInfoSegment);

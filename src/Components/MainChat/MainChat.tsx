import React from 'react';
import { TMainReduxStates } from '../../redux/types/redux-types';
import HeaderBar from '../Header/HeaderBar';
import { IMainChatProps, IMainChatStates } from './types/main-chat-types';
import 'firebase/firestore';
import { fetchTemplateVer2 } from '../../redux/available-templates/action-available-template';
import { fetchLabels } from '../../redux/available-labels/action-available-labels';
import { fetchDepartments } from '../../redux/available-departments/action-available-departments';
import { ThunkDispatch } from 'redux-thunk';
import { compose } from 'redux';
import withAuth from '../hoc/withAuth';
import { connect } from 'react-redux';
import ModalSendCreditSimulation from '../ModalSendCreditSimulation';
import customizeMediaQuery from '../hoc/CustomizeMediaQuery';
import RecentClientChat from './RecentClientChat';
import ActiveConversation from './ActiveConversation';
import ActiveClientInfoSegment from './ActiveClientInfoSegment';
import { DocumentReference, updateDoc } from 'firebase/firestore';
import SettingTemplate from '../Template/SettingTemplateModal';
import CreateTemplateModal from '../Template/CreateTemplateModal';
import ModalInitMessage from '../ModalInitMessage';
import ModalSendPriceList from '../ModalSendPriceList/ModalSendPriceList';
import ModalSendPriceListSimple from '../ModalSendPriceListSimple/ModalSendPriceListSimple';
import ModalClientFinalDecision from '../ClientFinalDecission/ModalClientFinalDecision';
import { onLogout, poolingClass } from '../../helpers/history';
import moment from 'moment';
import IdleTimerComponent from '../IdleTimer/IdleTimerComponent';
import ModalListAdmin from '../AdminList/ModalListAdmin';
import ModalOfferAcquisition from '../ModalOfferAcquisition';
import ModalAddNewConversationFlow from '../ConversationFlow/ModalAddNewConversationFlow';
import ModalUpdateConversationFlow from '../ConversationFlow/ModalUpdateConversationFlow';
import ModalListConversationFlow from '../ConversationFlow/ModalListConversationFlow';
import ImageModalCaptureV2 from '../ImageModalCapture.V2';
import ModalUpdatePhoneNumber from '../ModalUpdatePhoneNumber/ModalUpdatePhoneNumber';
import ModalSetPhoneNumberAs from '../ModalUpdatePhoneNumber/ModalSetPhoneNumberAs';
import ModalAcquisitionAsLeads from '../ModalAcquisitionAsLeads';
import ModalSelectPlanAd from '../Trimobi/BuyAdPlanTrimobi/ModalBuyAdPlan';
import ModalUpgradePlanTrimobi from '../Trimobi/UpgradePlanTrimobi/ModalUpgradePlanTrimobi';
import ModalCreateAdTrimobi from '../Trimobi/CreateAdTrimobi/ModalCreateAdTrimobi';
import ModalTrimobiMenu from '../Trimobi/Menu/ModalTrimobiMenu';
import ModalUploadMediaArt from '../ModalUploadMediaArt/ModalUploadAmartaArt';
import ModalSendToFreeLeads from '../ModalSendToFreeLeads.v2';
import ModalChatDebug from '../ModalChatDebug/ModalChatDebug';

class MainChat extends React.Component<IMainChatProps, IMainChatStates> {
  // private refIdleTimer: IdleTimer | null = null;

  private idleDetector = {
    poolingCommand: () => {
      if (this.props.admin.adminSession?.ref) {
        const lastHeartbeat = moment();
        const nextExpired = moment().add(
          parseInt(process.env.REACT_APP_TIMEOUT_IDLE as any),
          'seconds',
        );
        const adminSessionRef = this.props.admin.adminSession?.ref;
        if (adminSessionRef) {
          updateDoc(adminSessionRef, {
            last_heartbeat: lastHeartbeat.toDate(),
            auto_end_session_at: nextExpired.toDate(),
          })
            .then()
            .catch(() => {});
        }
      }
    },
    onIdle: async () => {
      await onLogout();
    },
    onReturn: () => {
      this.idleDetector.poolingCommand();
      this.idleDetector.setPooling();
    },
    setPooling: () => {
      poolingClass.setPool(setInterval(this.idleDetector.poolingCommand, 10000));
    },
  };

  constructor(props: IMainChatProps) {
    super(props);
    this.state = {};
  }

  async componentDidMount() {
    if (this.props.admin.admin) {
      this.props.fetchTemplates(this.props.admin.admin.doc_project);
      this.props.fetchLabels(this.props.admin.admin.doc_project);
      this.props.fetchDepartments(this.props.admin.admin.doc_project);
    }

    this.idleDetector.setPooling();
  }

  render() {
    if (!this.props.project.project && !this.props.admin.admin) {
      return <div />;
    }
    return (
      <>
        <IdleTimerComponent
          onIdle={this.idleDetector.onIdle}
          onActive={this.idleDetector.onReturn}
          timeout={parseInt(process.env.REACT_APP_TIMEOUT_IDLE as any) * 1000}
          crossTab={true}
        />
        <div className="flex flex-col w-screen h-[100dvh]">
          <HeaderBar />
          <div className={'main-container-chat flex flex-row w-screen relative'}>
            <RecentClientChat />
            <ActiveConversation />
            <ActiveClientInfoSegment />
          </div>
        </div>
        <ImageModalCaptureV2 />
        <ModalSendCreditSimulation />
        {this.props.modalSendFreeLeads.open && <ModalSendToFreeLeads />}
        <SettingTemplate />
        <CreateTemplateModal />
        {this.props.modalInitMessage.open && <ModalInitMessage />}
        {this.props.modalSendPriceList.open && <ModalSendPriceList />}
        {this.props.modalSendPriceListSimple.open && <ModalSendPriceListSimple />}
        <ModalAddNewConversationFlow />
        <ModalUpdateConversationFlow />
        <ModalListConversationFlow />
        <ModalSetPhoneNumberAs />
        <ModalAcquisitionAsLeads />
        {this.props.modalCustomerFinalDecision.modalOpen && <ModalClientFinalDecision />}
        <ModalListAdmin />
        <ModalOfferAcquisition />
        <ModalUpdatePhoneNumber />
        <ModalCreateAdTrimobi />
        <ModalSelectPlanAd />
        <ModalUpgradePlanTrimobi />
        <ModalTrimobiMenu />
        <ModalUploadMediaArt />
        <ModalChatDebug />
      </>
    );
  }
}

const mapStateToProps = (states: TMainReduxStates) => ({
  admin: states.reducerAdmin,
  conversations: states.reducerConversation,
  project: states.reducerProject,
  modalSendFreeLeads: states.modalSendToFreeLeads,
  modalInitMessage: states.modalInitMessageReducer,
  modalSendPriceList: states.modalSendPriceList,
  modalSendPriceListSimple: states.modalSendPriceListSimple,
  modalCustomerFinalDecision: states.modalUpdateCustomerFinalDecision,
  modalSetImageAs: states.modalSetImageAs,
  modalAmartaArt: states.modalAmartaArt,
});

const mapDispatchToProps = (dispatch: ThunkDispatch<TMainReduxStates, void, any>) => ({
  fetchTemplates: (project: DocumentReference) => dispatch(fetchTemplateVer2(project)),
  fetchLabels: (project: DocumentReference) => dispatch(fetchLabels(project)),
  fetchDepartments: (project: DocumentReference, callback?: () => void) =>
    dispatch(fetchDepartments(project, callback)),
});

export default compose<React.ComponentType>(
  withAuth,
  connect(mapStateToProps, mapDispatchToProps),
  customizeMediaQuery,
)(MainChat);

import React from 'react';
import { Grid } from 'semantic-ui-react';
import ChatRoomList from '../ContactList/ContactList';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { compose } from 'redux';
import { connect } from 'react-redux';
import customizeMediaQuery, { CustomizeMediaQuery } from '../hoc/CustomizeMediaQuery';
import { useSelector, useDispatch } from 'react-redux';

interface IRecentClientChatProps extends CustomizeMediaQuery {}

const RecentClientChat: React.FC<IRecentClientChatProps> = (props) => {
  const conversations = useSelector((state: TMainReduxStates) => state.reducerConversation);

  if (props.mediaQueries.isTabletOrMobile) {
    if (!conversations.chatRoom) {
      return (
        <div className={'main-column-recent-chat w-full'}>
          <ChatRoomList />
        </div>
      );
    } else {
      return <div />;
    }
  } else {
    return (
      <div className={'w-1/4 flex flex-col h-full'}>
        <ChatRoomList />
      </div>
    );
  }
};

export default compose<React.ComponentType>(connect(null), customizeMediaQuery)(RecentClientChat);

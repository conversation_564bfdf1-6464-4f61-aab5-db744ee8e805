import { IConversationStates } from '../../../redux/types/conversation-type';
import { TMainReduxStates } from '../../../redux/types/redux-types';
import { CustomizeMediaQuery } from '../../hoc/CustomizeMediaQuery';
import { DocumentReference } from 'firebase/firestore';

export interface IMainChatReduxDispatchProps {
  fetchTemplates: (project: DocumentReference) => void;
  fetchDepartments: (project: DocumentReference, callback?: () => void) => void;
  fetchLabels: (project: DocumentReference) => void;
}

export interface IMainChatReduxStatesProps {
  conversations: IConversationStates;
  admin: TMainReduxStates['reducerAdmin'];
  project: TMainReduxStates['reducerProject'];
  modalSendFreeLeads: TMainReduxStates['modalSendToFreeLeads'];
  modalInitMessage: TMainReduxStates['modalInitMessageReducer'];
  modalSendPriceList: TMainReduxStates['modalSendPriceList'];
  modalSendPriceListSimple: TMainReduxStates['modalSendPriceListSimple'];
  modalCustomerFinalDecision: TMainReduxStates['modalUpdateCustomerFinalDecision'];
  modalSetImageAs: TMainReduxStates['modalSetImageAs'];
  modalAmartaArt: TMainReduxStates['modalAmartaArt'];
}

export interface IMainChatProps
  extends IMainChatReduxDispatchProps,
    IMainChatReduxStatesProps,
    CustomizeMediaQuery {}

export interface IMainChatStates {}

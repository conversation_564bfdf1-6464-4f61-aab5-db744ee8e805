import React, { Component } from 'react';
import { Button, Message, Modal } from 'semantic-ui-react';
import { TMainReduxStates } from '../redux/types/redux-types';
import { connect } from 'react-redux';
import { mainApiServices } from '../services/MainApiServices';
import { AxiosError } from 'axios';
import { mainStore } from '../redux/reducers';
import modalAcquisitionAsLeads from '../redux/modal-acquisition-as-leads/modalAcquisitionAsLeads.slice';
import { fetchCustomerThunk } from '../redux/customerInfo/customerInfoSlice';

interface Props {
  modal: TMainReduxStates['modalAcquisitionAsLeads'];
  customer: TMainReduxStates['customerReducer'];
  project: TMainReduxStates['reducerProject'];
  chatRoom: TMainReduxStates['reducerConversation'];
}

class ModalAcquisitionAsLeads extends Component<Props> {
  onClose = () => {
    mainStore.dispatch(modalAcquisitionAsLeads.actions.close());
  };

  onSubmit = async () => {
    const { customer, chatRoom, project } = this.props;
    if (!customer.ref || !chatRoom.chatRoom || !project.project) return;

    mainStore.dispatch(modalAcquisitionAsLeads.actions.setSubmittingStatus());

    try {
      await mainApiServices.adminAcquisitionAsLeads({
        clientRefPath: customer.ref.path,
        organization: 'amartahonda',
        chatRoomRefPath: chatRoom.chatRoom.ref.path,
        projectRefPath: project.project.ref.path,
      });

      mainStore.dispatch(modalAcquisitionAsLeads.actions.close());

      mainStore.dispatch(
        fetchCustomerThunk({
          clientDocRef: customer.ref,
        }) as any,
      );
    } catch (e: any) {
      const error = e as AxiosError<any>;
      mainStore.dispatch(
        modalAcquisitionAsLeads.actions.setErrorResult(error.response?.data.error.messages || ''),
      );
    }
  };

  render() {
    const { modal } = this.props;
    return (
      <Modal
        open={this.props.modal.open}
        size={'mini'}
      >
        <Modal.Header>Akuisisi Sebagai Leads</Modal.Header>
        <Modal.Content>
          Anda akan mengakuisisi ini sebagai leads?
          {modal.errorMessages && (
            <div className={'mt-5'}>
              <Message
                negative={true}
                size={'small'}
              >
                {modal.errorMessages}
              </Message>
            </div>
          )}
        </Modal.Content>
        <Modal.Actions>
          <Button
            primary={true}
            onClick={this.onSubmit}
            loading={modal.submitting}
          >
            Submit
          </Button>
          <Button
            onClick={this.onClose}
            disabled={modal.submitting}
          >
            Batal
          </Button>
        </Modal.Actions>
      </Modal>
    );
  }
}

const mapStateToProps = (states: TMainReduxStates) => {
  return {
    modal: states.modalAcquisitionAsLeads,
    customer: states.customerReducer,
    project: states.reducerProject,
    chatRoom: states.reducerConversation,
  };
};

export default connect(mapStateToProps)(ModalAcquisitionAsLeads);

import React, { Component } from 'react';
import { Button, Form, Input, Modal, ModalHeader, Select } from 'semantic-ui-react';
import SelectVehicleFromAutoTrimitra from '../SelectVehicle/SelectVehicleFromAutoTrimitra';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { compose } from 'redux';
import { connect } from 'react-redux';
import { mainStore } from '../../redux/reducers';
import modalAddNewOwnedVehicleSlice from '../../redux/owned-vehicle/modalAddNewOwnedVehicle.slice';
import { mainApiServices } from '../../services/MainApiServices';
import { fetchCustomerThunk } from '../../redux/customerInfo/customerInfoSlice';
import InputLicensePlate from '../LicensePlate/InputLicensePlate';

interface Props {
  modal: TMainReduxStates['modalAddNewOwnedVehicle'];
  customer: TMainReduxStates['customerReducer'];
}

class ModalAddNewVehicle extends Component<Props> {
  onSubmit = async () => {
    const { customer, modal } = this.props;
    if (!customer.client || !customer.ref || !modal.licensePlateRegistrationCodeDetails) return;

    const dispatch = mainStore.dispatch;
    const actions = modalAddNewOwnedVehicleSlice.actions;

    dispatch(actions.setLoading(true));

    try {
      await mainApiServices.addOwnedVehicle({
        _clientPath: customer.ref.path,

        brandName: modal.brandName,
        brandUuid: modal.brandUuid,
        modelUuid: modal.modelUuid,
        modelName: modal.modelName,
        modelCategory: modal.modelCategory,
        mileage: modal.mileage,
        year: modal.year,
        variantFreeText: modal.variantFreeText,

        licensePlateCode: modal.licensePlateRegistrationCode,
        licensePlateNumber: modal.licensePlateNumber,
        licensePlateSeries: modal.licensePlateSeries,
        licensePlateCodeDetails: modal.licensePlateRegistrationCodeDetails,
      });

      dispatch(
        fetchCustomerThunk({
          clientDocRef: customer.ref,
        }) as any,
      );

      dispatch(actions.close());
    } catch (e) {
      dispatch(actions.setLoading(false));
    }
  };

  render() {
    const dispatch = mainStore.dispatch;
    const actions = modalAddNewOwnedVehicleSlice.actions;
    const { modal } = this.props;

    return (
      <Modal
        open={modal.open}
        size={'small'}
      >
        <ModalHeader>Tambah Kendaraan</ModalHeader>
        <Modal.Content>
          <Form>
            <SelectVehicleFromAutoTrimitra
              useFreeTextVariant={true}
              onBrandChange={(brand) => {
                dispatch(actions.setBrand(brand));
              }}
              onModelChange={(model) => {
                dispatch(actions.setModel(model));
              }}
              onVariantFreeTextChange={(text) => {
                dispatch(actions.setVariantFreeText(text));
              }}
              brand={{
                name: modal.brandName,
                uuid: modal.brandUuid,
              }}
              model={{
                uuid: modal.modelUuid,
                name: modal.modelName,
                category: modal.modelCategory,
              }}
              variantFreeText={modal.variantFreeText}
            />
            <Form.Field required={true}>
              <label>Tahun</label>
              <Select
                options={Array.from({ length: new Date().getFullYear() - 1990 + 1 }, (_, i) => ({
                  text: (1990 + i).toString(),
                  value: (1990 + i).toString(),
                }))}
                value={this.props.modal.year}
                onChange={(event, data) => {
                  dispatch(actions.setYear(data.value as string));
                }}
              />
            </Form.Field>
            <Form.Field required={true}>
              <label>Kilometer</label>
              <Input
                value={this.props.modal.mileage}
                onChange={(event) => {
                  dispatch(actions.setMileage(event.target.value));
                }}
              />
            </Form.Field>
            <InputLicensePlate
              registrationCode={modal.licensePlateRegistrationCode}
              number={modal.licensePlateNumber}
              series={modal.licensePlateSeries}
              registrationCodeOnChange={(value) =>
                dispatch(actions.setLicensePlateRegistrationCode(value))
              }
              numberOnChange={(value) => dispatch(actions.setLicensePlateNumber(value || ''))}
              seriesOnChange={(value) => dispatch(actions.setLicensePlateSeries(value || ''))}
            />
          </Form>
        </Modal.Content>
        <Modal.Actions>
          <Button onClick={(e) => dispatch(actions.close())}>Batal</Button>
          <Button
            loading={modal.loading}
            onClick={this.onSubmit}
            primary
          >
            Tambah
          </Button>
        </Modal.Actions>
      </Modal>
    );
  }
}

const mapStateToProps = (s: TMainReduxStates) => {
  return {
    modal: s.modalAddNewOwnedVehicle,
    customer: s.customerReducer,
  };
};

export default compose(connect(mapStateToProps))(ModalAddNewVehicle);

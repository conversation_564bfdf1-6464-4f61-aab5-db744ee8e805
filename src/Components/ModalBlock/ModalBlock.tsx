import React, { Component } from 'react';
import { Button, Form, Modal, TextArea, TextAreaProps } from 'semantic-ui-react';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { connect } from 'react-redux';
import { updateDoc } from 'firebase/firestore';
import { mainStore } from '../../redux/reducers';
import modalBlockPhoneNumberSlice from '../../redux/modalBlockPhoneNumber/modalBlockPhoneNumber.slice';

interface Props {
  modal: TMainReduxStates['modalBlockPhoneNumber'];
  conversation: TMainReduxStates['reducerConversation'];
}

interface State {
  reason: string;
}

class ModalBlock extends Component<Props, State> {
  onReasonChange = (e: React.ChangeEvent<HTMLTextAreaElement>, d: TextAreaProps) => {
    this.setState({
      reason: d.value as string,
    });
  };

  block = async () => {
    if (!this.state.reason) return;
    const chatRoomRef = this.props.conversation.chatRoom?.ref;
    if (!chatRoomRef) return;

    await updateDoc(chatRoomRef, {
      blocked: true,
      blockReason: this.state.reason,
    });

    this.close();
  };

  close = () => {
    mainStore.dispatch(modalBlockPhoneNumberSlice.actions.close());
  };

  render() {
    return (
      <Modal
        open={this.props.modal.open}
        size={'mini'}
      >
        <Modal.Header>Konfirmasi Blokir Nomor Telepon</Modal.Header>
        <Modal.Content>
          <p>Apa anda yakin akan memblokir nomor telepon ini?</p>
          <Form>
            <Form.Field required={true}>
              <label>Alasan Diblokir</label>
              <TextArea
                placeholder={'Masukan alasan diblokir'}
                onChange={this.onReasonChange}
              />
            </Form.Field>
          </Form>
        </Modal.Content>
        <Modal.Actions>
          <Button onClick={this.close}>Batal</Button>
          <Button
            color={'red'}
            onClick={this.block}
          >
            Blokir
          </Button>
        </Modal.Actions>
      </Modal>
    );
  }
}

const mapStateToProps = (s: TMainReduxStates) => {
  return {
    modal: s.modalBlockPhoneNumber,
    conversation: s.reducerConversation,
  };
};

export default connect(mapStateToProps)(ModalBlock);

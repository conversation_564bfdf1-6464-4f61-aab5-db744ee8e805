import { Button, Divider, Icon, Image, Modal } from 'semantic-ui-react';
import { TMainReduxStates } from '../../redux/types/redux-types';
import modalCheckBbnSlice from '../../redux/modal-bbn/modalCheckBbnSlice';
import { connect } from 'react-redux';
import { mainStore } from '../../redux/reducers';

interface Props {
  modal: TMainReduxStates['modalBbn'];
}

function isUrl(string: string): boolean {
  const urlPattern = /^(https?:\/\/)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(:\d+)?(\/[^\s]*)?$/;
  return urlPattern.test(string);
}

function hasValidBpkbImages(images: string[] | null | undefined): boolean {
  if (!images || !Array.isArray(images) || images.length === 0) return false;
  return images.some((img) => img && typeof img === 'string' && img.trim() !== '' && isUrl(img));
}

const ModalCekBbn = (props: Props) => {
  const dispatch = mainStore.dispatch;
  const actions = modalCheckBbnSlice.actions;

  const onClose = () => {
    dispatch(actions.close());
  };

  const { modal } = props;

  const { bbn } = modal;

  if (!bbn) return <div />;

  return (
    <Modal
      open={modal.open}
      size={'small'}
    >
      <Modal.Header>Detail BBN</Modal.Header>
      <Modal.Content>
        <div>
          <div className={'text-lg font-bold mb-2'}>Customer</div>
          <div className={'mb-1'}>
            <div>Nama</div>
            <div className={'font-semibold'}>{bbn.customer_doc_name}</div>
          </div>
          <div className={'mb-1'}>
            <div>Order Code</div>
            <div className={'font-semibold'}>{bbn.order_code}</div>
          </div>
        </div>
        <Divider />
        <div>
          <div className={'text-lg font-bold mb-2'}>Kendaraan</div>
          <div className={'mb-1'}>
            <div>Variant</div>
            <div className={'font-semibold'}>{bbn.vehicle_variant_name}</div>
          </div>
          <div className={'mb-1'}>
            <div>Nomor Mesin</div>
            <div className={'font-semibold'}>{bbn.vehicle_engine_number}</div>
          </div>
          <div className={'mb-1'}>
            <div>Nomor Rangka</div>
            <div className={'font-semibold'}>{bbn.vehicle_chassis_number}</div>
          </div>
          <div className={'mb-1'}>
            <div>Plat Nomor</div>
            <div className={'font-semibold'}>{bbn.vehicle_license_plate}</div>
          </div>
        </div>
        <Divider />
        <div>
          <div className={'text-lg font-bold mb-2'}>Notice</div>
          <div className={'mb-1'}>
            <div>Nomor Notice</div>
            <div className={'font-semibold'}>{bbn.vehicle_notice_number || 'Tidak ada'}</div>
          </div>
          <div className={'mb-1'}>
            <div>Tanggal Notice</div>
            <div className={'font-semibold'}>{bbn.vehicle_notice_entry_date || 'Tidak ada'}</div>
          </div>
          <div className={'mb-1'}>
            <div>Foto Notice</div>
            <div className={'py-1'}>
              {isUrl(bbn.vehicle_notice_image) ? (
                <div className="bg-green-500 text-white px-3 py-1 rounded-full text-xs font-medium shadow-sm flex items-center gap-1 w-fit">
                  <Icon name={'check'} /> Ada
                </div>
              ) : (
                <div className="bg-red-500 text-white px-3 py-1 rounded-full text-xs font-medium shadow-sm flex items-center gap-1 w-fit">
                  <Icon name={'times'} /> Tidak Ada
                </div>
              )}
            </div>
          </div>
        </div>

        <Divider />

        <div>
          <div className={'text-lg font-bold mb-2'}>BPKB</div>
          <div className={'mb-1'}>
            <div>Nomor BPKB</div>
            <div className={'font-semibold'}>
              {bbn.vehicle_certificate_of_ownership_number || 'Tidak ada'}
            </div>
          </div>
          <div className={'mb-1'}>
            <div>Tanggal BPKB</div>
            <div className={'font-semibold'}>
              {bbn.vehicle_certificate_of_ownership_entry_date || 'Tidak ada'}
            </div>
          </div>
          <div className={'mb-1'}>
            <div>Foto BPKB</div>
            <div className={'py-1'}>
              {hasValidBpkbImages(bbn.vehicle_certificate_of_ownership_image) ? (
                <div className="bg-green-500 text-white px-3 py-1 rounded-full text-xs font-medium shadow-sm flex items-center gap-1 w-fit">
                  <Icon name={'check'} /> Ada
                </div>
              ) : (
                <div className="bg-red-500 text-white px-3 py-1 rounded-full text-xs font-medium shadow-sm flex items-center gap-1 w-fit">
                  <Icon name={'times'} /> Tidak Ada
                </div>
              )}
            </div>
          </div>
        </div>

        <Divider />

        <div>
          <div className={'text-lg font-bold mb-2'}>STNK</div>
          <div className={'mb-1'}>
            <div>Nomor STNK</div>
            <div className={'font-semibold'}>
              {bbn.vehicle_registration_certificate_number || 'Tidak ada'}
            </div>
          </div>
          <div className={'mb-1'}>
            <div>Tanggal STNK</div>
            <div className={'font-semibold'}>
              {bbn.vehicle_registration_certificate_entry_date || 'Tidak ada'}
            </div>
          </div>
          <div className={'mb-1'}>
            <div>Foto STNK</div>
            <div className={'py-1'}>
              {isUrl(bbn.vehicle_registration_certificate_image) ? (
                <div className="bg-green-500 text-white px-3 py-1 rounded-full text-xs font-medium shadow-sm flex items-center gap-1 w-fit">
                  <Icon name={'check'} /> Ada
                </div>
              ) : (
                <div className="bg-red-500 text-white px-3 py-1 rounded-full text-xs font-medium shadow-sm flex items-center gap-1 w-fit">
                  <Icon name={'times'} /> Tidak Ada
                </div>
              )}
            </div>
          </div>
        </div>
      </Modal.Content>
      <Modal.Actions>
        <Button onClick={onClose}>Tutup</Button>
      </Modal.Actions>
    </Modal>
  );
};

const mapStateToProps = (states: TMainReduxStates) => {
  return {
    modal: states.modalBbn,
  };
};

export default connect(mapStateToProps)(ModalCekBbn);

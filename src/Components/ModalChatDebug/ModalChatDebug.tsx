import React from 'react';
import { <PERSON><PERSON>, <PERSON>dal } from 'semantic-ui-react';
import { useDispatch, useSelector } from 'react-redux';
import { TMainReduxStates } from '../../redux/types/redux-types';
import modalChatDebugSlice from '../../redux/modal-chat-debug/modalChatDebug.slice';
import moment from 'moment';

const ModalChatDebug: React.FC = () => {
  const dispatch = useDispatch();
  const { open, messageEntity } = useSelector((state: TMainReduxStates) => state.modalChatDebug);

  const onClose = () => {
    dispatch(modalChatDebugSlice.actions.close());
  };

  return (
    <Modal
      open={open}
      onClose={onClose}
      size="large"
    >
      <Modal.Header>Debug Message</Modal.Header>
      <Modal.Content scrolling>
        <pre>
          {JSON.stringify(
            messageEntity,
            (key, value) => {
              if (value && typeof value === 'object') {
                // Check for Firestore Timestamp-like objects (duck-typing).
                // This will be applied recursively by JSON.stringify.
                if (typeof value.seconds === 'number' && typeof value.nanoseconds === 'number') {
                  return moment(new Date(value.seconds * 1000).toISOString()).format(
                    'YYYY-MM-DD HH:mm:ss',
                  );
                }
                // Check for Firestore DocumentReference-like objects (duck-typing).
                // This will be applied recursively by JSON.stringify.
                if (typeof value.path === 'string' && value.path.includes('/')) {
                  return value.path;
                }
              }
              return value;
            },
            2,
          )}
        </pre>
      </Modal.Content>
      <Modal.Actions>
        <Button onClick={onClose}>Close</Button>
      </Modal.Actions>
    </Modal>
  );
};

export default ModalChatDebug;

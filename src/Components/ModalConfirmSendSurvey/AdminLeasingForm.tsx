import React from 'react';
import { Card, Checkbox, Form, Select } from 'semantic-ui-react';
import AdminLeasingB2B from '../../entities/AdminLeasingB2B';

interface AdminLeasingFormProps {
  leasingList: {
    code: string;
    name: string;
    disabled: boolean;
  }[];
  leasingAdmins: AdminLeasingB2B[];
  sendWithLeasing: boolean;
  selectedLeasing: string | null;
  selectedLeasingAdmin: string | null;
  lockLeasing: boolean;
  lockLeasingAdmin: boolean;
  onLeasingChange: (e: React.SyntheticEvent, d: any) => void;
  onLeasingAdminChange: (e: React.SyntheticEvent, d: any) => void;
  onToggleLeasing: (e: React.SyntheticEvent, d: any) => void;
}

const AdminLeasingForm: React.FC<AdminLeasingFormProps> = ({
  leasingList,
  leasingAdmins,
  sendWithLeasing,
  selectedLeasing,
  selectedLeasingAdmin,
  lockLeasing,
  lockLeasingAdmin,
  onLeasingChange,
  onLeasingAdminChange,
  onToggleLeasing,
}) => {
  return (
    <div className="bg-white rounded-lg shadow-sm p-4">
      <Form>
        <Form.Field className="mb-4">
          <Checkbox
            onChange={onToggleLeasing}
            checked={sendWithLeasing}
            label={'Tentukan Admin Leasing'}
            disabled={lockLeasing}
            className="text-gray-700"
          />
        </Form.Field>
      </Form>
      {sendWithLeasing && (
        <Card
          fluid
          className="border-0 shadow-sm"
        >
          <Card.Content
            header={<h3 className="text-gray-800 font-medium">Admin Leasing B2B</h3>}
            className="!bg-gray-50 !py-3"
          />
          <Card.Content className="p-4">
            <Form>
              <Form.Field className="mb-4">
                <label className="!text-gray-800 !font-medium !mb-2">Leasing</label>
                <Select
                  options={leasingList.map((leasing) => ({
                    value: leasing.code,
                    description: leasing.code,
                    text: leasing.name,
                    disabled: leasing.disabled,
                  }))}
                  onChange={onLeasingChange}
                  value={selectedLeasing ?? undefined}
                  placeholder={'Pilih Leasing'}
                  disabled={lockLeasing}
                  className="w-full"
                />
              </Form.Field>
              <Form.Field>
                <label className="!text-gray-800 !font-medium !mb-2">Admin Leasing</label>
                <Select
                  options={leasingAdmins.map((admin) => ({
                    value: admin.ref.id,
                    text: `${admin.email} - ${admin.full_name}`,
                    description: admin.full_name,
                  }))}
                  value={selectedLeasingAdmin ?? undefined}
                  onChange={onLeasingAdminChange}
                  placeholder={'Pilih Leasing Admin'}
                  search={true}
                  disabled={lockLeasingAdmin}
                  className="w-full"
                />
              </Form.Field>
            </Form>
          </Card.Content>
        </Card>
      )}
    </div>
  );
};

export default AdminLeasingForm;

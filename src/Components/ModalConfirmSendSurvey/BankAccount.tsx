import { Card, CardContent, Form, FormField, Grid, Input } from 'semantic-ui-react';
import SelectBankAccount, { BankInfo } from '../BankAccount/SelectBankAccount';

interface Props {
  title: string;
  onBankChange?: (value: BankInfo | null) => void;
  onAccountNameChange?: (value: string) => void;
  onAccountNumberChange?: (value: string) => void;
  bankValue?: string;
  accountNameValue?: string;
  accountNumberValue?: string;
}

const BankAccount = (props: Props) => {
  return (
    <Card
      fluid
      className="border-0 shadow-sm"
    >
      <Card.Content
        header={<h3 className="text-gray-800 font-medium">{props.title}</h3>}
        className="!bg-gray-50 !py-3"
      />
      <CardContent className="!p-0">
        <div className="p-4">
          <Form>
            <Grid
              stackable
              columns={2}
            >
              {/* Left Column */}
              <Grid.Column>
                <FormField className="!mb-0">
                  <label className="!block text-sm !font-medium !text-gray-600 !mb-1">Bank</label>
                  <div className="relative">
                    <SelectBankAccount
                      onChange={(bank) => {
                        props.onBankChange?.(bank);
                      }}
                      value={props.bankValue}
                    />
                  </div>
                </FormField>
              </Grid.Column>

              {/* Right Column */}
              <Grid.Column>
                <div className="grid gap-4">
                  <FormField className="!mb-0">
                    <label className="!block text-sm !font-medium !text-gray-600 !mb-1">
                      Nama Rekening
                    </label>
                    <Input
                      onChange={(event, data) => {
                        props.onAccountNameChange?.(data.value);
                      }}
                      value={props.accountNameValue}
                      className="w-full !h-[38px]"
                      placeholder="Masukkan nama rekening"
                    />
                  </FormField>

                  <FormField className="!mb-0">
                    <label className="!block text-sm !font-medium !text-gray-600 !mb-1">
                      Nomor Rekening
                    </label>
                    <Input
                      onChange={(event, data) => {
                        props.onAccountNumberChange?.(data.value);
                      }}
                      value={props.accountNumberValue}
                      className="w-full !h-[38px]"
                      placeholder="Masukkan nomor rekening"
                    />
                  </FormField>
                </div>
              </Grid.Column>
            </Grid>
          </Form>
        </div>
      </CardContent>
    </Card>
  );
};

export default BankAccount;

import { Card, Grid } from 'semantic-ui-react';
import React from 'react';
import { Timestamp } from 'firebase/firestore';
import { IdCard } from '../../entities/types/client-entity-types';
import { useDispatch } from 'react-redux';
import { openLightbox } from '../../redux/lightbox/lightbox.slice';

interface Props {
  idCard: IdCard<Timestamp>;
  title: string;
}

const IdCardModalConfirmSendSurvey = (props: Props) => {
  const { idCard } = props;
  const dispatch = useDispatch();

  const handleViewPhoto = () => {
    if (idCard.idCardImage) {
      dispatch(openLightbox(idCard.idCardImage));
    }
  };

  return (
    <Card
      fluid
      className="border-0 shadow-sm"
    >
      <Card.Content className="!p-0">
        <div className="bg-gray-50 px-4 py-3 border-b">
          <h3 className="text-gray-800 font-medium">{props.title}</h3>
        </div>
        <div className="p-4">
          <Grid
            stackable
            columns={2}
          >
            {/* Left Column */}
            <Grid.Column>
              {/* Personal Information */}
              <div className="mb-4">
                <div className="flex items-center mb-2">
                  <div className="w-2 h-2 rounded-full bg-blue-500 mr-2"></div>
                  <span className="text-sm font-medium text-gray-600">Informasi Pribadi</span>
                </div>
                <div className="bg-gray-50 rounded p-3">
                  <Grid>
                    <Grid.Row className="py-1">
                      <Grid.Column
                        width="6"
                        className="text-gray-600"
                      >
                        Nama Lengkap
                      </Grid.Column>
                      <Grid.Column
                        width="10"
                        className="text-gray-800 font-medium"
                      >
                        {idCard.fullName}
                      </Grid.Column>
                    </Grid.Row>
                    <Grid.Row className="py-1">
                      <Grid.Column
                        width="6"
                        className="text-gray-600"
                      >
                        No KTP
                      </Grid.Column>
                      <Grid.Column
                        width="10"
                        className="text-gray-800 font-medium"
                      >
                        {idCard.idCardNumber}
                      </Grid.Column>
                    </Grid.Row>
                    <Grid.Row className="py-1">
                      <Grid.Column
                        width="6"
                        className="text-gray-600"
                      >
                        Foto KTP
                      </Grid.Column>
                      <Grid.Column width="10">
                        <button
                          onClick={handleViewPhoto}
                          className="text-blue-600 hover:text-blue-800 font-medium inline-flex items-center bg-transparent border-0 cursor-pointer p-0"
                        >
                          <span>Lihat Foto</span>
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4 ml-1"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z" />
                            <path d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z" />
                          </svg>
                        </button>
                      </Grid.Column>
                    </Grid.Row>
                  </Grid>
                </div>
              </div>

              {/* Additional Information */}
              <div>
                <div className="flex items-center mb-2">
                  <div className="w-2 h-2 rounded-full bg-yellow-500 mr-2"></div>
                  <span className="text-sm font-medium text-gray-600">Informasi Tambahan</span>
                </div>
                <div className="bg-gray-50 rounded p-3">
                  <Grid>
                    <Grid.Row className="py-1">
                      <Grid.Column
                        width="6"
                        className="text-gray-600"
                      >
                        Pekerjaan
                      </Grid.Column>
                      <Grid.Column width="10">
                        <span className="text-gray-800 font-medium">{idCard.occupation}</span>
                        <span className="text-gray-400 mx-2">•</span>
                        <span className="text-gray-600">{idCard.occupationCode}</span>
                      </Grid.Column>
                    </Grid.Row>
                    <Grid.Row className="py-1">
                      <Grid.Column
                        width="6"
                        className="text-gray-600"
                      >
                        Status Pernikahan
                      </Grid.Column>
                      <Grid.Column width="10">
                        <span className="text-gray-800 font-medium">{idCard.maritalStatus}</span>
                        <span className="text-gray-400 mx-2">•</span>
                        <span className="text-gray-600">{idCard.maritalStatusCode}</span>
                      </Grid.Column>
                    </Grid.Row>
                    <Grid.Row className="py-1">
                      <Grid.Column
                        width="6"
                        className="text-gray-600"
                      >
                        Nama Ibu Kandung
                      </Grid.Column>
                      <Grid.Column
                        width="10"
                        className="text-gray-800 font-medium"
                      >
                        {idCard.birthMother}
                      </Grid.Column>
                    </Grid.Row>
                  </Grid>
                </div>
              </div>
            </Grid.Column>

            {/* Right Column */}
            <Grid.Column>
              {/* Address Information */}
              <div>
                <div className="flex items-center mb-2">
                  <div className="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                  <span className="text-sm font-medium text-gray-600">Informasi Alamat</span>
                </div>
                <div className="bg-gray-50 rounded p-3">
                  <Grid.Row className="py-1">
                    <Grid.Column
                      width="16"
                      className="text-gray-800"
                    >
                      {idCard.fullAddress}
                    </Grid.Column>
                  </Grid.Row>
                  <div className="mt-2 pt-2 border-t border-gray-200">
                    <div className="grid grid-cols-1 gap-2 text-sm">
                      <div className="py-1">
                        <span className="text-gray-600">Kelurahan/Desa:</span>
                        <span className="ml-2 font-medium text-gray-800">
                          {idCard.subDistrict.name}
                        </span>
                      </div>
                      <div className="py-1">
                        <span className="text-gray-600">Kecamatan:</span>
                        <span className="ml-2 font-medium text-gray-800">
                          {idCard.district.name}
                        </span>
                      </div>
                      <div className="py-1">
                        <span className="text-gray-600">Kota/Kabupaten:</span>
                        <span className="ml-2 font-medium text-gray-800">{idCard.city.name}</span>
                      </div>
                      <div className="py-1">
                        <span className="text-gray-600">Provinsi:</span>
                        <span className="ml-2 font-medium text-gray-800">
                          {idCard.province.name}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Grid.Column>
          </Grid>
        </div>
      </Card.Content>
    </Card>
  );
};

export default IdCardModalConfirmSendSurvey;

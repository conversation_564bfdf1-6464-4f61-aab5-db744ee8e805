import { Component, SyntheticEvent } from 'react';
import {
  <PERSON>ton,
  Card,
  Checkbox,
  Form,
  Grid,
  Header,
  Message,
  Modal,
  Segment,
} from 'semantic-ui-react';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { connect } from 'react-redux';
import ClientEntity from '../../entities/ClientEntity';
import { compose } from 'redux';
import AdminLeasingB2B from '../../entities/AdminLeasingB2B';
import { autotrimitraServices } from '../../services/autotrimitraServices';
import {
  arrayUnion,
  collection,
  doc,
  getDoc,
  getDocs,
  query,
  Timestamp,
  where,
  writeBatch,
} from 'firebase/firestore';
import { firestoreB2BVer9, firestoreIdealVer9 } from '../../services/myFirebase';
import { catalogueServices } from '../../services/catalogue/catalogueServices';
import { VariantProduct } from '../../services/types/catalaogueTypes';
import { AxiosError } from 'axios';
import { IPromoCode } from '../../services/types/promoServiceTypes';
import b2bServices from '../../services/b2b/b2bServices';
import moment from 'moment';
import { mainApiServices } from '../../services/MainApiServices';
import getDataDealCode, { IGetDataDealCodeHelper } from '../../helpers/getDataDealCode';
import HTMLReactParser from 'html-react-parser';
import BankAccount from './BankAccount';
import VehicleAndCreditSchemeModalConfirmSendSurvey from './VehicleAndCreditScheme.ModalConfirmSendSurvey';
import FamilyRegisterModalConfirmSendSurvey from './FamilyRegister.ModalConfirmSendSurvey';
import IdCardModalConfirmSendSurvey from './IdCard.ModalConfirmSendSurvey';
import { BankInfo } from '../BankAccount/SelectBankAccount';
import AdminLeasingForm from './AdminLeasingForm';
import PromoCodeForm from './PromoCodeForm';
import SelfieModalConfirmSendSurvey from './Selfie.ModalConfirmSendSurvey';

export interface ModalConfirmSendSurveyProps {
  conversations: TMainReduxStates['reducerConversation'];
  customer: TMainReduxStates['customerReducer'];
  onSuccess: () => void;
  onCancel: () => void;
  admin: TMainReduxStates['reducerAdmin'];
}

interface ILeasingCompany {
  name: string;
  code: string;
  disabled: boolean;
}

interface BankAccount {
  bank: {
    sk: string;
    name: string;
    shortName: string;
    swiftCode: string;
  } | null;
  accountNumber: string;
  accountName: string;
}

export interface IModalConfirmSendSurveyStates {
  leasingList: ILeasingCompany[];

  sending: boolean;
  error: string | null;

  clientEntity: ClientEntity | null;

  leasingAdmins: AdminLeasingB2B[];
  sendWithLeasing: boolean;
  selectedLeasing: string | null;
  selectedLeasingAdmin: string | null;

  selectedPromoCode: IPromoCode | null;

  selectedDealCode: IGetDataDealCodeHelper | null;

  lockLeasing: boolean;
  lockLeasingAdmin: boolean;

  enableOwnerBankAccount: boolean;
  ownerBankAccount: BankAccount;

  enableGuarantorBankAccount: boolean;
  guarantorBankAccount: BankAccount;

  usePromoCode: boolean;
}

class ModalConfirmSendSurvey extends Component<
  ModalConfirmSendSurveyProps,
  IModalConfirmSendSurveyStates
> {
  constructor(props: ModalConfirmSendSurveyProps) {
    super(props);

    this.state = {
      error: null,
      sending: false,
      clientEntity: null,

      leasingList: [],
      leasingAdmins: [],
      sendWithLeasing: false,
      selectedLeasing: null,
      selectedLeasingAdmin: null,

      selectedPromoCode: null,
      selectedDealCode: null,

      lockLeasing: false,
      lockLeasingAdmin: false,

      enableOwnerBankAccount: false,
      ownerBankAccount: {
        bank: null,
        accountName: '',
        accountNumber: '',
      },
      enableGuarantorBankAccount: false,
      guarantorBankAccount: {
        bank: null,
        accountName: '',
        accountNumber: '',
      },

      usePromoCode: false,
    };
  }

  fetch = async () => {
    const currentState = { ...this.state } as IModalConfirmSendSurveyStates;

    const clientRef = this.props.conversations.chatRoom?.clients[0];

    if (!clientRef) return;

    const getClientFirebase = await getDoc(clientRef.withConverter(ClientEntity.converter));
    const clientDataFromFirestore = getClientFirebase.data()!;

    const getLeasing = await getDocs(collection(firestoreB2BVer9, 'leasing_companies'));

    const listLeasing: ILeasingCompany[] = [];
    getLeasing.forEach((result) => {
      const data = result.data();
      listLeasing.push({
        code: result.ref.id,
        name: data.name,
        disabled: !data.order_source_permission?.e_survey || false,
      });
    });

    let selectedDealCode: null | IGetDataDealCodeHelper = null;

    if (clientDataFromFirestore.survey?.credit_scheme?.dealCode) {
      try {
        selectedDealCode = await getDataDealCode(
          clientDataFromFirestore.survey.credit_scheme.dealCode,
        );
      } catch (e) {
        selectedDealCode = null;
      }
    }

    if (selectedDealCode) {
      currentState.selectedDealCode = selectedDealCode;
      if (selectedDealCode.fincoId) {
        const collections = collection(firestoreB2BVer9, 'admins');
        const q = query(
          collections.withConverter(AdminLeasingB2B.converter),
          where('email', '==', selectedDealCode.fincoId),
          where('active', '==', true),
        );

        const get = await getDocs(q);

        get.forEach((result) => {
          const data = result.data();
          currentState.leasingAdmins.push(data);
          currentState.sendWithLeasing = true;
          currentState.selectedLeasing = data.leasing_company.code || '';
          currentState.selectedLeasingAdmin = result.id;
          currentState.lockLeasing = true;
          currentState.lockLeasingAdmin = true;
        });
      } else {
        const collections = collection(firestoreB2BVer9, 'admins');
        const q = query(
          collections.withConverter(AdminLeasingB2B.converter),
          where(
            'leasing_company.code',
            '==',
            clientDataFromFirestore.survey?.credit_scheme?.selectedLeasingCode || '',
          ),
          where('active', '==', true),
        );

        const get = await getDocs(q);

        get.forEach((result) => {
          const data = result.data();
          currentState.leasingAdmins.push(data);
        });

        currentState.sendWithLeasing = true;
        currentState.lockLeasing = true;
        currentState.selectedLeasing =
          clientDataFromFirestore.survey?.credit_scheme?.selectedLeasingCode || '';
      }
    }

    currentState.clientEntity = clientDataFromFirestore;
    currentState.leasingList = listLeasing;

    this.setState({
      ...currentState,
    });
  };

  checkBoxWithLeasing = {
    onChange: (e: SyntheticEvent, d: any) => {
      this.setState({
        sendWithLeasing: d?.checked ?? false,
        selectedLeasing: null,
        selectedLeasingAdmin: null,
      });
    },
  };

  checkBoxEnableBankAccountOwnerOnChange = (e: SyntheticEvent, d: any) => {
    this.setState({
      enableOwnerBankAccount: d?.checked || false,
    });
  };

  checkBoxEnableBankAccountGuarantorOnChange = (e: SyntheticEvent, d: any) => {
    this.setState({
      enableGuarantorBankAccount: d?.checked || false,
    });
  };

  bankAccountOwner = {
    onBankChange: (bank: BankInfo | null) => {
      const state = this.state.ownerBankAccount;
      if (bank) {
        state.bank = {
          sk: bank.SK,
          name: bank.Name,
          shortName: bank.ShortName,
          swiftCode: bank.Swift,
        };
      } else {
        state.bank = null;
      }
      this.setState({
        ownerBankAccount: state,
      });
    },

    onAccountNameChange: (value: string) => {
      const state = this.state.ownerBankAccount;
      state.accountName = value;
      this.setState({
        ownerBankAccount: state,
      });
    },

    onAccountNumberChange: (value: string) => {
      const state = this.state.ownerBankAccount;
      state.accountNumber = value;
      this.setState({
        ownerBankAccount: state,
      });
    },
  };

  bankAccountGuarantor = {
    onBankChange: (bank: BankInfo | null) => {
      const state = this.state.guarantorBankAccount;
      if (bank) {
        state.bank = {
          sk: bank.SK,
          name: bank.Name,
          shortName: bank.ShortName,
          swiftCode: bank.Swift,
        };
      } else {
        state.bank = null;
      }
      this.setState({
        guarantorBankAccount: state,
      });
    },

    onAccountNameChange: (value: string) => {
      const state = this.state.guarantorBankAccount;
      state.accountName = value;
      this.setState({
        guarantorBankAccount: state,
      });
    },

    onAccountNumberChange: (value: string) => {
      const state = this.state.guarantorBankAccount;
      state.accountNumber = value;
      this.setState({
        guarantorBankAccount: state,
      });
    },
  };

  promoCodeSelect = (p: IPromoCode | null) => {
    this.setState({
      selectedPromoCode: p || null,
    });
  };

  leasingSelect = {
    onChange: (e: SyntheticEvent, d: any) => {
      this.setState(
        {
          selectedLeasing: d.value,
          leasingAdmins: [],
        },
        () => this.leasingAdmin.fetch(),
      );
    },
  };

  leasingAdmin = {
    fetch: async (leasingCode?: string) => {
      const actualLeasingCode = leasingCode ?? this.state.selectedLeasing;
      const collections = collection(firestoreB2BVer9, 'admins');

      const q = query(
        collections.withConverter(AdminLeasingB2B.converter),
        where('leasing_company.code', '==', actualLeasingCode),
        where('active', '==', true),
      );
      const get = await getDocs(q);

      let currentState = { ...this.state };

      get.forEach((result) => {
        const data = result.data();
        currentState.leasingAdmins.push(data);
      });

      this.setState({
        ...currentState,
      });
    },
    onChange: (e: SyntheticEvent, d: any) => {
      this.setState({
        selectedLeasingAdmin: d.value,
      });
    },
  };

  private sendSurvey = async () => {
    this.setState(
      {
        sending: true,
      },
      async () => {
        if (
          this.state.sendWithLeasing &&
          (!this.state.selectedLeasing || !this.state.selectedLeasingAdmin)
        ) {
          this.setState({
            error: 'Ada data yang tidak lengkap,\ncoba periksa Leasing dan Adminnya',
            sending: false,
          });
          return;
        }

        const client = this.props.customer.client;
        if (!client) return;

        let area = client.profile.area?.text.toUpperCase() ?? null;

        const dreamVehicle = client.dream_vehicle;
        const getVehicleFromAutotrimitra = await autotrimitraServices.getVehicleVariantModel({
          code: client.dream_vehicle?.variant_code,
        });
        const autotrimitraVehicle = getVehicleFromAutotrimitra!.data[0]!;

        let price = 0;
        let vehicleImage = '';
        const getVariantColors = await catalogueServices.getVariantByAreaAMH({
          area: area ?? '',
          variantCode: dreamVehicle?.variant_code,
        });

        if (getVariantColors?.data) {
          const find = (getVariantColors.data as VariantProduct[]).find(
            (value) => value.color_code === dreamVehicle?.color_code,
          );
          if (find) {
            price = find.price;
            vehicleImage = find.url_image;
          }
        }

        let source = 'IDEAL';

        if (this.state.selectedDealCode) {
          source = 'DEAL_IDEAL';
        }

        let promoCode = this.state.selectedPromoCode?.promo_code || '';

        if (this.state.selectedDealCode) promoCode = this.state.selectedDealCode.promoCode.code;

        const bodyPayload: any = {
          ideal: {
            adminEmail: this.props.admin.admin?.email || '',
          },
          promoCode: promoCode,
          area: area,
          agentCode: this.props.admin.admin?.amartaVip?.mediatorCode || '',
          family_register_owner: {
            family_register_image: client.details.familyRegister?.familyRegisterImage || '',
            family_register_number: client.details.familyRegister?.familyRegisterNumber || '',
          },
          id_card_owner: {
            full_name: client.details.idCardOwner?.fullName || '',
            id_card_image: client.details.idCardOwner?.idCardImage || '',
            id_card_number: client.details.idCardOwner?.idCardNumber || '',
            birth_date: client.details.idCardOwner?.dateOfBirth.toDate() || '',
            birth_place: client.details.idCardOwner?.placeOfBirth || '',
            full_address: client.details.idCardOwner?.fullAddress || '',
            marital_status_code: client.details.idCardOwner?.maritalStatusCode || '',
            marital_status: client.details.idCardOwner?.maritalStatus || '',
            occupation_code: client.details.idCardOwner?.occupationCode || '',
            occupation: client.details.idCardOwner?.occupation || '',
            birth_mother: client.details.idCardOwner?.birthMother || '',
            last_education_code: client.details.idCardOwner?.lastEducationCode || '',
            last_education_name: client.details.idCardOwner?.lastEducation || '',
          },
          id_card_guarantor: {
            full_name: client.details.idCardGuarantor?.fullName || '',
            id_card_image: client.details.idCardGuarantor?.idCardImage || '',
            id_card_number: client.details.idCardGuarantor?.idCardNumber || '',
            birth_date: client.details.idCardGuarantor?.dateOfBirth.toDate() || '',
            birth_place: client.details.idCardGuarantor?.placeOfBirth || '',
            full_address: client.details.idCardGuarantor?.fullAddress || '',
            marital_status_code: client.details.idCardGuarantor?.maritalStatusCode || '',
            marital_status: client.details.idCardGuarantor?.maritalStatus || '',
            occupation_code: client.details.idCardGuarantor?.occupationCode || '',
            occupation: client.details.idCardGuarantor?.occupation || '',
            birth_mother: client.details.idCardGuarantor?.birthMother || '',
            last_education_code: client.details.idCardGuarantor?.lastEducationCode || '',
            last_education_name: client.details.idCardGuarantor?.lastEducation || '',
          },
          id_card_order_maker: {
            full_name: client.details.idCardOrderMaker?.fullName || '',
            id_card_image: client.details.idCardOrderMaker?.idCardImage || '',
            id_card_number: client.details.idCardOrderMaker?.idCardNumber || '',
            birth_date: client.details.idCardOrderMaker?.dateOfBirth.toDate() || '',
            birth_place: client.details.idCardOrderMaker?.placeOfBirth || '',
            full_address: client.details.idCardOrderMaker?.fullAddress || '',
            marital_status_code: client.details.idCardOrderMaker?.maritalStatusCode || '',
            marital_status: client.details.idCardOrderMaker?.maritalStatus || '',
            occupation_code: client.details.idCardOrderMaker?.occupationCode || '',
            occupation: client.details.idCardOrderMaker?.occupation || '',
            birth_mother: client.details.idCardOrderMaker?.birthMother || '',
            last_education_code: client.details.idCardOrderMaker?.lastEducationCode || '',
            last_education_name: client.details.idCardOrderMaker?.lastEducation || '',
          },
          id_card_guarantor_spouse:
            client.details.idCardGuarantor?.maritalStatusCode === 'MARRIED'
              ? {
                  full_name: client.details.idCardGuarantorSpouse?.fullName || '',
                  id_card_image: client.details.idCardGuarantorSpouse?.idCardImage || '',
                  id_card_number: client.details.idCardGuarantorSpouse?.idCardNumber || '',
                  birth_date: client.details.idCardGuarantorSpouse?.dateOfBirth.toDate() || '',
                  birth_place: client.details.idCardGuarantorSpouse?.placeOfBirth || '',
                  full_address: client.details.idCardGuarantorSpouse?.fullAddress || '',
                  marital_status_code:
                    client.details.idCardGuarantorSpouse?.maritalStatusCode || '',
                  marital_status: client.details.idCardGuarantorSpouse?.maritalStatus || '',
                  occupation_code: client.details.idCardGuarantorSpouse?.occupationCode || '',
                  occupation: client.details.idCardGuarantorSpouse?.occupation || '',
                  birth_mother: client.details.idCardGuarantorSpouse?.birthMother || '',
                  last_education_code:
                    client.details.idCardGuarantorSpouse?.lastEducationCode || '',
                  last_education_name: client.details.idCardGuarantorSpouse?.lastEducation || '',
                }
              : null,
          source: source,
          source_path: this.props.conversations?.chatRoom?.ref?.path,
          vehicle: {
            alternative_color: {
              code: 'SAME_AS_ORDER',
              name: 'tanpa preferensi warna',
            },
            price: price,
            brand_name: autotrimitraVehicle.brand_name,
            brand_uuid: autotrimitraVehicle.brand_uuid,
            model_uuid: autotrimitraVehicle.model_uuid,
            model_name: autotrimitraVehicle.model_name,
            image_url: vehicleImage,
            color_code: dreamVehicle?.color_code,
            color_name: dreamVehicle?.color_name,
            variant_name: autotrimitraVehicle.variant_name,
            variant_code: dreamVehicle?.variant_code,
            variant_uuid: autotrimitraVehicle.variant_uuid,
          },

          contact: {
            phone_number_owner: client.details.owner_phone_number,
            phone_number_guarantor: client.details.guarantor_phone_number,
            phone_number_order_maker: client.details.order_maker_phone_number,
          },

          address_owner: {
            full_address: client.details.idCardOwner?.fullAddress || '',
            province_code: client.details.idCardOwner?.province.code || '',
            province_name: client.details.idCardOwner?.province.name || '',
            city_code: client.details.idCardOwner?.city.code || '',
            city_name: client.details.idCardOwner?.city.name || '',
            district_code: client.details.idCardOwner?.district.code || '',
            district_name: client.details.idCardOwner?.district.name || '',
            sub_district_code: client.details.idCardOwner?.subDistrict.code || '',
            sub_district_name: client.details.idCardOwner?.subDistrict.name || '',
            zip_code: client.details.idCardOwner?.zipCode || '',
            hamlet: client.details.idCardOwner?.hamlet || '',
            neighbourhood: client.details.idCardOwner?.neighbourhood || '',
          },
          address_order_maker: {
            full_address: client.details.idCardOrderMaker?.fullAddress || '',
            province_code: client.details.idCardOrderMaker?.province.code || '',
            province_name: client.details.idCardOrderMaker?.province.name || '',
            city_code: client.details.idCardOrderMaker?.city.code || '',
            city_name: client.details.idCardOrderMaker?.city.name || '',
            district_code: client.details.idCardOrderMaker?.district.code || '',
            district_name: client.details.idCardOrderMaker?.district.name || '',
            sub_district_code: client.details.idCardOrderMaker?.subDistrict.code || '',
            sub_district_name: client.details.idCardOrderMaker?.subDistrict.name || '',
            zip_code: client.details.idCardOrderMaker?.zipCode || '',
            hamlet: client.details.idCardOrderMaker?.hamlet || '',
            neighbourhood: client.details.idCardOrderMaker?.neighbourhood || '',
          },
          address_guarantor: {
            full_address: client.details.idCardGuarantor?.fullAddress || '',
            province_code: client.details.idCardGuarantor?.province.code || '',
            province_name: client.details.idCardGuarantor?.province.name || '',
            city_code: client.details.idCardGuarantor?.city.code || '',
            city_name: client.details.idCardGuarantor?.city.name || '',
            district_code: client.details.idCardGuarantor?.district.code || '',
            district_name: client.details.idCardGuarantor?.district.name || '',
            sub_district_code: client.details.idCardGuarantor?.subDistrict.code || '',
            sub_district_name: client.details.idCardGuarantor?.subDistrict.name || '',
            zip_code: client.details.idCardGuarantor?.zipCode || '',
            hamlet: client.details.idCardGuarantor?.hamlet || '',
            neighbourhood: client.details.idCardGuarantor?.neighbourhood || '',
          },
          address_guarantor_current_address: {
            full_address: client.details.idCardGuarantor?.domicileFullAddress || '',
            province_code: client.details.idCardGuarantor?.domicileProvince.code || '',
            province_name: client.details.idCardGuarantor?.domicileProvince.name || '',
            city_code: client.details.idCardGuarantor?.domicileCity.code || '',
            city_name: client.details.idCardGuarantor?.domicileCity.name || '',
            district_code: client.details.idCardGuarantor?.domicileDistrict.code || '',
            district_name: client.details.idCardGuarantor?.domicileDistrict.name || '',
            sub_district_code: client.details.idCardGuarantor?.domicileSubDistrict.code || '',
            sub_district_name: client.details.idCardGuarantor?.domicileSubDistrict.name || '',
            zip_code: client.details.idCardGuarantor?.domicileZipCode || '',
            hamlet: client.details.idCardGuarantor?.domicileHamlet || '',
            neighbourhood: client.details.idCardGuarantor?.domicileNeighbourhood || '',
          },
          address_guarantor_spouse:
            client.details.idCardGuarantor?.maritalStatusCode === 'MARRIED'
              ? {
                  full_address: client.details.idCardGuarantorSpouse?.fullAddress || '',
                  province_code: client.details.idCardGuarantorSpouse?.province.code || '',
                  province_name: client.details.idCardGuarantorSpouse?.province.name || '',
                  city_code: client.details.idCardGuarantorSpouse?.city.code || '',
                  city_name: client.details.idCardGuarantorSpouse?.city.name || '',
                  district_code: client.details.idCardGuarantorSpouse?.district.code || '',
                  district_name: client.details.idCardGuarantorSpouse?.district.name || '',
                  sub_district_code: client.details.idCardGuarantorSpouse?.subDistrict.code || '',
                  sub_district_name: client.details.idCardGuarantorSpouse?.subDistrict.name || '',
                  zip_code: client.details.idCardGuarantorSpouse?.zipCode || '',
                  hamlet: client.details.idCardGuarantorSpouse?.hamlet || '',
                  neighbourhood: client.details.idCardGuarantorSpouse?.neighbourhood || '',
                }
              : null,
          credit: {
            dp_amount: client.survey?.credit_scheme?.down_payment ?? 0,
            installment_amount:
              (client.survey?.credit_scheme?.installment ?? 0) -
              (client.survey?.credit_scheme?.discountInstallment ?? 0),
            tenor:
              (client.survey?.credit_scheme?.tenor ?? 0) -
              (client.survey?.credit_scheme?.discountTenor ?? 0),
          },
          home_ownership_status_code: '',
          home_ownership_status: '',

          surveyTime: client.survey?.credit_scheme?.surveyTime?.toDate() || null,
          surveyGmapUrl: client.survey?.credit_scheme?.surveyGmapUrl || null,
          selfiePhoto: client.details.selfie?.selfieImage || null,
          dealCode: client.survey?.credit_scheme?.dealCode || null,

          amartaVip: this.props.admin.admin?.amartaVip || null,
        };

        if (this.state.sendWithLeasing) {
          bodyPayload['target_admin_leasing'] = this.state.selectedLeasingAdmin;
        }

        if (
          this.state.enableOwnerBankAccount &&
          this.state.ownerBankAccount.bank &&
          this.state.ownerBankAccount.accountNumber
        ) {
          bodyPayload['ownerBankAccount'] = {
            bank_name: this.state.ownerBankAccount.bank.name,
            bank_short_name: this.state.ownerBankAccount.bank.shortName,
            bank_code: this.state.ownerBankAccount.bank.sk,
            bank_swift_code: this.state.ownerBankAccount.bank.swiftCode,
            account_name: this.state.ownerBankAccount.accountName,
            account_number: this.state.ownerBankAccount.accountNumber,
          };
        }

        if (
          this.state.enableGuarantorBankAccount &&
          this.state.guarantorBankAccount.bank &&
          this.state.guarantorBankAccount.accountNumber
        ) {
          bodyPayload['guarantorBankAccount'] = {
            bank_name: this.state.guarantorBankAccount.bank?.name || '',
            bank_short_name: this.state.guarantorBankAccount.bank?.shortName || '',
            bank_code: this.state.guarantorBankAccount.bank?.sk || '',
            bank_swift_code: this.state.guarantorBankAccount.bank.swiftCode,
            account_name: this.state.guarantorBankAccount.accountName,
            account_number: this.state.guarantorBankAccount.accountNumber,
          };
        }

        try {
          const send = await b2bServices.createSurveyOrder(bodyPayload);

          if (!send.ok) {
            const error = send.originalError as AxiosError<any>;
            throw Object.assign(
              new Error(error.response?.data?.data?.messages),
              send.originalError.response?.data,
            );
          }

          // updateLeadsService.updateLeadsOfferCode({
          //     offer_code: send.data?.data.offerCode ?? "",
          //     phone_number: orderMakerProfile?.Phone ?? "",
          // }).then().catch();

          const batch = writeBatch(firestoreIdealVer9);
          if (this.state.clientEntity && this.state.clientEntity.ref) {
            batch.update(this.state.clientEntity.ref, {
              'survey.last_send': Timestamp.now(),
              'survey.offer_code': send.data?.data.offerCode,
              order_histories: arrayUnion({
                offer_code: send.data?.data.offerCode || null,
                order_code: null,
                created_at: new Date(),
                survey_order_type: 'vehicle',
                payment_scheme: 'CREDIT',
                source: 'IDEAL',
                createdByAdminIdeal: {
                  ref: this.props.admin.admin!.ref,
                  name: this.props.admin.admin!.name,
                  email: this.props.admin.admin!.email,
                },
                orderCreatedBy: this.props.admin.admin!.email,
              }),
            });
          }

          if (this.props.conversations.chatRoom && this.props.conversations.chatRoom.ref) {
            const projectRef = this.props.conversations.chatRoom.ref.parent.parent!;
            const labelDoc = doc(projectRef, '/labels/8ab7agmjHroXC5ycWqJ0');
            batch.update(this.props.conversations.chatRoom.ref, {
              label: labelDoc,
              label_updated_at: Timestamp.now(),
            });
          }

          await batch.commit();

          await mainApiServices
            .logSendPriceList({
              event: 'createSurveyOrderB2b',
              phone_number: client.contacts.whatsapp || '',
              name: client.profile.name || '',
              city_group: client.profile.area?.text || '',
              vehicle: {
                model_name: autotrimitraVehicle.model_name || '',
                variant_name: autotrimitraVehicle.variant_name || '',
                variant_code: dreamVehicle?.variant_code || '',
                variant_color_code: dreamVehicle?.color_code || '',
                variant_color_name: dreamVehicle?.color_name || '',
              },
              discount_promo: this.promoDp(),
              otr: dreamVehicle?.price || 0,
              pricelists: [
                {
                  down_payment: client.survey?.credit_scheme?.down_payment || 0,
                  tenor: client.survey?.credit_scheme?.tenor || 0,
                  installment: client.survey?.credit_scheme?.installment || 0,
                  discount_down_payment: null,
                  discount_installment: null,
                  discount_tenor: null,
                },
              ],
              admin_id: this.props.admin.admin?.email || '',
              credit: {
                offer_code: send.data?.data.offerCode,
                down_payment: client.survey?.credit_scheme?.down_payment || 0,
                discount_down_payment: this.promoDp(),
                tenor: client.survey?.credit_scheme?.tenor || 0,
                discount_tenor: client.survey?.credit_scheme?.discountTenor,
                installment: client.survey?.credit_scheme?.installment || 0,
                discount_installment: client.survey?.credit_scheme?.discountInstallment,
              },
            })
            .then()
            .catch();

          this.setState(
            {
              sending: false,
            },
            () => {
              this.props.onSuccess();
            },
          );
        } catch (e: any) {
          let message = e.toString();

          if ('type' in e && e.type === 'UNPROCESSABLE_ENTITY') {
            message = '<ul>';
            for (let [key, value] of Object.entries(e.data)) {
              message += `\n${(value as { msg: string }).msg} (${key})`;
            }
            message += '</ul>';
          }

          this.setState({
            error: message,
          });
        }

        this.setState({
          sending: false,
        });
      },
    );
  };

  promoDp = () => {
    let dpUser = this.props.customer.client?.survey?.credit_scheme?.down_payment || 0;
    if (this.state.selectedPromoCode?.discount_type === 'nominal') {
      return dpUser - this.state.selectedPromoCode.discount_value;
    } else if (this.state.selectedPromoCode?.discount_type === 'percent') {
      const otr = this.props.customer.client?.dream_vehicle?.price || 0;
      return dpUser - (this.state.selectedPromoCode.discount_value * otr) / 100;
    } else {
      return 0;
    }
  };

  componentDidMount() {
    this.fetch();
  }

  render() {
    const { client } = this.props.customer;
    const { admin } = this.props.admin;
    return (
      <>
        <Modal
          open={true}
          onClose={this.props.onCancel}
          className="bg-gray-50"
          size="small"
        >
          <Modal.Header className="bg-white border-b px-6 py-4">
            <div className="flex flex-col">
              <h2 className="text-2xl font-semibold text-gray-800">Konfirmasi Kirim Survey</h2>
            </div>
          </Modal.Header>
          <Modal.Content className="p-6">
            <div className="space-y-6">
              {/* Card Informasi Agen */}
              {admin?.amartaVip && (
                <Card
                  fluid
                  className="border-0 shadow-sm"
                >
                  <Card.Content
                    header={<h3 className="text-gray-800 font-medium">Informasi Agen</h3>}
                    className="!bg-gray-50 !py-3"
                  />
                  <Card.Content className="p-4">
                    <div className="bg-gray-50 rounded-lg p-3">
                      <Grid>
                        <Grid.Row className="py-2">
                          <Grid.Column
                            width="4"
                            className="text-gray-600"
                          >
                            Kode Agen
                          </Grid.Column>
                          <Grid.Column
                            width="12"
                            className="text-gray-800 font-medium"
                          >
                            {admin.amartaVip.mediatorCode}
                          </Grid.Column>
                        </Grid.Row>
                        <Grid.Row className="py-2">
                          <Grid.Column
                            width="4"
                            className="text-gray-600"
                          >
                            Nama Agen
                          </Grid.Column>
                          <Grid.Column
                            width="12"
                            className="text-gray-800 font-medium"
                          >
                            {admin.amartaVip.mediatorName}
                          </Grid.Column>
                        </Grid.Row>
                      </Grid>
                    </div>
                  </Card.Content>
                </Card>
              )}

              {/* Message kode deal */}
              {this.state.clientEntity?.survey?.credit_scheme?.dealCode && (
                <Message
                  warning={true}
                  className="bg-amber-50 border-amber-200 text-amber-800"
                >
                  <Message.Header className="text-amber-900">
                    Kode Deal digunakan {this.state.clientEntity.survey.credit_scheme.dealCode}
                  </Message.Header>
                  <p className="mt-2">
                    Kode Deal digunakan kemungkinan sistem akan mengunci Leasing atau Admin Leasing.
                  </p>
                </Message>
              )}

              <div className="space-y-6">
                <AdminLeasingForm
                  leasingList={this.state.leasingList}
                  leasingAdmins={this.state.leasingAdmins}
                  sendWithLeasing={this.state.sendWithLeasing}
                  selectedLeasing={this.state.selectedLeasing}
                  selectedLeasingAdmin={this.state.selectedLeasingAdmin}
                  lockLeasing={this.state.lockLeasing}
                  lockLeasingAdmin={this.state.lockLeasingAdmin}
                  onLeasingChange={this.leasingSelect.onChange}
                  onLeasingAdminChange={this.leasingAdmin.onChange}
                  onToggleLeasing={this.checkBoxWithLeasing.onChange}
                />

                <PromoCodeForm
                  usePromoCode={this.state.usePromoCode}
                  selectedDealCode={this.state.selectedDealCode}
                  selectedPromoCode={this.state.selectedPromoCode}
                  client={this.props.customer.client}
                  onPromoCodeChange={this.promoCodeSelect}
                  onTogglePromoCode={(e, d) =>
                    this.setState({
                      usePromoCode: d?.checked ?? false,
                    })
                  }
                />

                <div className="bg-white rounded-lg shadow-sm p-4">
                  <Form>
                    <Form.Field className="mb-4">
                      <Checkbox
                        label={'Akun Bank Pemilik'}
                        checked={this.state.enableOwnerBankAccount}
                        onChange={this.checkBoxEnableBankAccountOwnerOnChange}
                        className="text-gray-700"
                      />
                    </Form.Field>
                  </Form>
                  {this.state.enableOwnerBankAccount && (
                    <BankAccount
                      bankValue={this.state.ownerBankAccount.bank?.sk || undefined}
                      accountNameValue={this.state.ownerBankAccount.accountName}
                      accountNumberValue={this.state.ownerBankAccount.accountNumber}
                      onBankChange={this.bankAccountOwner.onBankChange}
                      onAccountNameChange={this.bankAccountOwner.onAccountNameChange}
                      onAccountNumberChange={this.bankAccountOwner.onAccountNumberChange}
                      title={'Akun Bank Pemilik'}
                    />
                  )}
                </div>

                <div className="bg-white rounded-lg shadow-sm p-4">
                  <Form>
                    <Form.Field className="mb-4">
                      <Checkbox
                        label={'Akun Bank Penjamin'}
                        onChange={this.checkBoxEnableBankAccountGuarantorOnChange}
                        checked={this.state.enableGuarantorBankAccount}
                        className="text-gray-700"
                      />
                    </Form.Field>
                  </Form>
                  {this.state.enableGuarantorBankAccount && (
                    <BankAccount
                      bankValue={this.state.guarantorBankAccount.bank?.sk || undefined}
                      accountNameValue={this.state.guarantorBankAccount.accountName}
                      accountNumberValue={this.state.guarantorBankAccount.accountNumber}
                      onBankChange={this.bankAccountGuarantor.onBankChange}
                      onAccountNameChange={this.bankAccountGuarantor.onAccountNameChange}
                      onAccountNumberChange={this.bankAccountGuarantor.onAccountNumberChange}
                      title={'Akun Bank Penjamin'}
                    />
                  )}
                </div>
              </div>

              {/* Vehicle and Credit Information */}
              {this.state.clientEntity && (
                <div className="bg-white rounded-lg shadow-sm p-0">
                  <VehicleAndCreditSchemeModalConfirmSendSurvey
                    clientEntity={this.state.clientEntity}
                    promoDownPayment={this.promoDp()}
                  />
                </div>
              )}

              {/* Survey Schedule Information */}
              <Card
                fluid
                className="border-0 shadow-sm"
              >
                <Card.Content
                  header={<h3 className="text-gray-800 font-medium">Jadwal Survey</h3>}
                  className="!bg-gray-50 !py-3"
                />
                <Card.Content className="p-4">
                  <div className="bg-gray-50 rounded-lg p-3">
                    <Grid>
                      <Grid.Row className="py-2">
                        <Grid.Column
                          width="4"
                          className="text-gray-600"
                        >
                          Jadwal Survey
                        </Grid.Column>
                        <Grid.Column
                          width="12"
                          className="text-gray-800 font-medium"
                        >
                          {this.state.clientEntity?.survey?.credit_scheme?.surveyTime ? (
                            moment(
                              this.state.clientEntity?.survey?.credit_scheme?.surveyTime.toDate(),
                            ).format('DD MMM YYYY HH:mm')
                          ) : (
                            <i>Belum ada</i>
                          )}
                        </Grid.Column>
                      </Grid.Row>
                      <Grid.Row className="py-2">
                        <Grid.Column
                          width="4"
                          className="text-gray-600"
                        >
                          Url Google Map
                        </Grid.Column>
                        <Grid.Column width="12">
                          {this.state.clientEntity?.survey?.credit_scheme?.surveyGmapUrl ? (
                            <a
                              href={this.state.clientEntity?.survey?.credit_scheme?.surveyGmapUrl}
                              target="_blank"
                              rel="noreferrer"
                              className="text-blue-600 hover:text-blue-800 font-medium"
                            >
                              Buka Google Map
                            </a>
                          ) : (
                            <i className="text-gray-500">Belum ada</i>
                          )}
                        </Grid.Column>
                      </Grid.Row>
                    </Grid>
                  </div>
                </Card.Content>
              </Card>

              {/* Document Information */}
              <div className="space-y-4">
                {/* Selfie Section */}
                {client?.details.selfie?.selfieImage ? (
                  <SelfieModalConfirmSendSurvey selfie={client.details.selfie} />
                ) : (
                  <Segment className="border border-red-200 bg-red-50">
                    <div className="absolute -top-3 left-3 bg-red-500 text-white px-3 py-1 rounded-full text-xs font-medium shadow-sm">
                      Belum Lengkap!
                    </div>
                    <Header
                      as="h3"
                      className="text-gray-800"
                    >
                      Foto Selfie
                    </Header>
                    <i className="text-gray-600">Tidak Ada Data</i>
                  </Segment>
                )}

                {client?.details.familyRegister?.familyRegisterNumber ? (
                  <FamilyRegisterModalConfirmSendSurvey
                    familyRegister={client.details.familyRegister}
                  />
                ) : (
                  <Segment className="border border-red-200 bg-red-50">
                    <div className="absolute -top-3 left-3 bg-red-500 text-white px-3 py-1 rounded-full text-xs font-medium shadow-sm">
                      Belum Lengkap!
                    </div>
                    <Header
                      as="h3"
                      className="text-gray-800"
                    >
                      Kartu Keluarga Pemilik
                    </Header>
                    <i className="text-gray-600">Tidak Ada Data</i>
                  </Segment>
                )}

                {client?.details.idCardOwner?.idCardNumber ? (
                  <IdCardModalConfirmSendSurvey
                    idCard={client.details.idCardOwner}
                    title={'KTP Pemilik'}
                  />
                ) : (
                  <Segment className="border border-red-200 bg-red-50">
                    <div className="absolute -top-3 left-3 bg-red-500 text-white px-3 py-1 rounded-full text-xs font-medium shadow-sm">
                      Belum Lengkap!
                    </div>
                    <Header
                      as="h3"
                      className="text-gray-800"
                    >
                      KTP Pemilik
                    </Header>
                    <i className="text-gray-600">Tidak Ada Data</i>
                  </Segment>
                )}

                {client?.details.idCardOrderMaker?.idCardNumber ? (
                  <IdCardModalConfirmSendSurvey
                    idCard={client.details.idCardOrderMaker}
                    title={'KTP Pemesan'}
                  />
                ) : (
                  <Segment className="border border-red-200 bg-red-50">
                    <div className="absolute -top-3 left-3 bg-red-500 text-white px-3 py-1 rounded-full text-xs font-medium shadow-sm">
                      Belum Lengkap!
                    </div>
                    <Header
                      as="h3"
                      className="text-gray-800"
                    >
                      KTP Pemesan
                    </Header>
                    <i className="text-gray-600">Tidak Ada Data</i>
                  </Segment>
                )}

                {client?.details.idCardGuarantor?.idCardNumber ? (
                  <IdCardModalConfirmSendSurvey
                    idCard={client.details.idCardGuarantor}
                    title={'KTP Penjamin'}
                  />
                ) : (
                  <Segment className="border border-red-200 bg-red-50">
                    <div className="absolute -top-3 left-3 bg-red-500 text-white px-3 py-1 rounded-full text-xs font-medium shadow-sm">
                      Belum Lengkap!
                    </div>
                    <Header
                      as="h3"
                      className="text-gray-800"
                    >
                      KTP Penjamin
                    </Header>
                    <i className="text-gray-600">Tidak Ada Data</i>
                  </Segment>
                )}

                {client?.details.idCardGuarantor?.maritalStatusCode === 'MARRIED' && (
                  <div>
                    {client.details.idCardGuarantorSpouse ? (
                      <IdCardModalConfirmSendSurvey
                        idCard={client.details.idCardGuarantorSpouse}
                        title={'KTP Pasangan dari Penjamin'}
                      />
                    ) : (
                      <Segment className="border border-red-200 bg-red-50">
                        <div className="absolute -top-3 left-3 bg-red-500 text-white px-3 py-1 rounded-full text-xs font-medium shadow-sm">
                          Belum Lengkap!
                        </div>
                        <Header
                          as="h3"
                          className="text-gray-800"
                        >
                          KTP Pasangan Penjamin
                        </Header>
                        <i className="text-gray-600">Tidak Ada Data</i>
                      </Segment>
                    )}
                  </div>
                )}
              </div>

              {!this.state.error ? (
                <Message
                  warning
                  className="bg-amber-50 border-amber-200 text-amber-800"
                >
                  Pastikan data survey sudah lengkap.
                </Message>
              ) : (
                <Message
                  error
                  className="bg-red-50 border-red-200 text-red-800"
                >
                  {HTMLReactParser(this.state.error)}
                </Message>
              )}
            </div>
          </Modal.Content>
          <Modal.Actions className="bg-gray-50 border-t px-6 py-4">
            <Button
              color="black"
              onClick={this.props.onCancel}
              className="!bg-gray-600 hover:!bg-gray-700 text-white"
            >
              Batal
            </Button>
            <Button
              content="Kirim Survey"
              labelPosition="right"
              icon="checkmark"
              onClick={this.sendSurvey}
              loading={this.state.sending}
              className="!bg-blue-600 hover:!bg-blue-700 !text-white"
            />
          </Modal.Actions>
        </Modal>
      </>
    );
  }
}

const mapStateToProps = (states: TMainReduxStates) => ({
  conversations: states.reducerConversation,
  customer: states.customerReducer,
  admin: states.reducerAdmin,
});

export default compose(connect(mapStateToProps, null))(ModalConfirmSendSurvey);

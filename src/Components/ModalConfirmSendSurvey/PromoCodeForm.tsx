import React from 'react';
import { Card, Checkbox, Form } from 'semantic-ui-react';
import SelectPromoCode from '../SelectPromoCode';
import currencyFormat from '../../helpers/currencyFormat';
import { IPromoCode } from '../../services/types/promoServiceTypes';
import { IClientEntity } from '../../entities/types/client-entity-types';
import { useSelector } from 'react-redux';
import { TMainReduxStates } from '../../redux/types/redux-types';

interface PromoCodeFormProps {
  usePromoCode: boolean;
  selectedDealCode: {
    promoCode: {
      code: string;
    };
  } | null;
  selectedPromoCode: IPromoCode | null;
  client: IClientEntity | null;
  onPromoCodeChange: (p: IPromoCode | null) => void;
  onTogglePromoCode: (e: React.SyntheticEvent, d: any) => void;
}

const PromoCodeForm: React.FC<PromoCodeFormProps> = ({
  usePromoCode,
  selectedDealCode,
  selectedPromoCode,
  client,
  onPromoCodeChange,
  onTogglePromoCode,
}) => {
  const admin = useSelector((state: TMainReduxStates) => state.reducerAdmin);

  const isAdminAmartaVip = admin?.admin?.amartaVip;

  return (
    <div className="bg-white rounded-lg shadow-sm p-4">
      <Form>
        <Form.Field className="mb-4">
          <Checkbox
            onChange={onTogglePromoCode}
            checked={usePromoCode}
            label={'Gunakan Kode Promo'}
            className="text-gray-700"
          />
        </Form.Field>
      </Form>
      {usePromoCode && (
        <Card
          fluid
          className="border-0 shadow-sm"
        >
          <Card.Content
            header={<h3 className="text-gray-800 font-medium">Kode Promo</h3>}
            className="!bg-gray-50 !py-3"
          />
          {selectedDealCode ? (
            <Card.Content className="!bg-gray-50 !py-3">
              Kode Promo: {selectedDealCode.promoCode.code}
            </Card.Content>
          ) : (
            <Card.Content>
              <Form>
                <SelectPromoCode
                  city_group={client?.profile.area?.text.toUpperCase() || ''}
                  purchase_method={'credit'}
                  vehicle_model={client?.dream_vehicle?.model_name || ''}
                  vehicle_variant={client?.dream_vehicle?.variant_code || ''}
                  vehicle_variant_color={client?.dream_vehicle?.color_code || ''}
                  tenor={client?.survey?.credit_scheme?.tenor || 0}
                  transaction_amount={client?.survey?.credit_scheme?.down_payment || 0}
                  onChange={onPromoCodeChange}
                  value={selectedPromoCode?.promo_code || ''}
                  allow_agent={isAdminAmartaVip ? true : false}
                />
                {selectedPromoCode && (
                  <Form.Field className="mt-4">
                    <label className="!text-gray-700 !font-medium">Diskon Uang Muka</label>
                    {selectedPromoCode.discount_type === 'nominal' && (
                      <span className="text-gray-600">
                        {currencyFormat(selectedPromoCode.discount_value)}
                      </span>
                    )}
                    {selectedPromoCode.discount_type === 'percent' && (
                      <span className="text-gray-600">
                        {currencyFormat(selectedPromoCode.discount_value)}%
                      </span>
                    )}
                  </Form.Field>
                )}
              </Form>
            </Card.Content>
          )}
        </Card>
      )}
    </div>
  );
};

export default PromoCodeForm;

import { Card, Grid } from 'semantic-ui-react';
import React from 'react';
import { useDispatch } from 'react-redux';
import { openLightbox } from '../../redux/lightbox/lightbox.slice';
import { ISelfie } from '../../entities/types/client-entity-types';

interface Props {
  selfie: ISelfie;
}

const SelfieModalConfirmSendSurvey = (props: Props) => {
  const { selfie } = props;
  const dispatch = useDispatch();

  const handleViewPhoto = () => {
    if (selfie.selfieImage) {
      dispatch(openLightbox(selfie.selfieImage));
    }
  };

  return (
    <Card
      fluid
      className="border-0 shadow-sm"
    >
      <Card.Content className="!p-0">
        <div className="bg-gray-50 px-4 py-3 border-b">
          <h3 className="text-gray-800 font-medium">Foto Selfie</h3>
        </div>
        <div className="p-4">
          <div className="bg-gray-50 rounded-lg p-3">
            <Grid>
              <Grid.Row className="py-1">
                <Grid.Column
                  width="5"
                  className="text-gray-600"
                >
                  Foto Selfie
                </Grid.Column>
                <Grid.Column width="11">
                  <button
                    onClick={handleViewPhoto}
                    className="text-blue-600 hover:text-blue-800 font-medium inline-flex items-center bg-transparent border-0 cursor-pointer p-0"
                  >
                    <span>Lihat Foto</span>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4 ml-1"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z" />
                      <path d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z" />
                    </svg>
                  </button>
                </Grid.Column>
              </Grid.Row>
            </Grid>
          </div>
        </div>
      </Card.Content>
    </Card>
  );
};

export default SelfieModalConfirmSendSurvey;

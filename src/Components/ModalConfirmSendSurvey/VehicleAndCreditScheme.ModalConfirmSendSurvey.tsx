import { Card, Grid } from 'semantic-ui-react';
import currencyFormat from '../../helpers/currencyFormat';
import React from 'react';
import ClientEntity from '../../entities/ClientEntity';

interface Props {
  clientEntity: ClientEntity;
  promoDownPayment: number;
}

const VehicleAndCreditSchemeModalConfirmSendSurvey = (props: Props) => {
  const clientEntity = props.clientEntity;

  return (
    <Card
      fluid
      className="border-0 shadow-sm"
    >
      <Card.Content className="!p-0">
        <div className="bg-gray-50 px-4 py-3 border-b flex items-center justify-between">
          <h3 className="text-gray-800 font-medium">Kendaraan dan Skema Kredit</h3>
          <div className="bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-medium shadow-sm ml-2">
            {clientEntity.profile.area?.text.toUpperCase()}
          </div>
        </div>

        <div className="p-4">
          {/* Vehicle Info */}
          <div className="mb-4">
            <div className="text-sm text-gray-500 mb-2">Kendaraan yang Dipilih</div>
            <div className="text-lg font-medium text-gray-800">
              {clientEntity?.dream_vehicle?.variant_name}
            </div>
          </div>

          <Grid
            stackable
            columns={2}
          >
            {/* Left Column */}
            <Grid.Column>
              {/* Credit Information */}
              <div className="mb-4">
                <div className="flex items-center mb-2">
                  <div className="w-2 h-2 rounded-full bg-blue-500 mr-2"></div>
                  <span className="text-sm font-medium text-gray-600">Informasi Uang Muka</span>
                </div>
                <div className="bg-gray-50 rounded p-3">
                  <Grid>
                    <Grid.Row className="py-1">
                      <Grid.Column
                        width="6"
                        className="text-gray-600"
                      >
                        Uang Muka Konsumen
                      </Grid.Column>
                      <Grid.Column
                        width="10"
                        className="text-gray-800 font-medium"
                      >
                        {currencyFormat(clientEntity.survey?.credit_scheme?.down_payment || 0)}
                      </Grid.Column>
                    </Grid.Row>
                    <Grid.Row className="py-1">
                      <Grid.Column
                        width="6"
                        className="text-gray-600"
                      >
                        Setelah Diskon
                      </Grid.Column>
                      <Grid.Column width="10">
                        <span className="text-gray-800 font-medium">
                          {currencyFormat(props.promoDownPayment)}
                        </span>
                        <span className="ml-2 text-xs text-gray-400">(Tidak dikirim ke B2B)</span>
                      </Grid.Column>
                    </Grid.Row>
                  </Grid>
                </div>
              </div>

              {/* Tenor Information */}
              <div>
                <div className="flex items-center mb-2">
                  <div className="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                  <span className="text-sm font-medium text-gray-600">Informasi Tenor</span>
                </div>
                <div className="bg-gray-50 rounded p-3">
                  <Grid>
                    <Grid.Row className="py-1">
                      <Grid.Column
                        width="6"
                        className="text-gray-600"
                      >
                        Tenor Awal
                      </Grid.Column>
                      <Grid.Column
                        width="10"
                        className="text-gray-800 font-medium"
                      >
                        {clientEntity.survey?.credit_scheme?.tenor ?? 0} kali
                      </Grid.Column>
                    </Grid.Row>
                    <Grid.Row className="py-1">
                      <Grid.Column
                        width="6"
                        className="text-gray-600"
                      >
                        Potongan
                      </Grid.Column>
                      <Grid.Column
                        width="10"
                        className="text-gray-800 font-medium"
                      >
                        {clientEntity.survey?.credit_scheme?.discountTenor ?? 0} kali
                      </Grid.Column>
                    </Grid.Row>
                    <Grid.Row className="py-1">
                      <Grid.Column
                        width="6"
                        className="text-gray-600"
                      >
                        Tenor Final
                      </Grid.Column>
                      <Grid.Column
                        width="10"
                        className="text-gray-800 font-medium"
                      >
                        {(clientEntity.survey?.credit_scheme?.tenor ?? 0) -
                          (clientEntity.survey?.credit_scheme?.discountTenor ?? 0)}{' '}
                        kali
                      </Grid.Column>
                    </Grid.Row>
                  </Grid>
                </div>
              </div>
            </Grid.Column>

            {/* Right Column */}
            <Grid.Column>
              {/* Installment Information */}
              <div>
                <div className="flex items-center mb-2">
                  <div className="w-2 h-2 rounded-full bg-yellow-500 mr-2"></div>
                  <span className="text-sm font-medium text-gray-600">Informasi Angsuran</span>
                </div>
                <div className="bg-gray-50 rounded p-3">
                  <Grid>
                    <Grid.Row className="py-1">
                      <Grid.Column
                        width="6"
                        className="text-gray-600"
                      >
                        Angsuran Awal
                      </Grid.Column>
                      <Grid.Column
                        width="10"
                        className="text-gray-800 font-medium"
                      >
                        {currencyFormat(clientEntity.survey?.credit_scheme?.installment ?? 0)}
                      </Grid.Column>
                    </Grid.Row>
                    <Grid.Row className="py-1">
                      <Grid.Column
                        width="6"
                        className="text-gray-600"
                      >
                        Potongan
                      </Grid.Column>
                      <Grid.Column
                        width="10"
                        className="text-gray-800 font-medium"
                      >
                        {currencyFormat(
                          clientEntity.survey?.credit_scheme?.discountInstallment ?? 0,
                        )}
                      </Grid.Column>
                    </Grid.Row>
                  </Grid>
                </div>

                {/* Final Installment */}
                <div className="mt-3 bg-green-100 rounded p-3">
                  <div className="flex justify-between items-center">
                    <span className="text-green-700 font-medium">Angsuran Final</span>
                    <span className="text-lg font-semibold text-green-700">
                      {currencyFormat(
                        (clientEntity.survey?.credit_scheme?.installment ?? 0) -
                          (clientEntity.survey?.credit_scheme?.discountInstallment ?? 0),
                      )}
                    </span>
                  </div>
                </div>
              </div>
            </Grid.Column>
          </Grid>
        </div>
      </Card.Content>
    </Card>
  );
};

export default VehicleAndCreditSchemeModalConfirmSendSurvey;

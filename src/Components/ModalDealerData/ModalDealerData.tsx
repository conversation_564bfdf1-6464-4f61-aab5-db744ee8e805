import React, { useEffect, use<PERSON><PERSON>back } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>er, Segment, Grid, Image, List, Icon } from 'semantic-ui-react';
import { useSelector } from 'react-redux';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { mainStore } from '../../redux/reducers';
import modalDealerDataSlice from '../../redux/modal-dealer-data/modalDealerDataSlice';
import { catalogueServices } from '../../services/catalogue/catalogueServices';
import { mainApiServices } from '../../services/MainApiServices';
import chatTextInputSlice from '../../redux/chat-text-input/chatTextInputSlice';
import { FiMapPin, FiPhone, FiClock, FiSend, FiHome, FiGlobe, FiMail } from 'react-icons/fi';
import {
  FaWhatsapp,
  FaInstagram,
  FaFacebook,
  <PERSON>a<PERSON><PERSON><PERSON>,
  <PERSON>aYoutube,
  FaTik<PERSON>,
} from 'react-icons/fa';

const actions = modalDealerDataSlice.actions;

export interface ModalDealerDataProps {
  onClick?: (send: { text: string; image?: string }) => void;
}

const ModalDealerData: React.FC<ModalDealerDataProps> = ({ onClick }) => {
  const modalDealerData = useSelector((state: TMainReduxStates) => state.modalDealerData);
  const conversation = useSelector((state: TMainReduxStates) => state.reducerConversation);
  const admin = useSelector((state: TMainReduxStates) => state.reducerAdmin);
  const project = useSelector((state: TMainReduxStates) => state.reducerProject);

  const { dealerData, fetching, errorMessage } = modalDealerData;

  const onClose = () => {
    mainStore.dispatch(actions.resetState());
  };

  // Handle sending individual branch data
  const handleSendBranch = useCallback(
    (branchData: any) => {
      if (!dealerData || !branchData) return;

      // Format pesan untuk cabang dealer
      let message = `*${dealerData.name}*\n`;
      message += `📍 *Cabang: ${branchData.name}*\n\n`;

      // Alamat
      message += `🏢 *Alamat:*\n${branchData.address}\n\n`;

      // Kontak
      message += `📞 *Telepon:* ${branchData.phone_number}\n`;
      if (branchData.whatsapp) {
        message += `💬 *WhatsApp:* ${branchData.whatsapp}\n`;
      }

      // Jam operasional
      if (branchData.operational_hours) {
        message += `\n🕒 *Jam Operasional:*\n${formatOperationalHours(branchData.operational_hours)}\n`;
      }

      // Koordinat jika ada
      if (branchData.latitude && branchData.longitude) {
        message += `\n📍 *Koordinat:* ${branchData.latitude}, ${branchData.longitude}\n`;
      }

      // Media sosial jika ada
      if (branchData.social_media && Object.keys(branchData.social_media).length > 0) {
        message += `\n🌐 *Media Sosial:*\n`;
        Object.entries(branchData.social_media).forEach(([platform, url]: [string, any]) => {
          message += `• ${platform.charAt(0).toUpperCase() + platform.slice(1)}: ${url}\n`;
        });
      }

      // Set text to chat input using the same pattern as ModalSelectCatalogue
      mainStore.dispatch(chatTextInputSlice.actions.setFile(null));
      mainStore.dispatch(chatTextInputSlice.actions.setHtmlContentEditable(message));

      // Close modal
      onClose();
    },
    [dealerData],
  );

  const fetchDealerData = async () => {
    // Default company to 'amarta' if not available
    const company = project.project?.group;

    let companyCode = 'amarta';

    if (company === 'amartamotor') {
      companyCode = 'amarta';
    } else if (company === 'amartamobil') {
      companyCode = 'vinfast';
    }

    mainStore.dispatch(actions.setFetching(true));
    mainStore.dispatch(actions.setErrorMessage(''));

    try {
      const response = await catalogueServices.getDealerData(companyCode);

      if (response.ok && response.data) {
        mainStore.dispatch(actions.setDealerData(response.data.data));
      } else {
        mainStore.dispatch(actions.setErrorMessage('Gagal mengambil data dealer'));
      }
    } catch (error: any) {
      mainStore.dispatch(actions.setErrorMessage(error.message || 'Terjadi kesalahan'));
    } finally {
      mainStore.dispatch(actions.setFetching(false));
    }
  };

  const onSendDealerData = () => {
    if (!dealerData) return;

    // Format pesan dealer data
    let message = `*${dealerData.name}*\n\n`;
    message += `🏢 *Alamat:*\n${dealerData.address.join('\n')}\n\n`;
    message += `📞 *Telepon:* ${dealerData.phone_number}\n`;
    message += `💬 *WhatsApp:* ${dealerData.whatsapp}\n\n`;

    if (dealerData.description) {
      message += `ℹ️ *Deskripsi:*\n${dealerData.description}\n\n`;
    }

    // Tambahkan informasi alamat detail jika ada
    if (dealerData.addresses && dealerData.addresses.length > 0) {
      message += `🏢 *Cabang:*\n`;
      dealerData.addresses.forEach((addr: any, index: number) => {
        message += `${index + 1}. ${addr.name}\n`;
        message += `   📍 ${addr.address}\n`;
        message += `   📞 ${addr.phone_number}\n`;
        if (addr.whatsapp) {
          message += `   💬 ${addr.whatsapp}\n`;
        }
        message += `\n`;
      });
    }

    // Set text to chat input using the same pattern as handleSendBranch
    mainStore.dispatch(chatTextInputSlice.actions.setFile(null));
    mainStore.dispatch(chatTextInputSlice.actions.setHtmlContentEditable(message));

    // Close modal
    onClose();
  };

  useEffect(() => {
    if (modalDealerData.open && !dealerData) {
      fetchDealerData();
    }
  }, [modalDealerData.open, dealerData]);

  const formatOperationalHours = (hours: any) => {
    if (!hours) return '-';

    const dayNames: { [key: string]: string } = {
      mon: 'Senin',
      tue: 'Selasa',
      wed: 'Rabu',
      thu: 'Kamis',
      fri: 'Jumat',
      sat: 'Sabtu',
      sun: 'Minggu',
    };

    return Object.entries(hours)
      .map(([day, time]: [string, any]) => `${dayNames[day]}: ${time.open} - ${time.close}`)
      .join(', ');
  };

  const getSocialMediaIcon = (platform: string) => {
    switch (platform.toLowerCase()) {
      case 'instagram':
        return <FaInstagram className="w-4 h-4" />;
      case 'facebook':
        return <FaFacebook className="w-4 h-4" />;
      case 'twitter':
      case 'x':
        return <FaTwitter className="w-4 h-4" />;
      case 'youtube':
        return <FaYoutube className="w-4 h-4" />;
      case 'tiktok':
        return <FaTiktok className="w-4 h-4" />;
      default:
        return <FiGlobe className="w-4 h-4" />;
    }
  };

  return (
    <Modal
      open={modalDealerData.open}
      onClose={onClose}
      size="large"
      className="!w-[95%] max-w-6xl"
    >
      <Modal.Header className="!flex !items-center !justify-between !p-6 !border-b !border-gray-200">
        <div className="flex items-center gap-3">
          <FiHome className="w-6 h-6 text-blue-600" />
          <span className="text-2xl font-semibold text-gray-800">Data Dealer</span>
        </div>
      </Modal.Header>

      <Modal.Content className="!p-6 !max-h-[70vh] !overflow-y-auto">
        {fetching && (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            <span className="ml-3 text-gray-600">Memuat data dealer...</span>
          </div>
        )}

        {errorMessage && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex items-center gap-2 text-red-800">
              <Icon name="exclamation triangle" />
              <span>{errorMessage}</span>
            </div>
          </div>
        )}

        {dealerData && !fetching && (
          <div className="space-y-6">
            {/* Header Info */}
            <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
              <div className="p-6">
                <div className="flex flex-col md:flex-row gap-6">
                  <div className="flex-shrink-0">
                    {dealerData.logo && (
                      <div className="w-32 h-32 bg-gray-50 rounded-lg flex items-center justify-center overflow-hidden">
                        <img
                          src={dealerData.logo}
                          alt={dealerData.name}
                          className="max-w-full max-h-full object-contain"
                        />
                      </div>
                    )}
                  </div>
                  <div className="flex-1 space-y-4">
                    <div>
                      <h2 className="text-2xl font-bold text-gray-900 mb-1">{dealerData.name}</h2>
                      <p className="text-gray-600">{dealerData.legal_name}</p>
                    </div>

                    {dealerData.description && (
                      <p className="text-gray-700 leading-relaxed">{dealerData.description}</p>
                    )}

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="flex items-center gap-2">
                        <FiPhone className="w-4 h-4 text-gray-400" />
                        <span className="text-sm text-gray-600">Telepon:</span>
                        <span className="text-sm font-medium text-gray-900">
                          {dealerData.phone_number}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <FaWhatsapp className="w-4 h-4 text-green-500" />
                        <span className="text-sm text-gray-600">WhatsApp:</span>
                        <span className="text-sm font-medium text-gray-900">
                          {dealerData.whatsapp}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <FiMapPin className="w-4 h-4 text-gray-400" />
                        <span className="text-sm text-gray-600">Wilayah:</span>
                        <span className="text-sm font-medium text-gray-900">
                          {dealerData.default_city_group}
                        </span>
                      </div>
                    </div>

                    {/* Social Media */}
                    {dealerData.social_media && (
                      <div>
                        <p className="text-sm text-gray-600 mb-2">Media Sosial:</p>
                        <div className="flex flex-wrap gap-2">
                          {Object.entries(dealerData.social_media).map(
                            ([platform, url]: [string, any]) => (
                              <a
                                key={platform}
                                href={url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="flex items-center gap-1 px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded-md text-xs text-gray-700 transition-colors"
                              >
                                {getSocialMediaIcon(platform)}
                                <span className="capitalize">{platform}</span>
                              </a>
                            ),
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Alamat Utama */}
            {dealerData.address && dealerData.address.length > 0 && (
              <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
                <div className="p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <FiMapPin className="w-5 h-5 text-blue-600" />
                    Alamat Utama
                  </h3>
                  <div className="space-y-2">
                    {dealerData.address.map((addr: string, index: number) => (
                      <div
                        key={index}
                        className="flex items-start gap-2 p-3 bg-gray-50 rounded-lg"
                      >
                        <FiMapPin className="w-4 h-4 text-gray-400 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700">{addr}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Cabang */}
            {dealerData.addresses && dealerData.addresses.length > 0 && (
              <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
                <div className="p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <FiHome className="w-5 h-5 text-blue-600" />
                    Daftar Cabang ({dealerData.addresses.length})
                  </h3>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    {dealerData.addresses.map((addr: any, index: number) => (
                      <div
                        key={index}
                        className="bg-gray-50 rounded-lg border border-gray-200 hover:shadow-md transition-shadow"
                      >
                        <div className="p-4">
                          <div className="flex items-start justify-between mb-3">
                            <h4 className="font-semibold text-gray-900">{addr.name}</h4>
                            <button
                              className="flex items-center gap-1 px-3 py-1.5 text-sm text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors"
                              onClick={() => handleSendBranch(addr)}
                            >
                              <FiSend className="w-4 h-4" />
                              Kirim
                            </button>
                          </div>

                          <div className="space-y-3">
                            <div className="flex items-start gap-2">
                              <FiMapPin className="w-4 h-4 text-gray-400 mt-0.5 flex-shrink-0" />
                              <span className="text-sm text-gray-700">{addr.address}</span>
                            </div>

                            <div className="flex items-center gap-2">
                              <FiPhone className="w-4 h-4 text-gray-400" />
                              <span className="text-sm text-gray-700">{addr.phone_number}</span>
                            </div>

                            {addr.whatsapp && (
                              <div className="flex items-center gap-2">
                                <FaWhatsapp className="w-4 h-4 text-green-500" />
                                <span className="text-sm text-gray-700">{addr.whatsapp}</span>
                              </div>
                            )}

                            {addr.operational_hours && (
                              <div className="flex items-start gap-2">
                                <FiClock className="w-4 h-4 text-gray-400 mt-0.5 flex-shrink-0" />
                                <div>
                                  <p className="text-xs text-gray-500 mb-1">Jam Operasional:</p>
                                  <p className="text-sm text-gray-700">
                                    {formatOperationalHours(addr.operational_hours)}
                                  </p>
                                </div>
                              </div>
                            )}

                            {/* Social Media untuk cabang */}
                            {addr.social_media && (
                              <div>
                                <p className="text-xs text-gray-500 mb-2">Media Sosial:</p>
                                <div className="flex flex-wrap gap-1">
                                  {Object.entries(addr.social_media).map(
                                    ([platform, url]: [string, any]) => (
                                      <a
                                        key={platform}
                                        href={url}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="flex items-center gap-1 px-2 py-1 bg-white hover:bg-gray-100 rounded text-xs text-gray-600 transition-colors"
                                      >
                                        {getSocialMediaIcon(platform)}
                                      </a>
                                    ),
                                  )}
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </Modal.Content>

      <Modal.Actions className="!flex !items-center !justify-end !gap-3 !p-6 !bg-gray-50 !border-t !border-gray-200">
        <Button
          onClick={onClose}
          className="!bg-white !text-gray-700 !border !border-gray-300 hover:!bg-gray-50"
        >
          Tutup
        </Button>
        <Button
          primary
          onClick={onSendDealerData}
          disabled={!dealerData || fetching}
          className="!flex !items-center !gap-2 !bg-blue-600 !text-white hover:!bg-blue-700 disabled:!opacity-50"
        >
          <FiSend className="w-4 h-4" />
          Kirim Semua Data
        </Button>
      </Modal.Actions>
    </Modal>
  );
};

export default ModalDealerData;
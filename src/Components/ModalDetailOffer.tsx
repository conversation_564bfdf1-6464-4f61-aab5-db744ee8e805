import React, { Component } from 'react';
import { Button, Dropdown, Icon, Image, Modal, Segment } from 'semantic-ui-react';
import currencyFormat from '../helpers/currencyFormat';
import { TMainReduxStates } from '../redux/types/redux-types';
import { connect } from 'react-redux';
import moment from 'moment';
import { IGetOfferSuccessResponse } from '../services/types/offerServiceTypes';
import { mainStore } from '../redux/reducers';
import modalDetailOfferSlice, {
  modalDetailOfferFetch,
} from '../redux/modal-detail-offer/modalDetailOfferSlice';
import modalUpdateDataToOtodisSlice from '../redux/modal-update-data-to-otodis/modalUpdateDataToOtodisSlice';
import modalUpdateDataPromoCodeToOtodisSlice from '../redux/modal-update-data-to-otodis/modalUpdateDataPromoCodeToOtodisSlice';
import { offerServices } from '../services/offerServices';
import { Timestamp, updateDoc } from 'firebase/firestore';
import confirmDialog from './callableDialog/confirmDialog';
import errorDialog from './callableDialog/errorDialog';
import successDialog from './callableDialog/successDialog';
import { AxiosError } from 'axios';
import { ErrorResponseUpdateDataToOtodis } from '../services/types/offerServices.types';
import { openModalCheckBbnThunk } from '../redux/modal-bbn/modalCheckBbnSlice';
import paymentReceiptServices from '../services/paymentReceiptServices/paymentReceiptServices';
import { mainApiServices } from '../services/MainApiServices';

interface Props {
  modal: TMainReduxStates['modalDetailOfferCode'];
  admin: TMainReduxStates['reducerAdmin'];
  customer: TMainReduxStates['customerReducer'];
  conversation: TMainReduxStates['reducerConversation'];
}

class ModalDetailOffer extends Component<Props> {
  onClose = () => {
    mainStore.dispatch(modalDetailOfferSlice.actions.open({ open: false }));
  };

  qcStatus = () => {
    if (this.props.modal.dataOfferCode?.offer_status === 'ORDER') {
      return 'TRUE';
    } else {
      return this.props.modal.dataOfferCode?.qc_status ? 'TRUE' : 'FALSE';
    }
  };

  idCardRender = (title: string, idCard: IGetOfferSuccessResponse['data']['id_card_guarantor']) => {
    return (
      <Segment className={'offer-detail-section'}>
        <div className={'offer-detail-section-title'}>{title}</div>
        <div>
          <Image
            src={idCard.id_card_image}
            bordered={true}
            size={'large'}
          />
        </div>
        <div className={'offer-detail-section-description'}>
          <div className={'offer-detail-item-description-row'}>
            <div>Nama Lengkap</div>
            <div>{idCard.full_name}</div>
          </div>
          <div className={'offer-detail-item-description-row'}>
            <div>Tempat Lahir</div>
            <div>{idCard.birth_place}</div>
          </div>
          <div className={'offer-detail-item-description-row'}>
            <div>Tanggal Lahir</div>
            <div>{moment(idCard.birth_date).format('YYYY-MM-DD')}</div>
          </div>
          <div className={'offer-detail-item-description-row'}>
            <div>Alamat</div>
            <div>{idCard.full_address}</div>
          </div>
          <div className={'offer-detail-item-description-row'}>
            <div>Status Kawin</div>
            <div>{idCard.marital_status}</div>
          </div>
          <div className={'offer-detail-item-description-row'}>
            <div>Pekerjaan</div>
            <div>{idCard.occupation}</div>
          </div>
        </div>
      </Segment>
    );
  };

  updateDataToOtodis = () => {
    mainStore.dispatch(modalUpdateDataToOtodisSlice.actions.open());
  };

  updateDataPromoCodeToOtodis = () => {
    mainStore.dispatch(modalUpdateDataPromoCodeToOtodisSlice.actions.open());
  };

  createBill = async () => {
    const confirm = await confirmDialog({
      content: 'Lanjutkan membuat bill?',
      title: 'Konfirmasi',
      okButton: 'Ya',
      cancelButton: 'Batal',
    });

    if (!confirm) return;

    mainStore.dispatch(modalDetailOfferSlice.actions.setCreatingBillLoading(true));

    let { customer, admin, modal } = this.props;
    try {
      await offerServices.createBill({
        admin_name: admin.admin?.name || '',
        admin_id: admin.admin?.email || '',
        offer_code: modal.offerCodeToView || '',
        company: 'AMARTA',
        update_type: 'create-bill-offer',
      });

      const listOrderHistory = [...(customer.client?.order_histories || [])];
      const findIndex = listOrderHistory.findIndex(
        (o) => o.offer_code === modal.orderHistory?.offer_code,
      );

      if (findIndex >= 0) {
        listOrderHistory[findIndex] = {
          ...listOrderHistory[findIndex],
          createBill: {
            success: true,
            updatedAt: Timestamp.now(),
            offerCode: modal.offerCodeToView || '',
            dataUpdate: null,
          },
        };

        await updateDoc(customer.ref!, {
          order_histories: listOrderHistory,
        });
      }

      mainStore.dispatch(modalDetailOfferSlice.actions.setCreatingBillLoading(false));

      await successDialog({
        title: 'Sukses',
        content: 'Berhasil membuat bill',
        okButton: 'OK',
        cancelButton: false,
      });

      mainStore.dispatch(modalDetailOfferFetch(modal.offerCodeToView || '') as any);
    } catch (e) {
      const error = e as AxiosError<ErrorResponseUpdateDataToOtodis>;
      errorDialog({
        title: 'Error',
        content: error.response?.data.error.message || 'Gagal create bill',
        cancelButton: 'Tutup',
        okButton: false,
      });
      mainStore.dispatch(modalDetailOfferSlice.actions.setCreatingBillLoading(false));
    }
  };

  sendPaymentReceipt = async () => {
    mainStore.dispatch(
      mainStore.dispatch(modalDetailOfferSlice.actions.setSendPaymentReceiptLoading(true)),
    );
    const check = await paymentReceiptServices({
      company: 'amarta',
      offerCode: this.props.modal.offerCodeToView || '',
      // offerCode: "11346722316820341",
      adminName: this.props.admin.admin?.name || '',
    });

    if (check) {
      await mainApiServices.sendMessageV2({
        adminSessionPath: this.props.admin.adminSession!.ref.path,
        roomPath: this.props.conversation.chatRoom!.ref!.path,
        phoneNumber: this.props.conversation.chatRoom?.contacts[0] ?? '',
        text: 'Bukti Pembayaran',
        fileUrl: check,
        filename: `payment_receipt_${this.props.modal.offerCodeToView}.pdf`,
        fileType: 'document',
      });

      mainStore.dispatch(mainStore.dispatch(modalDetailOfferSlice.actions.open({ open: false })));
    } else {
      errorDialog({
        title: 'Error',
        content: 'Belum ada pembayaran',
        cancelButton: 'Tutup',
        okButton: false,
      });
      mainStore.dispatch(modalDetailOfferSlice.actions.setSendPaymentReceiptLoading(false));
    }
  };

  render() {
    let { dataOfferCode, open } = this.props.modal;
    return (
      <Modal
        open={open}
        onClose={this.onClose}
        closeOnDimmerClick={true}
      >
        <Modal.Header>Detail Offer</Modal.Header>

        {this.props.modal.errorMessage && (
          <Modal.Content>
            <Segment>{this.props.modal.errorMessage}</Segment>
          </Modal.Content>
        )}

        {this.props.modal.fetching && (
          <Modal.Content>
            <Segment>
              <Icon
                name={'spinner'}
                loading={true}
              />
              <span>Mohon Tunggu</span>
            </Segment>
          </Modal.Content>
        )}

        {dataOfferCode && !this.props.modal.fetching && (
          <Modal.Content>
            <Dropdown
              text={'Aksi'}
              button={true}
            >
              <Dropdown.Menu>
                <Dropdown.Item onClick={this.updateDataToOtodis}>
                  Update Data ke Otodis
                </Dropdown.Item>
                <Dropdown.Item onClick={this.updateDataPromoCodeToOtodis}>
                  Update Kode Promo ke Otodis
                </Dropdown.Item>
                <Dropdown.Item onClick={this.createBill}>Create Bill</Dropdown.Item>
                <Dropdown.Divider />
                <Dropdown.Item
                  onClick={(event) => {
                    mainStore.dispatch(
                      openModalCheckBbnThunk(dataOfferCode.order?.product || '') as any,
                    );
                  }}
                >
                  Cek BBN
                </Dropdown.Item>
                <Dropdown.Item
                  onClick={this.sendPaymentReceipt}
                  disabled={this.props.modal.sendingPaymentReceipt}
                >
                  Kirim Bukti Pembayaran
                </Dropdown.Item>
              </Dropdown.Menu>
            </Dropdown>

            {this.props.modal.sendingPaymentReceipt && (
              <Segment basic={true}>
                <Icon
                  name={'spinner'}
                  loading={true}
                />{' '}
                Mengirim Bukti Pembayaran ...
              </Segment>
            )}

            {this.props.modal.orderHistory?.lastUpdateDataToOtodis && (
              <Segment className={'offer-detail-section'}>
                <div className={'offer-detail-section-title'}>Terakhir update Data ke Otodis</div>
                <div className={'offer-detail-section-description'}>
                  <div className={'offer-detail-item-description-row'}>
                    <div>Status</div>
                    <div>
                      {this.props.modal.orderHistory?.lastUpdateDataToOtodis?.success
                        ? 'Berhasil'
                        : 'Gagal'}
                    </div>
                  </div>
                  <div className={'offer-detail-item-description-row'}>
                    <div>Tanggal</div>
                    <div>
                      {moment(
                        this.props.modal.orderHistory?.lastUpdateDataToOtodis?.updatedAt.toDate(),
                      ).format('YYYY-MM-DD HH:mm')}
                    </div>
                  </div>
                </div>
              </Segment>
            )}

            {this.props.modal.orderHistory?.lastUpdateDataPromoCodeToOtodis && (
              <Segment className={'offer-detail-section'}>
                <div className={'offer-detail-section-title'}>
                  Terakhir update Kode Promo ke Otodis
                </div>
                <div className={'offer-detail-section-description'}>
                  <div className={'offer-detail-item-description-row'}>
                    <div>Status</div>
                    <div>
                      {this.props.modal.orderHistory?.lastUpdateDataPromoCodeToOtodis?.success
                        ? 'Berhasil'
                        : 'Gagal'}
                    </div>
                  </div>
                  <div className={'offer-detail-item-description-row'}>
                    <div>Tanggal</div>
                    <div>
                      {moment(
                        this.props.modal.orderHistory?.lastUpdateDataPromoCodeToOtodis?.updatedAt.toDate(),
                      ).format('YYYY-MM-DD HH:mm')}
                    </div>
                  </div>
                </div>
              </Segment>
            )}

            {this.props.modal.orderHistory?.createBill && (
              <Segment className={'offer-detail-section'}>
                <div className={'offer-detail-section-title'}>Buat Bill</div>
                <div className={'offer-detail-section-description'}>
                  <div className={'offer-detail-item-description-row'}>
                    <div>Status</div>
                    <div>
                      {this.props.modal.orderHistory?.createBill?.success ? 'Berhasil' : 'Gagal'}
                    </div>
                  </div>
                  <div className={'offer-detail-item-description-row'}>
                    <div>Tanggal</div>
                    <div>
                      {moment(
                        this.props.modal.orderHistory?.lastUpdateDataPromoCodeToOtodis?.updatedAt.toDate(),
                      ).format('YYYY-MM-DD HH:mm')}
                    </div>
                  </div>
                </div>
              </Segment>
            )}

            <Segment className={'offer-detail-section'}>
              <div className={'offer-detail-section-title'}>Data Offer</div>
              <div className={'offer-detail-section-description'}>
                <div className={'offer-detail-item-description-row'}>
                  <div>No Transaksi</div>
                  <div>{dataOfferCode.transaction_code}</div>
                </div>
                <div className={'offer-detail-item-description-row'}>
                  <div>No Order</div>
                  <div>{dataOfferCode.order?.code || <i>Belum Ada</i>}</div>
                </div>
                <div className={'offer-detail-item-description-row'}>
                  <div>Area</div>
                  <div>{dataOfferCode.area}</div>
                </div>
                <div className={'offer-detail-item-description-row'}>
                  <div>Metode Pembelian</div>
                  <div>{dataOfferCode.purchase_method}</div>
                </div>
                <div className={'offer-detail-item-description-row'}>
                  <div>Tanggal Transaksi</div>
                  <div>{moment(dataOfferCode.transaction_time).format('YYYY-MM-DD')}</div>
                </div>
                {dataOfferCode.detail_promo[0] && (
                  <div className={'offer-detail-item-description-row'}>
                    <div>Kode Promo</div>
                    <div>{dataOfferCode.detail_promo[0].code}</div>
                  </div>
                )}
                <div className={'offer-detail-item-description-row'}>
                  <div>QC Status</div>
                  <div>{this.qcStatus()}</div>
                </div>
                <div className={'offer-detail-item-description-row'}>
                  <div>Status Offer</div>
                  <div>{dataOfferCode.offer_status}</div>
                </div>
              </div>
            </Segment>

            <Segment className={'offer-detail-section'}>
              <div className={'offer-detail-section-title'}>Nomor Telepon</div>
              <div className={'offer-detail-section-description'}>
                <div className={'offer-detail-item-description-row'}>
                  <div>Pemilik</div>
                  <div>{dataOfferCode.contact.phone_number_owner}</div>
                </div>
                <div className={'offer-detail-item-description-row'}>
                  <div>Pemesan</div>
                  <div>{dataOfferCode.contact.phone_number_order_maker}</div>
                </div>
                <div className={'offer-detail-item-description-row'}>
                  <div>Penjamin</div>
                  <div>{dataOfferCode.contact.phone_number_guarantor}</div>
                </div>
              </div>
            </Segment>

            <Segment className={'offer-detail-section'}>
              <div className={'offer-detail-section-title'}>Kendaraan</div>
              <div className={'offer-detail-section-description'}>
                <div className={'offer-detail-item-description-row'}>
                  <div>Model</div>
                  <div>{dataOfferCode.vehicle.model_name}</div>
                </div>
                <div className={'offer-detail-item-description-row'}>
                  <div>Variant</div>
                  <div>{dataOfferCode.vehicle.variant_name}</div>
                </div>
                <div className={'offer-detail-item-description-row'}>
                  <div>Warna</div>
                  <div>{dataOfferCode.vehicle.color_name}</div>
                </div>
                <div className={'offer-detail-item-description-row'}>
                  <div>No Mesin</div>
                  <div>{dataOfferCode.order?.product || <i>Belum Ada</i>}</div>
                </div>
              </div>
            </Segment>

            {dataOfferCode.purchase_method === 'credit' && dataOfferCode.credit && (
              <Segment className={'offer-detail-section'}>
                <div className={'offer-detail-section-title'}>Skema Kredit</div>
                <div className={'offer-detail-section-description'}>
                  <div className={'offer-detail-item-description-row'}>
                    <div>Uang Muka</div>
                    <div>{currencyFormat(dataOfferCode.credit.dp_amount)}</div>
                  </div>
                  <div className={'offer-detail-item-description-row'}>
                    <div>Cicilan</div>
                    <div>{currencyFormat(dataOfferCode.credit.installment_amount)}</div>
                  </div>
                  <div className={'offer-detail-item-description-row'}>
                    <div>Tenor</div>
                    <div>{dataOfferCode.credit.tenor} kali</div>
                  </div>
                  <div className={'offer-detail-item-description-row'}>
                    <div>Leasing</div>
                    <div>
                      {dataOfferCode.credit.finco_name} - {dataOfferCode.credit.finco_code}
                    </div>
                  </div>
                  <div className={'offer-detail-item-description-row'}>
                    <div>Kode Promo</div>
                    <div>{dataOfferCode.detail_promo?.[0]?.code || 'Tidak ada'}</div>
                  </div>
                  <div className={'offer-detail-item-description-row'}>
                    <div>Diskon</div>
                    <div>{currencyFormat(dataOfferCode.detail_promo?.[0]?.discount || 0)}</div>
                  </div>
                  <div className={'offer-detail-item-description-row'}>
                    <div>Total Tagihan</div>
                    <div>{currencyFormat(dataOfferCode.price_and_bill.total_bill)}</div>
                  </div>
                </div>
              </Segment>
            )}

            {this.idCardRender('KTP Pemilik', dataOfferCode.id_card_owner)}
            {this.idCardRender('KTP Pemesan', dataOfferCode.id_card_order_maker)}
            {this.idCardRender('KTP Penjamin', dataOfferCode.id_card_guarantor)}

            {/*{this.addressRender()}*/}
            {/*{this.addressRender()}*/}
            {/*{this.addressRender()}*/}
          </Modal.Content>
        )}

        <Modal.Actions>
          <Button onClick={this.onClose}>Tutup</Button>
        </Modal.Actions>
      </Modal>
    );
  }
}

const mapStateToProps = (states: TMainReduxStates) => {
  return {
    modal: states.modalDetailOfferCode,
    conversation: states.reducerConversation,
    customer: states.customerReducer,
    admin: states.reducerAdmin,
  };
};

export default connect(mapStateToProps)(ModalDetailOffer);

import React, { ChangeEvent, Component } from 'react';
import { Button, Card, Form, Input, Message, Modal, Segment, Select } from 'semantic-ui-react';
import { TMainReduxStates } from '../redux/types/redux-types';
import { connect } from 'react-redux';
import { mainStore } from '../redux/reducers';
import modalInitMessageSlice from '../redux/modal-init-message/modalInitMessageSlice';
import { collection, getDocs, query, where } from 'firebase/firestore';
import ProviderTemplate from '../entities/ProviderTemplate';
import { DropdownProps } from 'semantic-ui-react/dist/commonjs/modules/Dropdown/Dropdown';
import { InputOnChangeData } from 'semantic-ui-react/dist/commonjs/elements/Input/Input';
import { mainApiServices } from '../services/MainApiServices';
import maskSanitizedPhoneNumber from '../helpers/maskPhoneNumber/maskSanitizedPhoneNumber';
import { IoSend, IoPhonePortrait, IoDocumentText, IoClose } from 'react-icons/io5';
import { BiMessageDetail } from 'react-icons/bi';

interface StateProps {
  modal: TMainReduxStates['modalInitMessageReducer'];
  project: TMainReduxStates['reducerProject']['project'];
  admin: TMainReduxStates['reducerAdmin'];
}

interface Props extends StateProps {}

interface States {
  templates: ProviderTemplate[];
  selectedTemplate: null | ProviderTemplate;
  phoneNumber: string;
  variableVal: string[];
  sending: boolean;

  success: boolean;
  errorMessages: string;
}

class ModalInitMessage extends Component<Props, States> {
  constructor(props: Props) {
    super(props);

    this.state = {
      phoneNumber: this.props.modal.preContact?.phoneNumber ?? '',
      templates: [],
      selectedTemplate: null,
      variableVal: [],

      sending: false,

      success: false,
      errorMessages: '',
    };
  }

  onClose = () => {
    mainStore.dispatch(
      modalInitMessageSlice.actions.open({
        open: false,
      }),
    );
  };

  submit = async () => {
    if (!this.state.selectedTemplate) return;
    if (!this.props.project) return;

    this.setState(
      {
        sending: true,
      },
      async () => {
        let currentState: States = Object.assign({}, this.state);
        try {
          await mainApiServices.sendTemplateInitMessage({
            phoneNumber: this.state.phoneNumber,
            variables: this.state.variableVal,
            templateName: this.state.selectedTemplate!.id,
            projectPath: this.props.project!.ref.path,
          });

          currentState.success = true;
        } catch (e: any) {
          currentState.errorMessages = 'Gagal mengirimkan template';
        }

        this.setState({
          ...currentState,
          sending: false,
        });
      },
    );
  };

  fetch = async () => {
    if (!this.props.project) return;

    const templateCollection = collection(this.props.project.ref, 'provider_templates');

    const get = await getDocs(
      query(
        templateCollection.withConverter(ProviderTemplate.converter),
        where('idealInitMessage', '==', true),
      ),
    );

    const t: ProviderTemplate[] = [];

    get.forEach((result) => {
      const data = result.data();
      t.push(data);
    });

    this.setState({
      templates: t,
    });
  };

  onPhoneNumberChange = (e: ChangeEvent, data: InputOnChangeData) => {
    this.setState({
      phoneNumber: data.value,
    });
  };

  onTemplateChange = (e: React.SyntheticEvent, d: DropdownProps) => {
    const find = this.state.templates.find((t) => t.id === d.value);

    if (!find) return;

    this.setState({
      selectedTemplate: find,
      variableVal: find.variables.map(() => ''),
    });
  };

  onVarValChange = (val: string, index: number) => {
    const varStates = JSON.parse(JSON.stringify(this.state.variableVal));
    varStates[index] = val;
    this.setState({
      variableVal: varStates,
    });
  };

  componentDidMount() {
    this.fetch().then();
  }

  render() {
    const { modal } = this.props;
    return (
      <Modal
        open={modal.open}
        onClose={this.onClose}
        size={'tiny'}
        closeOnEscape={false}
        className="!rounded-xl shadow-xl"
      >
        <div className="bg-gray-50 px-6 py-4 flex items-center justify-between border-b border-gray-200">
          <div className="flex items-center gap-2">
            <BiMessageDetail className="text-xl text-blue-500" />
            <h2 className="text-lg font-semibold text-gray-800">Kirim Pesan</h2>
          </div>
          <button
            onClick={this.onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <IoClose className="text-xl" />
          </button>
        </div>

        <Modal.Content className="!p-6 !bg-white">
          <Form>
            <Form.Field
              required={true}
              className="!mb-6"
            >
              <label className="block text-sm font-medium text-gray-700 mb-2">Nomor Telepon</label>
              {this.props.modal.preContact ? (
                <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg border border-gray-200">
                  <IoPhonePortrait className="text-gray-400 text-lg" />
                  {this.props.admin.admin!.admin_rank === 1 ? (
                    <div>
                      <div className="font-medium text-gray-800">
                        {this.props.modal.preContact.phoneNumber}
                      </div>
                      <div className="text-sm text-gray-500">
                        {this.props.modal.preContact.name}
                      </div>
                    </div>
                  ) : (
                    <span className="text-gray-600 italic">
                      {maskSanitizedPhoneNumber(this.props.modal.preContact.phoneNumber)}
                    </span>
                  )}
                </div>
              ) : (
                <Input
                  value={this.state.phoneNumber}
                  placeholder="Masukan nomor telepon"
                  onChange={this.onPhoneNumberChange}
                  className="w-full"
                  icon={'phone'}
                  iconPosition="left"
                />
              )}
            </Form.Field>

            <Form.Field
              required={true}
              className="!mb-6"
            >
              <label className="block text-sm font-medium text-gray-700 mb-2">Pilih Template</label>
              <Select
                search={true}
                value={this.state.selectedTemplate?.id ?? undefined}
                placeholder="Pilih Template"
                options={this.state.templates.map((t) => ({
                  text: t.id,
                  value: t.id,
                  key: t.id,
                }))}
                onChange={this.onTemplateChange}
                className="w-full"
              />
            </Form.Field>

            {this.state.selectedTemplate && (
              <Card
                fluid
                className="!shadow-none !border !border-gray-200 !rounded-lg !mb-6"
              >
                <Card.Content className="!p-4">
                  <div className="flex items-start gap-2">
                    <IoDocumentText className="text-blue-500 text-lg mt-1" />
                    <div className="text-gray-700 whitespace-pre-wrap">
                      {this.state.selectedTemplate.body}
                    </div>
                  </div>
                </Card.Content>
              </Card>
            )}

            {this.state.selectedTemplate?.variables &&
              this.state.selectedTemplate.variables.length > 0 && (
                <Segment className="!shadow-none !border !border-gray-200 !rounded-lg">
                  <h3 className="text-base font-medium text-gray-800 mb-4">Variable</h3>
                  <div className="space-y-4">
                    {this.state.selectedTemplate.variables.map((v, i) => (
                      <Form.Field
                        key={i.toString()}
                        required={true}
                      >
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          {v.name.toUpperCase()}
                        </label>
                        <Input
                          value={this.state.variableVal[i] ?? undefined}
                          onChange={(event, data) => this.onVarValChange(data.value, i)}
                          className="w-full"
                          placeholder={`Masukan ${v.name}`}
                        />
                      </Form.Field>
                    ))}
                  </div>
                </Segment>
              )}
          </Form>

          {this.state.success && (
            <Message
              positive={true}
              className="!mt-6 !rounded-lg !shadow-sm [animation:fadeIn_0.3s_ease-in-out]"
            >
              <div className="flex items-center gap-2">
                <IoSend className="text-green-600" />
                <span>Sukses mengirim template</span>
              </div>
            </Message>
          )}

          {this.state.errorMessages && (
            <Message
              negative={true}
              className="!mt-6 !rounded-lg !shadow-sm [animation:fadeIn_0.3s_ease-in-out]"
            >
              <div className="flex items-center gap-2">
                <IoClose className="text-red-600" />
                <span>{this.state.errorMessages}</span>
              </div>
            </Message>
          )}
        </Modal.Content>

        <div className="bg-gray-50 px-6 py-4 flex justify-end gap-3 border-t border-gray-200">
          <Button
            onClick={this.onClose}
            className="!bg-white !text-gray-700 hover:!bg-gray-100 !transition-colors !border !border-gray-200 !rounded-lg"
          >
            Tutup
          </Button>
          <Button
            primary
            onClick={this.submit}
            loading={this.state.sending}
            disabled={this.state.sending}
            className="!bg-blue-500 hover:!bg-blue-600 !transition-colors !rounded-lg !flex !items-center !gap-2"
          >
            <IoSend />
            <span>Kirim</span>
          </Button>
        </div>
      </Modal>
    );
  }
}

const mapStateToProps = (s: TMainReduxStates) => {
  return {
    modal: s.modalInitMessageReducer,
    project: s.reducerProject.project,
    admin: s.reducerAdmin,
  };
};

export default connect(mapStateToProps)(ModalInitMessage);

import React, { Component } from 'react';
import {
  But<PERSON>,
  Container,
  Divider,
  Form,
  Icon,
  Input,
  Message,
  Modal,
  Table,
  TableBody,
  TableHeader,
  TableHeaderCell,
  TableRow,
} from 'semantic-ui-react';
import { connect } from 'react-redux';
import { TMainReduxStates } from '../redux/types/redux-types';
import { mainStore } from '../redux/reducers';
import acquisitionOfferCodeSlice, {
  submitSearchOfferCodeByPOThunk,
} from '../redux/acquisition-offer-code/acquisitionOfferCode.slice';
import b2bServices from '../services/b2b/b2bServices';
import successDialog from './callableDialog/successDialog';
import { fetchStatusB2b } from '../redux/modalStatusB2b/modalStatusB2bSlice';
import errorDialog from './callableDialog/errorDialog';
import currencyFormat from '../helpers/currencyFormat';
import { AxiosError } from 'axios';

interface Props {
  modalAcquisition: TMainReduxStates['acquisitionOfferCodeSlice'];
  modalStatusB2b: TMainReduxStates['modalStatusB2b'];
  admin: TMainReduxStates['reducerAdmin'];
}

class ModalOfferAcquisition extends Component<Props> {
  disableButtonSubmit = () => {
    if (!this.props.modalAcquisition.dataOffer) return true;
    else if (this.props.modalAcquisition.submitting) return true;

    return false;
  };

  onSubmit = async () => {
    mainStore.dispatch(acquisitionOfferCodeSlice.actions.setSubmitStatus(true));
    try {
      await b2bServices.acquisition({
        offerCodeInB2b: this.props.modalAcquisition.offerCode,
        offerCodeInOtodis: this.props.modalAcquisition.dataOffer?.transaction_code || '',
        poNumber: this.props.modalAcquisition.dataOffer?.credit.po_code || '',
        adminId: this.props.admin.admin?.email || '',
        adminName: this.props.admin.admin?.name || '',
      });

      mainStore.dispatch(fetchStatusB2b(this.props.modalAcquisition.offerCode) as any);

      await successDialog({
        content: 'Kode offer di otodis berhasil diakuisisi ke b2b',
        title: 'Sukses',
        okButton: 'OK',
        cancelButton: false,
      });

      mainStore.dispatch(acquisitionOfferCodeSlice.actions.close());
    } catch (e) {
      let errorMessage = 'Kode offer di otodis gagal diakuisisi di b2b';
      const error = e as AxiosError<any>;
      errorMessage = error.response?.data.data?.messages || errorMessage;
      await errorDialog({
        content: errorMessage,
        title: 'Gagal',
        okButton: 'OK',
        cancelButton: false,
      });
      mainStore.dispatch(acquisitionOfferCodeSlice.actions.setSubmitStatus(false));
    }
  };

  diffSchemeCreditCheck = () => {
    return {
      dp:
        this.props.modalStatusB2b.dataStatusB2b?.survey.credit_scheme.down_payment !==
        this.props.modalAcquisition.dataOffer?.credit.dp_amount,
      tenor:
        this.props.modalStatusB2b.dataStatusB2b?.survey.credit_scheme.tenor !==
        this.props.modalAcquisition.dataOffer?.credit.tenor,
      installment:
        this.props.modalStatusB2b.dataStatusB2b?.survey.credit_scheme.installment !==
        this.props.modalAcquisition.dataOffer?.credit.installment_amount,
    };
  };

  render() {
    return (
      <Modal
        open={this.props.modalAcquisition.open}
        size={'small'}
      >
        <Modal.Header>Akuisisi Kode Offer</Modal.Header>
        <Modal.Content>
          <Form>
            <Form.Field>
              <label>No PO</label>
              <Input
                action={{
                  content: 'Cari',
                  loading: this.props.modalAcquisition.searchingDataOffer,
                  onClick: () => {
                    mainStore.dispatch(submitSearchOfferCodeByPOThunk() as any);
                  },
                }}
                placeholder={'Masukan Nomor PO'}
                onChange={(event, data) => {
                  mainStore.dispatch(acquisitionOfferCodeSlice.actions.changePoNumber(data.value));
                }}
                value={this.props.modalAcquisition.poNumber}
              />
            </Form.Field>
          </Form>
          <Divider />
          {!this.props.modalAcquisition.dataOffer && (
            <Container>
              <span>Hasil pencarian akan muncul disini</span>
            </Container>
          )}
          {this.props.modalAcquisition.dataOffer && (
            <div>
              {this.diffSchemeCreditCheck().dp && (
                <Message
                  size={'small'}
                  warning={true}
                >
                  <p>
                    {' '}
                    <Icon name="attention" /> Ada perbedaan pada Uang Muka
                  </p>
                </Message>
              )}
              {this.diffSchemeCreditCheck().tenor && (
                <Message
                  size={'small'}
                  warning={true}
                >
                  <p>
                    {' '}
                    <Icon name="attention" /> Ada perbedaan pada Tenor
                  </p>
                </Message>
              )}
              {this.diffSchemeCreditCheck().installment && (
                <Message
                  size={'small'}
                  warning={true}
                >
                  <p>
                    {' '}
                    <Icon name="attention" /> Ada perbedaan pada angsuran
                  </p>
                </Message>
              )}
              <Table definition={true}>
                <TableHeader>
                  <TableRow>
                    <TableHeaderCell></TableHeaderCell>
                    <TableHeaderCell>Otodis</TableHeaderCell>
                    <TableHeaderCell>B2B</TableHeaderCell>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <Table.Row>
                    <Table.Cell>No. Offer</Table.Cell>
                    <Table.Cell>
                      {this.props.modalAcquisition.dataOffer?.transaction_code}
                    </Table.Cell>
                    <Table.Cell>
                      {this.props.modalStatusB2b.dataStatusB2b?.survey.offer_code}
                    </Table.Cell>
                  </Table.Row>
                  <Table.Row>
                    <Table.Cell>No. PO</Table.Cell>
                    <Table.Cell>{this.props.modalAcquisition.dataOffer?.credit.po_code}</Table.Cell>
                    <Table.Cell></Table.Cell>
                  </Table.Row>
                  <Table.Row>
                    <Table.Cell>Nm. Pemesan</Table.Cell>
                    <Table.Cell>
                      {this.props.modalAcquisition.dataOffer?.id_card_order_maker.full_name}
                    </Table.Cell>
                    <Table.Cell>
                      {this.props.modalStatusB2b.dataStatusB2b?.survey.order_make_id_card.full_name}
                    </Table.Cell>
                  </Table.Row>
                  <Table.Row>
                    <Table.Cell>Nm. Pemilik</Table.Cell>
                    <Table.Cell>
                      {this.props.modalAcquisition.dataOffer?.id_card_owner.full_name}
                    </Table.Cell>
                    <Table.Cell>
                      {this.props.modalStatusB2b.dataStatusB2b?.survey.owner_id_card.full_name}
                    </Table.Cell>
                  </Table.Row>
                  <Table.Row>
                    <Table.Cell>Nm. Penjamin</Table.Cell>
                    <Table.Cell>
                      {this.props.modalAcquisition.dataOffer?.id_card_guarantor.full_name}
                    </Table.Cell>
                    <Table.Cell>
                      {this.props.modalStatusB2b.dataStatusB2b?.survey.guarantor_id_card.full_name}
                    </Table.Cell>
                  </Table.Row>
                  <Table.Row>
                    <Table.Cell>Kendaraan</Table.Cell>
                    <Table.Cell>
                      {`${this.props.modalAcquisition.dataOffer?.vehicle.variant_name.toUpperCase()} - ${this.props.modalAcquisition.dataOffer?.vehicle.color_name.toUpperCase()}`}
                    </Table.Cell>
                    <Table.Cell>
                      {`${this.props.modalStatusB2b.dataStatusB2b?.survey.vehicle.variant.name} - ${this.props.modalStatusB2b.dataStatusB2b?.survey.vehicle.color}`}
                    </Table.Cell>
                  </Table.Row>
                  <Table.Row warning={this.diffSchemeCreditCheck().dp}>
                    <Table.Cell>DP</Table.Cell>
                    <Table.Cell>
                      {this.diffSchemeCreditCheck().dp && <Icon name="attention" />}
                      {currencyFormat(this.props.modalAcquisition.dataOffer.credit.dp_amount)}
                    </Table.Cell>
                    <Table.Cell>
                      {currencyFormat(
                        this.props.modalStatusB2b.dataStatusB2b?.survey.credit_scheme
                          .down_payment || 0,
                      )}
                    </Table.Cell>
                  </Table.Row>
                  <Table.Row warning={this.diffSchemeCreditCheck().tenor}>
                    <Table.Cell>Tenor</Table.Cell>
                    <Table.Cell>
                      {this.diffSchemeCreditCheck().tenor && <Icon name="attention" />}
                      {this.props.modalAcquisition.dataOffer.credit.tenor}
                    </Table.Cell>
                    <Table.Cell>
                      {this.props.modalStatusB2b.dataStatusB2b?.survey.credit_scheme.tenor}
                    </Table.Cell>
                  </Table.Row>
                  <Table.Row warning={this.diffSchemeCreditCheck().installment}>
                    <Table.Cell>Angsuran</Table.Cell>
                    <Table.Cell>
                      {this.diffSchemeCreditCheck().installment && <Icon name="attention" />}
                      {currencyFormat(
                        this.props.modalAcquisition.dataOffer.credit.installment_amount,
                      )}
                    </Table.Cell>
                    <Table.Cell>
                      {currencyFormat(
                        this.props.modalStatusB2b.dataStatusB2b?.survey.credit_scheme.installment ||
                          0,
                      )}
                    </Table.Cell>
                  </Table.Row>
                </TableBody>
              </Table>
            </div>
          )}
        </Modal.Content>
        <Modal.Actions>
          <Button
            primary={true}
            disabled={this.disableButtonSubmit()}
            onClick={this.onSubmit}
          >
            Submit Akuisisi
          </Button>
          <Button
            onClick={() => {
              mainStore.dispatch(acquisitionOfferCodeSlice.actions.close());
            }}
          >
            Tutup
          </Button>
        </Modal.Actions>
      </Modal>
    );
  }
}

const mapStateToProps = (states: TMainReduxStates) => {
  return {
    modalAcquisition: states.acquisitionOfferCodeSlice,
    modalStatusB2b: states.modalStatusB2b,
    admin: states.reducerAdmin,
  };
};

export default connect(mapStateToProps)(ModalOfferAcquisition);

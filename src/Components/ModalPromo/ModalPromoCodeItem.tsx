import React from 'react'; // Import React untuk menggunakan JSX dan membuat komponen fungsional
import { IPromoCode } from '../../services/types/promoServiceTypes'; // Import tipe data untuk promo code
import { But<PERSON>, Grid, Card, Segment } from 'semantic-ui-react'; // Import komponen dari semantic-ui-react untuk membangun UI
import currencyFormat from '../../helpers/currencyFormat'; // Import fungsi untuk memformat nilai mata uang
import { Model } from '../../services/types/catalaogueTypes'; // Import tipe data untuk model kendaraan

// Interface Props mendefinisikan properti yang diterima oleh komponen
interface Props {
  promoCode: IPromoCode; // Data promo code yang akan ditampilkan di komponen
  area: string; // Nama area atau city group terkait dengan promo code
  model: Model; // Objek model yang berisi informasi kendaraan
  purchaseMethod: string; // Metode pembelian, misalnya "cash" atau "credit"
  onSend: (text: string) => void; // Callback yang dipanggil saat promo code dikirim, menerima parameter teks promo
}

// Komponen fungsional ModalPromoCodeItem untuk menampilkan informasi promo code
const ModalPromoCodeItem: React.FC<Props> = ({
  promoCode, // Mengambil properti promoCode dari props
  purchaseMethod, // Mengambil properti purchaseMethod dari props
  area, // Mengambil properti area dari props
  model, // Mengambil properti model dari props
  onSend, // Mengambil callback onSend dari props
}) => {
  // Fungsi untuk menangani pengiriman promo code ketika tombol di klik
  const handleSendPromo = () => {
    // Membuat string teks promo dengan menyusun data yang ada (model, metode, area, kode promo, dan potongan)
    let text =
      `Promo untuk model kendaraan ${model.model_name.toUpperCase()}, pembelian ${purchaseMethod.toUpperCase()}, dan area ${area.toUpperCase()}.\n\n` +
      `Kode Promo: *${promoCode.promo_code}*\n` +
      `Potongan: *${promoCode.discount_type === 'nominal' ? currencyFormat(promoCode.discount_value) : promoCode.discount_value + '%'}*\n`;
    // Memanggil fungsi onSend dengan teks promo yang telah dibuat sebagai argumen
    onSend(text);
  };

  // Render UI komponen dengan menggunakan Grid.Column untuk tata letak responsif dan Card untuk menampilkan detail promo code
  return (
    // Grid.Column untuk mengatur lebar tampilan pada perangkat mobile, tablet, dan komputer
    <Grid.Column
      mobile={16}
      tablet={6}
      computer={5}
    >
      {/* Card fluid membuat kartu yang lebar sesuai dengan kontainer */}
      <Card fluid>
        {/* Bagian utama dari Card yang berisi informasi promo */}
        <Card.Content>
          {/* Header menampilkan kode promo */}
          <Card.Header>{promoCode.promo_code}</Card.Header>
          {/* Meta menampilkan tipe diskon dalam huruf kapital */}
          <Card.Meta>{promoCode.discount_type.toUpperCase()}</Card.Meta>
          {/* Deskripsi menampilkan detail diskon, target tenor, dan metode pembelian */}
          <Card.Description>
            <strong>Nilai Diskon: </strong>
            {promoCode.discount_type === 'nominal'
              ? currencyFormat(promoCode.discount_value) // Jika diskon berupa nominal, gunakan fungsi format
              : `${promoCode.discount_value}%`}{' '}
            {/* Jika diskon berupa persentase */}
            <br />
            <strong>Target Tenor: </strong>
            {promoCode.target_tenor.length ? promoCode.target_tenor.join(', ') : '-'}{' '}
            {/* Tampilkan target tenor jika ada */}
            <br />
            <strong>Purchase Methods: </strong>
            {promoCode.purchase_method.length ? promoCode.purchase_method.join(', ') : '-'}{' '}
            {/* Tampilkan metode pembelian jika ada */}
          </Card.Description>
        </Card.Content>
        {/* Bagian tambahan dari Card untuk menampilkan tombol aksi */}
        <Card.Content extra>
          {/* Tombol dengan properti primary dan fluid untuk tampilan penuh, diklik memicu handleSendPromo */}
          <Button
            primary
            fluid
            onClick={handleSendPromo}
          >
            Kirim {/* Teks tombol */}
          </Button>
        </Card.Content>
      </Card>
    </Grid.Column>
  );
};

// Mengekspor komponen menggunakan React.memo agar komponen tidak melakukan re-render ulang
// jika properti tidak berubah.
export default React.memo(ModalPromoCodeItem);

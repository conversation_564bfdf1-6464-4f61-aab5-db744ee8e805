import { Component, SyntheticEvent } from 'react';
import { Button, DropdownProps, Form, Grid, Modal, Segment, Select } from 'semantic-ui-react';
import SelectAreaFromCatalog from '../SelectCityGroup/SelectAreaFromCatalog';
import { catalogueServices } from '../../services/catalogue/catalogueServices';
import { promoService } from '../../services/promo/promoServices';
import ModalPromoCodeItem from './ModalPromoCodeItem';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { connect } from 'react-redux';
import { mainStore } from '../../redux/reducers';
import modalSendPromoSlice from '../../redux/modal-send-promo/modalSendPromoSlice';
import { mainApiServices } from '../../services/MainApiServices';

// Mendapatkan action dari modalSendPromoSlice untuk mengupdate state Redux
const actions = modalSendPromoSlice.actions;

// Interface untuk properti Redux yang di-mapping ke komponen
interface ReduxProps {
  customer: TMainReduxStates['customerReducer'];
  modalSendPromo: TMainReduxStates['modalSendPromo'];
  conversation: TMainReduxStates['reducerConversation'];
  admin: TMainReduxStates['reducerAdmin'];
  project: TMainReduxStates['reducerProject'];
}

// Interface untuk properti yang diterima oleh komponen (selain dari Redux)
interface Props extends ReduxProps {
  model: string; // Model kendaraan yang diharapkan
  cityGroup: string; // Grup kota (area) yang diharapkan
}

// Class component ModalSendPromo yang menangani tampilan dan logika modal promo
class ModalSendPromo extends Component<Props> {
  // Fungsi untuk menutup modal dan mereset state di Redux
  private onClose = () => {
    // Dispatch action untuk mereset state modal
    mainStore.dispatch(actions.resetState());
  };

  // Objek 'model' yang menangani logika pengambilan dan pemilihan model kendaraan
  private model = {
    // Fungsi untuk mengambil model berdasarkan city group secara asynchronous
    fetchModel: async () => {
      // Mulai menandai bahwa proses pengambilan model sedang berlangsung
      mainStore.dispatch(actions.setFetchingModel(true));
      try {
        // Memanggil API untuk mendapatkan model berdasarkan city group
        const fetchModel = await catalogueServices.getModelByCityGroup({
          area: this.props.cityGroup || '',
        });
        // Ambil data model, jika tidak ada set sebagai array kosong
        const models = fetchModel?.data ?? [];
        // Update state dengan daftar model yang didapat
        mainStore.dispatch(actions.setModels(models));
        // Kembalikan daftar model
        return models;
      } finally {
        // Nonaktifkan status fetching model setelah request selesai
        mainStore.dispatch(actions.setFetchingModel(false));
      }
    },
    // Fungsi untuk menangani event pemilihan model dari dropdown
    onSelect: (event: SyntheticEvent<HTMLElement>, data: DropdownProps) => {
      // Cari model yang sesuai berdasarkan value yang dipilih
      const selectedModel =
        this.props.modalSendPromo.models.find((value) => value.model_name === data.value) || null;
      // Update state dengan model yang dipilih
      mainStore.dispatch(actions.setSelectedModel(selectedModel));
    },
  };

  // Objek 'purchaseScheme' menangani logika perubahan metode pembelian (cash atau credit)
  private purchaseScheme = {
    // Fungsi untuk menangani event perubahan pada dropdown metode pembelian
    onChange: (e: SyntheticEvent, d: DropdownProps) => {
      // Update state Redux dengan metode pembelian yang dipilih
      mainStore.dispatch(actions.setPurchaseMethod(d.value as 'cash' | 'credit' | null));
    },
  };

  // Objek 'selectArea' menangani logika pemilihan area (city group)
  private selectArea = {
    // Fungsi untuk menangani event pemilihan city group
    onSelect: (text: string) => {
      // Update state Redux dengan city group yang dipilih
      mainStore.dispatch(actions.setSelectedCityGroup(text));
      // Reset model yang dipilih sebelumnya dari state
      mainStore.dispatch(actions.setSelectedModel(null));
      // Reset daftar model dari state
      mainStore.dispatch(actions.setModels([]));
      // Ambil model baru berdasarkan city group yang baru dipilih
      this.model.fetchModel().catch();
    },
  };

  // Fungsi untuk melakukan submit pencarian promo code berdasarkan parameter yang di-set di state
  onSubmit = async () => {
    // Tandai state bahwa promo code sedang di-fetch dan reset daftar promo code yang lama
    mainStore.dispatch(actions.setFetchingPromoCodes(true));
    mainStore.dispatch(actions.setPromoCodes([]));

    try {
      // Destructuring props untuk kemudahan akses nilai-nilai state modalSendPromo
      const { modalSendPromo } = this.props;
      // Memanggil API untuk mendapatkan promo code dengan parameter yang diperlukan
      const get = await promoService.getPromo({
        promo_type: 'new_vehicle',
        purchase_method: modalSendPromo.purchaseMethod ?? 'credit',
        city_group: modalSendPromo.selectedCityGroup ?? '',
        vehicle_model: modalSendPromo.selectedModel?.model_name ?? '',
        allow_agent: false,
      });
      // Update state dengan promo code yang diterima dari API
      mainStore.dispatch(actions.setPromoCodes(get?.data ?? []));
    } catch (e) {
      // Jika ada error, reset promo codes menjadi array kosong
      mainStore.dispatch(actions.setPromoCodes([]));
    } finally {
      // Nonaktifkan status fetching promo code setelah request selesai
      mainStore.dispatch(actions.setFetchingPromoCodes(false));
    }
  };

  // Fungsi untuk mengirim promo code via API dan menutup modal setelah pengiriman
  private onSendPromo = async (text: string) => {
    // Mengirim pesan ke server menggunakan API dengan parameter yang diambil dari Redux state
    await mainApiServices.sendMessageV2({
      roomPath: this.props.conversation.chatRoom!.ref!.path,
      text: text,
      adminSessionPath: this.props.admin.adminSession!.ref.path,
      phoneNumber: this.props.conversation.chatRoom?.contacts[0] ?? '',
    });
    // Setelah pengiriman, panggil fungsi untuk menutup modal
    this.onClose();
  };

  // Fungsi inisialisasi yang dijalankan ketika modal dibuka
  private init = () => {
    if (this.props.cityGroup) {
      // Update state dengan city group yang diterima sebagai prop
      mainStore.dispatch(actions.setSelectedCityGroup(this.props.cityGroup));
      // Mengambil model berdasarkan city group dan kemudian update model yang dipilih jika sesuai
      this.model.fetchModel().then(() => {
        // Ambil nama model dari props
        const modelName = this.props.model;
        if (modelName) {
          // Cari model yang sesuai dari daftar model yang sudah ada di state
          const findModel = this.props.modalSendPromo.models.find(
            (m) => m.model_name === modelName,
          );
          if (findModel) {
            // Update state dengan model yang ditemukan dari pencarian
            mainStore.dispatch(actions.setSelectedModel(findModel));
          }
        }
      });
    }
  };

  // Lifecycle method: Mengecek perubahan props untuk melakukan inisialisasi ulang atau auto-submit jika diperlukan
  componentDidUpdate(prevProps: Props) {
    // Periksa apakah properti 'open' dari modal telah berubah
    if (prevProps.modalSendPromo.open !== this.props.modalSendPromo.open) {
      if (this.props.modalSendPromo.open) {
        // Jika modal baru saja dibuka, inisialisasi data awal
        this.init();
      }
    }

    // Auto-submit: Jika city group, model, dan metode pembelian sudah terisi dan ada perubahan, jalankan submit
    if (
      !this.props.modalSendPromo.fetchingPromoCodes &&
      this.props.modalSendPromo.selectedCityGroup &&
      this.props.modalSendPromo.selectedModel &&
      this.props.modalSendPromo.purchaseMethod &&
      (prevProps.modalSendPromo.selectedCityGroup !== this.props.modalSendPromo.selectedCityGroup ||
        prevProps.modalSendPromo.selectedModel !== this.props.modalSendPromo.selectedModel ||
        prevProps.modalSendPromo.purchaseMethod !== this.props.modalSendPromo.purchaseMethod)
    ) {
      // Panggil fungsi onSubmit untuk mengambil promo code
      this.onSubmit();
    }
  }

  // Render method untuk menampilkan UI modal promo
  render() {
    // Destructuring nilai state modalSendPromo dari props untuk kemudahan akses
    const { modalSendPromo } = this.props;

    return (
      <Modal
        // Mengontrol apakah modal terbuka berdasar state Redux
        open={this.props.modalSendPromo.open}
        // Mengizinkan modal tertutup jika pengguna mengklik area di luar modal
        closeOnDimmerClick={true}
        // Fungsi yang dijalankan ketika modal ditutup
        onClose={this.onClose}
      >
        <Modal.Header>Promo</Modal.Header>
        {/* Tampilan peringatan untuk project yang tidak memiliki akses ke menu promo */}
        {this.props.project.project?.group !== 'amartamotor' && (
          <Modal.Content className="p-6">
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
              <h3 className="text-red-800 font-medium mb-2">
                Project ini tidak bisa mengakses menu ini
              </h3>
            </div>
          </Modal.Content>
        )}
        {/* Tampilan konten modal ketika project memiliki akses ke menu promo */}
        {this.props.project.project?.group === 'amartamotor' && (
          <>
            <Modal.Content>
              <Segment className="!bg-gray-50 !p-4 !rounded-lg !mb-4">
                <Grid
                  columns={2}
                  divided
                  className="!m-0"
                >
                  <Grid.Column className="!p-0 !pr-2">
                    <div className="flex flex-col space-y-1">
                      {/* Menampilkan nama client */}
                      <span className="text-sm font-medium text-gray-500">Nama</span>
                      <span className="text-base font-semibold text-gray-800">
                        {this.props.customer?.client?.profile.name || '-'}
                      </span>
                    </div>
                  </Grid.Column>
                  <Grid.Column className="!p-0 !pl-2">
                    <div className="flex flex-col space-y-1">
                      {/* Menampilkan nomor telepon client */}
                      <span className="text-sm font-medium text-gray-500">Telepon</span>
                      <span className="text-base font-semibold text-gray-800">
                        {this.props.customer?.client?.contacts.whatsapp || '-'}
                      </span>
                    </div>
                  </Grid.Column>
                </Grid>
              </Segment>
              <Segment>
                <Form>
                  <Form.Group>
                    <Form.Field>
                      <label>City Group</label>
                      {/* Komponen untuk memilih city group */}
                      <SelectAreaFromCatalog
                        onChange={this.selectArea.onSelect}
                        value={modalSendPromo.selectedCityGroup ?? undefined}
                      />
                    </Form.Field>
                    <Form.Field>
                      <label>Model</label>
                      {/* Dropdown untuk memilih model kendaraan */}
                      <Select
                        loading={modalSendPromo.fetchingModel}
                        placeholder={'Pilih model'}
                        multiple={false}
                        value={modalSendPromo.selectedModel?.model_name ?? ''}
                        onChange={this.model.onSelect}
                        options={modalSendPromo.models.map((value) => ({
                          key: value.model_name,
                          value: value.model_name,
                          text: value.model_name.toUpperCase(),
                        }))}
                        search={true}
                        disabled={!modalSendPromo.selectedCityGroup}
                      />
                    </Form.Field>
                    <Form.Field>
                      <label>Metode Beli</label>
                      {/* Dropdown untuk memilih metode pembelian (cash atau credit) */}
                      <Select
                        onChange={this.purchaseScheme.onChange}
                        value={modalSendPromo.purchaseMethod ?? undefined}
                        placeholder={'Pilih Metode Beli'}
                        options={[
                          {
                            value: 'cash',
                            text: 'Cash',
                          },
                          {
                            value: 'credit',
                            text: 'Kredit',
                          },
                        ]}
                      />
                    </Form.Field>
                    <Form.Field>
                      <label>&ensp;</label>
                      {/* Tombol untuk memicu pencarian promo code */}
                      <Button
                        onClick={this.onSubmit}
                        loading={modalSendPromo.fetchingPromoCodes}
                        primary
                      >
                        Cari Promo
                      </Button>
                    </Form.Field>
                  </Form.Group>
                </Form>
              </Segment>
            </Modal.Content>
            <Modal.Content>
              {/* Tampilkan promo code jika ada; jika tidak, tampilkan pesan placeholder */}
              {modalSendPromo.promoCodes.length > 0 ? (
                <Grid>
                  {modalSendPromo.promoCodes.map((p) => (
                    <ModalPromoCodeItem
                      key={p.promo_code}
                      promoCode={p}
                      purchaseMethod={modalSendPromo.purchaseMethod!}
                      area={modalSendPromo.selectedCityGroup!}
                      model={modalSendPromo.selectedModel!}
                      onSend={this.onSendPromo}
                    />
                  ))}
                </Grid>
              ) : (
                <Segment
                  placeholder
                  textAlign="center"
                >
                  <p>Promo code kosong.</p>
                </Segment>
              )}
            </Modal.Content>
          </>
        )}
        {/* Tombol aksi untuk menutup modal */}
        <Modal.Actions>
          <Button onClick={this.onClose}>Tutup</Button>
        </Modal.Actions>
      </Modal>
    );
  }
}

// Mapping state Redux ke properti komponen
const mapStateToProps = (states: TMainReduxStates) => ({
  customer: states.customerReducer,
  modalSendPromo: states.modalSendPromo,
  conversation: states.reducerConversation,
  admin: states.reducerAdmin,
  project: states.reducerProject,
});

// Menghubungkan komponen dengan Redux store
export default connect(mapStateToProps)(ModalSendPromo);

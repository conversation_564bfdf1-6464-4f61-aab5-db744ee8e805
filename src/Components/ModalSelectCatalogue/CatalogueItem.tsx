import React, { Component } from 'react';
import { Button, Image } from 'semantic-ui-react';
import { IoCheckmarkCircle, IoClose, IoInformationCircle, IoSend } from 'react-icons/io5';
import { BiLoaderAlt } from 'react-icons/bi';
import currencyFormat from '../../helpers/currencyFormat';
import {
  GetAvailabilityMultiCityGroupResponse,
  IStock,
  VariantProduct,
} from '../../services/types/catalaogueTypes';
import { catalogueServices } from '../../services/catalogue/catalogueServices';
import { mainApiServices } from '../../services/MainApiServices';
import { getOrganizationByCode } from '../../helpers/organizationHelper';

export interface ICatalogueStates {
  selectedImagePreview: null | string;
  checkingAvailability: boolean;
  stockAvailability: null | IStock | 'NOT_FOUND';
  variantProducts: VariantProduct[];
  stockAvailabilityCityGroup: GetAvailabilityMultiCityGroupResponse[];
}

export interface ICatalogueProps {
  onClick: (message: { text: string; image?: string }) => void;
  catalogueData: VariantProduct;
  cityGroup: string;
  organization?: string;
}

class CatalogueItem extends Component<ICatalogueProps, ICatalogueStates> {
  private onSendClick = {
    link: async () => {
      const image = this.props.catalogueData.url_image;
      const imageLink =
        mainApiServices.baseApi.getBaseURL() + 'image-show-from-s3?path=' + (image ?? '');

      // const phoneNumber = mainStore.getState().reducerConversation.chatRoom?.contacts[0];
      // const getProfile = await profileServices.getProfile(phoneNumber ?? '');

      let utm = '';
      let main = '';
      let baseUrl = '';

      if (this.props.organization === 'amartahonda') {
        // utm = '?utm_source=amarta-ideal&utm_medium=referral&trid=' + (getProfile?.[0].TrackingId ?? '');
        baseUrl = `https://amartahonda.com/baru/${this.props.cityGroup}/${this.props.catalogueData.variant_code.replace('/', '')}`;
        main = baseUrl + utm;
      }
      if (this.props.organization === 'amartachery') {
        // utm = '?utm_source=chery-ideal&utm_medium=referral&trid=' + (getProfile?.[0].TrackingId ?? '');
        baseUrl = `https://amartachery.com/variant/${this.props.catalogueData.variant_code.replace('/', '')}`;
        main = baseUrl + utm;
      }
      if (this.props.organization === 'amartavinfast') {
        // utm = '?utm_source=vinfast-ideal&utm_medium=referral&trid=' + (getProfile?.[0].TrackingId ?? '');
        baseUrl = `https://amartavinfast.com/variant/${this.props.catalogueData.variant_code.replace('/', '')}`;
        main = baseUrl + utm;
      }
      if (this.props.organization === 'amartaneta') {
        // utm = '?utm_source=neta-ideal&utm_medium=referral&trid=' + (getProfile?.[0].TrackingId ?? '');
        baseUrl = `https://amartaneta.com/variant/${this.props.catalogueData.variant_code.replace('/', '')}`;
        main = baseUrl + utm;
      }

      // Membuat pesan yang lebih informatif
      const messageText =
        `🚗 *${this.props.catalogueData.variant_name}*\n\n` +
        `💰 Harga: ${currencyFormat(this.props.catalogueData.price)}\n` +
        `📍 Area: ${this.props.cityGroup.toUpperCase()}\n\n` +
        `🔗 Untuk informasi lebih detail tentang spesifikasi, fitur, dan penawaran khusus, silakan kunjungi link berikut:\n${main}`;

      this.props.onClick({
        text: messageText,
        image: imageLink,
      });
    },

    sendStockMultiCityGroup: (params: {
      cityGroup: string;
      shipmentCost: number;
      colorCode: string;
      stock: number;
    }) => {
      const find = this.state.variantProducts.find((v) => v.color_code === params.colorCode);
      const stock = params.stock;

      let text = `💰 *${currencyFormat((find?.price ?? 0) + params.shipmentCost)}*\n\n`;
      text += `🚗 ${find?.variant_name.toUpperCase()}\n`;
      text += `🎨 Warna: ${find?.color_name.toUpperCase()}\n`;
      text += `📦 Sisa Stok: ${stock?.toString() ?? '0'} unit.`;

      if (params.cityGroup !== this.props.cityGroup.toUpperCase()) {
        text += `\n\n_Catatan: Harga sudah termasuk biaya pengiriman unit yang akan dikirim dari daerah ${params.cityGroup}_.`;
      }

      const image = find?.url_image ?? '';

      this.props.onClick({
        text,
        image: image,
      });
    },
  };

  constructor(props: ICatalogueProps) {
    super(props);

    this.state = {
      variantProducts: [],
      selectedImagePreview: null,
      stockAvailability: null,
      checkingAvailability: false,
      stockAvailabilityCityGroup: [],
    };
  }

  fetchVariantColors = async () => {
    const get = await catalogueServices.getVariantByAreaAMH<VariantProduct[]>({
      variantCode: this.props.catalogueData.variant_code,
      area: this.props.cityGroup,
      company:
        this.parseOrganization(this.props.organization || 'amartahonda')?.companyCode || 'amarta',
    });

    let data: VariantProduct[] = get?.data ?? [];

    this.setState({
      variantProducts: data,
    });
  };

  parseAvailableStockColorCityGroup = (params: { colorCode: string; cityGroup: string }) => {
    const findRowCityGroupStock = this.state.stockAvailabilityCityGroup.find(
      (s) => s.city_group.toUpperCase() === params.cityGroup.toUpperCase(),
    );

    if (!findRowCityGroupStock) return 0;
    if (!findRowCityGroupStock.stock.data.color) return 0;
    const colorCodesCityGroup = Object.keys(findRowCityGroupStock.stock.data.color);
    if (colorCodesCityGroup.indexOf(params.colorCode) === -1) return 0;
    return findRowCityGroupStock.stock.data.color[params.colorCode] as number;
  };

  componentDidMount() {
    this.fetchVariantColors().then(() => {
      this.getStockAvailabilityCityGroup().then();
    });
  }

  render() {
    return (
      <div className="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 overflow-hidden border border-gray-100">
        <div className="relative">
          <Image
            src={this.props.catalogueData.url_image}
            className="w-full h-48 object-cover"
            alt={this.props.catalogueData.variant_name}
          />
          <div className="absolute top-2 right-2">
            <div className="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-medium">
              {currencyFormat(this.props.catalogueData.price)}
            </div>
          </div>
        </div>

        <div className="p-4">
          <div className="mb-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-1">
              {this.props.catalogueData.variant_name}
            </h3>
            <p className="text-sm text-gray-500">{this.props.catalogueData.variant_code}</p>
          </div>

          <div className="flex gap-2 mb-4">
            <Button
              onClick={this.onSendClick.link}
              className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 !flex items-center justify-center"
              size="small"
            >
              <IoSend className="mr-1" />
              &ensp;Kirim Informasi
            </Button>
          </div>

          <div className="border-t pt-4">
            <h4 className="text-sm font-semibold text-gray-700 mb-3">
              Stock warna dari berbagai wilayah
            </h4>

            {this.state.checkingAvailability && (
              <div className="flex items-center text-gray-500 text-sm">
                <BiLoaderAlt className="animate-spin mr-2" />
                Memeriksa ketersediaan...
              </div>
            )}

            {!this.state.checkingAvailability &&
              this.state.stockAvailabilityCityGroup.length === 0 && (
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 text-center">
                  <IoInformationCircle className="text-gray-400 text-xl mx-auto mb-2" />
                  <p className="text-gray-600 text-sm">
                    Tidak ada data stok yang tersedia untuk saat ini.
                  </p>
                </div>
              )}

            <div className="space-y-4">
              {this.state.stockAvailabilityCityGroup.map((cityGroup) => (
                <div
                  key={cityGroup.city_group}
                  className={`p-3 rounded-lg ${
                    cityGroup.city_group === this.props.cityGroup
                      ? 'bg-blue-50 border border-blue-200'
                      : 'bg-gray-50 border border-gray-200'
                  }`}
                >
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <div className="font-medium text-gray-800">
                        {cityGroup.city_group.toUpperCase()}
                      </div>
                      <div className="text-sm text-gray-600">
                        OTR:{' '}
                        <span className="font-semibold">
                          {currencyFormat(cityGroup.shipment_cost + this.props.catalogueData.price)}
                        </span>
                      </div>
                    </div>
                    {cityGroup.city_group === this.props.cityGroup && (
                      <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
                        City Group Sama
                      </span>
                    )}
                  </div>

                  <div className="space-y-2">
                    {this.state.variantProducts.map((variantProduct) => {
                      const stock = this.parseAvailableStockColorCityGroup({
                        cityGroup: cityGroup.city_group,
                        colorCode: variantProduct.color_code,
                      });

                      return (
                        <div
                          key={variantProduct.color_code}
                          className="flex items-center justify-between bg-white p-2 rounded border border-gray-100"
                        >
                          <div className="flex items-center">
                            {stock > 0 ? (
                              <IoCheckmarkCircle className="text-green-500 text-lg mr-2" />
                            ) : (
                              <IoClose className="text-red-500 text-lg mr-2" />
                            )}
                            <div>
                              <div className="text-sm font-medium">
                                {variantProduct.color_name.toUpperCase()}
                              </div>
                              <div className="text-xs text-gray-500">
                                {variantProduct.color_code} -{' '}
                                <span className="font-medium text-gray-700">{stock} Unit</span>
                              </div>
                            </div>
                          </div>

                          <Button
                            size="mini"
                            className="bg-blue-50 hover:bg-blue-100 text-blue-600 !flex items-center"
                            onClick={() =>
                              this.onSendClick.sendStockMultiCityGroup({
                                cityGroup: cityGroup.city_group,
                                colorCode: variantProduct.color_code,
                                shipmentCost: cityGroup.shipment_cost,
                                stock: stock,
                              })
                            }
                          >
                            <IoSend className="mr-1" /> Kirim
                          </Button>
                        </div>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  private parseOrganization = (org: string) => {
    return getOrganizationByCode(org);
  };

  private getStockAvailabilityCityGroup = async () => {
    this.setState(
      {
        stockAvailabilityCityGroup: [],
        checkingAvailability: true,
      },
      async () => {
        const variantCode = this.props.catalogueData.variant_code;
        const cityGroup = this.props.cityGroup.toUpperCase();

        const getCities = await catalogueServices.getAvailableCityByCityGroup({
          cityGroup: cityGroup,
          company:
            this.parseOrganization(this.props.organization || 'amartahonda')?.companyCode ||
            'amarta',
        });

        if (getCities.data && getCities.data.data.length < 1) {
          this.setState({
            stockAvailabilityCityGroup: [],
            checkingAvailability: false,
          });
          return;
        }

        const cityCode = getCities.data?.data[0].city_code || '';

        let stock: GetAvailabilityMultiCityGroupResponse[] = [];
        try {
          const getStock = await catalogueServices.checkAvailabilityMultiDealer({
            cityCode: cityCode,
            variantCode: variantCode,
            company:
              this.parseOrganization(this.props.organization || 'amartahonda')?.companyCode ||
              'amarta',
          });

          stock = getStock?.data || [];
        } catch (e) {
          this.setState({
            stockAvailabilityCityGroup: [],
            checkingAvailability: false,
          });
          return;
        }

        this.setState({
          stockAvailabilityCityGroup: stock,
          checkingAvailability: false,
        });
      },
    );
  };
}

export default CatalogueItem;

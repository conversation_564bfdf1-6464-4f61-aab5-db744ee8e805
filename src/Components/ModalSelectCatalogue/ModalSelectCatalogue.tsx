import React, { SyntheticEvent, useCallback, useEffect, useRef } from 'react';
import { Button, DropdownProps, Form, Header, Modal, Select, Segment } from 'semantic-ui-react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'react-icons/io5';
import { catalogueServices } from '../../services/catalogue/catalogueServices';
import CatalogueItem from './CatalogueItem';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { useDispatch, useSelector } from 'react-redux';
import SelectAreaFromCatalog from '../SelectCityGroup/SelectAreaFromCatalog';
import modalSelectCatalogueSlice from '../../redux/modal-select-catalogue/modalSelectCatalogueSlice';
import chatTextInputSlice from '../../redux/chat-text-input/chatTextInputSlice';
import { mainStore } from '../../redux/reducers';
import SelectOrganization, { OrganizationInfo } from '../SelectOrganization/SelectOrganization';
import { getOrganizationByCode } from '../../helpers/organizationHelper';

export interface ModalSelectCatalogueProps {}

const ModalSelectCatalogue: React.FC<ModalSelectCatalogueProps> = () => {
  // Redux setup
  const dispatch = useDispatch();
  const actions = modalSelectCatalogueSlice.actions;
  const firstRender = useRef(true); // Flag to avoid effect execution on first render

  const parseOrganization = (org: string) => {
    return getOrganizationByCode(org);
  };

  // State selection from Redux store
  const {
    visible,
    fetchingModel,
    models,
    variants,
    selectedCityGroup,
    selectedModel,
    variantFilter,
    organization,
  } = useSelector((state: TMainReduxStates) => state.modalSelectCatalogue);

  // Customer data from global state
  const customer = useSelector((state: TMainReduxStates) => state.customerReducer);

  // Fetch models based on selected city group
  const fetchModel = useCallback(async () => {
    if (!selectedCityGroup) return;
    try {
      dispatch(actions.setFetchingModel(true));
      const response = await catalogueServices.getModelByCityGroup({
        area: selectedCityGroup,
        company: parseOrganization(organization || 'amartahonda')?.companyCode || 'amarta',
      });
      dispatch(actions.setModels(response?.data ?? []));
    } finally {
      dispatch(actions.setFetchingModel(false));
    }
  }, [selectedCityGroup, dispatch, actions]);

  // Fetch available variants based on selected model and area
  const getAvailableVariant = useCallback(async () => {
    if (!selectedModel?.name || !selectedCityGroup) return;
    try {
      const response = await catalogueServices.getVariantByAreaAMH({
        modelName: selectedModel.name,
        area: selectedCityGroup,
        company: parseOrganization(organization || 'amartahonda')?.companyCode || 'amarta',
      });
      dispatch(actions.setVariants(response?.data ?? []));
    } catch (error) {
      dispatch(actions.setVariants([]));
    }
  }, [selectedModel, selectedCityGroup, dispatch, actions]);

  const handleSelectOrganization = useCallback(
    (data: OrganizationInfo | null) => {
      dispatch(actions.setOrganization(data?.organization || ''));
    },
    [dispatch, actions],
  );

  // Event handler for selecting area/city group
  const handleSelectArea = useCallback(
    (text: string) => {
      dispatch(actions.setSelectedCityGroup(text || null));
    },
    [dispatch, actions],
  );

  // Event handler for selecting vehicle model
  const handleSelectModel = useCallback(
    (_: SyntheticEvent<HTMLElement>, data: DropdownProps) => {
      const value = data.value?.toString().toUpperCase();
      const model = value ? models.find((m) => m.model_name.toUpperCase() === value) : null;
      dispatch(actions.setSelectedModel(model ? { name: model.model_name } : null));
    },
    [models, dispatch, actions],
  );

  // Event handler for variant filtering
  const handleVariantFilter = useCallback(
    (_: unknown, data: DropdownProps) => {
      const variant = variants.find((v) => v.variant_code === data.value);
      dispatch(
        actions.setVariantFilter(
          variant
            ? {
                name: variant.variant_name,
                code: variant.variant_code,
              }
            : null,
        ),
      );
    },
    [variants, dispatch, actions],
  );

  // Initialize data when modal is opened
  useEffect(() => {
    if (visible && firstRender.current) {
      const initializeData = async () => {
        if (customer.client?.profile.area?.text) {
          await fetchModel();
          if (selectedModel) await getAvailableVariant();
        }
        firstRender.current = false;
      };
      initializeData();
    }
  }, [visible, customer, fetchModel, selectedModel, getAvailableVariant]);

  // Refresh models when city group changes
  useEffect(() => {
    if (!firstRender.current) fetchModel();
  }, [fetchModel]);

  // Refresh variants when model changes
  useEffect(() => {
    if (!firstRender.current) getAvailableVariant();
  }, [getAvailableVariant]);

  // Close modal and reset state
  const handleClose = useCallback(() => dispatch(actions.setClose()), [dispatch, actions]);

  // Handle sending catalog to chat
  const handleSend = useCallback(
    (send: { text: string; image?: string }) => {
      const filename = send.image?.split('/').pop()?.split('?')[0];

      if (send.image) {
        // Save image to chat state
        mainStore.dispatch(
          chatTextInputSlice.actions.setFile({
            source: 'url',
            url: {
              url: send.image || '',
              name: filename || '',
              type: 'image',
            },
          }),
        );
      } else {
        mainStore.dispatch(chatTextInputSlice.actions.setFile(null));
      }

      // Set catalog text to chat input
      mainStore.dispatch(chatTextInputSlice.actions.setHtmlContentEditable(send.text));
      handleClose();
    },
    [handleClose],
  );

  // Customer information section
  const renderCustomerInfo = () => {
    const { client } = customer;

    if (!client) {
      return (
        <div className="text-gray-500 italic text-center">Tidak ada informasi klien tersedia</div>
      );
    }

    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="flex flex-col space-y-1">
          <span className="text-sm font-medium text-gray-500">Nama</span>
          <span className="text-base font-semibold text-gray-800">
            {client.profile.name || '-'}
          </span>
        </div>
        <div className="flex flex-col space-y-1">
          <span className="text-sm font-medium text-gray-500">Telepon</span>
          <span className="text-base font-semibold text-gray-800">
            {client.contacts.whatsapp || '-'}
          </span>
        </div>
        <div className="flex flex-col space-y-1">
          <span className="text-sm font-medium text-gray-500">City Group</span>
          <span className="text-base font-semibold text-gray-800">
            {client.profile.area?.text || '-'}
          </span>
        </div>
      </div>
    );
  };

  // Modal UI
  return (
    <Modal
      open={visible}
      closeOnDimmerClick={true}
      closeOnEscape={true}
      onClose={handleClose}
      className="!bg-white rounded-xl shadow-2xl"
    >
      <Modal.Header className="bg-gradient-to-r from-blue-500 to-blue-600 text-white py-4 px-6 rounded-t-xl">
        <div className="flex items-center">
          <IoBook className="text-xl mr-2" />
          <span className="text-xl font-semibold">Kirim Katalog</span>
        </div>
      </Modal.Header>
      <Modal.Content className="p-6">
        {/* Customer Information Section */}
        <Segment className="mb-6 border border-blue-100 bg-blue-50 rounded-lg">
          <div className="flex items-center gap-2 mb-3">
            <IoPerson className="text-blue-600 text-lg" />
            <h3 className="text-blue-700 font-medium m-0">Informasi Klien</h3>
          </div>
          {renderCustomerInfo()}
        </Segment>

        <div className="bg-blue-50 rounded-lg p-4 mb-6 border border-blue-200">
          <Form
            size="small"
            className="space-y-4"
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Form.Field
                className="!mb-0"
                required={true}
              >
                <label className="text-gray-700 font-medium mb-2 block">Organization</label>
                <SelectOrganization
                  value={organization || ''}
                  onChange={handleSelectOrganization}
                />
              </Form.Field>
              <Form.Field
                className="!mb-0"
                required={true}
              >
                <label className="text-gray-700 font-medium mb-2 block">City Group</label>
                <SelectAreaFromCatalog
                  disabled={!organization}
                  onChange={handleSelectArea}
                  value={selectedCityGroup ?? ''}
                  companyCode={parseOrganization(organization || 'amartahonda')?.companyCode || ''}
                />
              </Form.Field>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Form.Field className="!mb-0">
                <label className="text-gray-700 font-medium mb-2 block">Model</label>
                <Select
                  loading={fetchingModel}
                  placeholder="Pilih model"
                  value={selectedModel?.name.toUpperCase() || ''}
                  onChange={handleSelectModel}
                  options={models.map((value) => ({
                    key: value.model_name,
                    value: value.model_name.toUpperCase(),
                    text: value.model_name.toUpperCase(),
                  }))}
                  clearable={true}
                  search={true}
                  disabled={!selectedCityGroup}
                  className="w-full"
                />
              </Form.Field>
              <Form.Field className="!mb-0">
                <label className="text-gray-700 font-medium mb-2 block">Filter Variant</label>
                <Select
                  disabled={!selectedModel}
                  placeholder="Pilih variant"
                  options={variants.map((v) => ({
                    text: `${v.variant_name} - ${v.variant_code}`,
                    value: v.variant_code,
                  }))}
                  clearable={true}
                  search={true}
                  onChange={handleVariantFilter}
                  value={variantFilter?.code || ''}
                  className="w-full"
                />
              </Form.Field>
            </div>
          </Form>
        </div>

        {variants.length === 0 && (
          <div className="bg-gray-50 rounded-lg p-8 text-center">
            <Header
              icon
              className="flex flex-col items-center justify-center text-gray-500"
            >
              <IoList className="text-4xl mb-4" />
              <span className="text-lg">Hasil katalog akan muncul disini.</span>
            </Header>
          </div>
        )}

        {variants.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {variants
              .filter((v) => !variantFilter || v.variant_code === variantFilter.code)
              .map((variant) => (
                <div key={`${variant.variant_code}-${variant.color_code}`}>
                  <CatalogueItem
                    catalogueData={variant}
                    onClick={handleSend}
                    cityGroup={selectedCityGroup ?? ''}
                    organization={organization || ''}
                  />
                </div>
              ))}
          </div>
        )}
      </Modal.Content>

      <Modal.Actions className="bg-gray-50 px-6 py-4 rounded-b-xl border-t">
        <Button onClick={handleClose}>Tutup</Button>
      </Modal.Actions>
    </Modal>
  );
};

export default ModalSelectCatalogue;

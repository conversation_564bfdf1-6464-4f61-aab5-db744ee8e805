import React, { ChangeEvent, Component } from 'react';
import { Model, VariantProduct } from '../services/types/catalaogueTypes';
import { IResponseAvailableTenorItem } from '../services/types/credit_simulation_types';
import { CreditSimulationServices } from '../services/creditSimulationServices';
import currencyFormat from '../helpers/currencyFormat';
import { DropdownProps } from 'semantic-ui-react/dist/commonjs/modules/Dropdown/Dropdown';
import { TMainReduxStates } from '../redux/types/redux-types';
import { connect } from 'react-redux';
import { setModalSendCreditSimulationVisibility } from '../redux/modal-send-credit-simulation/action-modal-send-credit-simulation';
import { mainApiServices } from '../services/MainApiServices';
import {
  Button,
  Divider,
  Form,
  Grid,
  Input,
  Message,
  Modal,
  Segment,
  Select,
} from 'semantic-ui-react';
import SelectAreaFromCatalog from './SelectCityGroup/SelectAreaFromCatalog';
import SelectVehicleFromCatalogue from './SelectVehicle/SelectVehicleFromCatalogue';
import { SemanticDatepickerProps } from 'react-semantic-ui-datepickers/dist/types';
import DatePicker from 'react-semantic-ui-datepickers';
import moment from 'moment';
import 'moment/locale/id';
import { catalogueServices } from '../services/catalogue/catalogueServices';
import { AxiosError } from 'axios';

moment.locale('id');

interface IModalSendCreditSimulationProps {
  conversation: TMainReduxStates['reducerConversation'];
  modal: TMainReduxStates['modalSendCreditSimulationReducer'];
  admin: TMainReduxStates['reducerAdmin'];
  closeModal: () => void;
}

interface IModalSendCreditSimulationStates {
  area: null | { value: string; text: string };
  model: string | null;
  variant: null | {
    name: string;
    code: string;
  };
  color: null | VariantProduct;

  downPayment: number;
  downPaymentDiscount: number;

  startDate: Date | null;

  selectedTenor: IResponseAvailableTenorItem | null;

  finalInstallment: number | null;

  availableTenor: IResponseAvailableTenorItem[];
  fetchingTenor: boolean;

  simulating: boolean;
  sendToWhatsapp: boolean;

  success: string | null;
  error: string | null;
  errorFetchCreditSimulation: string | null;
}

class ModalSendCreditSimulation extends Component<
  IModalSendCreditSimulationProps,
  IModalSendCreditSimulationStates
> {
  private area = {
    onChange: async (value: string, text: string) => {
      this.setState(
        {
          area: null,
          model: null,
          variant: null,
          color: null,
        },
        () => {
          this.setState({
            area: {
              value,
              text,
            },
            model: null,
            variant: null,
            color: null,
          });
        },
      );
    },
  };
  private model = {
    setAvailableTenor: async (modelName: string) => {
      this.setState(
        {
          fetchingTenor: true,
        },
        async () => {
          let states: IModalSendCreditSimulationStates = { ...this.state };

          try {
            const get = await CreditSimulationServices.getRate({
              model: modelName,
              cityGroup: this.state.area?.text.toLowerCase() ?? '',
            });
            states.availableTenor = get ?? [];
          } catch (e: any) {
            const error = e as AxiosError<any>;
            states.availableTenor = [];
            states.errorFetchCreditSimulation = error.response?.data.error.message;
          }

          this.setState({
            ...states,
            fetchingTenor: false,
          });
        },
      );
    },
    onChange: async (model: Model) => {
      this.setState(
        {
          model: model.model_name.toLowerCase(),
          variant: null,
          color: null,
          availableTenor: [],
          selectedTenor: null,
          finalInstallment: null,
        },
        () => {
          this.model.setAvailableTenor(model.model_name);
        },
      );
    },
  };
  private variant = {
    onChange: (variant: VariantProduct) => {
      this.setState({
        variant: {
          name: variant.variant_name,
          code: variant.variant_code,
        },
        color: null,
        finalInstallment: null,
      });
    },
  };
  private color = {
    onChange: (variantColor: VariantProduct) => {
      this.setState({
        color: variantColor,
        finalInstallment: null,
      });
    },
  };
  private downPayment = {
    onChange: (params: ChangeEvent<HTMLInputElement>) => {
      if (params) {
        this.setState({
          downPayment: parseInt(params.target.value),
          finalInstallment: null,
        });
      }
    },
  };
  private downPaymentDiscount = {
    onChange: (params: ChangeEvent<HTMLInputElement>) => {
      if (params) {
        this.setState({
          downPaymentDiscount: parseInt(params.target.value),
          finalInstallment: null,
        });
      }
    },
  };
  private tenor = {
    onChange: (event: React.SyntheticEvent<HTMLElement>, data: DropdownProps) => {
      const find = this.state.availableTenor.find((tenor) => tenor.tenor === data.value);
      this.setState({
        selectedTenor: find ?? null,
        finalInstallment: null,
      });
    },
  };
  private downPaymentCalc = {
    total: () => {
      const downPaymentDiscount = isNaN(this.state.downPaymentDiscount)
        ? 0
        : this.state.downPaymentDiscount;

      return this.downPaymentCalc.downPaymentCredit() - downPaymentDiscount;
    },
    downPaymentCredit: () => {
      const downPayment = isNaN(this.state.downPayment) ? 0 : this.state.downPayment;
      const downPaymentDiscount = isNaN(this.state.downPaymentDiscount)
        ? 0
        : this.state.downPaymentDiscount;

      return downPayment + downPaymentDiscount;
    },
  };
  private installmentCalc = {
    range: () => {
      const lowest =
        (this.state?.finalInstallment ?? 0) - (this.state.selectedTenor?.range_down ?? 0);
      const highest =
        (this.state?.finalInstallment ?? 0) + (this.state.selectedTenor?.range_up ?? 0);

      return { lowest, highest };
    },
    dateRange: () => {
      const start = moment(this.state.startDate);
      const end = start.clone().add((this.state.selectedTenor?.tenor ?? 1) - 1, 'months');

      return { start, end };
    },
  };
  private startDate = {
    onChange: (event: React.SyntheticEvent | undefined, data: SemanticDatepickerProps) => {
      this.setState({
        startDate: (data.value as Date | null) ?? null,
        finalInstallment: null,
      });
    },
  };

  constructor(props: IModalSendCreditSimulationProps) {
    super(props);

    this.state = {
      area: null,
      model: null,
      variant: null,
      color: null,
      availableTenor: [],
      startDate: new Date(),
      selectedTenor: null,
      fetchingTenor: false,
      downPayment: 0,
      downPaymentDiscount: 0,
      simulating: false,
      finalInstallment: 0,
      sendToWhatsapp: false,
      success: null,
      error: null,
      errorFetchCreditSimulation: null,
    };
  }

  onSend = async () => {
    this.setState(
      {
        sendToWhatsapp: true,
        error: null,
        success: null,
      },
      async () => {
        let states: IModalSendCreditSimulationStates = { ...this.state };

        try {
          await mainApiServices.sendMessageV2({
            roomPath: this.props.conversation.chatRoom!.ref!.path,
            text: this.summary.sendToWhatsapp(),
            adminSessionPath: this.props.admin.adminSession!.ref.path,
            phoneNumber: this.props.conversation.chatRoom?.contacts[0] ?? '',
          });

          states.success = 'Berhasil mengirim pesan simulasi kredit.';
        } catch (e: any) {
          states.success = 'Gagal mengirim pesan simulasi kredit : ' + e.toString() + '.';
        }

        this.setState({
          ...states,
          sendToWhatsapp: false,
        });
      },
    );
  };

  private summary = {
    allowToShow: () => {
      let allowed = true;

      if (!this.state.finalInstallment) allowed = false;

      return allowed;
    },
    sendToWhatsapp: () => {
      return (
        `Paket Kredit ${this.state.variant?.name ?? '_UNKNOW_VARIANT_'} dengan OTR ${currencyFormat(this.state.color?.price ?? 0)} untuk area ${this.state.area?.text ?? '_UNKNOWN_AREA_'}` +
        `<br />` +
        `<br />` +
        `Uang Muka Kredit: ${currencyFormat(this.downPaymentCalc.downPaymentCredit())}` +
        `<br/>` +
        `Diskon Uang Muka: ${currencyFormat(this.state.downPaymentDiscount)}` +
        `<br/>` +
        `Uang Muka Bayar: ${currencyFormat(this.downPaymentCalc.total())}` +
        `<br />` +
        `<br />` +
        `Estimasi Angsuran/Cicilan:` +
        `<br/>` +
        `Angsuran per Bulan: ${currencyFormat(this.installmentCalc.range().lowest)} ~ ${currencyFormat(this.installmentCalc.range().highest)}` +
        `<br/>` +
        `Durasi: ${this.state.selectedTenor?.tenor ?? 0} Bulan` +
        `<br>` +
        `Tanggal Cicilan ke-1: ${this.installmentCalc.dateRange().start.format('DD MMM YYYY')}` +
        `<br>` +
        `Tanggal Cicilan ke-${this.state.selectedTenor?.tenor ?? 1}: ${this.installmentCalc.dateRange().end.format('DD MMM YYYY')}` +
        `<br />` +
        `<br />` +
        `Angsuran bisa berubah sesuai anjuran finance company / leasing selama belum tanda tangan perjanjian kredit.`
      );
    },
    jsxElement: () => {
      return (
        <Segment loading={this.state.simulating}>
          <p>
            Paket Kredit {this.state.variant?.name ?? <i>UNKNOWN_VARIANT</i>} dengan OTR{' '}
            {currencyFormat(this.state.color?.price ?? 0)} untuk area{' '}
            {this.state.area?.text ?? <i>UNKNOWN_AREA</i>}
          </p>
          <div
            style={{
              marginTop: '8px',
            }}
          >
            Uang Muka Kredit: {currencyFormat(this.downPaymentCalc.downPaymentCredit())}
            <br />
            Diskon Uang Muka: {currencyFormat(this.state.downPaymentDiscount)}
            <br />
            Uang Muka Bayar: {currencyFormat(this.downPaymentCalc.total())}
          </div>

          <div
            style={{
              marginTop: '8px',
            }}
          >
            Estimasi Angsuran/Cicilan:
            <br />
            Angsuran per Bulan: {currencyFormat(this.installmentCalc.range().lowest)} ~{' '}
            {currencyFormat(this.installmentCalc.range().highest)}
            <br />
            Durasi: {this.state.selectedTenor?.tenor ?? 0} Bulan
            <br />
            Tanggal Cicilan ke-1: {this.installmentCalc.dateRange().start.format('DD MMM YYYY')}
            <br />
            Tanggal Cicilan ke-{this.state.selectedTenor?.tenor ?? 1}:{' '}
            {this.installmentCalc.dateRange().end.format('DD MMM YYYY')}
          </div>

          <div
            style={{
              marginTop: '8px',
            }}
          >
            *Angsuran bisa berubah sesuai anjuran finance company / leasing selama belum tanda
            tangan perjanjian kredit.
          </div>

          <div
            style={{
              marginTop: '8px',
            }}
          >
            <Button
              loading={this.state.sendToWhatsapp}
              onClick={this.onSend}
            >
              Kirim Hasil Simulasi
            </Button>
          </div>
        </Segment>
      );
    },
  };

  componentDidUpdate(
    prevProps: Readonly<IModalSendCreditSimulationProps>,
    prevState: Readonly<IModalSendCreditSimulationStates>,
    snapshot?: any,
  ) {
    if (prevProps.modal.visible !== this.props.modal.visible) {
      if (this.props.modal.visible) this.fetchUser().then();
    }
  }

  onClose = () => {
    this.props.closeModal();
    this.setState({
      area: null,
      model: null,
      variant: null,
      color: null,
      availableTenor: [],
      startDate: new Date(),
      selectedTenor: null,
      fetchingTenor: false,
      downPayment: 0,
      downPaymentDiscount: 0,
      simulating: false,
      finalInstallment: 0,
      sendToWhatsapp: false,
      success: null,
      error: null,
    });
  };

  render() {
    return (
      <React.Fragment>
        <Modal open={this.props.modal.visible}>
          <Modal.Header>Simulasi Kredit</Modal.Header>
          <Modal.Content>
            <Grid>
              <Grid.Column
                widescreen={8}
                mobile={16}
              >
                <Form>
                  <Form.Field>
                    <SelectAreaFromCatalog
                      value={this.state.area?.value}
                      onChange={this.area.onChange}
                    />
                  </Form.Field>
                  <Divider />
                  {this.state.area && (
                    <React.Fragment>
                      <SelectVehicleFromCatalogue
                        cityGroup={this.state.area.text}
                        variant={{
                          selected: this.state.variant ?? undefined,
                          onChange: this.variant.onChange,
                        }}
                        model={{
                          selected: this.state.model ?? undefined,
                          onChange: this.model.onChange,
                        }}
                        color={{
                          selected: this.state.color
                            ? {
                                code: this.state.color.color_code,
                                name: this.state.color.color_name,
                              }
                            : undefined,
                          onChange: this.color.onChange,
                        }}
                      />
                    </React.Fragment>
                  )}
                  <Form.Field>
                    <Select
                      options={this.state.availableTenor.map((dp) => ({
                        value: dp.tenor,
                        text: `${dp.tenor} kali`,
                      }))}
                      placeholder={'Pilih Tenor'}
                      loading={this.state.fetchingTenor}
                      disabled={!this.state.model}
                      onChange={this.tenor.onChange}
                    />
                  </Form.Field>

                  {this.state.errorFetchCreditSimulation && (
                    <Form.Field>
                      <Message
                        negative
                        size={'tiny'}
                      >
                        <Message.Header>Gagal Mendapatkan Simulasi Kredit</Message.Header>
                        <p>{this.state.errorFetchCreditSimulation}</p>
                      </Message>
                    </Form.Field>
                  )}

                  <Divider />
                  <Form.Field>
                    <label>Uang Muka Konsumen</label>
                    <Input
                      placeholder={'Uang Muka Kredit'}
                      value={this.state.downPayment}
                      onChange={this.downPayment.onChange}
                      disabled={!this.state.color}
                      input={{
                        type: 'number',
                      }}
                    />
                  </Form.Field>
                  <Form.Field>
                    <label>Diskon Uang Muka</label>
                    <Input
                      placeholder={'Diskon Uang Muka'}
                      value={this.state.downPaymentDiscount}
                      onChange={this.downPaymentDiscount.onChange}
                      disabled={!this.state.color}
                      input={{
                        type: 'number',
                      }}
                    />
                  </Form.Field>
                  <Form.Field>
                    <label>Tanggal Cicilan Pertama</label>
                    <DatePicker
                      placeholder={'Tanggal Mulai Kredit'}
                      onChange={this.startDate.onChange}
                      value={this.state.startDate}
                      format={'DD MMM YYYY'}
                    />
                  </Form.Field>
                </Form>
                <Segment>
                  <div>
                    <span>OTR</span> :{' '}
                    <strong>{currencyFormat(this.state.color?.price ?? 0)}</strong>
                  </div>
                  <div>
                    <span>Uang Muka Kredit</span> :{' '}
                    <strong>{currencyFormat(this.downPaymentCalc.downPaymentCredit())}</strong>
                  </div>
                </Segment>
                <Button
                  loading={this.state.simulating}
                  primary={true}
                  onClick={this.onSubmitSimulate}
                >
                  SIMULASIKAN
                </Button>
              </Grid.Column>
              <Grid.Column
                widescreen={8}
                mobile={16}
              >
                <h5>Hasil Simulasi</h5>
                {this.summary.allowToShow() ? (
                  this.summary.jsxElement()
                ) : (
                  <Segment>
                    Lengkapi seluruh data lalu klik <strong>SIMULASIKAN.</strong>
                  </Segment>
                )}
                {!!this.state.error && (
                  <Message negative>
                    <Message.Header>Gagal Mengirim</Message.Header>
                    <p>{this.state.error}</p>
                  </Message>
                )}
                {!!this.state.success && (
                  <Message positive>
                    <Message.Header>Sukses</Message.Header>
                    <p>{this.state.success}</p>
                  </Message>
                )}
              </Grid.Column>
            </Grid>
          </Modal.Content>
          <Modal.Actions>
            <Button onClick={this.onClose}>Tutup</Button>
          </Modal.Actions>
        </Modal>
      </React.Fragment>
    );
  }

  private onSubmitSimulate = async () => {
    if (!this.state.selectedTenor) return;
    if (!this.state.color) return;
    this.setState(
      {
        simulating: true,
      },
      async () => {
        let states: IModalSendCreditSimulationStates = { ...this.state };

        try {
          const get = await CreditSimulationServices.simulate({
            tenor: this.state.selectedTenor?.tenor ?? 0,
            down_payment: this.downPaymentCalc.downPaymentCredit(),
            price: this.state.color?.price ?? 0,
            rate: this.state.selectedTenor?.rate ?? 0,
          });

          states.finalInstallment = get.PaymentPerMonth;
        } catch (e: any) {
          console.log(e);
        }

        this.setState({ ...states, simulating: false });
      },
    );
  };

  private fetchUser = async () => {
    const getClientEntities = await this.props.conversation.chatRoom?.getClientEntities({
      refresh: true,
    });
    if (!getClientEntities) return;
    const clientEntity = getClientEntities[0];

    let states: IModalSendCreditSimulationStates = { ...this.state };

    if (clientEntity.profile.area) {
      states.area = { ...clientEntity.profile.area };
    }

    if (!!clientEntity.dream_vehicle?.color_code) {
      try {
        let loadVariant = await catalogueServices.getVariantByAreaAMH<VariantProduct[]>({
          area: states.area?.text ?? '',
          variantCode: clientEntity.dream_vehicle?.variant_code ?? '',
        });

        const findVariantProduct = loadVariant?.data.find(
          (value) => value.color_code === clientEntity.dream_vehicle?.color_code,
        );

        if (findVariantProduct) {
          states.color = findVariantProduct;
          states.model = findVariantProduct.model_name;
          states.variant = {
            name: findVariantProduct.variant_name,
            code: findVariantProduct.variant_code,
          };
        }
      } catch (e: any) {}
    }

    this.setState({ ...states }, () => {
      if (states.model) {
        this.model.setAvailableTenor(states.model ?? '');
      }
    });
  };
}

const mapStateToProps = (states: TMainReduxStates) => ({
  conversation: states.reducerConversation,
  modal: states.modalSendCreditSimulationReducer,
  admin: states.reducerAdmin,
});

const mapDispatchToProps = (dispatch: any) => {
  return {
    closeModal: () => dispatch(setModalSendCreditSimulationVisibility(false)),
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(ModalSendCreditSimulation);

import React, { ChangeEvent, Component, SyntheticEvent } from 'react';
import {
  Button,
  DropdownProps,
  Form,
  Icon,
  Message,
  Modal,
  Segment,
  Select,
} from 'semantic-ui-react';
import { Area, Model, VariantProduct } from '../../services/types/catalaogueTypes';
import { catalogueServices } from '../../services/catalogue/catalogueServices';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { connect } from 'react-redux';
import SelectAreaFromCatalog from '../SelectCityGroup/SelectAreaFromCatalog';
import PriceListTable from './PriceListTable';
import { mainStore } from '../../redux/reducers';
import modalPriceListSlice, {
  fetchPriceList,
} from '../../redux/modal-price-list/modalPriceListSlice';
import ModalViewPriceListTabularData from './ModalViewPriceListTabularData';
import DatePicker from 'react-semantic-ui-datepickers';
import TenorConfigurator from './TenorConfigurator';
import { PriceListRow } from '../../services/types/mainApiService/mainApiService.types';
import { mainApiServices } from '../../services/MainApiServices';
import { AxiosError } from 'axios';
import { SemanticDatepickerProps } from 'react-semantic-ui-datepickers/dist/types';

interface Props {
  customer: TMainReduxStates['customerReducer'];
  conversation: TMainReduxStates['reducerConversation'];
  priceList: TMainReduxStates['modalSendPriceList'];
  admin: TMainReduxStates['reducerAdmin'];
  project: TMainReduxStates['reducerProject'];
}

interface States {
  fetchingArea: boolean;
  fetchingModel: boolean;

  areas: Area[];
  models: Model[];
  variants: VariantProduct[];

  selectedCityGroup: string | null;
  selectedModel: Model | null;
  selectedVariant: VariantProduct | null;
  creating: boolean;
  sending: boolean;

  selectedTenor: string[];

  errorMessage: string;
}

class ModalSendPriceList extends Component<Props, States> {
  constructor(props: Props) {
    super(props);

    this.state = {
      fetchingArea: false,
      fetchingModel: false,

      areas: [],
      models: [],
      variants: [],

      selectedCityGroup: null,
      selectedModel: null,
      selectedVariant: null,

      creating: false,
      sending: false,

      selectedTenor: [],

      errorMessage: '',
    };
  }

  onOpenTabularPriceList = () => {
    mainStore.dispatch(modalPriceListSlice.actions.openPriceListModalTabular());
  };

  close = () => {
    mainStore.dispatch(modalPriceListSlice.actions.close());
  };

  onChangeTitle = (e: ChangeEvent<HTMLInputElement>) => {
    mainStore.dispatch(modalPriceListSlice.actions.setTitle(e.target.value));
  };
  onChangeSubTitle = (e: ChangeEvent<HTMLInputElement>) => {
    mainStore.dispatch(modalPriceListSlice.actions.setSubTitle(e.target.value));
  };

  private selectArea = {
    onSelect: (cityGroup: string) => {
      mainStore.dispatch(modalPriceListSlice.actions.clearRowAndPriceList());
      this.setState(
        {
          selectedCityGroup: cityGroup || null,
          selectedModel: null,
          models: [],
          variants: [],
        },
        () => {
          this.selectModel.fetchModel().catch();
        },
      );
    },
  };

  private selectVariant = {
    fetchVariant: async () => {
      await new Promise((resolve) => {
        this.setState(
          {
            variants: [],
            selectedVariant: null,
          },
          async () => {
            let currentState: States = { ...this.state };
            const getVariants = await catalogueServices.getVariantByAreaAMH({
              modelName: this.state.selectedModel?.model_name ?? '',
              area: this.state.selectedCityGroup ?? '',
            });
            currentState.variants = (getVariants?.data || []) as VariantProduct[];
            this.setState(
              {
                ...currentState,
              },
              () => resolve(true),
            );
          },
        );
      });
    },
    onSelect: (event: SyntheticEvent<HTMLElement>, data: DropdownProps) => {
      const variant =
        this.state.variants.find((value) => value.variant_code === data.value) || null;
      mainStore.dispatch(modalPriceListSlice.actions.clearRowAndPriceList());
      mainStore.dispatch(modalPriceListSlice.actions.setTitle(variant?.variant_name || ''));
      mainStore.dispatch(
        modalPriceListSlice.actions.setVehicle({
          cityGroup: this.state.selectedCityGroup!,
          variant: variant!,
        }),
      );
      this.setState(
        {
          selectedVariant: variant,
        },
        () => {
          this.fetchPriceList();
        },
      );
    },
  };

  private selectModel = {
    fetchModel: () => {
      return new Promise((resolve) => {
        this.setState(
          {
            fetchingModel: true,
            models: [],
            selectedModel: null,
          },
          async () => {
            let currentState: States = { ...this.state };
            const fetchModel = await catalogueServices.getModelByCityGroup({
              area: this.state.selectedCityGroup ?? '',
            });

            currentState.models = fetchModel?.data ?? [];
            currentState.fetchingModel = false;

            this.setState({ ...currentState }, () => resolve(true));
          },
        );
      });
    },
    onSelect: (event: SyntheticEvent<HTMLElement>, data: DropdownProps) => {
      mainStore.dispatch(modalPriceListSlice.actions.clearRowAndPriceList());
      this.setState(
        {
          selectedModel: this.state.models.find((value) => value.model_name === data.value) ?? null,
        },
        () => {
          this.selectVariant.fetchVariant().then();
        },
      );
    },
  };

  fetchPriceList = async () => {
    mainStore.dispatch(
      fetchPriceList({
        cityGroup: this.state.selectedCityGroup!,
        variantCode: this.state.selectedVariant!.variant_code,
        modelName: this.state.selectedModel!.model_name,
      }) as any,
    );
  };

  onSubmit = async () => {
    this.setState({
      creating: true,
      sending: false,
    });

    let idLinkPriceList: string | null = null;

    const { normalPriceList } = this.props.priceList;
    const priceLists: PriceListRow[] = [];
    normalPriceList.forEach((pl) => {
      pl.installments.forEach((tenor) => {
        priceLists.push({
          downPayment: pl.downPayment || 0,
          installment: tenor.installment || 0,
          tenor: tenor.tenor || 0,
          installmentDiscount: tenor.discountInstallment || 0,
          tenorDiscount:
            this.props.priceList.selectedTenor.find((v) => v.tenor === tenor.tenor)?.discount || 0,
        });
      });
    });

    try {
      const create = await mainApiServices.createPriceList({
        admin: this.props.admin.admin?.email || '',
        source: 'IDEAL',
        title: this.props.priceList.title,
        subTitle: this.props.priceList.subTitle,
        cityGroup: this.state.selectedCityGroup || '',
        phoneNumber: this.props.customer.client?.contacts.whatsapp || '',
        expiredAt: this.props.priceList.expiredAt || null,
        priceListHighLight: [],
        priceListNormal: priceLists,
        variantCode: this.state.selectedVariant?.variant_code || '',
      });

      idLinkPriceList = create || null;
    } catch (e: any) {
      let messages = '';
      if ('response' in e) {
        const _e = e as AxiosError<any>;
        if (_e.response?.data.error?.type === 'UNPROCESSABLE_ENTITY') {
          for (const [k, v] of Object.entries(_e.response?.data.error.messages)) {
            const _v = v as any;
            messages += `\n${_v.msg} (${k})`;
          }
        }
        messages =
          _e.response?.data.messages || _e.response?.data.error.messages || 'Terjadi Error';
      }
      this.setState({
        creating: false,
        errorMessage: messages,
      });

      return;
    }

    this.setState({
      creating: false,
      sending: true,
    });

    const linkPriceList = 'https://amartahonda.com/pl/' + idLinkPriceList;
    const messages = `🚗 Berikut adalah pricelist untuk ${
      this.state.selectedVariant?.variant_name || ''
    } \n\n🔗 ${linkPriceList}`;
    try {
      await mainApiServices.sendMessageV2({
        roomPath: this.props.conversation.chatRoom?.ref.path || '',
        phoneNumber: this.props.customer.client?.contacts.whatsapp || '',
        text: messages,
        adminSessionPath: this.props.admin.adminSession!.ref.path,
      });
    } catch (e) {
      this.setState({
        sending: false,
        errorMessage: 'Terjadi error pada saat mengirim link.',
      });

      return;
    }

    mainStore.dispatch(modalPriceListSlice.actions.close());
  };

  componentDidMount() {
    const area = this.props.customer.client?.profile.area;
    if (area) {
      this.setState(
        {
          selectedCityGroup: area.value,
        },
        () => {
          const modelName = this.props.customer.client?.dream_vehicle?.model_name;
          if (modelName) {
            this.selectModel.fetchModel().then(() => {
              this.setState(
                {
                  selectedModel:
                    this.state.models.find(
                      (m) => m.model_name.toUpperCase() === modelName.toUpperCase(),
                    ) || null,
                },
                () => {
                  const variantCode = this.props.customer.client?.dream_vehicle?.variant_code;
                  if (variantCode) {
                    this.selectVariant.fetchVariant().then(() => {
                      const variant =
                        this.state.variants.find(
                          (v) => v.variant_code.toUpperCase() === variantCode.toUpperCase(),
                        ) || null;
                      mainStore.dispatch(
                        modalPriceListSlice.actions.setTitle(variant?.variant_name || ''),
                      );
                      mainStore.dispatch(
                        modalPriceListSlice.actions.setVehicle({
                          cityGroup: this.state.selectedCityGroup!,
                          variant: variant!,
                        }),
                      );
                      this.setState(
                        {
                          selectedVariant: variant,
                        },
                        () => {
                          this.fetchPriceList();
                        },
                      );
                    });
                  }
                },
              );
            });
          }
        },
      );
    }
  }

  onExpiredAtChange = (event: React.SyntheticEvent | undefined, data: SemanticDatepickerProps) => {
    if (data.value instanceof Date) {
      mainStore.dispatch(modalPriceListSlice.actions.setExpiredAt(data.value));
    }
  };

  render() {
    return (
      <Modal
        open={this.props.priceList.open}
        centered={false}
        onClose={this.close}
      >
        <Modal.Header>Kirim Price List</Modal.Header>

        {this.props.project.project?.group !== 'amartamotor' && (
          <Modal.Content>
            <Segment>
              <h3 className="text-red-800 font-medium mb-2">
                Project ini tidak bisa mengakses menu ini
              </h3>
            </Segment>
          </Modal.Content>
        )}
        {this.props.project.project?.group === 'amartamotor' && (
          <>
            <Modal.Content>
              <Segment>
                <Form size={'small'}>
                  <Form.Group widths={'equal'}>
                    <Form.Input
                      label={'Judul'}
                      placeholder={'Masukan Judul'}
                      required={true}
                      onChange={this.onChangeTitle}
                      value={this.props.priceList.title}
                    />
                    <Form.Input
                      label={'Sub Judul'}
                      placeholder={'Masukan Sub Judul'}
                      onChange={this.onChangeSubTitle}
                      value={this.props.priceList.subTitle}
                    />
                  </Form.Group>
                  <Form.Field required={true}>
                    <label>Expired At</label>
                    <DatePicker
                      datePickerOnly={true}
                      minDate={new Date()}
                      value={this.props.priceList.expiredAt}
                      onChange={this.onExpiredAtChange}
                    />
                  </Form.Field>
                </Form>
              </Segment>
              <Segment>
                <Form size={'small'}>
                  <Form.Group
                    widths={'equal'}
                    inline={true}
                  >
                    <Form.Field required={true}>
                      <label>City Group</label>
                      <SelectAreaFromCatalog
                        onChange={this.selectArea.onSelect}
                        value={this.state.selectedCityGroup ?? undefined}
                      />
                    </Form.Field>
                    <Form.Field required={true}>
                      <label>Model</label>
                      <Select
                        loading={this.state.fetchingModel}
                        placeholder={'Pilih model'}
                        multiple={false}
                        value={this.state.selectedModel?.model_name || ''}
                        onChange={this.selectModel.onSelect}
                        options={this.state.models.map((value) => ({
                          key: value.model_name,
                          value: value.model_name,
                          text: value.model_name.toUpperCase(),
                        }))}
                        search={true}
                        disabled={!this.state.selectedCityGroup}
                      />
                    </Form.Field>
                    <Form.Field required={true}>
                      <label>Pilih Variant</label>
                      <Select
                        placeholder={'Pilih Variant'}
                        multiple={false}
                        onChange={this.selectVariant.onSelect}
                        options={this.state.variants.map((value) => ({
                          key: value.variant_code,
                          value: value.variant_code,
                          text: value.variant_name.toUpperCase(),
                        }))}
                        search={true}
                        disabled={!this.state.selectedModel}
                        value={this.state.selectedVariant?.variant_code || ''}
                      />
                    </Form.Field>
                  </Form.Group>
                </Form>

                <div
                  style={{
                    width: '300px',
                    marginTop: '16px',
                  }}
                >
                  {this.props.priceList.priceListState === 'fetching' && (
                    <div>
                      <Icon
                        name={'spinner'}
                        loading={true}
                      />{' '}
                      Memeriksa ketersediaan
                    </div>
                  )}
                  {this.props.priceList.priceListState === 'not_found' && (
                    <div
                      style={{
                        color: '#B03060',
                        fontWeight: 'bold',
                      }}
                    >
                      <Icon name={'close'} /> Pricelist tidak tersedia
                    </div>
                  )}
                  {this.props.priceList.priceListState === 'available' && (
                    <div
                      style={{
                        display: 'flex',
                        gap: 6,
                        alignItems: 'center',
                      }}
                    >
                      <div
                        style={{
                          color: '#016936',
                          fontWeight: 'bold',
                        }}
                      >
                        <Icon name={'check'} /> Pricelist tersedia
                      </div>
                      <div>
                        <Button
                          size={'mini'}
                          onClick={this.onOpenTabularPriceList}
                        >
                          Lihat
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </Segment>
              <TenorConfigurator />
              <PriceListTable />
              {this.state.errorMessage && (
                <Message error={true}>
                  <pre>{this.state.errorMessage}</pre>
                </Message>
              )}
            </Modal.Content>
          </>
        )}
        <Modal.Actions>
          <Button onClick={this.close}>Tutup</Button>
          {this.props.priceList.priceListState === 'available' && (
            <Button
              primary={true}
              onClick={this.onSubmit}
              loading={this.state.sending || this.state.creating}
            >
              Submit
            </Button>
          )}
        </Modal.Actions>
        <ModalViewPriceListTabularData />
      </Modal>
    );
  }
}

const mapStateToProps = (states: TMainReduxStates) => ({
  conversation: states.reducerConversation,
  customer: states.customerReducer,
  priceList: states.modalSendPriceList,
  admin: states.reducerAdmin,
  project: states.reducerProject,
});

export default connect(mapStateToProps)(ModalSendPriceList);

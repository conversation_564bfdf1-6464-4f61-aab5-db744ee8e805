import React, { Component } from 'react';
import { <PERSON><PERSON>, <PERSON>dal, Table } from 'semantic-ui-react';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { connect } from 'react-redux';
import { mainStore } from '../../redux/reducers';
import modalPriceListSlice from '../../redux/modal-price-list/modalPriceListSlice';
import currencyFormat from '../../helpers/currencyFormat';

interface Props {
  priceList: TMainReduxStates['modalSendPriceList'];
}

class ModalViewPriceListTabularData extends Component<Props> {
  onClose = () => {
    mainStore.dispatch(modalPriceListSlice.actions.closePriceListModalTabular());
  };

  dpPercentage = (dp: number) => {
    const otr = this.props.priceList.vehicle?.variant?.price || 0;
    const percentage = (dp / otr) * 100;
    return Math.ceil(percentage * 10) / 10;
  };

  renderHeadAndTenor = () => {
    const headDataTabular =
      this.props.priceList.priceList?.tabular_view[1]?.filter((v) => !!v) || [];

    const tenorHeaderComponents = headDataTabular.map((t, i) => {
      return <Table.HeaderCell key={t}>{t}</Table.HeaderCell>;
    });

    return <Table.Row>{tenorHeaderComponents}</Table.Row>;
  };

  renderBody = () => {
    const bodyDatas = this.props.priceList.priceList?.tabular_view.slice(2) || [];

    const body = bodyDatas.map((r, i) => {
      return (
        <Table.Row key={i.toString()}>
          {r
            .filter((v) => !!v)
            .map((c, i) => {
              return (
                <Table.Cell key={c}>
                  {currencyFormat(parseInt(c))}
                  &ensp;
                  {i === 0 && <strong>({this.dpPercentage(parseInt(c))}%)</strong>}
                </Table.Cell>
              );
            })}
        </Table.Row>
      );
    });

    return <Table.Body>{body}</Table.Body>;
  };

  render() {
    return (
      <Modal
        open={this.props.priceList.openPriceListModalTabular}
        onClose={this.onClose}
        closeOnDimmerClick={true}
      >
        <Modal.Header>
          Price List {this.props.priceList.vehicle?.variant?.variant_name}
        </Modal.Header>

        <Modal.Content>
          <Table
            size={'small'}
            celled={true}
            compact={true}
          >
            <Table.Header>
              <Table.Row>
                <Table.HeaderCell
                  rowSpan={2}
                  textAlign={'center'}
                >
                  Down Payment
                </Table.HeaderCell>
                <Table.HeaderCell
                  colSpan={
                    this.props.priceList.priceList?.tabular_view[1]?.filter((v) => !!v).length
                  }
                  textAlign={'center'}
                >
                  Tenor
                </Table.HeaderCell>
              </Table.Row>
              {this.renderHeadAndTenor()}
            </Table.Header>
            {this.renderBody()}
          </Table>
        </Modal.Content>

        <Modal.Actions>
          <Button onClick={this.onClose}>Tutup</Button>
        </Modal.Actions>
      </Modal>
    );
  }
}

const mapStateToProps = (states: TMainReduxStates) => ({
  priceList: states.modalSendPriceList,
});

export default connect(mapStateToProps)(ModalViewPriceListTabularData);

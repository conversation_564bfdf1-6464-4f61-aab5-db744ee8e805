import React, { Component } from 'react';
import { Button, Segment } from 'semantic-ui-react';
import PriceRow from './PriceRow';
import { connect } from 'react-redux';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { mainStore } from '../../redux/reducers';
import modalPriceListSlice from '../../redux/modal-price-list/modalPriceListSlice';

interface Props {
  priceListModal: TMainReduxStates['modalSendPriceList'];
}

class PriceListTable extends Component<Props> {
  addRow = () => {
    mainStore.dispatch(modalPriceListSlice.actions.addRow());
  };

  render() {
    return (
      <div>
        <Segment>
          <div>
            <strong>Price List</strong>
          </div>
          <div>
            <div style={{ marginBottom: '8px' }}>
              <Button
                onClick={this.addRow}
                className={'btn-price-list-add-row'}
                disabled={this.props.priceListModal.selectedTenor.length === 0}
              >
                Tambah
              </Button>
            </div>
            <div>
              {this.props.priceListModal.normalPriceList.length > 0 ? (
                this.props.priceListModal.normalPriceList.map((p) => {
                  return (
                    <PriceRow
                      group={'normal'}
                      uidRow={p.key}
                      key={p.key}
                    />
                  );
                })
              ) : (
                <div className={'empty-price-list-row'}>Tidak ada baris pricelist</div>
              )}
            </div>
          </div>
        </Segment>
      </div>
    );
  }
}

const mapStateToProps = (states: TMainReduxStates) => {
  return {
    priceListModal: states.modalSendPriceList,
  };
};

export default connect(mapStateToProps)(PriceListTable);

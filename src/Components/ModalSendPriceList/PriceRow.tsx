import React, { Component, SyntheticEvent } from 'react';
import { DropdownProps, Form, Segment } from 'semantic-ui-react';
import { mainStore } from '../../redux/reducers';
import modalPriceListSlice from '../../redux/modal-price-list/modalPriceListSlice';
import collect, { Collection } from 'collect.js';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { connect } from 'react-redux';
import { FormattedData } from '../../services/types/TabularPriceList.types';
import currencyFormat from '../../helpers/currencyFormat';
import PriceRowInstallment from './PriceRowInstallment';

interface Props {
  group: 'normal' | 'highlight';
  uidRow: string;
  modalPriceList: TMainReduxStates['modalSendPriceList'];
}

class PriceRow extends Component<Props> {
  private priceList?: Collection<FormattedData>;

  constructor(props: Props) {
    super(props);
    this.priceList = collect(this.props.modalPriceList.priceList?.formatted || []);
  }

  dpPercentage = (dp: number) => {
    const otr = this.props.modalPriceList.vehicle?.variant?.price || 0;
    const percentage = (dp / otr) * 100;
    return Math.ceil(percentage * 10) / 10;
  };

  private delete = () => {
    mainStore.dispatch(modalPriceListSlice.actions.deleteRow({ uidRow: this.props.uidRow }));
  };

  private downPayment = {
    data: () => {
      const downPayment = this.priceList?.groupBy('dp');
      return downPayment?.keys().all() || [];
    },
    onChange: (event: SyntheticEvent<HTMLElement>, data: DropdownProps) => {
      const value = data.value as number;
      mainStore.dispatch(
        modalPriceListSlice.actions.setValueDownPayment({
          value: value,
          uidRow: this.props.uidRow,
        }),
      );
      mainStore.dispatch(modalPriceListSlice.actions.autoFillInstallmentAllDp());
    },
    value: () => {
      let selectedDp: number | null = null;
      const index = this.props.modalPriceList.normalPriceList.findIndex(
        (v) => v.key === this.props.uidRow,
      );
      selectedDp = this.props.modalPriceList.normalPriceList[index]?.downPayment;

      return selectedDp;
    },
  };

  render() {
    const rowData = this.props.modalPriceList.normalPriceList.find(
      (r) => r.key === this.props.uidRow,
    )!;
    return (
      <Segment>
        <Form>
          <Form.Select
            label={'Down Payment'}
            width={5}
            options={this.downPayment.data().map((d) => {
              return {
                value: parseFloat(d),
                text: (
                  <div>
                    <span>{currencyFormat(parseFloat(d))}</span>
                    &ensp;
                    <span>({this.dpPercentage(parseInt(d))}%)</span>
                  </div>
                ),
                description:
                  this.props.modalPriceList.normalPriceList
                    .map((pl) => pl.downPayment)
                    .indexOf(parseInt(d)) > -1
                    ? 'Assigned'
                    : null,
              };
            })}
            value={this.downPayment.value() || ''}
            placeholder={'Uang Muka'}
            onChange={this.downPayment.onChange}
            required={true}
          />

          {rowData.installments.map((i) => {
            return (
              <PriceRowInstallment
                uidRow={this.props.uidRow}
                key={i.tenor.toString()}
                installment={i}
              />
            );
          })}

          <Form.Button onClick={this.delete}>Hapus</Form.Button>
        </Form>
      </Segment>
    );
  }
}

const mapStateToProps = (s: TMainReduxStates) => {
  return {
    modalPriceList: s.modalSendPriceList,
  };
};

export default connect(mapStateToProps)(PriceRow);

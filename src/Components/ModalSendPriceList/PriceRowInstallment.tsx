import React, { Component } from 'react';
import { Form, Input } from 'semantic-ui-react';
import currencyFormat from '../../helpers/currencyFormat';
import modalPriceListSlice, { Installment } from '../../redux/modal-price-list/modalPriceListSlice';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { connect } from 'react-redux';
import { mainStore } from '../../redux/reducers';
import { InputOnChangeData } from 'semantic-ui-react/dist/commonjs/elements/Input/Input';

interface Props {
  installment: Installment;
  uidRow: string;
  modalPriceList: TMainReduxStates['modalSendPriceList'];
}

class PriceRowInstallment extends Component<Props> {
  onChange = (event: React.ChangeEvent<HTMLInputElement>, data: InputOnChangeData) => {
    mainStore.dispatch(
      modalPriceListSlice.actions.setValueDiscountInstallment({
        value: parseInt(data.value),
        uidRow: this.props.uidRow,
        tenor: this.props.installment.tenor,
      }),
    );
  };

  value = () => {
    const { normalPriceList } = this.props.modalPriceList;
    const index = normalPriceList.findIndex((p) => p.key === this.props.uidRow);
    const findIndexInstallment = normalPriceList[index].installments?.findIndex(
      (i) => i.tenor === this.props.installment.tenor,
    );
    return normalPriceList[index].installments[findIndexInstallment].discountInstallment || 0;
  };

  render() {
    const i = this.props.installment;
    const dp = this.props.modalPriceList.normalPriceList.find((k) => k.key === this.props.uidRow);

    return (
      <Form.Group
        inline={true}
        widths={'equal'}
      >
        <Form.Field disabled={dp?.downPayment === 0 || !dp?.downPayment}>
          <label>Angsuran dengan tenor {i.tenor}</label>
          <Input
            placeholder={'Angsuran'}
            readOnly={true}
            value={currencyFormat(i.installment)}
            label={'readonly'}
          />
        </Form.Field>
        <Form.Input
          label={`Diskon Angsuran untuk Tenor ${i.tenor}`}
          placeholder={'Diskon Angsuran'}
          value={this.value()}
          onChange={this.onChange}
          disabled={dp?.downPayment === 0 || !dp?.downPayment}
        />
      </Form.Group>
    );
  }
}

const mapStateToProps = (s: TMainReduxStates) => {
  return {
    modalPriceList: s.modalSendPriceList,
  };
};

export default connect(mapStateToProps)(PriceRowInstallment);

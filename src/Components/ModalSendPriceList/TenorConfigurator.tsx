import React, { Component, SyntheticEvent } from 'react';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { connect } from 'react-redux';
import collect from 'collect.js';
import { Form, FormInput, Message, Segment } from 'semantic-ui-react';
import { DropdownProps } from 'semantic-ui-react/dist/commonjs/modules/Dropdown/Dropdown';
import { mainStore } from '../../redux/reducers';
import modalPriceListSlice from '../../redux/modal-price-list/modalPriceListSlice';

interface Props {
  priceList: TMainReduxStates['modalSendPriceList'];
}

class TenorConfigurator extends Component<Props> {
  tenor = {
    onSelect: (event: SyntheticEvent<HTMLElement>, data: DropdownProps) => {
      mainStore.dispatch(
        modalPriceListSlice.actions.setAvailableTenor({
          value: data.value as number[],
        }),
      );
      mainStore.dispatch(modalPriceListSlice.actions.autoFillInstallmentAllDp());
    },
    data: (): string[] => {
      const pl = this.props.priceList.priceList;
      if (!pl) return [];
      const groupBy = collect(pl.formatted).groupBy('tenor');
      return groupBy.keys().toArray();
    },
  };

  discountTenor = {
    onChange: (tenor: number, value: string | null) => {
      mainStore.dispatch(
        modalPriceListSlice.actions.setValueDiscountTenor({
          value: value ? parseInt(value) : null,
          tenor,
        }),
      );
    },
    value: (tenor: number) => {
      const find = this.props.priceList.selectedTenor.find((t) => t.tenor === tenor);
      if (find) {
        return find.discount?.toString() || '';
      }
      return '';
    },
  };

  render() {
    return (
      <Segment>
        <Form>
          <Form.Select
            label={'Tentukan Tenor mana yang akan di munculkan'}
            options={this.tenor.data().map((t) => {
              return {
                text: t,
                value: parseInt(t),
              };
            })}
            multiple={true}
            onChange={this.tenor.onSelect}
            placeholder={'Pilih Tenor'}
            disabled={!this.props.priceList.priceList}
            value={this.props.priceList.selectedTenor?.map((t) => t.tenor) || []}
          />
        </Form>

        {this.props.priceList.selectedTenor.length > 0 && (
          <div style={{ marginTop: '16px' }}>
            <Message
              size={'mini'}
              info={true}
            >
              <span>Semua input diskon tenor bersifat opsional</span>
            </Message>
            <Form>
              <Form.Group
                inline={true}
                widths={'equal'}
              >
                {this.props.priceList.selectedTenor.map((t) => {
                  return (
                    <FormInput
                      key={t.tenor.toString()}
                      label={'Diskon Tenor ' + t.tenor}
                      placeholder={'Diskon Tenor ' + t.tenor}
                      type={'number'}
                      value={this.discountTenor.value(t.tenor)}
                      onChange={(event, data) => this.discountTenor.onChange(t.tenor, data.value)}
                    />
                  );
                })}
              </Form.Group>
            </Form>
          </div>
        )}
      </Segment>
    );
  }
}

const mapStateToProps = (states: TMainReduxStates) => ({
  priceList: states.modalSendPriceList,
});

export default connect(mapStateToProps)(TenorConfigurator);

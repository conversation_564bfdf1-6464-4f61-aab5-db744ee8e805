import React, { Component } from 'react';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { connect } from 'react-redux';
import collect, { Collection } from 'collect.js';
import { FormattedData } from '../../services/types/TabularPriceList.types';
import { Table } from 'semantic-ui-react';
import TableCell from './TableCell';

interface Props {
  priceList: TMainReduxStates['modalSendPriceListSimple'];
}

class PriceListTable extends Component<Props> {
  availableTenors = () => {
    const formatted = this.props.priceList?.priceList?.formatted;
    if (!formatted) return [];
    const groupBy: Collection<FormattedData> = collect<FormattedData>(formatted).groupBy('tenor');
    return groupBy
      .keys()
      .map((v) => parseInt(v))
      .toArray<number>();
  };
  availableDownPayment = () => {
    const formatted = this.props.priceList?.priceList?.formatted;
    if (!formatted) return [];
    const groupBy: Collection<FormattedData> = collect<FormattedData>(formatted).groupBy('dp');
    return groupBy
      .keys()
      .map((v) => parseInt(v))
      .toArray<number>();
  };

  renderBody = () => {
    const rows: JSX.Element[][] = [];
    for (const dp of this.availableDownPayment()) {
      const columns: JSX.Element[] = [];

      const cellKeyDp = `dp#${dp}`;
      columns.push(
        <TableCell
          key={cellKeyDp}
          cellKey={cellKeyDp}
          cellType={'downPayment'}
          value={dp}
          tenor={0}
          downPayment={dp}
          installment={0}
        />,
      );

      for (let i = 0; i < this.availableTenors().length; i++) {
        const tenor = this.availableTenors()[i];

        const formatted = this.props.priceList?.priceList?.formatted;
        const find = formatted?.find((v) => v.dp === dp && v.tenor === tenor);
        let cellKeyInstallment = `dp#${dp}#tenor${tenor}#installment#${find?.installment || 0}#${i}`;
        let exist = !!this.props.priceList.selected[cellKeyInstallment];

        columns.push(
          <TableCell
            key={cellKeyInstallment}
            cellKey={cellKeyInstallment}
            cellType={'installment'}
            value={find?.installment || 0}
            tenor={tenor}
            downPayment={dp}
            installment={find?.installment || 0}
            selected={exist}
          />,
        );
      }

      rows.push(columns);
    }

    return rows;
  };

  render() {
    return (
      <div>
        <span>Klik Pada Kolom Angsuran untuk memilih angsuran</span>
        <Table
          size={'small'}
          compact={true}
        >
          <Table.Header>
            <Table.Row>
              <Table.HeaderCell rowSpan={2}>Uang Muka</Table.HeaderCell>
              {this.availableTenors().map((r) => {
                return <Table.HeaderCell key={r.toString()}>{r}</Table.HeaderCell>;
              })}
            </Table.Row>
          </Table.Header>
          <Table.Body>
            {this.renderBody()?.map((r, i) => {
              return (
                <Table.Row key={i.toString()}>
                  {r.map((c) => {
                    return c;
                  })}
                </Table.Row>
              );
            })}
          </Table.Body>
        </Table>
      </div>
    );
  }
}

const mapStateToProps = (states: TMainReduxStates) => {
  return {
    priceList: states['modalSendPriceListSimple'],
  };
};

export default connect(mapStateToProps)(PriceListTable);

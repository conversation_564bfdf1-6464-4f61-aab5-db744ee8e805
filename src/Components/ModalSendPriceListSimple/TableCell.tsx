import React, { Component } from 'react';
import { Table } from 'semantic-ui-react';
import currencyFormat from '../../helpers/currencyFormat';
import { mainStore } from '../../redux/reducers';
import modalPriceListSliceSimple from '../../redux/modal-price-list-simple/modalPriceListSliceSimple';
import customizeMediaQuery, { CustomizeMediaQuery } from '../hoc/CustomizeMediaQuery';
import { compose } from '@reduxjs/toolkit';
import { connect } from 'react-redux';
import { TMainReduxStates } from '../../redux/types/redux-types';

interface Props extends CustomizeMediaQuery {
  cellType: 'downPayment' | 'installment';
  value: number;

  tenor: number;
  downPayment: number;
  installment: number;
  cellKey: string;
  selected?: boolean;

  priceList: TMainReduxStates['modalSendPriceListSimple'];
}

class TableCell extends Component<Props> {
  dpPercentage = () => {
    const otr = this.props.priceList.vehicle?.variant?.price || 0;
    const percentage = (this.props.downPayment / otr) * 100;
    return Math.ceil(percentage * 10) / 10;
  };

  onClick = () => {
    if (this.props.cellType !== 'installment') {
      return;
    }

    if (this.props.value === 0) return;

    const { cellKey, installment, downPayment, tenor } = this.props;
    if (this.props.selected) {
      mainStore.dispatch(modalPriceListSliceSimple.actions.removeSelected(cellKey));
    } else {
      mainStore.dispatch(
        modalPriceListSliceSimple.actions.addSelected({
          cellKey: cellKey,
          installment: installment,
          tenor: tenor,
          downPayment: downPayment,
        }),
      );
    }
  };

  innerHTML = () => {
    switch (this.props.cellType) {
      case 'downPayment':
        return `${currencyFormat(this.props.downPayment)} (${this.dpPercentage()}%)`;
      case 'installment':
        return `${this.props.mediaQueries.isMobile ? `${this.props.tenor}x - ` : ''}${currencyFormat(this.props.installment)} `;
    }
  };

  className = () => {
    let clsName = '';
    if (this.props.cellType === 'installment') clsName += 'selectable-installment ';
    if (this.props.selected) clsName += 'selected ';
    return clsName;
  };

  render() {
    return (
      <Table.Cell
        disabled={this.props.cellType === 'downPayment'}
        className={this.className()}
        selectable={this.props.cellType === 'installment'}
        onClick={this.onClick}
        active={this.props.selected}
      >
        {this.innerHTML()}
      </Table.Cell>
    );
  }
}
const mapStateToProps = (states: TMainReduxStates) => {
  return {
    priceList: states['modalSendPriceListSimple'],
  };
};

export default compose<React.ComponentType<Omit<Props, 'mediaQueries' | 'priceList'>>>(
  customizeMediaQuery,
  connect(mapStateToProps),
)(TableCell);

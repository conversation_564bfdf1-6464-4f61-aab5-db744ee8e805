import React from 'react';
import { But<PERSON>, Form, Modal, Select } from 'semantic-ui-react';
import { TMainReduxStates } from '../redux/types/redux-types';
import { useSelector } from 'react-redux';
import SelectAreaFromCatalog from './SelectCityGroup/SelectAreaFromCatalog';
import { mainStore } from '../redux/reducers';
import modalSendToFreeLeadsSlice from '../redux/modal-send-to-free-leads/modalSendToFreeLeadsSlice';
import SelectVehicleFromCatalogue_v2 from './SelectVehicle/SelectVehicleFromCatalogue_v2';
import { getOrganizationsByGroup, getOrganizationByCode } from '../helpers/organizationHelper';
import { Model } from '../services/types/catalaogueTypes';
import { IFreeLeadsParams } from '../services/types/mainApiService/mainApiService.types';
import { AxiosError } from 'axios';
import * as yup from 'yup';
import { mainApiServices } from '../services/MainApiServices';
import SelectRegionWithLimit from './SelectRegion/SelectRegionWithLimit';
import { CommonRegionProps } from './SelectRegion/SelectRegion';
import randomNumber from '../helpers/randomNumber';
import successDialog from './callableDialog/successDialog';
import { fetchCustomerThunk } from '../redux/customerInfo/customerInfoSlice';

const ModalSendToFreeLeads = () => {
  const modal = useSelector((state: TMainReduxStates) => state.modalSendToFreeLeads);
  const project = useSelector((state: TMainReduxStates) => state.reducerProject);
  const client = useSelector((state: TMainReduxStates) => state.customerReducer);
  const chatRoom = useSelector((state: TMainReduxStates) => state.reducerConversation.chatRoom);

  const organizationOptions = () => {
    const groupName = project?.project?.group === 'amartamotor' ? 'amartamotor' : 'amartamobil';
    const organizations = getOrganizationsByGroup(groupName);

    return organizations.map((org) => ({
      key: org.organization,
      text: org.name,
      value: org.organization,
    }));
  };

  const onClose = () => {
    mainStore.dispatch(modalSendToFreeLeadsSlice.actions.close());
  };

  const onSubmit = async () => {
    mainStore.dispatch(modalSendToFreeLeadsSlice.actions.setLoading(true));
    mainStore.dispatch(modalSendToFreeLeadsSlice.actions.clearErrorMessage());
    // validate form using yup
    const schema = yup.object().shape({
      organization: yup.string().required('Organisasi harus diisi'),
      cityGroup: yup.string().required('Grup kota harus diisi'),
      model: yup.string().required('Model kendaraan harus diisi'),
    });

    try {
      await schema.validate(
        {
          organization: modal?.organization || '',
          cityGroup: modal?.cityGroup || '',
          model: modal?.model?.model_name || '',
        },
        {
          abortEarly: false,
        },
      );
    } catch (error) {
      if (error instanceof yup.ValidationError) {
        mainStore.dispatch(modalSendToFreeLeadsSlice.actions.setErrorMessages(error.errors));
      }
      return;
    }

    const payload: IFreeLeadsParams = {
      externalId: randomNumber(5).toString(),
      area: modal?.cityGroup ?? '',
      paymentPlan: null,
      price: parseOrganization(modal?.organization || '')?.leads_price || 10000,
      purchasePlan: null,
      vehicleUsage: null,
      cityCode: modal?.city?.code ?? '',
      cityName: modal?.city?.name ?? '',
      driverLicense_number: '',
      email: '',
      firstName: client?.client?.profile.name ?? '',
      hasVehicleLoan: false,
      idCard_number: '',
      lastName: '',
      nextTotalVehicleOwnerShip: '',
      notes: '',
      organization: parseOrganization(modal?.organization || '')?.organization || '',
      phoneNumber: client?.client?.contacts.whatsapp ?? '',
      provinceCode: modal?.province?.code ?? '',
      provinceName: modal?.province?.name ?? '',
      source: 'ideal',
      title: 'Kakak',
      vehicleOptions: [
        {
          brand: {
            name: modal?.model?.brand_name ?? '',
          },
          model: {
            name: modal?.model?.model_name ?? '',
          },
          variant: {
            name: '',
            code: '',
          },
          color: {
            name: '',
            code: '',
          },
        },
      ],
      ideal: {
        chat_room_ref: chatRoom?.ref.path ?? null,
      },
    };

    try {
      const send = await mainApiServices.addFreeLeads(payload);
      if (send.ok) {
        mainStore.dispatch(modalSendToFreeLeadsSlice.actions.close());
        await successDialog({
          title: 'Berhasil',
          content: 'Berhasil mengirim ke free leads',
          cancelButton: false,
          okButton: 'OK',
        });

        mainStore.dispatch(
          fetchCustomerThunk({
            clientDocRef: client?.ref!,
          }) as any,
        );
      } else {
        mainStore.dispatch(
          modalSendToFreeLeadsSlice.actions.pushErrorMessage('Gagal mengirim ke free leads'),
        );
      }
    } catch (e: any) {
      let errorString: string[] = ['Gagal mengirim ke free leads'];
      console.log(e);
      if ('response' in e) {
        const error = e as AxiosError<any>;
        const errorResponseData = error.response?.data;
        if (errorResponseData?.error.messages) {
          if (errorResponseData.error.type === 'UNPROCESSABLE_ENTITY') {
            for (const [, value] of Object.entries(errorResponseData.error.messages)) {
              const v = value as any;
              errorString.push(v.msg);
            }
          } else {
            errorString = [errorResponseData.error.messages];
          }
        } else {
          errorString = ['Terjadi kesalahan'];
        }
      }
      mainStore.dispatch(modalSendToFreeLeadsSlice.actions.setErrorMessages(errorString));
    }
  };

  const parseOrganization = (org: string) => {
    return getOrganizationByCode(org);
  };

  const onOrganizationChange = (data: string) => {
    const find = parseOrganization(data);
    if (!find) return;

    mainStore.dispatch(modalSendToFreeLeadsSlice.actions.setOrganization(find.organization));
  };

  const onCityGroupChange = async (
    cityGroup: string,
    area: string,
    province_name: string,
    province_code: string,
  ) => {
    mainStore.dispatch(modalSendToFreeLeadsSlice.actions.setCityGroup(cityGroup));
  };

  const onModelChange = (data: Model) => {
    mainStore.dispatch(modalSendToFreeLeadsSlice.actions.setModel(data));
  };

  const onProvinceChange = (region: CommonRegionProps | null) => {
    mainStore.dispatch(modalSendToFreeLeadsSlice.actions.setProvince(region));
  };

  const onCityChange = (region: CommonRegionProps | null) => {
    mainStore.dispatch(modalSendToFreeLeadsSlice.actions.setCity(region));
  };

  return (
    <Modal
      open={modal?.open}
      size={'tiny'}
    >
      <Modal.Header>Kirim ke Free Leads</Modal.Header>
      <Modal.Content>
        <div style={{ marginBottom: '16px' }}>
          Pastikan semua data sudah benar dan terisi sebelum dikirim ke Free Leads.
        </div>

        <Form>
          <Form.Field required>
            <label>Organization</label>
            <Select
              placeholder="Select organization"
              options={organizationOptions()}
              search
              selection
              onChange={(event, data) => {
                onOrganizationChange(data.value as string);
              }}
              value={modal?.organization || ''}
            />
          </Form.Field>

          <Form.Field required>
            <label>City Group</label>
            <SelectAreaFromCatalog
              onChange={onCityGroupChange}
              value={modal?.cityGroup || ''}
              companyCode={parseOrganization(modal?.organization || '')?.companyCode || ''}
            />
          </Form.Field>

          <SelectVehicleFromCatalogue_v2
            cityGroup={modal?.cityGroup || ''}
            company={parseOrganization(modal?.organization || '')?.companyCode || ''}
            limit={1}
            onModelChange={onModelChange}
            selectedModel={modal?.model?.model_name || ''}
          />

          <SelectRegionWithLimit
            limit={2} // Hanya menampilkan provinsi dan kota
            userLabel={true}
            province={{
              onChange: onProvinceChange,
              selectedCode: modal?.province?.code || '',
            }}
            city={{
              onChange: onCityChange,
              selectedCode: modal?.city?.code || '',
            }}
          />
        </Form>

        {modal.errorMessages.length > 0 && (
          <div className="mt-4 p-3 rounded-md bg-red-50 border border-red-200">
            {modal.errorMessages.map((error) => (
              <div
                key={error}
                className="py-1 text-sm text-red-600 flex items-start"
              >
                <svg
                  className="w-4 h-4 mr-2 flex-shrink-0"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clipRule="evenodd"
                  />
                </svg>
                {error}
              </div>
            ))}
          </div>
        )}
      </Modal.Content>
      <Modal.Actions>
        <Button onClick={onClose}>Batal</Button>
        <Button
          primary
          onClick={onSubmit}
        >
          Kirim ke Free Leads
        </Button>
      </Modal.Actions>
    </Modal>
  );
};

export default ModalSendToFreeLeads;

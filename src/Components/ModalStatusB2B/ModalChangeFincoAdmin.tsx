import React, { Component, SyntheticEvent } from 'react';
import { Button, Form, Message, Modal, Select } from 'semantic-ui-react';
import { connect } from 'react-redux';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { mainStore } from '../../redux/reducers';
import modalChangeFincoAdminSlice, {
  fetchAdminLeasingChangeAdminLeasing,
} from '../../redux/modalStatusB2b/modalChangeFincoAdminSlice';
import { DropdownProps } from 'semantic-ui-react/dist/commonjs/modules/Dropdown/Dropdown';
import { fetchStatusB2b } from '../../redux/modalStatusB2b/modalStatusB2bSlice';
import b2bServices from '../../services/b2b/b2bServices';
import { AxiosError } from 'axios';

interface Props {
  modalChangeAdminFinco: TMainReduxStates['modalChangeFincoAdminB2b'];
}

class ModalChangeFincoAdmin extends Component<Props> {
  leasingSelect = {
    onChange: (e: SyntheticEvent, d: DropdownProps) => {
      mainStore.dispatch(modalChangeFincoAdminSlice.actions.onLeasingChange(d.value as string));
      mainStore.dispatch(fetchAdminLeasingChangeAdminLeasing(d.value as string) as any);
    },
  };

  adminLeasingSelect = {
    onChange: (e: SyntheticEvent, d: DropdownProps) => {
      mainStore.dispatch(
        modalChangeFincoAdminSlice.actions.onAdminLeasingChange(d.value as string),
      );
    },
  };

  onSubmit = async () => {
    if (!this.props.modalChangeAdminFinco.selectedLeasingAdmin) return;

    mainStore.dispatch(modalChangeFincoAdminSlice.actions.setLoadingStatus(true));

    const update = await b2bServices.changeFincoAdmin({
      offerCode: this.props.modalChangeAdminFinco.offerCodeToUpdate,
      adminRefId: this.props.modalChangeAdminFinco.selectedLeasingAdmin,
    });

    if (update.ok) {
      mainStore.dispatch(modalChangeFincoAdminSlice.actions.close());

      mainStore.dispatch(fetchStatusB2b(this.props.modalChangeAdminFinco.offerCodeToUpdate) as any);
    } else {
      const error = update.originalError as AxiosError<any>;
      let errorMessage = error.response?.data?.data.messages || 'Gagal update finco admin';

      mainStore.dispatch(modalChangeFincoAdminSlice.actions.setLoadingStatus(false));
      mainStore.dispatch(modalChangeFincoAdminSlice.actions.setErrorMessage(errorMessage));
    }
  };

  onCancel = () => {
    mainStore.dispatch(modalChangeFincoAdminSlice.actions.close());
  };

  render() {
    return (
      <Modal
        open={this.props.modalChangeAdminFinco.open}
        size={'mini'}
      >
        <Modal.Header>Ganti Finco Admin</Modal.Header>
        <Modal.Content>
          <Form>
            <Form.Field>
              <label>Leasing</label>
              <Select
                options={this.props.modalChangeAdminFinco.leasingList.map((leasing) => {
                  return {
                    value: leasing.code,
                    description: leasing.code,
                    text: leasing.name,
                    disabled: leasing.disabled,
                  };
                })}
                onChange={this.leasingSelect.onChange}
                value={this.props.modalChangeAdminFinco.selectedLeasing || ''}
                placeholder={'Pilih Leasing'}
              />
            </Form.Field>
            <Form.Field>
              <label>Admin Leasing</label>
              <Select
                options={this.props.modalChangeAdminFinco.leasingAdminList.map((admin) => ({
                  value: admin.ref.id,
                  text: `${admin.email} - ${admin.full_name}`,
                  description: admin.full_name,
                }))}
                value={this.props.modalChangeAdminFinco.selectedLeasingAdmin || ''}
                onChange={this.adminLeasingSelect.onChange}
                placeholder={'Pilih Leasing Admin'}
                search={true}
              />
            </Form.Field>
          </Form>
          {this.props.modalChangeAdminFinco.errorMessage && (
            <Message
              error={true}
              size={'small'}
              style={{ marginTop: '8px' }}
            >
              {this.props.modalChangeAdminFinco.errorMessage}
            </Message>
          )}
        </Modal.Content>
        <Modal.Actions>
          <Button
            loading={this.props.modalChangeAdminFinco.updating}
            onClick={this.onSubmit}
          >
            Submit
          </Button>
          <Button onClick={this.onCancel}>Batal</Button>
        </Modal.Actions>
      </Modal>
    );
  }
}

const mapStateToProps = (s: TMainReduxStates) => {
  return {
    modalChangeAdminFinco: s.modalChangeFincoAdminB2b,
  };
};

export default connect(mapStateToProps)(ModalChangeFincoAdmin);

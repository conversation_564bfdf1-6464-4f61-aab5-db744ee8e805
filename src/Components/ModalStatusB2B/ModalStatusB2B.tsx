import React, { Component } from 'react';
import { But<PERSON>, Modal, Segment } from 'semantic-ui-react';
import { connect } from 'react-redux';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { mainStore } from '../../redux/reducers';
import modalStatusB2bSlice from '../../redux/modalStatusB2b/modalStatusB2bSlice';
import acquisitionOfferCodeSlice from '../../redux/acquisition-offer-code/acquisitionOfferCode.slice';
import modalDetailOfferSlice, {
  modalDetailOfferFetch,
} from '../../redux/modal-detail-offer/modalDetailOfferSlice';
import ModalChangeFincoAdmin from './ModalChangeFincoAdmin';
import modalChangeFincoAdminSlice, {
  fetchLeasingChangeAdminLeasing,
} from '../../redux/modalStatusB2b/modalChangeFincoAdminSlice';

interface Props {
  modalStatusB2b: TMainReduxStates['modalStatusB2b'];
}

class ModalStatusB2B extends Component<Props> {
  onGetDetailClick = () => {
    if (!this.props.modalStatusB2b.orderHistory) return;
    if (!this.props.modalStatusB2b.dataStatusB2b?.survey.already_in_offer?.offer_code) return;
    mainStore.dispatch(
      modalDetailOfferSlice.actions.open({
        open: true,
        data: this.props.modalStatusB2b.orderHistory,
      }),
    );
    mainStore.dispatch(
      modalDetailOfferFetch(
        this.props.modalStatusB2b.dataStatusB2b.survey.already_in_offer?.offer_code,
      ) as any,
    );
  };

  render() {
    const { dataStatusB2b } = this.props.modalStatusB2b;

    return (
      <React.Fragment>
        <ModalChangeFincoAdmin />
        <Modal open={this.props.modalStatusB2b.open}>
          <Modal.Header>Status B2B</Modal.Header>
          {this.props.modalStatusB2b.fetching && <Modal.Content>Mohon Tunggu...</Modal.Content>}
          {this.props.modalStatusB2b.errorMessage && (
            <Modal.Content>{this.props.modalStatusB2b.errorMessage}</Modal.Content>
          )}
          {this.props.modalStatusB2b.dataStatusB2b && (
            <Modal.Content>
              <Segment className={'offer-detail-section'}>
                <div className={'offer-detail-section-title'}>Status Survey</div>
                <div className={'offer-detail-section-description'}>
                  <div className={'offer-detail-item-description-row'}>
                    <div>No Transaksi</div>
                    <div>{dataStatusB2b?.survey.offer_code}</div>
                  </div>
                  <div className={'offer-detail-item-description-row'}>
                    <div>Status</div>
                    <div>{dataStatusB2b?.survey.status}</div>
                  </div>
                  <div className={'offer-detail-item-description-row'}>
                    <div>Leasing</div>
                    <div>{dataStatusB2b?.survey.admin_leasing_target.leasing_company?.name}</div>
                  </div>
                  <div className={'offer-detail-item-description-row'}>
                    <div>Admin Leasing</div>
                    <div>{dataStatusB2b?.survey.admin_leasing_target?.full_name}</div>
                  </div>
                  <div className={'offer-detail-item-description-row'}>
                    <div>Nik CMO</div>
                    <div>
                      {
                        dataStatusB2b?.survey.admin_leasing_target?.settings?.adira_host_2_host
                          ?.nikcmo
                      }
                    </div>
                  </div>
                  <div className={'offer-detail-item-description-row'}>
                    <div>Akuisisi Offer</div>
                    <div>{dataStatusB2b?.survey.already_in_offer?.offer_code || ''}</div>
                  </div>
                </div>
              </Segment>
              {dataStatusB2b?.survey.adira_host_2_host && (
                <Segment className={'offer-detail-section'}>
                  <div className={'offer-detail-section-title'}>Adira Host 2 Host</div>
                  <div className={'offer-detail-section-description'}>
                    <div className={'offer-detail-item-description-row'}>
                      <div>Order No</div>
                      <div>
                        {dataStatusB2b?.survey.adira_host_2_host?.submitDataResponse?.OrderNo}
                      </div>
                    </div>
                    <div className={'offer-detail-item-description-row'}>
                      <div>Status</div>
                      <div>
                        {dataStatusB2b?.survey.adira_host_2_host?.status?.status_code || (
                          <i>Belum Ada</i>
                        )}
                      </div>
                    </div>
                  </div>
                </Segment>
              )}
              <div>
                {this.props.modalStatusB2b.dataStatusB2b.survey.status === 'OPEN' && (
                  <Button
                    onClick={(event) => {
                      mainStore.dispatch(
                        modalChangeFincoAdminSlice.actions.open(
                          this.props.modalStatusB2b.orderHistory?.offer_code || '',
                        ),
                      );
                      mainStore.dispatch(fetchLeasingChangeAdminLeasing() as any);
                    }}
                  >
                    Pilih Finco Admin
                  </Button>
                )}
                {!this.props.modalStatusB2b.dataStatusB2b?.survey.already_in_offer?.offer_code && (
                  <Button
                    primary
                    onClick={() => {
                      mainStore.dispatch(
                        acquisitionOfferCodeSlice.actions.open({
                          offerCode:
                            this.props.modalStatusB2b.dataStatusB2b?.survey.offer_code || '',
                        }),
                      );
                    }}
                    disabled={!this.props.modalStatusB2b.dataStatusB2b}
                  >
                    Akuisisi E-PO
                  </Button>
                )}
                {this.props.modalStatusB2b.dataStatusB2b?.survey.already_in_offer?.offer_code && (
                  <Button
                    primary
                    disabled={!this.props.modalStatusB2b.dataStatusB2b}
                    onClick={this.onGetDetailClick}
                  >
                    Detail Offer Akuisisi
                  </Button>
                )}
              </div>
            </Modal.Content>
          )}

          <Modal.Actions>
            <Button onClick={() => mainStore.dispatch(modalStatusB2bSlice.actions.close())}>
              Tutup
            </Button>
          </Modal.Actions>
        </Modal>
      </React.Fragment>
    );
  }
}

const mapStateToPros = (s: TMainReduxStates) => {
  return {
    modalStatusB2b: s.modalStatusB2b,
  };
};

const c = connect(mapStateToPros)(ModalStatusB2B);

export default c;

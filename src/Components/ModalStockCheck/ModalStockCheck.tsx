import React, { Component } from 'react';
import { Button, Form, Modal, Segment, Select, Table } from 'semantic-ui-react';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { connect } from 'react-redux';
import { mainStore } from '../../redux/reducers';
import stockCheckSlice from '../../redux/stock-check/stock-check.slice';
import { otodisServices } from '../../services/otodisServices';
import { DropdownProps } from 'semantic-ui-react/dist/commonjs/modules/Dropdown/Dropdown';
import currencyFormat from '../../helpers/currencyFormat';
import { catalogueServices } from '../../services/catalogue/catalogueServices';

interface Props {
  modal: TMainReduxStates['modalStockCheck'];
}

class ModalStockCheck extends Component<Props> {
  onClose = () => {
    mainStore.dispatch(stockCheckSlice.actions.close());
  };

  onClickCheckStock = async () => {
    mainStore.dispatch(stockCheckSlice.actions.setLoadingState(true));

    if (this.props.modal.viewStockMode === 'dealer') {
      const getDealers = await otodisServices.getDealer();
      if (getDealers?.data) {
        let stock = await Promise.all(
          getDealers.data.map(
            (dealer) =>
              new Promise<{ dealerName: string; count: number }>(async (resolve) => {
                try {
                  const getStock = await otodisServices.getStock({
                    vehicle_year: this.props.modal.vehicle?.year || '',
                    vehicle_color_code: this.props.modal.vehicle?.color.code ?? '',
                    vehicle_variant_code: this.props.modal.vehicle?.variant.code ?? '',
                    dealer_code: dealer.dealer_code,
                    lite_respone: true,
                    status_stock: 'ACTIVE',
                  });

                  resolve({
                    count: getStock?.data.length ?? 0,
                    dealerName: dealer.dealer_name,
                  });
                } catch (e) {
                  resolve({
                    count: 0,
                    dealerName: dealer.dealer_name,
                  });
                }
              }),
          ),
        );

        mainStore.dispatch(stockCheckSlice.actions.setStockAllDealer(stock));
      }
    } else if (this.props.modal.viewStockMode === 'cityGroup') {
      const getCities = await catalogueServices.getAvailableCityByCityGroup({
        cityGroup: this.props.modal.cityGroup,
      });
      const cityCode = getCities.data?.data[0].city_code || '';

      const getStock = await catalogueServices.checkAvailabilityMultiDealer({
        cityCode: cityCode,
        variantCode: this.props.modal.vehicle!.variant.code || '',
      });

      const stock = getStock?.data || [];

      mainStore.dispatch(stockCheckSlice.actions.setStockCityGroup(stock));
    }

    mainStore.dispatch(stockCheckSlice.actions.setLoadingState(false));
  };

  onChangeMode = (e: any, d: DropdownProps) => {
    mainStore.dispatch(stockCheckSlice.actions.onChangeViewStockMode(d.value as any));
  };

  render() {
    return (
      <Modal open={this.props.modal.open}>
        <Modal.Header>Periksa Stok</Modal.Header>
        <Modal.Content>
          <div className={'mb-5'}>
            <div className={'font-bold'}>
              {this.props.modal.vehicle?.variant.name.toUpperCase()}
            </div>
            <div>
              Pilihan Warna:{' '}
              <span className={'font-bold'}>
                {this.props.modal.vehicle?.color.name.toUpperCase()}
              </span>
            </div>
          </div>
          <Form>
            <Form.Group>
              <Form.Field>
                <Select
                  value={this.props.modal.viewStockMode}
                  options={[
                    {
                      text: 'Semua Dealer',
                      value: 'dealer',
                    },
                    {
                      text: 'Semua City Group',
                      value: 'cityGroup',
                    },
                  ]}
                  onChange={this.onChangeMode}
                />
              </Form.Field>
              <Form.Field>
                <Button
                  onClick={this.onClickCheckStock}
                  loading={this.props.modal.fetching}
                >
                  Periksa Stok
                </Button>
              </Form.Field>
            </Form.Group>
          </Form>
          {this.props.modal.viewStockMode === 'dealer' &&
            this.props.modal.stockAvailabilityAllDealer.length > 0 && (
              <Table>
                <Table.Header>
                  <Table.Row>
                    <Table.HeaderCell>Dealer</Table.HeaderCell>
                    <Table.HeaderCell>Stok</Table.HeaderCell>
                  </Table.Row>
                </Table.Header>

                <Table.Body>
                  {this.props.modal.stockAvailabilityAllDealer.map((value, index) => {
                    return (
                      <Table.Row key={index}>
                        <Table.Cell>{value.dealerName}</Table.Cell>
                        <Table.Cell>{value.count}</Table.Cell>
                      </Table.Row>
                    );
                  })}
                </Table.Body>
              </Table>
            )}

          {this.props.modal.viewStockMode === 'cityGroup' && (
            <div>
              {this.props.modal.stockAvailabilityByCityGroup.map((s) => {
                return (
                  <Segment>
                    <div className={'font-bold mb-3'}>Wilayah {s.city_group}</div>
                    <div>
                      {!!s.stock.data.color?.[this.props.modal.vehicle!.color.code] ? (
                        <div>
                          {s.stock.data.color[this.props.modal.vehicle!.color.code]! > 0 ? (
                            <span>
                              Tersedia {s.stock.data.color[this.props.modal.vehicle!.color.code]!}{' '}
                              unit
                            </span>
                          ) : (
                            <span>Tidak Tersedia</span>
                          )}
                        </div>
                      ) : (
                        'Warna Tidak Tersedia'
                      )}
                    </div>
                    <div>Ongkos Kirim: {currencyFormat(s.shipment_cost)}</div>
                  </Segment>
                );
              })}
            </div>
          )}
        </Modal.Content>
        <Modal.Actions>
          <Button onClick={this.onClose}>Tutup</Button>
        </Modal.Actions>
      </Modal>
    );
  }
}

const mapStateToProps = (s: TMainReduxStates) => {
  return {
    modal: s.modalStockCheck,
  };
};

export default connect(mapStateToProps)(ModalStockCheck);

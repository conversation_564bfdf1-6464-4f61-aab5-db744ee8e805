import React, { Component } from 'react';
import { Form, Segment } from 'semantic-ui-react';
import SelectPromoCode from '../SelectPromoCode';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { connect } from 'react-redux';
import { IPromoCode } from '../../services/types/promoServiceTypes';
import { mainStore } from '../../redux/reducers';
import modalUpdateDataPromoCodeToOtodisSlice from '../../redux/modal-update-data-to-otodis/modalUpdateDataPromoCodeToOtodisSlice';

interface Props {
  modalDetailOffer: TMainReduxStates['modalDetailOfferCode'];
  modal: TMainReduxStates['modalUpdateDataPromoCodeToOtodis'];
}

class ApplyPromoCodeDataToOtodis extends Component<Props> {
  onSelect = (v: IPromoCode | null) => {
    mainStore.dispatch(modalUpdateDataPromoCodeToOtodisSlice.actions.setPromoCode(v));
  };

  render() {
    return (
      <Segment attached>
        <Form>
          <SelectPromoCode
            city_group={this.props.modalDetailOffer.dataOfferCode?.area || ''}
            purchase_method={'credit'}
            vehicle_model={this.props.modalDetailOffer.dataOfferCode?.vehicle.model_name || ''}
            onChange={this.onSelect}
            value={this.props.modal.promoCode?.promo_code}
          />
        </Form>
      </Segment>
    );
  }
}

const mapStateToProps = (states: TMainReduxStates) => {
  return {
    modalDetailOffer: states.modalDetailOfferCode,
    modal: states.modalUpdateDataPromoCodeToOtodis,
  };
};
export default connect(mapStateToProps)(ApplyPromoCodeDataToOtodis);

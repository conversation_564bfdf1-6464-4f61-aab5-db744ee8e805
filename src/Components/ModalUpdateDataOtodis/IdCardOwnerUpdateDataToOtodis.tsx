import { Button, Divider, Form, Segment } from 'semantic-ui-react';
import moment from 'moment/moment';
import React from 'react';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { connect } from 'react-redux';
import { mainStore } from '../../redux/reducers';
import modalSetImageAsSlice from '../../redux/modal-capture-image/modalSetImageAs.slice';

interface Props {
  customer: TMainReduxStates['customerReducer'];
  modal: TMainReduxStates['modalUpdateDataToOtodis'];
}

const IdCardOwnerUpdateDataToOtodis = (props: Props) => {
  const openModalUpdateIdCardOwner = () => {
    mainStore.dispatch(
      modalSetImageAsSlice.actions.open({
        type: 'UPDATE',
        documentTarget: 'idCardOwner',
      }),
    );
  };

  const { client } = props.customer;
  return (
    <Segment attached>
      <Form>
        <Form.Field>
          <label>Gambar KTP</label>
          {client?.details.idCardOwner?.idCardNumber ? (
            <a
              href={client?.details.idCardOwner?.idCardImage}
              target={'_blank'}
              rel="noreferrer"
              className={'text-blue-500 font-bold'}
            >
              Lihat Foto di Tab Baru
            </a>
          ) : (
            <b>Belum ada</b>
          )}
        </Form.Field>
        <Form.Group widths={'equal'}>
          <Form.Field>
            <label>No KTP</label>
            <span>{client?.details.idCardOwner?.idCardNumber}</span>
          </Form.Field>
          <Form.Field>
            <label>Nama Lengkap</label>
            {client?.details.idCardOwner?.fullName}
          </Form.Field>
        </Form.Group>
        <Form.Group widths={'equal'}>
          <Form.Field>
            <label>Tempat Lahir</label>
            {client?.details.idCardOwner?.placeOfBirth}
          </Form.Field>
          <Form.Field>
            <label>Tanggal Lahir</label>
            <span>
              {moment(client?.details.idCardOwner?.dateOfBirth.toDate()).format('YYYY-MM-DD')}
            </span>
          </Form.Field>
        </Form.Group>
        <Form.Group widths={'equal'}>
          <Form.Field>
            <label>Status Pernikahan</label>
            <span>{client?.details.idCardOwner?.maritalStatus}</span>
          </Form.Field>
          <Form.Field>
            <label>Pekerjaan</label>
            <span>{client?.details.idCardOwner?.occupation}</span>
          </Form.Field>
        </Form.Group>
        <Form.Field>
          <label>Alamat</label>
          <span>{client?.details.idCardOwner?.fullAddress}</span>
        </Form.Field>
        <Divider horizontal={true}>Alamat Pemilik </Divider>
        <Form.Field>
          <label>Alamat</label>
          <span>{client?.details.idCardOwner?.fullAddress}</span>
        </Form.Field>
        <Form.Group widths={'equal'}>
          <Form.Field>
            <label>Provinsi</label>
            <span>{client?.details.idCardOwner?.province.name}</span>
          </Form.Field>
          <Form.Field>
            <label>Kota</label>
            <span>{client?.details.idCardOwner?.city.name}</span>
          </Form.Field>
        </Form.Group>
        <Form.Group widths={'equal'}>
          <Form.Field>
            <label>Kecamatan</label>
            <span>{client?.details.idCardOwner?.subDistrict.name}</span>
          </Form.Field>
          <Form.Field>
            <label>Kelurahan</label>
            <span>{client?.details.idCardOwner?.subDistrict.name}</span>
          </Form.Field>
        </Form.Group>
        <Form.Group widths={'equal'}>
          <Form.Field>
            <label>Kode Pos</label>
            <span>{client?.details.idCardOwner?.zipCode}</span>
          </Form.Field>
          <Form.Field>
            <label>RT/RW</label>
            <span>
              {client?.details.idCardOwner?.neighbourhood}/{client?.details.idCardOwner?.hamlet}
            </span>
          </Form.Field>
        </Form.Group>
        <Button onClick={openModalUpdateIdCardOwner}>Perbarui KTP Pemilik</Button>
      </Form>
    </Segment>
  );
};

const mapStateToProps = (states: TMainReduxStates) => {
  return {
    customer: states.customerReducer,
    modal: states.modalUpdateDataToOtodis,
  };
};

export default connect(mapStateToProps)(IdCardOwnerUpdateDataToOtodis);

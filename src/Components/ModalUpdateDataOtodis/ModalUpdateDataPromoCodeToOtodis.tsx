import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Message, Modal, Segment } from 'semantic-ui-react';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { connect } from 'react-redux';
import { mainStore } from '../../redux/reducers';
import ApplyPromoCodeDataToOtodis from './ApplyPromoCodeDataToOtodis';
import { offerServices } from '../../services/offerServices';
import { AxiosError } from 'axios';
import {
  ErrorResponseUpdateDataToOtodis,
  ParamsUpdateDataToOtodisBasic,
} from '../../services/types/offerServices.types';
import { modalDetailOfferFetch } from '../../redux/modal-detail-offer/modalDetailOfferSlice';
import { Timestamp, updateDoc } from 'firebase/firestore';
import modalUpdateDataPromoCodeToOtodisSlice from '../../redux/modal-update-data-to-otodis/modalUpdateDataPromoCodeToOtodisSlice';
import currencyFormat from '../../helpers/currencyFormat';

interface Props {
  customer: TMainReduxStates['customerReducer'];
  modal: TMainReduxStates['modalUpdateDataPromoCodeToOtodis'];
  modalDetailOffer: TMainReduxStates['modalDetailOfferCode'];
  admin: TMainReduxStates['reducerAdmin'];
}

function ModalUpdateDataPromoCodeToOtodis(props: Props) {
  const close = () => {
    if (props.modal.updating) return;
    mainStore.dispatch(modalUpdateDataPromoCodeToOtodisSlice.actions.close());
  };

  const submit = async () => {
    mainStore.dispatch(modalUpdateDataPromoCodeToOtodisSlice.actions.setProcessUpdating());
    const now = Timestamp.now();

    const { customer, modalDetailOffer, admin } = props;
    const predefinedData: Pick<
      ParamsUpdateDataToOtodisBasic<any>,
      'company' | 'offer_code' | 'admin_id' | 'admin_name'
    > = {
      company: 'AMARTA',
      offer_code: modalDetailOffer.offerCodeToView || '',
      admin_id: admin.admin?.email || '',
      admin_name: admin.admin?.name || '',
    };
    let dataUpdate = {
      promo_codes: props.modal.promoCode ? [props.modal.promoCode.promo_code] : [],
    };

    if (props.modal.promoCode) {
      try {
        await offerServices.updatePromoCode({
          update_type: 'promo-codes',
          ...predefinedData,
          promo_codes: dataUpdate.promo_codes,
        });
      } catch (e) {
        console.log('ERROR UPDATE KODE PROMO', e);
        const error = e as AxiosError<ErrorResponseUpdateDataToOtodis>;
        mainStore.dispatch(
          modalUpdateDataPromoCodeToOtodisSlice.actions.setProcessUpdatingError(
            error.response?.data?.error?.message || 'Terjadi error ketika update Kode Promo',
          ),
        );
        return;
      }
    }

    const listOrderHistories = [...(customer.client?.order_histories || [])];
    const findIndex = listOrderHistories.findIndex(
      (o) => o.offer_code === modalDetailOffer.orderHistory?.offer_code,
    );

    if (findIndex > -1) {
      listOrderHistories[findIndex] = {
        ...listOrderHistories[findIndex],
        lastUpdateDataPromoCodeToOtodis: {
          success: true,
          updatedAt: now,
          offerCode: props.modalDetailOffer.offerCodeToView || '',
          dataUpdate: dataUpdate,
        },
      };

      await updateDoc(customer.ref!, {
        order_histories: listOrderHistories,
      })
        .then(() => {})
        .catch(() => {});
    }
    mainStore.dispatch(modalDetailOfferFetch(modalDetailOffer.offerCodeToView || '') as any);
    mainStore.dispatch(modalUpdateDataPromoCodeToOtodisSlice.actions.setProcessFinishSuccess());
  };

  return (
    <Modal
      open={props.modal.open}
      size={'small'}
    >
      <Modal.Header content={'Update Data Kode Promo ke Otodis'} />
      <Modal.Content>
        <Segment>
          <Grid columns={2}>
            <Grid.Row>
              <Grid.Column>Kode Offer</Grid.Column>
              <Grid.Column>
                <strong>{props.modalDetailOffer.offerCodeToView}</strong>
              </Grid.Column>
            </Grid.Row>
            <Grid.Row>
              <Grid.Column>Kendaraan</Grid.Column>
              <Grid.Column>
                <strong>
                  {props.modalDetailOffer.dataOfferCode?.vehicle.variant_name.toUpperCase() +
                    ' ' +
                    props.modalDetailOffer.dataOfferCode?.vehicle.color_name.toUpperCase()}
                </strong>
              </Grid.Column>
            </Grid.Row>
            <Grid.Row>
              <Grid.Column>OTR</Grid.Column>
              <Grid.Column>
                <strong>
                  {currencyFormat(props.modalDetailOffer.dataOfferCode?.price_and_bill.otr || 0)}
                </strong>
              </Grid.Column>
            </Grid.Row>
            <Grid.Row>
              <Grid.Column>Uang Muka</Grid.Column>
              <Grid.Column>
                <strong>
                  {currencyFormat(props.modalDetailOffer.dataOfferCode?.credit.dp_amount || 0)}
                </strong>
              </Grid.Column>
            </Grid.Row>
            <Grid.Row>
              <Grid.Column>Tenor</Grid.Column>
              <Grid.Column>
                <strong>{props.modalDetailOffer.dataOfferCode?.credit.tenor || 0}</strong>
              </Grid.Column>
            </Grid.Row>
            <Grid.Row>
              <Grid.Column>Angsuran</Grid.Column>
              <Grid.Column>
                <strong>
                  {currencyFormat(props.modalDetailOffer.dataOfferCode?.credit.dp_amount || 0)}
                </strong>
              </Grid.Column>
            </Grid.Row>
          </Grid>
        </Segment>
        <Header
          as={'h5'}
          attached={'top'}
        >
          Kode Promo
        </Header>
        <ApplyPromoCodeDataToOtodis />
        {props.modal.success && <Message positive={true}>Berhasil Update Data ke Otodis</Message>}

        {props.modal.errorMessage && <Message negative={true}>{props.modal.errorMessage}</Message>}
      </Modal.Content>
      <Modal.Actions>
        <Button onClick={close}>Tutup</Button>
        <Button
          color={'blue'}
          loading={props.modal.updating}
          onClick={submit}
          disabled={!props.modal.promoCode}
        >
          Update ke Otodis
        </Button>
      </Modal.Actions>
    </Modal>
  );
}

const mapStateToProps = (states: TMainReduxStates) => {
  return {
    customer: states.customerReducer,
    modal: states.modalUpdateDataPromoCodeToOtodis,
    modalDetailOffer: states.modalDetailOfferCode,
    admin: states.reducerAdmin,
  };
};

export default connect(mapStateToProps)(ModalUpdateDataPromoCodeToOtodis);

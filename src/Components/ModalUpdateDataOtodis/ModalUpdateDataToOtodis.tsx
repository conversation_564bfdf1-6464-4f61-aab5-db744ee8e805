import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Message, Modal, Segment } from 'semantic-ui-react';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { connect } from 'react-redux';
import { mainStore } from '../../redux/reducers';
import modalUpdateDataToOtodisSlice from '../../redux/modal-update-data-to-otodis/modalUpdateDataToOtodisSlice';
import ShippingAddressFormUpdateDataToOtodis from './ShippingAddressFormUpdateDataToOtodis';
import IdCardOwnerUpdateDataToOtodis from './IdCardOwnerUpdateDataToOtodis';
import { offerServices } from '../../services/offerServices';
import { AxiosError } from 'axios';
import {
  ErrorResponseUpdateDataToOtodis,
  ParamsUpdateDataToOtodisBasic,
} from '../../services/types/offerServices.types';
import { modalDetailOfferFetch } from '../../redux/modal-detail-offer/modalDetailOfferSlice';
import { Timestamp, updateDoc } from 'firebase/firestore';
import moment from 'moment';

interface Props {
  customer: TMainReduxStates['customerReducer'];
  modal: TMainReduxStates['modalUpdateDataToOtodis'];
  modalDetailOffer: TMainReduxStates['modalDetailOfferCode'];
  admin: TMainReduxStates['reducerAdmin'];
}

function ModalUpdateDataToOtodis(props: Props) {
  const close = () => {
    if (props.modal.updating) return;
    mainStore.dispatch(modalUpdateDataToOtodisSlice.actions.close());
  };

  const submit = async () => {
    mainStore.dispatch(modalUpdateDataToOtodisSlice.actions.setProcessUpdating());
    const now = Timestamp.now();

    const { customer, modalDetailOffer, admin, modal } = props;
    const { shippingAddress, customerCodAmount } = modal;
    const idCard = customer.client?.details.idCardOwner;
    if (!idCard) {
      mainStore.dispatch(
        modalUpdateDataToOtodisSlice.actions.setProcessUpdatingError(
          'KTP Pemilik tidak ada datanya!',
        ),
      );
      return;
    }
    const predefinedData: Pick<
      ParamsUpdateDataToOtodisBasic<any>,
      'company' | 'offer_code' | 'admin_id' | 'admin_name'
    > = {
      company: 'AMARTA',
      offer_code: modalDetailOffer.offerCodeToView || '',
      admin_id: admin.admin?.email || '',
      admin_name: admin.admin?.name || '',
    };
    let dataUpdate = {
      id_card_owner: {
        id_card_number: idCard.idCardNumber,
        id_card_image: idCard.idCardImage || '',
        full_name: idCard.fullName,
        full_address: idCard.fullAddress,
        birth_place: idCard.placeOfBirth,
        birth_date: moment(idCard.dateOfBirth.toDate()).format('YYYY-MM-DD'),
        marital_status_code: idCard.maritalStatusCode?.toString() || '',
        marital_status: idCard.maritalStatus,
        occupation_code: idCard.occupationCode,
      },
      address_owner: {
        full_address: idCard.fullAddress,
        province_code: idCard.province.code,
        province_name: idCard.province.name,
        city_code: idCard.city.code,
        city_name: idCard.city.name,
        district_code: idCard.district.code,
        district_name: idCard.district.name,
        sub_district_code: idCard.subDistrict.code,
        sub_district_name: idCard.subDistrict.name,
        zip_code: idCard.zipCode,
      },
      address_shipping: {
        full_address: shippingAddress.fullAddress,
        province_code: shippingAddress.province?.code || '',
        province_name: shippingAddress.province?.name || '',
        city_code: shippingAddress.city?.code || '',
        city_name: shippingAddress.city?.name || '',
        district_code: shippingAddress.district?.code || '',
        district_name: shippingAddress.district?.name || '',
        sub_district_code: shippingAddress.district?.code || '',
        sub_district_name: shippingAddress.district?.name || '',
        zip_code: shippingAddress.zipCode,
        location_point: {
          point: `${shippingAddress.latitude}, ${shippingAddress.longitude}`,
          lat: `${shippingAddress.latitude}`,
          lng: `${shippingAddress.longitude}`,
        },
      },
      data_shipping: {
        mediator_name: '',
        mediator_phone_number: '',
        mediator_credit_amount: 0,
        customer_cash_on_delivery: customerCodAmount || 0,
        shipping_date: '',
        shipping_credit_payment_date: '',
      },
    };

    try {
      await offerServices.updateIdCard({
        update_type: 'id-card-owner',
        ...predefinedData,
        id_card_owner: dataUpdate.id_card_owner,
        address_owner: dataUpdate.address_owner,
      });
    } catch (e) {
      const error = e as AxiosError<ErrorResponseUpdateDataToOtodis>;
      mainStore.dispatch(
        modalUpdateDataToOtodisSlice.actions.setProcessUpdatingError(
          error.response?.data.error.message || 'Terjadi error ketika update KTP Pemilik',
        ),
      );
    }

    try {
      await offerServices.updateShippingAddress({
        update_type: 'address-shipping',
        ...predefinedData,
        address_shipping: dataUpdate.address_shipping,
        data_shipping: dataUpdate.data_shipping,
      });
    } catch (e) {
      const error = e as AxiosError<ErrorResponseUpdateDataToOtodis>;
      mainStore.dispatch(
        modalUpdateDataToOtodisSlice.actions.setProcessUpdatingError(
          error.response?.data.error.message || 'Terjadi error ketika update Alamat Kirim',
        ),
      );
      return;
    }

    const listOrderHistory = [...(customer.client?.order_histories || [])];
    const findIndex = listOrderHistory.findIndex(
      (o) => o.offer_code === modalDetailOffer.orderHistory?.offer_code,
    );

    if (findIndex >= 0) {
      listOrderHistory[findIndex] = {
        ...listOrderHistory[findIndex],
        lastUpdateDataToOtodis: {
          success: true,
          updatedAt: now,
          offerCode: props.modalDetailOffer.offerCodeToView || '',
          dataUpdate: dataUpdate,
        },
      };

      await updateDoc(customer.ref!, {
        order_histories: listOrderHistory,
      })
        .then((value) => {})
        .catch((reason) => {});
    }
    mainStore.dispatch(modalDetailOfferFetch(modalDetailOffer.offerCodeToView || '') as any);
    mainStore.dispatch(modalUpdateDataToOtodisSlice.actions.setProcessFinishSuccess());
  };

  return (
    <Modal
      open={props.modal.open}
      size={'small'}
    >
      <Modal.Header content={'Update Data ke Otodis'} />
      <Modal.Content>
        <Segment>
          <Grid columns={2}>
            <Grid.Row>
              <Grid.Column>Kode Offer yang Akan Di Update</Grid.Column>
              <Grid.Column>
                <strong>{props.modalDetailOffer.offerCodeToView}</strong>
              </Grid.Column>
            </Grid.Row>
          </Grid>
        </Segment>
        <Header
          as={'h5'}
          attached={'top'}
        >
          KTP Pemilik
        </Header>
        <IdCardOwnerUpdateDataToOtodis />

        <Header
          as={'h5'}
          attached={'top'}
        >
          Alamat Kirim
        </Header>
        <ShippingAddressFormUpdateDataToOtodis />

        {props.modal.success && <Message positive={true}>Berhasil Update Data ke Otodis</Message>}

        {props.modal.errorMessage && <Message negative={true}>{props.modal.errorMessage}</Message>}
      </Modal.Content>
      <Modal.Actions>
        <Button onClick={close}>Tutup</Button>
        <Button
          color={'blue'}
          loading={props.modal.updating}
          onClick={submit}
        >
          Update ke Otodis
        </Button>
      </Modal.Actions>
    </Modal>
  );
}

const mapStateToProps = (states: TMainReduxStates) => {
  return {
    customer: states.customerReducer,
    modal: states.modalUpdateDataToOtodis,
    modalDetailOffer: states.modalDetailOfferCode,
    admin: states.reducerAdmin,
  };
};

export default connect(mapStateToProps)(ModalUpdateDataToOtodis);

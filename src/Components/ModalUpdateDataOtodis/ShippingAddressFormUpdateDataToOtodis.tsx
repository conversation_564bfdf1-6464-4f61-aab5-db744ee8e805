import React, { Component } from 'react';
import { Button, Form, Input, Segment, TextArea } from 'semantic-ui-react';
import SelectRegion, { CommonRegionProps } from '../SelectRegion/SelectRegion';
import { mainStore } from '../../redux/reducers';
import modalUpdateDataToOtodisSlice from '../../redux/modal-update-data-to-otodis/modalUpdateDataToOtodisSlice';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { connect } from 'react-redux';

interface Props {
  modal: TMainReduxStates['modalUpdateDataToOtodis'];
  customer: TMainReduxStates['customerReducer'];
}

class ShippingAddressFormUpdateDataToOtodis extends Component<Props> {
  private region = {
    province: {
      onChange: (params: CommonRegionProps | null) =>
        mainStore.dispatch(modalUpdateDataToOtodisSlice.actions.setProvince(params)),
    },
    city: {
      onChange: (params: CommonRegionProps | null) =>
        mainStore.dispatch(modalUpdateDataToOtodisSlice.actions.setCity(params)),
    },
    district: {
      onChange: (params: CommonRegionProps | null) =>
        mainStore.dispatch(modalUpdateDataToOtodisSlice.actions.setDistrict(params)),
    },
    subDistrict: {
      onChange: (params: CommonRegionProps | null, postalCode: string | null) =>
        mainStore.dispatch(modalUpdateDataToOtodisSlice.actions.setSubDistrict(params)),
    },
    postalCode: {
      onChange: (postalCode: string | null) =>
        mainStore.dispatch(modalUpdateDataToOtodisSlice.actions.setZipCode(postalCode || '')),
    },
  };

  private fullAddress = {
    onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) =>
      mainStore.dispatch(modalUpdateDataToOtodisSlice.actions.setAddress(e.target.value)),
  };

  private latitude = {
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      mainStore.dispatch(modalUpdateDataToOtodisSlice.actions.setLatitude(value));
    },
  };

  private longitude = {
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      mainStore.dispatch(modalUpdateDataToOtodisSlice.actions.setLongitude(value));
    },
  };

  private customerCodAmount = {
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      mainStore.dispatch(
        modalUpdateDataToOtodisSlice.actions.setCustomerCodAmount(parseInt(value)),
      );
    },
  };

  private buttonSameLongLatAsSurvey = () => {
    const geo = this.props.customer.client?.survey?.credit_scheme?.surveyGeo;
    if (geo) {
      mainStore.dispatch(modalUpdateDataToOtodisSlice.actions.setLongitude(geo.long.toString()));
      mainStore.dispatch(modalUpdateDataToOtodisSlice.actions.setLatitude(geo.lat.toString()));
    }
  };

  componentDidMount() {
    this.buttonSameLongLatAsSurvey();
  }

  render() {
    const address = this.props.modal.shippingAddress;
    return (
      <Segment attached={true}>
        <Form>
          <Form.Field>
            <label>Alamat Lengkap</label>
            <TextArea
              value={address.fullAddress}
              onChange={this.fullAddress.onChange}
            />
          </Form.Field>
          <SelectRegion
            userLabel={true}
            postCode={{
              selectedCode: address.zipCode,
              onChange: this.region.postalCode.onChange,
            }}
            province={{
              selectedCode: address.province?.code,
              onChange: this.region.province.onChange,
            }}
            city={{
              selectedCode: address.city?.code,
              onChange: this.region.city.onChange,
            }}
            district={{
              selectedCode: address.district?.code,
              onChange: this.region.district.onChange,
            }}
            subDistrict={{
              selectedCode: address.subDistrict?.code,
              onChange: this.region.subDistrict.onChange,
            }}
          />
          <Form.Group widths={'equal'}>
            <Form.Field>
              <label>Latitude</label>
              <Input
                placeholder={'Latitude'}
                onChange={this.latitude.onChange}
                value={address.latitude}
                type={'number'}
                readOnly
              />
            </Form.Field>
            <Form.Field>
              <label>Longitude</label>
              <Input
                placeholder={'Longitude'}
                onChange={this.longitude.onChange}
                value={address.longitude}
                type={'number'}
                readOnly
              />
            </Form.Field>
          </Form.Group>
          <Form.Field>
            <label>Uang Yang Harus Dibayarkan Penerima</label>
            <Input
              type={'number'}
              value={this.props.modal.customerCodAmount}
              onChange={this.customerCodAmount.onChange}
            />
          </Form.Field>
          <Button onClick={this.buttonSameLongLatAsSurvey}>
            Samakan Long Lat dengan Lokasi Survey
          </Button>
        </Form>
      </Segment>
    );
  }
}

const mapStateToProps = (states: TMainReduxStates) => {
  return {
    modal: states.modalUpdateDataToOtodis,
    customer: states.customerReducer,
  };
};

export default connect(mapStateToProps)(ShippingAddressFormUpdateDataToOtodis);

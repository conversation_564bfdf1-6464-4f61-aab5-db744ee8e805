import React from 'react';
import { But<PERSON>, Form, Message, Modal, Select } from 'semantic-ui-react';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { connect } from 'react-redux';
import { DropdownProps } from 'semantic-ui-react/dist/commonjs/modules/Dropdown/Dropdown';
import { mainStore } from '../../redux/reducers';
import modalSetPhoneNumberAsSlice from '../../redux/modal-update-phone-number/modalSetPhoneNumberAs.slice';
import { mainApiServices } from '../../services/MainApiServices';
import { fetchCustomerThunk } from '../../redux/customerInfo/customerInfoSlice';
import maskSanitizedPhoneNumber from '../../helpers/maskPhoneNumber/maskSanitizedPhoneNumber';

interface Props {
  modal: TMainReduxStates['modalSetPhoneNumberAs'];
  client: TMainReduxStates['customerReducer'];
  admin: TMainReduxStates['reducerAdmin'];
}

const ModalSetPhoneNumberAs = (props: Props) => {
  const onTypeChange = (e: any, d: DropdownProps) => {
    mainStore.dispatch(modalSetPhoneNumberAsSlice.actions.changeType(d.value as any));
  };

  const onClose = () => {
    mainStore.dispatch(modalSetPhoneNumberAsSlice.actions.close());
  };

  const onSubmit = async () => {
    const { dispatch } = mainStore;
    const clientRef = props.client.ref;
    if (!clientRef || !props.modal.setPhoneNumberAs) return;

    dispatch(modalSetPhoneNumberAsSlice.actions.setSubmitState(true));

    try {
      await mainApiServices.setPhoneNumberAs({
        phoneNumber: props.modal.phoneNumber,
        clientRef: clientRef,
        type: props.modal.setPhoneNumberAs,
      });
      dispatch(modalSetPhoneNumberAsSlice.actions.setSubmitSuccess());
      dispatch(
        fetchCustomerThunk({
          clientDocRef: clientRef,
        }) as any,
      );
    } catch (e) {
      dispatch(
        modalSetPhoneNumberAsSlice.actions.setSubmitError({
          errorMessage: 'Gagal update nomor telepon',
        }),
      );
    }
  };

  return (
    <Modal
      open={props.modal.open}
      size={'mini'}
      onClose={onClose}
    >
      <Modal.Header>Gunakan Nomor Telepon</Modal.Header>
      <Modal.Content>
        <div className={'mb-2'}>
          <div>Nama:</div>
          <div className={'font-bold'}>{props.client.client?.profile.name}</div>
        </div>
        <div className={'mb-2'}>
          <div>Nomor Telepon:</div>
          <div className={'font-bold'}>
            {props.admin.admin?.admin_rank !== 1
              ? maskSanitizedPhoneNumber(props.modal.phoneNumber)
              : props.modal.phoneNumber}
          </div>
        </div>
        <Form>
          <Form.Field>
            <label>Gunakan Nomor Telepon Sebagai</label>
            <Select
              options={[
                {
                  text: 'Pemilik',
                  value: 'owner',
                },
                {
                  text: 'Penjamin',
                  value: 'guarantor',
                },
                {
                  text: 'Pemesan',
                  value: 'orderMaker',
                },
              ]}
              onChange={onTypeChange}
              value={props.modal.setPhoneNumberAs || ''}
            />
          </Form.Field>
        </Form>

        {props.modal.errorMessages && (
          <Message
            size={'small'}
            error={true}
          >
            <p>{props.modal.errorMessages}</p>
          </Message>
        )}

        {props.modal.submitResults === 'success' && (
          <Message
            size={'mini'}
            success={true}
          >
            <p>Berhasil Update Nomor Telepon</p>
          </Message>
        )}
      </Modal.Content>
      <Modal.Actions>
        <Button
          color={'blue'}
          onClick={onSubmit}
          disabled={props.modal.submitting}
          loading={props.modal.submitting}
        >
          Update
        </Button>
        <Button
          color="black"
          onClick={onClose}
        >
          Tutup
        </Button>
      </Modal.Actions>
    </Modal>
  );
};

const mapStateToProps = (states: TMainReduxStates) => {
  return {
    modal: states.modalSetPhoneNumberAs,
    client: states.customerReducer,
    admin: states.reducerAdmin,
  };
};

export default connect(mapStateToProps)(ModalSetPhoneNumberAs);

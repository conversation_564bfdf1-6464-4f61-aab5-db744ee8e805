import React, { Component } from 'react';
import { Button, Form, Input, Message, Modal } from 'semantic-ui-react';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { connect } from 'react-redux';
import { mainStore } from '../../redux/reducers';
import modalUpdatePhoneNumberSlice from '../../redux/modal-update-phone-number/modalUpdatePhoneNumber.slice';
import { mainApiServices } from '../../services/MainApiServices';
import { fetchCustomerThunk } from '../../redux/customerInfo/customerInfoSlice';

interface Props {
  modal: TMainReduxStates['modalUpdatePhoneNumber'];
  client: TMainReduxStates['customerReducer'];
  admin: TMainReduxStates['reducerAdmin'];
}

class ModalUpdatePhoneNumber extends Component<Props> {
  onClose = () => {
    mainStore.dispatch(modalUpdatePhoneNumberSlice.actions.close());
  };

  onSubmit = async () => {
    mainStore.dispatch(modalUpdatePhoneNumberSlice.actions.setUpdating(true));

    const { modal, client } = this.props;
    if (!client.ref) return;

    let isSuccess = false;

    try {
      await mainApiServices.setPhoneNumber({
        phoneNumberGuarantor: modal.phoneNumberGuarantor,
        phoneNumberOrderMaker: modal.phoneNumberOrderMaker,
        phoneNumberOwner: modal.phoneNumberOwner,
        clientRef: client.ref,
      });
      isSuccess = true;
    } catch (error) {
      mainStore.dispatch(
        modalUpdatePhoneNumberSlice.actions.setSubmitResult({
          submitResults: 'error',
          errorMessages: 'Terjadi kesalahan: Periksa input anda.',
        }),
      );
    }

    mainStore.dispatch(
      modalUpdatePhoneNumberSlice.actions.setSubmitResult({
        submitResults: 'success',
      }),
    );

    mainStore.dispatch(modalUpdatePhoneNumberSlice.actions.setUpdating(false));

    if (isSuccess) {
      mainStore.dispatch(
        fetchCustomerThunk({
          clientDocRef: this.props.client.ref!,
        }) as any,
      );
    }
  };

  render() {
    const { modal, admin } = this.props;
    return (
      <Modal
        open={modal.open}
        size={'small'}
        onClose={this.onClose}
      >
        <Modal.Header>Update Nomor Telepon</Modal.Header>
        <Modal.Content>
          <Message size={'small'}>
            <p>
              Harap masukan nomor telepon dengan diawali 62.
              <br />
              Contoh: 628576379899
            </p>
          </Message>
          {admin.admin!.admin_rank > 1 && (
            <Message
              size={'small'}
              info={true}
            >
              <p>Nomor telepon disembunyikan</p>
            </Message>
          )}
          <Form>
            <Form.Field>
              <label>Nomor Telepon Pemilik</label>
              <Input
                value={admin.admin?.admin_rank === 1 ? modal.phoneNumberOwner : undefined}
                type="number"
                onChange={(event, data) => {
                  mainStore.dispatch(
                    modalUpdatePhoneNumberSlice.actions.setPhoneNumberOwner(data.value),
                  );
                }}
              />
            </Form.Field>
            <Form.Field>
              <label>Nomor Telepon Pemesan</label>
              <Input
                value={admin.admin?.admin_rank === 1 ? modal.phoneNumberOrderMaker : undefined}
                type="number"
                onChange={(event, data) => {
                  mainStore.dispatch(
                    modalUpdatePhoneNumberSlice.actions.setPhoneNumberOrderMaker(data.value),
                  );
                }}
              />
            </Form.Field>
            <Form.Field>
              <label>Nomor Telepon Penjamin</label>
              <Input
                value={admin.admin?.admin_rank === 1 ? modal.phoneNumberGuarantor : undefined}
                type="number"
                onChange={(event, data) => {
                  mainStore.dispatch(
                    modalUpdatePhoneNumberSlice.actions.setPhoneNumberGuarantor(data.value),
                  );
                }}
              />
            </Form.Field>
          </Form>
          {modal.submitResults === 'success' && (
            <Message positive={true}>
              <p>Berhasil memperbarui nomor telepon</p>
            </Message>
          )}
          {modal.submitResults === 'error' && (
            <Message negative={true}>
              <p>{modal.errorMessages}</p>
            </Message>
          )}
        </Modal.Content>
        <Modal.Actions>
          <Button
            loading={modal.updating}
            color={'blue'}
            onClick={this.onSubmit}
          >
            Update
          </Button>
          <Button
            color="black"
            onClick={this.onClose}
          >
            Tutup
          </Button>
        </Modal.Actions>
      </Modal>
    );
  }
}

const mapStateToProps = (s: TMainReduxStates) => {
  return {
    modal: s.modalUpdatePhoneNumber,
    client: s.customerReducer,
    admin: s.reducerAdmin,
  };
};

export default connect(mapStateToProps)(ModalUpdatePhoneNumber);

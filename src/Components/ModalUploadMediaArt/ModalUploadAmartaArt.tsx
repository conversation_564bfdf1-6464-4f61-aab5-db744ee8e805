import React, { useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Button, Form, Icon, Modal } from 'semantic-ui-react';
import {
  closeModal,
  fetchFeed,
  resetErrorState,
  setSelectedArtCategory,
  setSelectedBrand,
} from '../../redux/modal-amarta-art/modalAmartaArt.slice';
import ArtCategorySelect from './components/ArtCategorySelect';
import BrandSelect from './components/BrandSelect';
import ArtImageGrid from './components/ArtImageGrid';
import { TMainReduxStates } from '../../redux/types/redux-types';

const ModalUploadMediaArt: React.FC = () => {
  const dispatch = useDispatch();
  const { isOpen, selectedArtCategory, selectedBrand, feedData, isLoading, error } = useSelector(
    (state: TMainReduxStates) => state.modalAmartaArt,
  );

  const fetch = useCallback(() => {
    if (selectedArtCategory && selectedBrand) {
      dispatch(resetErrorState());
      dispatch(
        fetchFeed({
          art_category: selectedArtCategory,
          brand_uid: selectedBrand.uuid,
        }) as any,
      );
    }
  }, [dispatch, selectedArtCategory, selectedBrand]);

  useEffect(() => {
    fetch();
  }, [fetch]);

  const handleClose = () => {
    dispatch(closeModal());
  };

  const handleArtCategoryChange = (value: string) => {
    dispatch(setSelectedArtCategory(value as 'feed' | 'feed_promo'));
  };

  const handleBrandChange = (brand: { name: string; uuid: string }) => {
    dispatch(setSelectedBrand(brand));
  };

  const handleSubmit = () => {
    fetch();
  };

  return (
    <Modal
      open={isOpen}
      onClose={handleClose}
      size="large"
      style={{ maxWidth: '1200px' }}
    >
      <Modal.Header
        style={{
          backgroundColor: '#f8f9fa',
          borderBottom: '1px solid #e9ecef',
          padding: '1.5rem',
          fontSize: '1.25rem',
          fontWeight: 600,
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem',
        }}
      >
        <Icon name="images" />
        Upload Media Art
      </Modal.Header>
      <Modal.Content style={{ padding: '1.5rem', backgroundColor: '#fff' }}>
        <Form size="large">
          <Form.Group
            widths="equal"
            style={{ marginBottom: '2rem' }}
          >
            <ArtCategorySelect
              value={selectedArtCategory || ''}
              onChange={handleArtCategoryChange}
            />
            <BrandSelect
              value={selectedBrand}
              onChange={handleBrandChange}
              loading={isLoading && !feedData.length}
            />
            <Form.Field>
              <Button
                primary
                icon
                labelPosition="right"
                onClick={handleSubmit}
                disabled={!selectedArtCategory || !selectedBrand}
                loading={isLoading}
                style={{
                  marginTop: '22px',
                  padding: '0.8rem 1.5rem',
                  borderRadius: '8px',
                  transition: 'all 0.2s ease',
                }}
                className="hover:shadow-md"
              >
                Fetch Art Items
                <Icon name="search" />
              </Button>
            </Form.Field>
          </Form.Group>
        </Form>

        {isLoading ? (
          <div
            style={{
              textAlign: 'center',
              padding: '3rem',
              backgroundColor: '#f8f9fa',
              borderRadius: '12px',
            }}
          >
            <Icon
              name="spinner"
              loading
              size="large"
            />
            <p style={{ marginTop: '1rem', color: '#6c757d' }}>Loading art items...</p>
          </div>
        ) : error ? (
          <div
            style={{
              textAlign: 'center',
              padding: '2rem',
              backgroundColor: '#fff3f3',
              borderRadius: '12px',
              border: '1px solid #ffcdd2',
            }}
          >
            <Icon
              name="warning circle"
              color="red"
              size="large"
            />
            <p style={{ marginTop: '1rem', color: '#d32f2f' }}>{error}</p>
          </div>
        ) : feedData.length === 0 ? (
          <div
            style={{
              textAlign: 'center',
              padding: '3rem',
              backgroundColor: '#f8f9fa',
              borderRadius: '12px',
            }}
          >
            <Icon
              name="image outline"
              size="large"
              color="grey"
            />
            <p style={{ marginTop: '1rem', color: '#6c757d' }}>
              No art items found for the selected category and brand.
            </p>
          </div>
        ) : (
          <div style={{ marginTop: '1rem' }}>
            <ArtImageGrid items={feedData} />
          </div>
        )}
      </Modal.Content>
      <Modal.Actions
        style={{
          padding: '1rem 1.5rem',
          backgroundColor: '#f8f9fa',
          borderTop: '1px solid #e9ecef',
        }}
      >
        <Button
          onClick={handleClose}
          style={{
            padding: '0.8rem 1.5rem',
            borderRadius: '8px',
            backgroundColor: '#fff',
            color: '#495057',
            border: '1px solid #e9ecef',
            transition: 'all 0.2s ease',
          }}
        >
          <Icon name="close" />
          Close
        </Button>
      </Modal.Actions>
    </Modal>
  );
};

export default ModalUploadMediaArt;

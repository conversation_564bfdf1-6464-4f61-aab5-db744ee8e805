import React from 'react';
import { Form, Select } from 'semantic-ui-react';

interface ArtCategorySelectProps {
  value: string;
  onChange: (value: string) => void;
}

const artCategoryOptions = [
  { key: 'feed', value: 'feed', text: 'Feed' },
  { key: 'feed_promo', value: 'feed_promo', text: 'Feed Promo' },
  { key: 'video_feed', value: 'video_feed', text: 'Video Feed' },
  {
    key: 'video_feed_promo',
    value: 'video_feed_promo',
    text: 'Video Feed Promo',
  },
];

const ArtCategorySelect: React.FC<ArtCategorySelectProps> = ({ value, onChange }) => {
  return (
    <Form.Field
      control={Select}
      label="Art Category"
      options={artCategoryOptions}
      placeholder="Select art category"
      value={value || ''}
      onChange={(event: React.SyntheticEvent<HTMLElement>, data: { value: string }) =>
        onChange(data.value)
      }
    />
  );
};

export default ArtCategorySelect;

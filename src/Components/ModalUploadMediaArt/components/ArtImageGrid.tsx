import React from 'react';
import { Grid } from 'semantic-ui-react';
import { ArtItem } from '../../../services/types/amartaArt.services.types';
import ArtImageItem from './ArtImageItem';

interface ArtImageGridProps {
  items: ArtItem[];
}

const ArtImageGrid: React.FC<ArtImageGridProps> = ({ items }) => {
  return (
    <Grid
      columns={3}
      doubling
      stackable
      style={{ margin: 0 }}
    >
      {items.map((item: ArtItem) => (
        <Grid.Column
          key={item.art_code}
          style={{ marginBottom: '2rem', padding: '0.75rem' }}
        >
          <ArtImageItem item={item} />
        </Grid.Column>
      ))}
    </Grid>
  );
};

export default ArtImageGrid;

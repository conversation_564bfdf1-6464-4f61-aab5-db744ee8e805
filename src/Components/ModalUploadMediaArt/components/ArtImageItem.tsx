import React, { useState } from 'react';
import { Dropdown, Icon, Image } from 'semantic-ui-react';
import {
  ArtItem,
  GetMediaByArtCodeResponse,
} from '../../../services/types/amartaArt.services.types';
import ReactPlayer from 'react-player';
import { useDispatch, useSelector } from 'react-redux';
import {
  closeModal,
  fetchFeed,
  resetErrorState,
} from '../../../redux/modal-amarta-art/modalAmartaArt.slice';
import { amartaArtServices } from '../../../services/amartaArt.services';
import moment from 'moment';
import chatTextInputSlice from '../../../redux/chat-text-input/chatTextInputSlice';
import errorDialog from '../../callableDialog/errorDialog';
import ButtonCopy from '../../Template/ButtonCopy';
import successDialog from '../../callableDialog/successDialog';
import { TMainReduxStates } from '../../../redux/types/redux-types';

interface ArtImageItemProps {
  item: ArtItem;
}

const ArtImageItem: React.FC<ArtImageItemProps> = ({ item }) => {
  const dispatch = useDispatch();
  const project = useSelector((state: TMainReduxStates) => state.reducerProject.project);
  const [isLoading, setIsLoading] = useState(false);

  const { selectedArtCategory, selectedBrand } = useSelector(
    (state: TMainReduxStates) => state.modalAmartaArt,
  );

  const fetch = () => {
    if (!selectedArtCategory || !selectedBrand) return;
    dispatch(resetErrorState());
    dispatch(
      fetchFeed({
        art_category: selectedArtCategory,
        brand_uid: selectedBrand.uuid,
      }) as any,
    );
  };

  const handleClick = async () => {
    setIsLoading(true);
    try {
      const response = await amartaArtServices.getMediaByArtCode(item.art_code);
      const imageDetails = response.data as GetMediaByArtCodeResponse['data'];

      const mediaId = imageDetails?.meta_data?.media_id;
      const isValid = mediaId && !isExpired(imageDetails) && !isProjectNotMatch(imageDetails);

      if (isValid) {
        dispatch(
          chatTextInputSlice.actions.setFile({
            source: 'whatsappMediaId',
            whatsappMediaId: {
              id: mediaId,
              previewUrl: item.art_url,
              type: item.art_url.match(/\.(mp4|mov|avi)$/i) ? 'video' : 'image',
            },
          }),
        );
        if (item.art_caption) {
          dispatch(chatTextInputSlice.actions.setHtmlContentEditable(item.art_caption));
        }
        dispatch(closeModal());
      } else {
        let errorMessage = '';
        if (!mediaId) {
          errorMessage = 'Media tidak memiliki ID yang valid';
        } else if (isExpired(imageDetails)) {
          errorMessage = 'Media telah melewati tanggal expired';
        } else if (isProjectNotMatch(imageDetails)) {
          errorMessage = 'Media tidak sesuai dengan project saat ini';
        }

        errorDialog({
          content: errorMessage,
          title: 'Gagal memuat detail media',
          cancelButton: false,
        });
      }
    } catch (error) {
      console.log(error);
      errorDialog({
        content: 'Gagal memuat detail media',
        title: 'Gagal memuat detail media',
      });
    } finally {
      fetch();
      setIsLoading(false);
    }
  };

  const isExpired = (imageDetails: GetMediaByArtCodeResponse['data']) => {
    const expiredDate = imageDetails?.meta_data?.media_expired_date;
    if (!expiredDate) return false;
    return moment().isAfter(moment(expiredDate));
  };

  const isProjectNotMatch = (imageDetails: GetMediaByArtCodeResponse['data']) => {
    return project?.ref?.id !== imageDetails?.meta_data?.project_id;
  };

  const handleCopyClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Menghentikan event propagation
  };

  const handleExtendArtValidity = async (e: React.MouseEvent) => {
    e.stopPropagation(); // Mencegah trigger handleClick
    if (!project) return;
    setIsLoading(true);

    try {
      await amartaArtServices.extendMediaValidity({
        type: 'update_meta_data',
        art_class: item.art_class,
        art_code: item.art_code,
        project_id: project.ref?.id,
        url_file: item.art_url,
        provider: project.provider,
        project_name: project.legal_name,
        project_phone_number: project?.phone_number,
        expired: moment().add(14, 'days').format('YYYY-MM-DD'),
      });

      await successDialog({
        content: 'Berhasil memperpanjang masa aktif art',
        title: 'Berhasil memperpanjang masa aktif art',
        cancelButton: false,
      });
    } catch {
      await errorDialog({
        content: 'Gagal memperpanjang masa aktif art',
        title: 'Gagal memperpanjang masa aktif art',
        cancelButton: false,
      });
    } finally {
      fetch();
      setIsLoading(false);
    }
  };

  const handleDropdownClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Mencegah trigger handleClick
  };

  return (
    <div
      style={{
        position: 'relative',
        transition: 'all 0.3s ease',
        cursor: isLoading ? 'wait' : 'pointer',
        borderRadius: '12px',
        overflow: 'hidden',
        aspectRatio: '1',
        backgroundColor: '#f8f9fa',
        boxShadow: '0 2px 8px rgba(0,0,0,0.08)',
        opacity: isLoading ? 0.7 : 1,
      }}
      className="hover:shadow-xl hover:scale-[1.02]"
      onClick={handleClick}
    >
      {item.art_url.match(/\.(mp4|mov|avi)$/i) ? (
        <div
          style={{
            position: 'relative',
            width: '100%',
            height: '100%',
          }}
        >
          <ReactPlayer
            url={item.art_url}
            width="100%"
            height="100%"
            muted
            loop
            playing={false}
            playIcon={<></>}
            style={{ objectFit: 'cover' }}
          />
          <div
            style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              backgroundColor: 'rgba(255,255,255,0.9)',
              borderRadius: '50%',
              width: '48px',
              height: '48px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Icon
              name="play circle"
              size="large"
              color="grey"
            />
          </div>
        </div>
      ) : (
        <Image
          src={item.art_url}
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'cover',
          }}
        />
      )}
      <div
        style={{
          position: 'absolute',
          top: '10px',
          right: '10px',
          zIndex: 10,
        }}
        onClick={handleDropdownClick}
      >
        <Dropdown
          icon="ellipsis vertical"
          floating
          direction="left"
          className="icon"
          button
          style={{
            backgroundColor: 'rgba(255,255,255,0.8)',
            borderRadius: '50%',
            padding: '6px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
          }}
        >
          <Dropdown.Menu>
            <Dropdown.Item
              text="Perpanjang Masa Aktif Art"
              icon="calendar plus"
              onClick={handleExtendArtValidity}
            />
          </Dropdown.Menu>
        </Dropdown>
      </div>
      <div
        style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          padding: '16px',
          background:
            'linear-gradient(to top, rgba(0,0,0,0.9) 0%, rgba(0,0,0,0.6) 70%, rgba(0,0,0,0) 100%)',
          backdropFilter: 'blur(4px)',
        }}
      >
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'flex-start',
            color: '#fff',
          }}
        >
          <div style={{ flex: 1 }}>
            <div
              style={{
                fontSize: '1em',
                fontWeight: 600,
                marginBottom: '4px',
                textShadow: '0 1px 2px rgba(0,0,0,0.3)',
              }}
            >
              {item.art_class}
            </div>
            <div
              style={{
                fontSize: '0.85em',
                opacity: 0.9,
                textShadow: '0 1px 2px rgba(0,0,0,0.3)',
                display: 'flex',
                alignItems: 'center',
                gap: '6px',
                marginBottom: '4px',
              }}
            >
              {item.art_code}
              <div onClick={handleCopyClick}>
                <ButtonCopy
                  textToCopy={item.art_code}
                  size="mini"
                />
              </div>
            </div>
            {item.art_caption && (
              <div
                style={{
                  fontSize: '0.85em',
                  opacity: 0.9,
                  textShadow: '0 1px 2px rgba(0,0,0,0.3)',
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  maxWidth: '90%',
                }}
              >
                {item.art_caption}
              </div>
            )}
          </div>
          <div
            style={{
              backgroundColor: 'rgba(255,255,255,0.95)',
              padding: '6px 12px',
              borderRadius: '20px',
              fontSize: '0.8em',
              fontWeight: 500,
              color: '#333',
              display: 'flex',
              alignItems: 'center',
              gap: '6px',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
              marginLeft: '12px',
              flexShrink: 0,
            }}
          >
            <Icon name={item.art_url.match(/\.(mp4|mov|avi)$/i) ? 'video' : 'image'} />
            {item.art_url.match(/\.(mp4|mov|avi)$/i) ? 'Video' : 'Image'}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ArtImageItem;

import React from 'react';
import { Form, Select } from 'semantic-ui-react';
import { IBrandModel } from '../../../services/types/autotrimitra-services-types';
import SelectVehicleFromAutoTrimitra from '../../SelectVehicle/SelectVehicleFromAutoTrimitra';

interface BrandSelectProps {
  value?: { name: string; uuid: string } | null;
  onChange: (brand: { name: string; uuid: string }) => void;
  loading?: boolean;
}

const BrandSelect: React.FC<BrandSelectProps> = ({ value, onChange }) => {
  const handleChange = (brand: { name: string; uuid: string }) => {
    onChange(brand);
  };

  return (
    // <Form.Field
    //   control={Select}
    // 	label="Brand"
    // 	options={brandOptions}
    // 	placeholder="Select brand"
    // 	value={value || ""}
    // 	onChange={(_: unknown, data: { value: string }) => handleChange(data.value)}
    // 	loading={loading}
    // 	search
    // />
    <SelectVehicleFromAutoTrimitra
      limit={1}
      brand={value || undefined}
      onBrandChange={(brand) => handleChange(brand)}
    />
  );
};

export default BrandSelect;

import React, { Component } from 'react';
import { Button, Form, Input, Message, Segment } from 'semantic-ui-react';
import logisticService from '../../services/logistic/logisticService';
import ModalCekBbn from '../ModalCekBbn/ModalCekBbn';
import { mainStore } from '../../redux/reducers';
import modalCekBbn from '../ModalCekBbn/ModalCekBbn';
import modalCheckBbnSlice from '../../redux/modal-bbn/modalCheckBbnSlice';

interface Props {}

interface States {
  engineNumber: string;
  loading: boolean;
  errorMessages: string;
}

class CheckBBN extends Component<Props, States> {
  constructor(props: Props) {
    super(props);
    this.state = {
      engineNumber: '',
      loading: false,
      errorMessages: '',
    };
  }

  setEngineNumber = (engineNumber: string) => {
    this.setState({
      engineNumber: engineNumber,
    });
  };

  onSubmit = async () => {
    if (!this.state.engineNumber) return;

    this.setState({
      loading: true,
      errorMessages: '',
    });

    try {
      const get = await logisticService.getBbnByEngineNumber(this.state.engineNumber);

      if (get.success.data.length > 0) {
        const bbnData = get.success.data[0];
        mainStore.dispatch(modalCheckBbnSlice.actions.open(bbnData));
        this.setState({
          loading: false,
        });
      } else {
        this.setState({
          loading: false,
          errorMessages: 'Data BBN tidak dapat ditemukan',
        });
      }
    } catch (error) {
      this.setState({
        loading: false,
        errorMessages: 'Data BBN tidak dapat ditemukan',
      });
    }
  };

  render() {
    return (
      <Segment basic={true}>
        <Form>
          <Form.Field>
            <label>Nomor Mesin</label>
            <Input
              value={this.state.engineNumber}
              onChange={(v) => this.setEngineNumber(v.target.value)}
              placeholder={'JM91E 1474225'}
            />
          </Form.Field>
          <Form.Field>
            <Button
              onClick={this.onSubmit}
              loading={this.state.loading}
              disabled={this.state.loading}
              primary={true}
            >
              Submit
            </Button>
          </Form.Field>
        </Form>
        {this.state.errorMessages && <Message error={true}>{this.state.errorMessages}</Message>}
        <ModalCekBbn />
      </Segment>
    );
  }
}

export default CheckBBN;

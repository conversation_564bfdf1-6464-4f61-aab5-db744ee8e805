import React, { Component } from 'react';
import { Accordion } from 'semantic-ui-react';
import { ClientInformationPanel } from './types/client-information-panel';
import { AccordionTitleProps } from 'semantic-ui-react/dist/commonjs/modules/Accordion/AccordionTitle';
import ProfileInformation from './ProfileInformation';
import Settings from '../ClientInformation/RoomSettings';
import PurchaseVehicleInformation from './PurchaseVehicleInformation';
import VehicleInformation from '../ClientInformation/VehicleInformation';
import Leads from './Leads';
import CreditScheme from '../ClientInformation/CreditScheme';
import OfferCodeHistories from './OfferCodeHistories';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { connect } from 'react-redux';
import ShipmentCost from '../ShipmentCost/ShipmentCost';
import Loan from './Loan/Loan';
import OwnedVehicle from './OwnedVehicle/OwnedVehicle';
import CheckBBN from './CheckBBN';
import TrimobiAd from './CreateAdTrimobi/TrimobiAd';

// Add react-icons imports
import { HiChevronRight } from 'react-icons/hi';
import {
  MdPerson,
  MdSettings,
  MdCreditCard,
  MdAttachMoney,
  MdHistory,
  MdAssignment,
  MdLocalShipping,
  MdDescription,
  MdCampaign,
  MdGroup,
} from 'react-icons/md';
import { IoCarSportOutline, IoCarOutline } from 'react-icons/io5';

interface ClientInformationWrapperStates {
  activePanel: ClientInformationPanel | null;
}

interface ClientInformationWrapperProps {
  admin: TMainReduxStates['reducerAdmin'];
}

class ClientInformationWrapper extends Component<
  ClientInformationWrapperProps,
  ClientInformationWrapperStates
> {
  constructor(props: ClientInformationWrapperProps) {
    super(props);
    this.state = {
      activePanel: null,
    };
  }

  panelTitleOnClick = (event: React.MouseEvent<HTMLDivElement>, data: AccordionTitleProps) => {
    this.setState({
      activePanel: data.index as ClientInformationPanel,
    });
  };

  renderAccordionTitle = (title: string, panel: ClientInformationPanel, icon: React.ReactNode) => {
    const isActive = this.state.activePanel === panel;
    return (
      <Accordion.Title
        active={isActive}
        index={panel}
        onClick={this.panelTitleOnClick}
        className={`
                    group
                    !px-6 !py-3.5 !border-b !border-gray-100
                    hover:!bg-gray-50/80 transition-all duration-200
                    ${isActive ? '!bg-blue-50/70 shadow-sm' : '!bg-white'}
                `}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div
              className={`
                            p-2 rounded-lg transition-all duration-200
                            ${
                              isActive
                                ? 'bg-blue-100/80 text-blue-600'
                                : 'bg-gray-100/80 text-gray-500 group-hover:bg-gray-200/80 group-hover:text-gray-600'
                            }
                        `}
            >
              {icon}
            </div>
            <span
              className={`
                            font-medium transition-colors duration-200
                            ${isActive ? 'text-blue-700' : 'text-gray-700 group-hover:text-gray-900'}
                        `}
            >
              {title}
            </span>
          </div>
          <HiChevronRight
            className={`
                            transition-all duration-200 text-lg
                            ${
                              isActive
                                ? 'rotate-90 text-blue-600'
                                : 'text-gray-400 group-hover:text-gray-500'
                            }
                        `}
          />
        </div>
      </Accordion.Title>
    );
  };

  renderAccordionContent = (panel: ClientInformationPanel, content: React.ReactNode) => (
    <Accordion.Content
      active={this.state.activePanel === panel}
      className={`
                !px-6 !py-4 !bg-white !border-b !border-gray-100
                ${this.state.activePanel === panel ? 'shadow-sm' : ''}
            `}
    >
      <div className="bg-gray-50/50 -mx-6 -mb-4 px-6 py-4">
        {this.state.activePanel === panel && content}
      </div>
    </Accordion.Content>
  );

  render() {
    return (
      <div className="h-full w-full flex flex-col">
        <div className="flex-1 overflow-y-auto">
          <div className="bg-white shadow-sm rounded-lg">
            <Accordion className="!border-none">
              {this.renderAccordionTitle(
                'Profil',
                ClientInformationPanel.PROFIL,
                <MdPerson className="text-xl" />,
              )}
              {this.renderAccordionContent(ClientInformationPanel.PROFIL, <ProfileInformation />)}

              {this.props.admin.admin?.level === 'owner' && (
                <>
                  {this.renderAccordionTitle(
                    'Pengaturan Room',
                    ClientInformationPanel.ROOM_SETTINGS,
                    <MdSettings className="text-xl" />,
                  )}
                  {this.renderAccordionContent(ClientInformationPanel.ROOM_SETTINGS, <Settings />)}
                </>
              )}

              {this.renderAccordionTitle(
                'Kendaraan yang Diinginkan',
                ClientInformationPanel.VEHICLE_DREAM,
                <IoCarSportOutline className="text-xl" />,
              )}
              {this.renderAccordionContent(
                ClientInformationPanel.VEHICLE_DREAM,
                <VehicleInformation />,
              )}

              {this.renderAccordionTitle(
                'Kendaraan yang Dimiliki',
                ClientInformationPanel.OWNED_VEHICLE,
                <IoCarOutline className="text-xl" />,
              )}
              {this.renderAccordionContent(ClientInformationPanel.OWNED_VEHICLE, <OwnedVehicle />)}

              {this.renderAccordionTitle(
                'Pengajuan Kredit Kendaraan',
                ClientInformationPanel.CREDIT_SCHEME,
                <MdCreditCard className="text-xl" />,
              )}
              {this.renderAccordionContent(ClientInformationPanel.CREDIT_SCHEME, <CreditScheme />)}

              {this.renderAccordionTitle(
                'Pembelian Tunai Kendaraan',
                ClientInformationPanel.PURCHASE_VEHICLE,
                <MdAttachMoney className="text-xl" />,
              )}
              {this.renderAccordionContent(
                ClientInformationPanel.PURCHASE_VEHICLE,
                <PurchaseVehicleInformation />,
              )}

              {this.renderAccordionTitle(
                'Riwayat Kode Offer',
                ClientInformationPanel.ORDER_HISTORIES,
                <MdHistory className="text-xl" />,
              )}
              {this.renderAccordionContent(
                ClientInformationPanel.ORDER_HISTORIES,
                <OfferCodeHistories />,
              )}

              {this.renderAccordionTitle(
                'Pengajuan Dana BPKB',
                ClientInformationPanel.LOAN,
                <MdAssignment className="text-xl" />,
              )}
              {this.renderAccordionContent(ClientInformationPanel.LOAN, <Loan />)}

              {this.renderAccordionTitle(
                'Biaya Logistik',
                ClientInformationPanel.SHIPMENT_COST,
                <MdLocalShipping className="text-xl" />,
              )}
              {this.renderAccordionContent(ClientInformationPanel.SHIPMENT_COST, <ShipmentCost />)}

              {this.renderAccordionTitle(
                'Leads',
                ClientInformationPanel.LEADS,
                <MdGroup className="text-xl" />,
              )}
              {this.renderAccordionContent(ClientInformationPanel.LEADS, <Leads />)}

              {this.renderAccordionTitle(
                'Cek BBN 1, STNK, BPKB',
                ClientInformationPanel.CHECK_BBN,
                <MdDescription className="text-xl" />,
              )}
              {this.renderAccordionContent(ClientInformationPanel.CHECK_BBN, <CheckBBN />)}

              {this.renderAccordionTitle(
                'Iklan Trimobi',
                ClientInformationPanel.CREATE_AD_TRIMOBI,
                <MdCampaign className="text-xl" />,
              )}
              {this.renderAccordionContent(ClientInformationPanel.CREATE_AD_TRIMOBI, <TrimobiAd />)}
            </Accordion>
          </div>
        </div>
      </div>
    );
  }
}

const mapStateToProps = (states: TMainReduxStates) => ({
  admin: states.reducerAdmin,
});

export default connect(mapStateToProps)(ClientInformationWrapper);

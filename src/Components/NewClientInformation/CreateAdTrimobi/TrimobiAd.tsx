import { useDispatch } from 'react-redux';
import { Button } from 'semantic-ui-react';
import { modalCreateAdSlice } from '../../../redux/modal-trimobi/modalCreateAd.slice';
import modalSelectPlanSlice from '../../../redux/modal-trimobi/modalSelectPlan.slice';
import modalUpgradePlanSlice from '../../../redux/modal-trimobi/modalUpgradePlan.slice';

const TrimobiAd = () => {
  const dispatch = useDispatch();

  return (
    <div className="flex flex-col space-y-4 p-4 bg-white rounded-lg shadow-md">
      <Button
        onClick={() => dispatch(modalSelectPlanSlice.actions.openModal())}
        icon="shopping cart"
        content="Beli Paket Iklan"
        labelPosition="right"
      />

      <Button
        onClick={() => dispatch(modalUpgradePlanSlice.actions.openModal())}
        icon="sync alternate"
        content="Upgrade Paket"
        labelPosition="right"
      />

      <Button
        onClick={() => dispatch(modalCreateAdSlice.actions.setIsOpen(true))}
        icon="add"
        content="Buat Iklan"
        labelPosition="right"
      />
    </div>
  );
};

export default TrimobiAd;

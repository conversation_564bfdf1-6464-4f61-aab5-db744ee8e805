import React, { Component } from 'react';
import { Divider, Form } from 'semantic-ui-react';
import { areas } from '../../config/areas';
import { VariantProduct } from '../../services/types/catalaogueTypes';
import { catalogueServices } from '../../services/catalogue/catalogueServices';
import currencyFormat from '../../helpers/currencyFormat';

export interface ICreditFundSimulationProps {}

export interface ICreditFundSimulationStates {
  licensePlate: string;
  area: null | (typeof areas)[0];
  model: string | null;
  variant: null | {
    name: string;
    code: string;
  };
  color: null | VariantProduct;
  year: string;
  fetchingUsedVehicle: boolean;

  liquefaction: number;
}

class CreditFundSimulation extends Component<
  ICreditFundSimulationProps,
  ICreditFundSimulationStates
> {
  constructor(props: ICreditFundSimulationProps) {
    super(props);

    this.state = {
      licensePlate: '',
      area: null,
      model: null,
      variant: null,
      color: null,
      year: '',
      fetchingUsedVehicle: false,

      liquefaction: 0,
    };
  }

  private liquefaction = {
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => {
      let value = 0;
      if (!isNaN(e.target.value as any)) {
        value = parseFloat(e.target.value);
      }

      this.setState({
        liquefaction: value,
      });
    },
  };

  private licensePlate = {
    onChange: (e: React.ChangeEvent<HTMLInputElement>) =>
      this.setState({
        licensePlate: e.target.value,
      }),
  };

  fetchUsedVehicle = async () => {
    if (!this.state.licensePlate) return;

    await this.setState({
      fetchingUsedVehicle: true,
    });
    let currentState = { ...this.state };
    try {
      const get = await catalogueServices.getUsedVehicle({
        area: 'bandung',
        licensePlate: this.state.licensePlate,
      });

      const vehicle = get?.data[0];

      if (vehicle) {
        currentState = {
          ...currentState,
          color: vehicle,
          variant: {
            name: vehicle.variant_name,
            code: vehicle.variant_code,
          },
          model: vehicle.model_name,
        };
      }

      currentState.fetchingUsedVehicle = false;
    } catch (e: any) {}
    await this.setState(currentState);
  };

  render() {
    return (
      <React.Fragment>
        <Form>
          <Form.Group>
            <Form.Field>
              <input
                placeholder={'Plat Nomor'}
                value={this.state.licensePlate}
                onChange={this.licensePlate.onChange}
              />
            </Form.Field>
            <Form.Button
              onClick={this.fetchUsedVehicle}
              loading={this.state.fetchingUsedVehicle}
            >
              Dapatkan Kendaraan
            </Form.Button>
          </Form.Group>
          {this.state.color && (
            <React.Fragment>
              <Form.Field>
                <label>Plat Nomor</label>
                <div>{this.state.color.license_plate}</div>
              </Form.Field>
              <Form.Field>
                <label>Model</label>
                <div>{this.state.model}</div>
              </Form.Field>
              <Form.Field>
                <label>Variant</label>
                <div>
                  {this.state.variant?.name} - {this.state.variant?.code}
                </div>
              </Form.Field>
              <Form.Field>
                <label>Warna</label>
                <div>
                  {this.state.color?.color_name.toUpperCase()} - {this.state.color?.color_code}
                </div>
              </Form.Field>
              <Form.Field>
                <label>Tahun Registrasi</label>
                <div>{this.state.color?.registration_year}</div>
              </Form.Field>
            </React.Fragment>
          )}

          <Form.Field>
            <label>OTR Kendaraan</label>
            <div>{currencyFormat(this.state.color?.price ?? 0)}</div>
          </Form.Field>

          <Form.Field>
            <label>Pencairan</label>
            <input
              type={'number'}
              placeholder={'Masukan Pencairan'}
              value={this.state.liquefaction <= 0 ? undefined : this.state.liquefaction}
              onChange={this.liquefaction.onChange}
            />
          </Form.Field>
          <Form.Field>
            <label>Komisi</label>
            <input />
          </Form.Field>

          <Form.Button
            content="Hitung"
            labelPosition="right"
            icon="calculator"
          />
        </Form>

        <Divider horizontal>Simulasi Cicilan</Divider>
      </React.Fragment>
    );
  }
}

export default CreditFundSimulation;

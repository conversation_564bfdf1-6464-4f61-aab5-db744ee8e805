import React, { Component } from 'react';
import { Divider, DropdownItemProps, Form, Select } from 'semantic-ui-react';
import { EVehicleCondition } from './types/vehicle_condition_types';
import { Model, VariantProduct } from '../../services/types/catalaogueTypes';
import { DropdownProps } from 'semantic-ui-react/dist/commonjs/modules/Dropdown/Dropdown';
import { catalogueServices } from '../../services/catalogue/catalogueServices';
import SelectVehicleFromCatalogue from '../SelectVehicle/SelectVehicleFromCatalogue';
import currencyFormat from '../../helpers/currencyFormat';
import SelectAreaFromCatalog from '../SelectCityGroup/SelectAreaFromCatalog';

export interface ICreditSimulationProps {}

export interface ICreditSimulationStates {
  vehicleCondition: EVehicleCondition | null;
  licensePlate: string;
  area: string;
  model: string | null;
  variant: null | {
    name: string;
    code: string;
  };
  color: null | VariantProduct;
  year: string;
  fetchingUsedVehicle: boolean;

  tenor: number;
  downPayment: number;
}

class CreditSimulation extends Component<ICreditSimulationProps, ICreditSimulationStates> {
  constructor(props: ICreditSimulationProps) {
    super(props);

    this.state = {
      licensePlate: '',
      vehicleCondition: EVehicleCondition.FIRST_HAND,
      area: '',
      model: null,
      variant: null,
      color: null,
      year: '',
      fetchingUsedVehicle: false,

      tenor: 0,
      downPayment: 0,
    };
  }

  private tenor = {
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => {
      let value = 0;
      if (!isNaN(e.target.value as any)) {
        value = parseFloat(e.target.value);
      }

      this.setState({
        tenor: value,
      });
    },
  };

  private countInstallment = () => {
    const price = this.state.color?.price ?? 0;
    const tenor = this.state.tenor;
    const downPayment = this.state.downPayment;
    if (tenor <= 0 || downPayment <= 0) return 0;
    const downPaymentToIDR = (price * downPayment) / 100;
    const restPrice = price - downPaymentToIDR;
    return restPrice / tenor;
  };

  private downPayment = {
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => {
      let value = 0;
      if (!isNaN(e.target.value as any)) {
        value = parseFloat(e.target.value);
      }

      this.setState({
        downPayment: value,
      });
    },
  };

  private licensePlate = {
    onChange: (e: React.ChangeEvent<HTMLInputElement>) =>
      this.setState({
        licensePlate: e.target.value,
      }),
  };

  private year = {
    onChange: (e: React.ChangeEvent<HTMLInputElement>) =>
      this.setState({
        year: e.target.value,
      }),
  };

  private vehicleCondition = {
    options: (): DropdownItemProps[] => {
      return [
        {
          value: EVehicleCondition.FIRST_HAND,
          text: 'Baru',
        },
        {
          value: EVehicleCondition.SECOND_HAND,
          text: 'Bekas',
        },
      ];
    },

    onChange: async (event: React.SyntheticEvent<HTMLElement>, data: DropdownProps) => {
      this.setState({
        vehicleCondition: data.value as EVehicleCondition,
        area: '',
        model: null,
        variant: null,
        color: null,
        year: '',
      });
    },
  };

  private area = {
    onChange: async (text: string, value: string) => {
      this.setState({
        area: text,
        model: null,
        variant: null,
        color: null,
      });
    },
  };

  private model = {
    onChange: (model: Model) => {
      this.setState({
        model: model.model_name.toLowerCase(),
        variant: null,
        color: null,
      });
    },
  };

  private variant = {
    onChange: (variant: VariantProduct) => {
      this.setState({
        variant: {
          name: variant.variant_name,
          code: variant.variant_code,
        },
        color: null,
      });
    },
  };

  private color = {
    onChange: (variantColor: VariantProduct) => {
      this.setState({
        color: variantColor,
      });
    },
  };

  fetchUsedVehicle = async () => {
    if (!this.state.licensePlate) return;

    this.setState({
      fetchingUsedVehicle: true,
    });
    let currentState = { ...this.state } as ICreditSimulationStates;
    try {
      const get = await catalogueServices.getUsedVehicle({
        area: 'bandung',
        licensePlate: this.state.licensePlate,
      });

      const vehicle = get?.data[0];

      if (vehicle) {
        currentState = {
          ...currentState,
          color: vehicle,
          variant: {
            name: vehicle.variant_name,
            code: vehicle.variant_code,
          },
          model: vehicle.model_name,
        };
      }

      currentState.fetchingUsedVehicle = false;
    } catch (e: any) {}
    this.setState(currentState);
  };

  render() {
    return (
      <React.Fragment>
        <Form>
          <Form.Field>
            <Select
              options={this.vehicleCondition.options()}
              placeholder={'Kondisi'}
              onChange={this.vehicleCondition.onChange}
              value={this.state.vehicleCondition as any}
            />
          </Form.Field>
        </Form>
        <Divider />

        <Form>
          {this.state.vehicleCondition === EVehicleCondition.SECOND_HAND && (
            <React.Fragment>
              <Form.Group>
                <Form.Field>
                  <input
                    placeholder={'Plat Nomor'}
                    value={this.state.licensePlate}
                    onChange={this.licensePlate.onChange}
                  />
                </Form.Field>
                <Form.Button
                  onClick={this.fetchUsedVehicle}
                  loading={this.state.fetchingUsedVehicle}
                >
                  Dapatkan Kendaraan
                </Form.Button>
              </Form.Group>
              {this.state.color && (
                <React.Fragment>
                  <Form.Field>
                    <label>Plat Nomor</label>
                    <div>{this.state.color.license_plate}</div>
                  </Form.Field>
                  <Form.Field>
                    <label>Model</label>
                    <div>{this.state.model}</div>
                  </Form.Field>
                  <Form.Field>
                    <label>Variant</label>
                    <div>
                      {this.state.variant?.name} - {this.state.variant?.code}
                    </div>
                  </Form.Field>
                  <Form.Field>
                    <label>Warna</label>
                    <div>
                      {this.state.color?.color_name.toUpperCase()} - {this.state.color?.color_code}
                    </div>
                  </Form.Field>
                  <Form.Field>
                    <label>Tahun Registrasi</label>
                    <div>{this.state.color?.registration_year}</div>
                  </Form.Field>
                </React.Fragment>
              )}
            </React.Fragment>
          )}

          {this.state.vehicleCondition === EVehicleCondition.FIRST_HAND && (
            <React.Fragment>
              <Form.Field>
                <SelectAreaFromCatalog
                  value={this.state.area}
                  onChange={this.area.onChange}
                />
              </Form.Field>
              {this.state.area && (
                <React.Fragment>
                  <SelectVehicleFromCatalogue
                    cityGroup={this.state.area}
                    variant={{
                      selected: this.state.variant ?? undefined,
                      onChange: this.variant.onChange,
                    }}
                    model={{
                      selected: this.state.model ?? undefined,
                      onChange: this.model.onChange,
                    }}
                    color={{
                      selected: this.state.color
                        ? {
                            code: this.state.color.color_code,
                            name: this.state.color.color_name,
                          }
                        : undefined,
                      onChange: this.color.onChange,
                    }}
                  />
                  <Form.Field>
                    <input
                      placeholder={'Tahun'}
                      value={this.state.year}
                      onChange={this.year.onChange}
                    />
                  </Form.Field>
                </React.Fragment>
              )}
            </React.Fragment>
          )}

          <Form.Field>
            <label>OTR Kendaraan</label>
            <div>{currencyFormat(this.state.color?.price ?? 0)}</div>
          </Form.Field>

          <Form.Field>
            <label>Berapa Persen Uang Muka</label>
            <input
              type={'number'}
              placeholder={'Masukan Uang Muka'}
              value={this.state.downPayment <= 0 ? undefined : this.state.downPayment}
              onChange={this.downPayment.onChange}
            />
          </Form.Field>
          <Form.Field>
            <label>Diskon</label>
            <input />
          </Form.Field>

          <Form.Button
            content="Hitung"
            labelPosition="right"
            icon="calculator"
          />
        </Form>

        <Divider horizontal>Simulasi Cicilan</Divider>
      </React.Fragment>
    );
  }
}

export default CreditSimulation;

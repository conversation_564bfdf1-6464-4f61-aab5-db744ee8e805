import React, { Component } from 'react';
import { Button, Message, Segment } from 'semantic-ui-react';
import { mainStore } from '../../redux/reducers';
import modalSendToFreeLeadsSlice from '../../redux/modal-send-to-free-leads/modalSendToFreeLeadsSlice';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { compose } from 'redux';
import { connect } from 'react-redux';
import moment from 'moment';
import modalAcquisitionAsLeadsSlice from '../../redux/modal-acquisition-as-leads/modalAcquisitionAsLeads.slice';
import { IoGift, IoPersonAdd, IoTime, IoCheckmarkCircle, IoCalendarOutline } from 'react-icons/io5';

interface OmitProps {
  customerSlice: TMainReduxStates['customerReducer'];
  admin: TMainReduxStates['reducerAdmin'];
}

interface Props extends OmitProps {}

interface States {}

class Leads extends Component<Props, States> {
  constructor(props: Props) {
    super(props);
    this.state = {};
  }

  onSendToFreeLeadsClick = () => {
    mainStore.dispatch(modalSendToFreeLeadsSlice.actions.open());
  };

  onAcquisitionAsLeadsClick = () => {
    mainStore.dispatch(modalAcquisitionAsLeadsSlice.actions.open());
  };

  acquired = () => {
    const { customerSlice } = this.props;
    if (customerSlice.client?.acquiredLeadsStatus?.acquired) {
      const data = customerSlice.client?.acquiredLeadsStatus;
      return {
        agentName: data.agentName,
        agentCode: data.agentCode,
        organization: data.organization || 'amartahonda',
        acquiredAt: data.acquiredAt.toDate(),
      };
    } else if (customerSlice.client?.freeLeadsStatus?.acquired) {
      const data = customerSlice.client?.freeLeadsStatus;
      return {
        agentName: data.agentName,
        agentCode: data.agentCode,
        organization: data.organization || 'amartahonda',
        acquiredAt: data.acquiredAt?.toDate(),
      };
    }
    return null;
  };

  acquisitionAllow = () => {
    let allow = true;
    if (!this.props.admin.admin?.amartaVip?.mediatorCode) allow = false;
    return allow;
  };

  render() {
    const { customerSlice } = this.props;
    const acquiredData = this.acquired();

    return (
      <div className="space-y-4">
        {/* Free Leads Section */}
        <div className="bg-white rounded-lg p-6 border border-gray-100">
          <div className="flex items-center gap-3 mb-4">
            <IoGift className="text-xl text-blue-600" />
            <h3 className="text-lg font-medium text-gray-900">Free Leads</h3>
          </div>

          {!customerSlice.client?.freeLeadsStatus?.submitted ? (
            <button
              onClick={this.onSendToFreeLeadsClick}
              className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <IoGift className="text-lg" />
              <span>Kirim ke Free Leads</span>
            </button>
          ) : (
            <div className="bg-green-50 border border-green-100 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <IoCheckmarkCircle className="text-xl text-green-600 mt-0.5" />
                <div>
                  <h4 className="text-green-800 font-medium mb-1">Sukses Dikirim ke Free Leads</h4>
                  <div className="flex items-center gap-2 text-green-700 text-sm">
                    <IoCalendarOutline className="text-base" />
                    <span>
                      {moment(customerSlice.client.freeLeadsStatus.createdAt!.toDate()).format(
                        'DD MMM YYYY - HH:mm',
                      )}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Leads Status Section */}
        <div className="bg-white rounded-lg p-6 border border-gray-100">
          <div className="flex items-center gap-3 mb-4">
            <IoPersonAdd className="text-xl text-purple-600" />
            <h3 className="text-lg font-medium text-gray-900">Status Akuisisi</h3>
          </div>

          {acquiredData ? (
            <div className="bg-purple-50 border border-purple-100 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <IoTime className="text-xl text-purple-600 mt-0.5" />
                <div>
                  <p className="text-purple-900 font-medium mb-1">
                    Diakuisisi oleh {acquiredData.agentName}
                  </p>
                  <div className="flex items-center gap-2 text-purple-700 text-sm">
                    <IoCalendarOutline className="text-base" />
                    <span>{moment(acquiredData.acquiredAt).format('DD MMM YYYY - HH:mm')}</span>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-gray-50 border border-gray-100 rounded-lg p-4 text-gray-600">
              Belum di akuisisi oleh agen manapun
            </div>
          )}

          {this.acquisitionAllow() && (
            <button
              onClick={this.onAcquisitionAsLeadsClick}
              className="mt-4 inline-flex items-center gap-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
            >
              <IoPersonAdd className="text-lg" />
              <span>Akuisisi Sebagai Leads</span>
            </button>
          )}
        </div>
      </div>
    );
  }
}

const mapStateToProps = (states: TMainReduxStates) => {
  return {
    customerSlice: states.customerReducer,
    admin: states.reducerAdmin,
  };
};

export default compose<React.ComponentType<Omit<Props, keyof OmitProps>>>(connect(mapStateToProps))(
  Leads,
);

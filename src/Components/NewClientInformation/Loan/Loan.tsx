import React, { Component } from 'react';
import { Button, Divider, Form, Icon, Input, Message, Segment, Select } from 'semantic-ui-react';
import { NumericFormat } from 'react-number-format';
import { TMainReduxStates } from '../../../redux/types/redux-types';
import { connect } from 'react-redux';
import currencyFormat from '../../../helpers/currencyFormat';
import { mainStore } from '../../../redux/reducers';
import { mainApiServices } from '../../../services/MainApiServices';
import HTMLReactParser from 'html-react-parser';
import { fetchCustomerThunk } from '../../../redux/customerInfo/customerInfoSlice';
import ModalConfirmLoan from './ModalConfirmLoan';
import modalLoanConfirmSlice from '../../../redux/loan/modalLoanConfirm.slice';
import { IOwnedVehicle } from '../../../entities/types/client-entity-types';
import moment from 'moment';

interface Props {
  customer: TMainReduxStates['customerReducer'];
}

interface States {
  tenor: number;
  amount: number;
  mode: 'read' | 'edit';
  selectedOwnedVehicle: IOwnedVehicle | null;

  submitting: boolean;
  statusUpdate: 'none' | 'success' | 'error';
  errorMessage: string;
}

class Loan extends Component<Props, States> {
  constructor(props: Props) {
    super(props);

    this.state = {
      tenor: 0,
      amount: 0,
      selectedOwnedVehicle: null,
      mode: 'read',

      submitting: false,
      statusUpdate: 'none',
      errorMessage: '',
    };
  }

  private ownedVehicleOnChange = (id: string) => {
    const ownedVehicle = this.props.customer.client?.owned_vehicle || [];
    const find = ownedVehicle.find((v) => v.id === id);

    this.setState({
      selectedOwnedVehicle: find || null,
    });
  };

  private amount = {
    setAmount: (v: string) => {
      this.setState({
        amount: parseInt(v),
      });
    },
  };

  private tenor = {
    setTenor: (v: string) => {
      this.setState({
        tenor: parseInt(v),
      });
    },
  };

  private changeMode = {
    toEdit: () => {
      const { customer } = this.props;
      this.setState({
        mode: 'edit',
        tenor: customer.client?.survey_loan?.scheme?.tenor || 0,
        amount: customer.client?.survey_loan?.scheme?.amount || 0,
      });
    },
  };

  private confirmUpdate = async () => {
    this.setState({
      submitting: true,
      statusUpdate: 'none',
      errorMessage: '',
    });
    try {
      await mainApiServices.updateLoanScheme({
        amount: this.state.amount,
        tenor: this.state.tenor,
        clientRefPath: this.props.customer!.ref!.path,
      });
      this.setState(
        {
          submitting: false,
          statusUpdate: 'success',
          mode: 'read',
        },
        () => {
          mainStore.dispatch(
            fetchCustomerThunk({
              clientDocRef: this.props.customer!.ref!,
            }) as any,
          );
        },
      );
    } catch (e) {
      this.setState(
        {
          statusUpdate: 'error',
          submitting: false,
          errorMessage: 'Gagal memperbarui skema dana pengajuan BPKB',
        },
        () => {
          console.log(this.state);
        },
      );
    }
  };

  openConfirmModal = () => {
    if (!this.state.selectedOwnedVehicle) return;
    mainStore.dispatch(modalLoanConfirmSlice.actions.open(this.state.selectedOwnedVehicle));
  };

  disableButton = () => {
    return !this.state.selectedOwnedVehicle;
  };

  render() {
    let { customer } = this.props;

    return (
      <Segment
        padded={true}
        basic={true}
      >
        {customer.client?.owned_vehicle?.length === 0 && (
          <Message warning={true}>
            Minimal harus mempunyai satu kendaraan yang dimiliki untuk mengajukan Dana Tunai BPKB
          </Message>
        )}

        {this.state.mode === 'read' && (
          <React.Fragment>
            <Segment>
              <div className={'flex-row mb-5'}>
                <div>Nominal</div>
                <div className={'font-bold'}>
                  {currencyFormat(customer.client?.survey_loan?.scheme?.amount || 0)}
                </div>
              </div>
              <div className={'flex-row mb-5'}>
                <div>Tenor</div>
                <div className={'font-bold'}>
                  {customer.client?.survey_loan?.scheme?.tenor || 0} kali
                </div>
              </div>

              <div>
                <Button
                  basic={true}
                  size={'small'}
                  onClick={() => {
                    this.changeMode.toEdit();
                  }}
                >
                  Perbarui
                </Button>
              </div>
            </Segment>
          </React.Fragment>
        )}

        {this.state.mode === 'edit' && (
          <React.Fragment>
            <Form>
              <Form.Field>
                <label>Nominal Pinjaman</label>
                <NumericFormat
                  customInput={Input}
                  thousandSeparator="."
                  decimalSeparator=","
                  prefix="Rp "
                  placeholder={'Masukan Nominal Pinjaman'}
                  value={this.state.amount}
                  onValueChange={(values) => this.amount.setAmount(values.value)}
                />
              </Form.Field>
              <Form.Field>
                <label>Tenor</label>
                <NumericFormat
                  customInput={Input}
                  placeholder={'Masukan Tenor'}
                  value={this.state.tenor}
                  onValueChange={(values) => this.tenor.setTenor(values.value)}
                />
              </Form.Field>
              <Form.Field>
                <Button
                  onClick={this.confirmUpdate}
                  loading={this.state.submitting}
                >
                  Simpan
                </Button>
              </Form.Field>
            </Form>
            {this.state.errorMessage && (
              <div className={'mt-5'}>
                <Message error={true}>{HTMLReactParser(this.state.errorMessage)}</Message>
              </div>
            )}
          </React.Fragment>
        )}

        {this.state.mode === 'read' && (
          <React.Fragment>
            <Form>
              <Form.Field>
                <label>Kendaraan</label>
                <Select
                  clearable={true}
                  options={
                    customer.client?.owned_vehicle.map((v) => {
                      return {
                        text: `${v.brand_name} ${v.model_name} ${v.variant_free_text} - ${v.year}`.toUpperCase(),
                        value: v.id,
                      };
                    }) || []
                  }
                  placeholder={'Pilih Kendaraan'}
                  value={this.state.selectedOwnedVehicle?.id || undefined}
                  onChange={(event, data) => this.ownedVehicleOnChange(data.value as string)}
                />
              </Form.Field>
            </Form>

            {this.state.selectedOwnedVehicle && (
              <Segment className={'p-3 mt-2'}>
                <div className={'mb-1 font-light'}>Kendaraan dipilih:</div>
                <div className={'mb-1 font-bold'}>
                  {this.state.selectedOwnedVehicle.brand_name.toUpperCase()}
                  &nbsp;
                  {this.state.selectedOwnedVehicle.model_name.toUpperCase()}
                  &nbsp;
                  {this.state.selectedOwnedVehicle.variant_free_text.toUpperCase()}
                  &nbsp;-&nbsp;
                  {this.state.selectedOwnedVehicle.year}
                </div>
                <div className={'flex gap-4'}>
                  <div>
                    <Icon name={'circle'} />
                    &nbsp;{this.state.selectedOwnedVehicle.license_plate}
                  </div>
                  <div>
                    <Icon name={'tachometer alternate'} />
                    &nbsp;{this.state.selectedOwnedVehicle.mileage}KM
                  </div>
                </div>
              </Segment>
            )}

            <div className={'mt-3'}>
              <Button
                primary={true}
                onClick={this.openConfirmModal}
                size={'small'}
                disabled={this.disableButton()}
              >
                Buat Pengajuan
              </Button>
            </div>
            <Divider />
            <div className={'flex gap-2 flex-col'}>
              <div>
                <div>Terakhir Kirim Survey:</div>
                <div className={'font-bold'}>
                  {customer.client?.survey_loan?.last_send
                    ? moment(customer.client.survey_loan.last_send.toDate()).format(
                        'DD MMM YYYY HH:mm',
                      )
                    : 'Belum Pernah'}
                </div>
              </div>
              <div>
                <div>Koder Order:</div>
                <div className={'font-bold'}>
                  {customer.client?.survey_loan?.order_code || 'Tidak ada'}
                </div>
              </div>
            </div>
          </React.Fragment>
        )}

        <ModalConfirmLoan />
      </Segment>
    );
  }
}

const mapStateToProps = (states: TMainReduxStates) => {
  return {
    customer: states.customerReducer,
  };
};

export default connect(mapStateToProps)(Loan);

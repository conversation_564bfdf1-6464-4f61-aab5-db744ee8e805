import React, { Component } from 'react';
import { <PERSON>ton, Card, Image, Message, Modal } from 'semantic-ui-react';
import { TMainReduxStates } from '../../../redux/types/redux-types';
import { connect } from 'react-redux';
import { mainStore } from '../../../redux/reducers';
import modalLoanConfirmSlice from '../../../redux/loan/modalLoanConfirm.slice';
import currencyFormat from '../../../helpers/currencyFormat';
import { IReqBodyB2bLoan } from '../../../services/b2b/b2bLoan.types';
import moment from 'moment';
import b2bServices from '../../../services/b2b/b2bServices';
import { arrayUnion, Timestamp, writeBatch } from 'firebase/firestore';
import { firestoreIdealVer9 } from '../../../services/myFirebase';
import HTMLReactParser from 'html-react-parser';
import { AxiosError } from 'axios';
import { fetchCustomerThunk } from '../../../redux/customerInfo/customerInfoSlice';
import ownedVehicle from '../OwnedVehicle/OwnedVehicle';

interface Props {
  customer: TMainReduxStates['customerReducer'];
  modal: TMainReduxStates['modalLoanConfirm'];
  admin: TMainReduxStates['reducerAdmin'];
}

class ModalConfirmLoan extends Component<Props> {
  private dispatch = mainStore.dispatch;
  private actions = modalLoanConfirmSlice.actions;

  constructor(props: Props) {
    super(props);
  }

  private onCancel = () => {
    mainStore.dispatch(modalLoanConfirmSlice.actions.close());
  };

  onSubmit = async () => {
    const { modal } = this.props;
    const customer = this.props.customer.client;
    const customerRef = this.props.customer.ref;
    if (!customer || !customerRef) return;

    const body: IReqBodyB2bLoan = {
      source: 'ideal',

      name: customer.details.idCardOwner?.fullName || '',
      email: '',
      phoneNumber: customer.contacts.whatsapp || '',
      vehicle: {
        brand: {
          name: modal.ownedVehicle?.brand_name || '',
          uuid: modal.ownedVehicle?.brand_uuid || '',
        },
        model: {
          name: modal.ownedVehicle?.model_name || '',
          uuid: modal.ownedVehicle?.model_uuid || '',
          category: modal.ownedVehicle?.model_category || '',
        },
        variant: {
          name: '',
          uuid: '',
          code: '',
        },
        variant_free_text: modal.ownedVehicle?.variant_free_text || '',
        condition: 'used',
        year: modal.ownedVehicle?.year || '',
        mileage: modal.ownedVehicle?.mileage || '',
        license_plate: modal.ownedVehicle?.license_plate || '',
      },
      scheme: {
        amount: customer.survey_loan?.scheme?.tenor || 0,
        tenor: customer.survey_loan?.scheme?.tenor || 0,
      },
      id_card: {
        id_card_image: customer.details.idCardOwner?.idCardImage || '',
        id_card_number: customer.details.idCardOwner?.idCardNumber || '',
        full_name: customer.details.idCardOwner?.fullName || '',
        full_address: customer.details.idCardOwner?.fullAddress || '',
        birth_date: customer.details.idCardOwner?.dateOfBirth
          ? moment(customer.details.idCardOwner.dateOfBirth.toDate()).format('YYYY-MM-DD')
          : '',
        birth_place: customer.details.idCardOwner?.placeOfBirth || '',
        marital_status_code: customer.details.idCardOwner?.maritalStatusCode || '',
        marital_status: customer.details.idCardOwner?.maritalStatus || '',
        occupation_code: customer.details.idCardOwner?.occupationCode || '',
        occupation: customer.details.idCardOwner?.occupation || '',
      },
      id_card_address: {
        full_address: customer.details.idCardOwner?.fullAddress || '',
        province: {
          name: customer.details.idCardOwner?.province.name || '',
          code: customer.details.idCardOwner?.province.code || '',
        },
        city: {
          name: customer.details.idCardOwner?.city.name || '',
          code: customer.details.idCardOwner?.city.code || '',
        },
        district: {
          name: customer.details.idCardOwner?.district.name || '',
          code: customer.details.idCardOwner?.district.code || '',
        },
        sub_district: {
          name: customer.details.idCardOwner?.subDistrict.name || '',
          code: customer.details.idCardOwner?.subDistrict.code || '',
        },
        zip_code: customer.details.idCardOwner?.zipCode || '',
        neighbourhood: customer.details.idCardOwner?.neighbourhood || '',
        hamlet: customer.details.idCardOwner?.hamlet || '',
      },
      domicile_address: {
        full_address: customer.details.idCardOwner?.domicileFullAddress || '',
        province: {
          name: customer.details.idCardOwner?.domicileProvince.name || '',
          code: customer.details.idCardOwner?.domicileProvince.code || '',
        },
        city: {
          name: customer.details.idCardOwner?.domicileCity.name || '',
          code: customer.details.idCardOwner?.domicileCity.code || '',
        },
        district: {
          name: customer.details.idCardOwner?.domicileDistrict.name || '',
          code: customer.details.idCardOwner?.domicileDistrict.code || '',
        },
        sub_district: {
          name: customer.details.idCardOwner?.domicileSubDistrict.name || '',
          code: customer.details.idCardOwner?.domicileSubDistrict.code || '',
        },
        zip_code: customer.details.idCardOwner?.domicileZipCode || '',
        neighbourhood: customer.details.idCardOwner?.domicileNeighbourhood || '',
        hamlet: customer.details.idCardOwner?.domicileHamlet || '',
      },
      family_register: {
        family_register_image: customer.details.familyRegister?.familyRegisterImage || '',
        family_register_number: customer.details.familyRegister?.familyRegisterNumber || '',
      },
      vehicle_registration: {
        vehicle_registration_number: '',
        vehicle_registration_image: '',
      },
      notice: {
        notice_number: '',
        notice_image: '',
      },
      vehicle_ownership_document: {
        vehicle_document_number: '',
        vehicle_document_image: '',
      },
    };

    this.dispatch(this.actions.setSubmitStatus('submitting'));
    const post = await b2bServices.createSurveyOrderLoan(body);

    const batch = writeBatch(firestoreIdealVer9);
    if (post.ok) {
      this.dispatch(this.actions.setSubmitStatus('success'));
      const clientRef = customerRef;
      batch.update(clientRef, {
        'survey_loan.last_send': Timestamp.now(),
        'survey_loan.order_code': post.data?.data,
        'survey_loan.offer_code': null,
        order_histories: arrayUnion({
          offer_code: null,
          order_code: post.data?.data || null,
          created_at: Timestamp.now(),
          survey_order_type: 'loan',
          payment_scheme: 'CREDIT',
          source: 'IDEAL',
          createdByAdminIdeal: {
            ref: this.props.admin.admin?.ref || null,
            name: this.props.admin.admin?.name || null,
            email: this.props.admin.admin?.email || null,
          },
          orderCreatedBy: this.props.admin.admin?.email || null,
        }),
      });

      await batch.commit().then().catch();

      mainStore.dispatch(
        fetchCustomerThunk({
          clientDocRef: clientRef,
        }) as any,
      );
    } else {
      this.dispatch(this.actions.setSubmitStatus('error'));
      const error = post.originalError as AxiosError<any>;
      const errorResponse = error.response?.data;
      let message = error.message;
      if ('type' in errorResponse && errorResponse.type === 'UNPROCESSABLE_ENTITY') {
        message = "<ul class='list-disc'>";
        for (let [key, value] of Object.entries(errorResponse.data)) {
          message += `<li>${(value as { msg: string }).msg} (${key})</li>`;
        }
        message += '</ul>';
      }
      this.dispatch(this.actions.setErrorMessages(message));
    }
  };

  render() {
    let { customer, modal } = this.props;

    return (
      <Modal open={modal.open}>
        <Modal.Header>Konfirmasi Pengajuan Dana</Modal.Header>
        <Modal.Content>
          <Card fluid={true}>
            <Card.Content>
              <Card.Header>Kendaraan</Card.Header>
            </Card.Content>
            <Card.Content>
              <div>
                <div className={'flex-col gap-2 mb-4'}>
                  <div>Brand</div>
                  <div className={'font-bold'}>{modal.ownedVehicle?.brand_name.toUpperCase()}</div>
                </div>
                <div className={'flex-col gap-2 mb-4'}>
                  <div>Model</div>
                  <div className={'font-bold'}>{modal.ownedVehicle?.model_name}</div>
                </div>
                <div className={'flex-col gap-2 mb-4'}>
                  <div>Variant</div>
                  <div className={'font-bold'}>{modal.ownedVehicle?.variant_free_text}</div>
                </div>
                <div className={'flex-col gap-2 mb-4'}>
                  <div>Tahun</div>
                  <div className={'font-bold'}>{modal.ownedVehicle?.year}</div>
                </div>
                <div className={'flex-col gap-2 mb-4'}>
                  <div>Kilometer Kendaraan</div>
                  <div className={'font-bold'}>{modal.ownedVehicle?.mileage || 'Tidak Ada'}</div>
                </div>
                <div className={'flex-col gap-2 mb-4'}>
                  <div>Kategori</div>
                  <div className={'font-bold'}>
                    {modal.ownedVehicle?.model_category.toUpperCase() || 'Tidak Ada'}
                  </div>
                </div>
                <div className={'flex-col gap-2 mb-4'}>
                  <div>Plat Nomor</div>
                  <div className={'font-bold'}>
                    {modal.ownedVehicle?.license_plate.toUpperCase() || 'Tidak Ada'}
                  </div>
                </div>
              </div>
            </Card.Content>
          </Card>
          <Card fluid={true}>
            <Card.Content>
              <Card.Header>Skema Pinjaman Dana</Card.Header>
            </Card.Content>
            <Card.Content>
              <div>
                <div className={'flex-col gap-2 mb-4'}>
                  <div>Tenor</div>
                  <div className={'font-bold'}>
                    {customer.client?.survey_loan?.scheme?.tenor || 0} kali
                  </div>
                </div>
                <div className={'flex-col gap-2 mb-4'}>
                  <div>Total Dana</div>
                  <div className={'font-bold'}>
                    {currencyFormat(customer.client?.survey_loan?.scheme?.amount || 0)}
                  </div>
                </div>
              </div>
            </Card.Content>
          </Card>
          <Card fluid={true}>
            <Card.Content>
              <Card.Header>KTP</Card.Header>
            </Card.Content>
            <Card.Content>
              <div>
                <div className={'flex-col gap-2 mb-4'}>
                  <div>Nama</div>
                  <div className={'font-bold'}>
                    {customer.client?.details.idCardOwner?.fullName}
                  </div>
                </div>
                <div className={'flex-col gap-2 mb-4'}>
                  <div>No KTP</div>
                  <div className={'font-bold'}>
                    {customer.client?.details.idCardOwner?.idCardNumber}
                  </div>
                </div>
                <div className={'flex-col gap-2 mb-4'}>
                  <div>Alamat</div>
                  <div className={'font-bold'}>
                    {customer.client?.details.idCardOwner?.fullAddress}
                  </div>
                </div>
                <div className={'flex-col gap-2 mb-4'}>
                  <div>Provinsi, Kota, Kecamatan, Kelurahan</div>
                  <div className={'font-bold'}>
                    {customer.client?.details.idCardOwner?.province.name.toUpperCase()}
                    &ensp;-&ensp;
                    {customer.client?.details.idCardOwner?.city.name.toUpperCase()}
                    &ensp;-&ensp;
                    {customer.client?.details.idCardOwner?.district.name.toUpperCase()}
                    &ensp;-&ensp;
                    {customer.client?.details.idCardOwner?.subDistrict.name.toUpperCase()}
                  </div>
                </div>
                <div className={'flex-col gap-2 mb-4'}>
                  <div>Kode Pos</div>
                  <div className={'font-bold'}>{customer.client?.details.idCardOwner?.zipCode}</div>
                </div>
                <div className={'flex-col gap-2 mb-6'}>
                  <div>RT / RW</div>
                  <div className={'font-bold'}>
                    {customer.client?.details.idCardOwner?.neighbourhood}
                    &ensp;/&ensp;
                    {customer.client?.details.idCardOwner?.hamlet}
                  </div>
                </div>
                <div>
                  <Image
                    src={customer.client?.details.idCardOwner?.idCardImage}
                    size={'huge'}
                  />
                </div>
              </div>
            </Card.Content>
          </Card>
          <Card fluid={true}>
            <Card.Content>
              <Card.Header>Alamat Domisili</Card.Header>
            </Card.Content>
            <Card.Content>
              <div>
                <div className={'flex-col gap-2 mb-4'}>
                  <div>Alamat</div>
                  <div className={'font-bold'}>
                    {customer.client?.details.idCardOwner?.domicileFullAddress}
                  </div>
                </div>
                <div className={'flex-col gap-2 mb-4'}>
                  <div>Provinsi, Kota, Kecamatan, Kelurahan</div>
                  <div className={'font-bold'}>
                    {customer.client?.details.idCardOwner?.domicileProvince.name.toUpperCase()}
                    &ensp;-&ensp;
                    {customer.client?.details.idCardOwner?.domicileCity.name.toUpperCase()}
                    &ensp;-&ensp;
                    {customer.client?.details.idCardOwner?.domicileDistrict.name.toUpperCase()}
                    &ensp;-&ensp;
                    {customer.client?.details.idCardOwner?.domicileSubDistrict.name.toUpperCase()}
                  </div>
                </div>
                <div className={'flex-col gap-2 mb-4'}>
                  <div>Kode Pos</div>
                  <div className={'font-bold'}>{customer.client?.details.idCardOwner?.zipCode}</div>
                </div>
                <div className={'flex-col gap-2 mb-4'}>
                  <div>RT / RW</div>
                  <div className={'font-bold'}>
                    {customer.client?.details.idCardOwner?.domicileNeighbourhood}
                    &ensp;/&ensp;
                    {customer.client?.details.idCardOwner?.domicileHamlet}
                  </div>
                </div>
              </div>
            </Card.Content>
          </Card>
          <Card fluid={true}>
            <Card.Content>
              <Card.Header>Kartu Keluarga</Card.Header>
            </Card.Content>
            <Card.Content>
              <div>
                <div className={'flex-col gap-2 mb-4'}>
                  <div>Nomor KK</div>
                  <div className={'font-bold'}>
                    {customer.client?.details.familyRegister?.familyRegisterNumber}
                  </div>
                </div>
                <div>
                  <Image
                    src={customer.client?.details.familyRegister?.familyRegisterImage}
                    size={'huge'}
                  />
                </div>
              </div>
            </Card.Content>
          </Card>
          {modal.submitStatus === 'success' && (
            <Message success={true}>
              Survey Order berhasil dibuat, anda sudah dapat menutup menu ini.
            </Message>
          )}
          {modal.errorMessages && (
            <Message error={true}>{HTMLReactParser(modal.errorMessages)}</Message>
          )}
        </Modal.Content>
        <Modal.Actions>
          <Button onClick={this.onCancel}>Tutup</Button>
          <Button
            primary={true}
            onClick={this.onSubmit}
            loading={modal.loading}
          >
            Submit
          </Button>
        </Modal.Actions>
      </Modal>
    );
  }
}

const mapStateToProps = (states: TMainReduxStates) => {
  return {
    customer: states.customerReducer,
    modal: states.modalLoanConfirm,
    admin: states.reducerAdmin,
  };
};

export default connect(mapStateToProps)(ModalConfirmLoan);

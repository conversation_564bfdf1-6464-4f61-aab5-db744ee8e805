import React, { Component } from 'react';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { connect } from 'react-redux';
import { Dropdown } from 'semantic-ui-react';
import moment from 'moment';
import { mainStore } from '../../redux/reducers';
import modalDetailOfferSlice, {
  modalDetailOfferFetch,
} from '../../redux/modal-detail-offer/modalDetailOfferSlice';
import ButtonCopy from '../Template/ButtonCopy';
import ModalStatusB2B from '../ModalStatusB2B/ModalStatusB2B';
import modalStatusB2bSlice, {
  fetchStatusB2b,
} from '../../redux/modalStatusB2b/modalStatusB2bSlice';
import ModalUpdateDataToOtodisIdCard from '../ModalUpdateDataOtodis/ModalUpdateDataToOtodis';
import ModalDetailOffer from '../ModalDetailOffer';
import { IClientEntityOrderHistory } from '../../entities/types/client-entity-types';
import ModalUpdateDataPromoCodeToOtodis from '../ModalUpdateDataOtodis/ModalUpdateDataPromoCodeToOtodis';
import {
  IoCarSport,
  IoTime,
  IoCalendarOutline,
  IoEllipsisVertical,
  IoDocumentText,
} from 'react-icons/io5';

interface ReduxProps {
  customer: TMainReduxStates['customerReducer'];
}

interface Props extends ReduxProps {}

class OfferCodeHistories extends Component<Props> {
  onGetDetailClick = (offerCode: string, data: IClientEntityOrderHistory) => {
    mainStore.dispatch(
      modalDetailOfferSlice.actions.open({
        open: true,
        data,
      }),
    );
    mainStore.dispatch(modalDetailOfferFetch(offerCode) as any);
  };

  onStatusB2bClick = (offerCode: string, data: IClientEntityOrderHistory) => {
    mainStore.dispatch(modalStatusB2bSlice.actions.open(data));
    mainStore.dispatch(fetchStatusB2b(offerCode) as any);
  };

  render() {
    if (!this.props.customer.client?.order_histories) {
      return <div className="flex items-center justify-center p-8 text-gray-500">Tidak ada</div>;
    }

    const { order_histories } = this.props.customer.client;

    if (order_histories.length === 0) {
      return (
        <div className="flex items-center justify-center p-8 text-gray-500">
          Tidak ada riwayat offer code
        </div>
      );
    }

    return (
      <div className="space-y-4">
        {order_histories.map((o) => (
          <div
            key={o.offer_code}
            className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden"
          >
            <div className="p-4 space-y-4">
              {/* Header */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div
                    className={`p-2 rounded-lg ${o.survey_order_type === 'loan' ? 'bg-red-50' : 'bg-blue-50'}`}
                  >
                    <IoCarSport
                      className={`text-xl ${o.survey_order_type === 'loan' ? 'text-red-600' : 'text-blue-600'}`}
                    />
                  </div>
                  <h3 className="font-medium text-gray-900">
                    {o.survey_order_type === 'loan' ? 'Loan Survey Order' : 'Vehicle Survey Order'}
                  </h3>
                </div>

                {o.survey_order_type !== 'loan' && (
                  <div className="flex items-center gap-2">
                    <ButtonCopy textToCopy={o.offer_code} />
                    <Dropdown
                      icon={<IoEllipsisVertical className="text-gray-600" />}
                      direction="left"
                    >
                      <Dropdown.Menu>
                        <Dropdown.Item onClick={() => this.onStatusB2bClick(o.offer_code, o)}>
                          <div className="flex items-center gap-2">
                            <IoDocumentText />
                            <span>B2B</span>
                          </div>
                        </Dropdown.Item>
                        <Dropdown.Item onClick={() => this.onGetDetailClick(o.offer_code, o)}>
                          <div className="flex items-center gap-2">
                            <IoDocumentText />
                            <span>Offer</span>
                          </div>
                        </Dropdown.Item>
                      </Dropdown.Menu>
                    </Dropdown>
                  </div>
                )}
              </div>

              {/* Content */}
              <div className="space-y-3">
                {o.survey_order_type === 'loan' && (
                  <div className="flex items-center gap-2 text-gray-600">
                    <IoDocumentText className="text-lg" />
                    <span className="text-sm">Kode Order:</span>
                    <span className="font-medium">{o.order_code}</span>
                  </div>
                )}

                <div className="flex items-center gap-2 text-gray-600">
                  <IoDocumentText className="text-lg" />
                  <span className="text-sm">Kode Offer:</span>
                  <a
                    target="_blank"
                    rel="noreferrer"
                    className="text-blue-600 hover:text-blue-700 font-medium"
                    href={`https://amartahonda.com/track?utm_source=b2b&id=${btoa(o.offer_code)}`}
                  >
                    {o.offer_code}
                  </a>
                </div>

                <div className="flex items-center gap-2 text-gray-600">
                  <IoCarSport className="text-lg" />
                  <span className="text-sm">Skema:</span>
                  <span className="font-medium">{o.payment_scheme}</span>
                </div>

                <div className="flex items-center gap-2 text-gray-600">
                  <IoCalendarOutline className="text-lg" />
                  <span className="text-sm">Tanggal Buat:</span>
                  <span className="font-medium">
                    {moment(o.created_at.toDate()).format('DD MMM YYYY HH:mm')}
                  </span>
                </div>
              </div>
            </div>
          </div>
        ))}

        <ModalStatusB2B />
        <ModalDetailOffer />
        <ModalUpdateDataToOtodisIdCard />
        <ModalUpdateDataPromoCodeToOtodis />
      </div>
    );
  }
}

const mapStateToProps = (states: TMainReduxStates) => ({
  customer: states.customerReducer,
});

export default connect(mapStateToProps)(OfferCodeHistories);

import React, { Component } from 'react';
import { connect } from 'react-redux';
import { TMainReduxStates } from '../../../redux/types/redux-types';
import ModalAddNewVehicle from '../../ModalAddNewOwnedVehicle/ModalAddNewVehicle';
import { mainStore } from '../../../redux/reducers';
import modalAddNewOwnedVehicleSlice from '../../../redux/owned-vehicle/modalAddNewOwnedVehicle.slice';
import OwnedVehicleItem from './OwnedVehicleItem';
import { IoAddCircleOutline } from 'react-icons/io5';

interface Props {
  conversation: TMainReduxStates['reducerConversation'];
  customer: TMainReduxStates['customerReducer'];
}

class OwnedVehicle extends Component<Props> {
  render() {
    const { customer } = this.props;

    return (
      <div className="bg-white rounded-lg shadow-sm p-6">
        <ModalAddNewVehicle />

        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-800">Kendaraan yang Dimiliki</h3>
          <button
            onClick={() => mainStore.dispatch(modalAddNewOwnedVehicleSlice.actions.open())}
            className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors duration-200"
          >
            <IoAddCircleOutline className="text-xl" />
            Tambah Kendaraan
          </button>
        </div>

        {customer.client?.owned_vehicle.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-8 px-4 bg-gray-50 rounded-lg">
            <p className="text-gray-500 text-center">Belum ada kendaraan yang ditambahkan</p>
          </div>
        ) : (
          <div className="grid gap-4">
            {customer.client?.owned_vehicle.map((v) => (
              <OwnedVehicleItem
                key={v.id}
                vehicle={v}
              />
            ))}
          </div>
        )}
      </div>
    );
  }
}

const mapStateToProps = (states: TMainReduxStates) => ({
  conversation: states.reducerConversation,
  customer: states.customerReducer,
});

export default connect(mapStateToProps)(OwnedVehicle);

import React from 'react';
import { IOwnedVehicle } from '../../../entities/types/client-entity-types';
import { TMainReduxStates } from '../../../redux/types/redux-types';
import { connect } from 'react-redux';
import { updateDoc } from 'firebase/firestore';
import { fetchCustomerThunk } from '../../../redux/customerInfo/customerInfoSlice';
import { mainStore } from '../../../redux/reducers';
import {
  IoCarSportOutline,
  IoCalendarOutline,
  IoSpeedometerOutline,
  IoTrashOutline,
  IoEllipseOutline,
} from 'react-icons/io5';

interface Props {
  vehicle: IOwnedVehicle;
  customer: TMainReduxStates['customerReducer'];
}

const OwnedVehicleItem = (props: Props) => {
  const vehicle = props.vehicle;
  const dispatch = mainStore.dispatch;

  const onDelete = async () => {
    if (!props.customer.client || !props.customer.ref) return;

    let ownedVehicle = props.customer.client.owned_vehicle;

    await updateDoc(props.customer.ref, {
      owned_vehicle: ownedVehicle.filter((v) => v.id !== vehicle.id),
    }).catch();

    dispatch(
      fetchCustomerThunk({
        clientDocRef: props.customer.ref,
      }) as any,
    );
  };

  return (
    <div className="bg-white border border-gray-100 rounded-xl p-4 hover:shadow-md transition-all duration-200">
      <div className="flex justify-between items-start">
        <div className="flex items-center gap-3">
          <div className="p-3 bg-blue-50 rounded-lg">
            <IoCarSportOutline className="text-2xl text-blue-600" />
          </div>
          <div>
            <h4 className="font-medium text-gray-900">
              {vehicle.brand_name.toUpperCase()} {vehicle.model_name.toUpperCase()}
            </h4>
            <p className="text-sm text-gray-600">{vehicle.variant_free_text.toUpperCase()}</p>
          </div>
        </div>

        <button
          onClick={onDelete}
          className="p-2 text-red-500 hover:bg-red-50 rounded-lg transition-colors duration-200"
        >
          <IoTrashOutline className="text-xl" />
        </button>
      </div>

      <div className="mt-4 grid grid-cols-2 gap-4">
        <div className="flex items-center gap-2 text-gray-600">
          <IoCalendarOutline className="text-lg" />
          <span className="text-sm">{vehicle.year}</span>
        </div>
        <div className="flex items-center gap-2 text-gray-600">
          <IoSpeedometerOutline className="text-lg" />
          <span className="text-sm">{vehicle.mileage.toLocaleString()} KM</span>
        </div>
      </div>

      <div className="mt-3 flex items-center gap-2 text-gray-600">
        <IoEllipseOutline className="text-lg" />
        <span className="text-sm font-medium">{vehicle.license_plate}</span>
      </div>
    </div>
  );
};

const mapStateToProps = (states: TMainReduxStates) => ({
  customer: states.customerReducer,
});

export default connect(mapStateToProps)(OwnedVehicleItem);

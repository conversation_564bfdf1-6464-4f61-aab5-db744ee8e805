/*
 * ProfileInformation Component
 * This component displays and updates customer profile information.
 * It manages editing mode, updates Redux store after API calls, and renders various UI sections.
 */

import React, { Component } from 'react';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { connect } from 'react-redux';
import { mainApiServices } from '../../services/MainApiServices';
import SelectAreaFromCatalog from '../SelectCityGroup/SelectAreaFromCatalog';
import { AxiosError } from 'axios';
import UpdateNotes from '../Notes/UpdateNotes';
import { mainStore } from '../../redux/reducers';
import ClientFinalDecision from '../ClientFinalDecission/ClientFinalDecision';
import modalUpdatePhoneNumberSlice from '../../redux/modal-update-phone-number/modalUpdatePhoneNumber.slice';
import { fetchCustomerThunk } from '../../redux/customerInfo/customerInfoSlice';
import TableAvailabilityDocumentV2 from './TableAvailabilityDocument.v2';
import maskSanitizedPhoneNumber from '../../helpers/maskPhoneNumber/maskSanitizedPhoneNumber';
import {
  IoCheckmarkCircle,
  IoCloseCircle,
  IoCloseOutline,
  IoPhonePortraitOutline,
  IoRefreshOutline,
  IoSaveOutline,
} from 'react-icons/io5';
import { AnimatePresence, motion } from 'framer-motion';
import SelectOrganization, { OrganizationInfo } from '../SelectOrganization/SelectOrganization';
import {
  getCompanyCodeByOrganization,
  getOrganizationName,
  getOrganizationByCode,
} from '../../helpers/organizationHelper';

// Interface for profile information fields
export interface IProfileInformationFields {
  contactName: string;
  phoneNumber: string;
  area: { text: string; value: string } | null;
  organization: string;
  organization_group: string;
}

// Interface for component state including profile fields and UI states (loading, editing, updating, error)
export interface IProfileInformationStates extends IProfileInformationFields {
  loading: boolean;
  editing: boolean;
  updating: boolean;
  error: null | string;
}

// Interface for component props mapped from Redux state
export interface IProfileInformationProps {
  conversation: TMainReduxStates['reducerConversation'];
  project: TMainReduxStates['reducerProject'];
  admin: TMainReduxStates['reducerAdmin'];
  customer: TMainReduxStates['customerReducer'];
}

class ProfileInformation extends Component<IProfileInformationProps, IProfileInformationStates> {
  // Handler for contact name change event.
  private contactName = {
    onChange: (e: React.ChangeEvent<HTMLInputElement>) =>
      this.setState({
        contactName: e.target.value,
      }),
  };
  // Handler for area selection changes.
  private area = {
    onChange: async (cityGroup: string) => {
      this.setState({
        area: {
          value: cityGroup,
          text: cityGroup,
        },
      });
    },
  };

  private organization = {
    onChange: (org: OrganizationInfo | null) => {
      if (org) {
        this.setState({
          organization: org.organization,
          organization_group: org.group,
          area: null,
        });
      } else {
        this.setState({
          organization: '',
          organization_group: '',
          area: null,
        });
      }
    },
  };

  // Constructor: initialize component state from customer data provided via Redux props.
  constructor(props: IProfileInformationProps) {
    super(props);

    const { client } = this.props.customer;

    this.state = {
      // Set initial values for profile fields
      contactName: client?.profile.name || client?.profile.temporary_name || '',
      phoneNumber: client?.profile.phone_number || client?.contacts.whatsapp || '',
      area: client?.profile.area || null,
      organization: client?.profile.organization || '',
      organization_group: client?.profile.organization_group || '',

      loading: false,

      editing: false,
      updating: false,

      error: '',
    };
  }

  // Enable editing mode when the update button is clicked.
  onUpdateClick = () => {
    this.setState({
      editing: true,
    });
  };

  // Cancel editing mode and revert any unsaved changes.
  onCancelUpdateClick = async () => {
    this.setState({
      editing: false,
      contactName:
        this.props.customer.client?.profile.name ||
        this.props.customer.client?.profile.temporary_name ||
        '',
      phoneNumber:
        this.props.customer.client?.profile.phone_number ||
        this.props.customer.client?.contacts.whatsapp ||
        '',
      area: this.props.customer.client?.profile.area || null,
      organization: this.props.customer.client?.profile.organization || '',
      organization_group: this.props.customer.client?.profile.organization_group || '',
    });
  };

  // Submit updated profile information and update Redux store.
  onSubmitClick = async () => {
    // Set updating flag and clear any previous error message.
    this.setState({
      updating: true,
      error: null,
    });

    await new Promise((resolve) => setTimeout(resolve, 200));

    const currentState: IProfileInformationStates = { ...this.state };

    try {
      // Attempt to update profile via API service.
      await mainApiServices.setProfile(
        {
          area: this.state.area,
          contactName: this.state.contactName,
          phoneNumber: this.state.phoneNumber,
          organization: this.state.organization,
          organization_group: this.state.organization_group,
        },
        this.props.conversation.chatRoom!.clients[0],
        this.props.conversation.chatRoom!.ref,
      );

      // Refresh customer details in Redux store.
      mainStore.dispatch(
        fetchCustomerThunk({
          clientDocRef: this.props.customer.ref!,
        }) as any,
      );

      // Exit editing mode on successful update.
      currentState.editing = false;
    } catch (e: any) {
      // Handle errors: capture error information and set error message accordingly.
      const error = e as AxiosError<any>;
      currentState.error = e.toString();

      if (error.response?.data.data) currentState.error = error.response?.data.data;
      else if (!!error.response?.data.error.messages) {
        if (error.response?.data.error.type === 'UNPROCESSABLE_ENTITY') {
          currentState.error = '';
          Object.keys(error.response?.data.error.messages).forEach((field) => {
            currentState.error += error.response?.data.error.messages[field].msg;
            currentState.error += ', ';
          });
        } else {
          currentState.error = JSON.stringify(error.response?.data.error);
        }
      }
    }

    // Reset updating flag and update component state with the latest values.
    currentState.updating = false;
    this.setState({ ...currentState });
  };

  // Dispatch action to open the phone number update modal.
  updatePhoneNumber = () => {
    mainStore.dispatch(
      modalUpdatePhoneNumberSlice.actions.open({
        phoneNumberOwner: this.props.customer.client?.details.owner_phone_number || '',
        phoneNumberGuarantor: this.props.customer.client?.details.guarantor_phone_number || '',
        phoneNumberOrderMaker: this.props.customer.client?.details.order_maker_phone_number || '',
      }),
    );
  };

  // Render method for displaying the profile information UI.
  render() {
    const cardVariants = {
      editing: { scale: 1.02, transition: { duration: 0.3 } },
      nonEditing: { scale: 1, transition: { duration: 0.3 } },
    };

    // Show loading indicator if the component is in loading state.
    if (this.state.loading) {
      return (
        <div className="flex items-center justify-center p-8">
          <div className="animate-pulse text-gray-600">Mohon Tunggu...</div>
        </div>
      );
    }

    return (
      <div className="space-y-6">
        {/* Basic Information Card with framer-motion animation */}
        <motion.div
          initial={false}
          animate={this.state.editing ? 'editing' : 'nonEditing'}
          variants={cardVariants}
          className="bg-white rounded-xl shadow-sm p-6 border border-gray-100 space-y-6"
        >
          {/* Phone Number */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">Nomor Telepon</label>
            <div className="text-gray-900">
              {this.props.admin.admin!.admin_rank > 1
                ? maskSanitizedPhoneNumber(this.state.phoneNumber)
                : this.state.phoneNumber}
            </div>
          </div>

          {/* Select Organization with AnimatePresence */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">Organization</label>
            <AnimatePresence mode="wait">
              {this.state.editing ? (
                <motion.div
                  key="editing-organization"
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 10 }}
                >
                  <SelectOrganization
                    value={this.state.organization}
                    onChange={this.organization.onChange}
                    clearable={true}
                  />
                </motion.div>
              ) : (
                <motion.div
                  key="static-organization"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="text-gray-900"
                >
                  {this.state.organization ? (
                    (this.organizationParseByGroup(this.state.organization)?.name ??
                    this.state.organization)
                  ) : (
                    <span className="italic text-gray-500">Belum Ada</span>
                  )}
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* City Group with AnimatePresence */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">City Group</label>
            <AnimatePresence mode="wait">
              {this.state.editing ? (
                <motion.div
                  key="editing-city"
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 10 }}
                >
                  <SelectAreaFromCatalog
                    clearable={true}
                    onChange={this.area.onChange}
                    value={this.state.area?.value}
                    companyCode={getCompanyCodeByOrganization(this.state.organization)}
                  />
                </motion.div>
              ) : (
                <motion.div
                  key="static-city"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="text-gray-900"
                >
                  {this.state.area?.text ?? <span className="italic text-gray-500">Belum Ada</span>}
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Full Name with AnimatePresence */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">Nama Lengkap</label>
            <AnimatePresence mode="wait">
              {this.state.editing ? (
                <motion.input
                  key="editing-input"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  placeholder={
                    this.props.customer.client?.profile.name ||
                    this.props.customer.client?.profile.temporary_name ||
                    'Nama Lengkap'
                  }
                  name="fullName"
                  value={this.state.contactName ?? ''}
                  onChange={this.contactName.onChange}
                  className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              ) : (
                <motion.div
                  key="static-text"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="text-gray-900"
                >
                  {this.state.contactName}
                  {this.props.customer.client?.profile.temporary_name &&
                    !this.props.customer.client?.profile.name && (
                      <span className="ml-2 text-xs font-normal bg-yellow-100 text-yellow-800 px-2 py-0.5 rounded-full">
                        nama sementara
                      </span>
                    )}
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-2">
            {this.state.editing ? (
              <>
                <button
                  onClick={this.onCancelUpdateClick}
                  className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors flex items-center gap-2"
                >
                  <IoCloseOutline className="text-lg" />
                  <span>Batal</span>
                </button>
                <button
                  onClick={this.onSubmitClick}
                  disabled={this.state.updating}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:bg-blue-300 flex items-center gap-2 flex-1"
                >
                  <IoSaveOutline className="text-lg" />
                  <span>{this.state.updating ? 'Menyimpan...' : 'Konfirmasi'}</span>
                </button>
              </>
            ) : (
              <button
                onClick={this.onUpdateClick}
                className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors flex items-center gap-2"
              >
                <IoRefreshOutline className="text-lg" />
                <span>Perbarui</span>
              </button>
            )}
          </div>

          {/* Error Message */}
          {this.state.error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <h4 className="text-red-800 font-medium mb-1">Gagal Memperbarui Profil</h4>
              <p className="text-red-700 text-sm">{this.state.error}</p>
            </div>
          )}
        </motion.div>

        {/* Phone Numbers Card */}
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100 space-y-4">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Nomor Telepon</h3>

          <div className="grid gap-4">
            {/* Owner Phone */}
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="space-y-1">
                <span className="text-sm text-gray-500">Pemilik</span>
                <div className="font-medium text-gray-900">
                  {this.props.customer.client?.details.owner_phone_number ? (
                    this.props.admin.admin!.admin_rank === 1 ? (
                      this.props.customer.client.details.owner_phone_number
                    ) : (
                      maskSanitizedPhoneNumber(
                        this.props.customer.client.details.owner_phone_number,
                      )
                    )
                  ) : (
                    <span className="italic text-gray-500">Belum ada</span>
                  )}
                </div>
              </div>
              {this.props.customer.client?.details.owner_phone_number ? (
                <IoCheckmarkCircle className="text-green-500 text-xl" />
              ) : (
                <IoCloseCircle className="text-red-500 text-xl" />
              )}
            </div>

            {/* Order Maker Phone */}
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="space-y-1">
                <span className="text-sm text-gray-500">Pemesan</span>
                <div className="font-medium text-gray-900">
                  {this.props.customer.client?.details.order_maker_phone_number ? (
                    this.props.admin.admin!.admin_rank === 1 ? (
                      this.props.customer.client.details.order_maker_phone_number
                    ) : (
                      maskSanitizedPhoneNumber(
                        this.props.customer.client.details.order_maker_phone_number,
                      )
                    )
                  ) : (
                    <span className="italic text-gray-500">Belum ada</span>
                  )}
                </div>
              </div>
              {this.props.customer.client?.details.order_maker_phone_number ? (
                <IoCheckmarkCircle className="text-green-500 text-xl" />
              ) : (
                <IoCloseCircle className="text-red-500 text-xl" />
              )}
            </div>

            {/* Guarantor Phone */}
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="space-y-1">
                <span className="text-sm text-gray-500">Penjamin</span>
                <div className="font-medium text-gray-900">
                  {this.props.customer.client?.details.guarantor_phone_number ? (
                    this.props.admin.admin!.admin_rank === 1 ? (
                      this.props.customer.client.details.guarantor_phone_number
                    ) : (
                      maskSanitizedPhoneNumber(
                        this.props.customer.client.details.guarantor_phone_number,
                      )
                    )
                  ) : (
                    <span className="italic text-gray-500">Belum ada</span>
                  )}
                </div>
              </div>
              {this.props.customer.client?.details.guarantor_phone_number ? (
                <IoCheckmarkCircle className="text-green-500 text-xl" />
              ) : (
                <IoCloseCircle className="text-red-500 text-xl" />
              )}
            </div>
          </div>

          <button
            onClick={this.updatePhoneNumber}
            className="w-full mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"
          >
            <IoPhonePortraitOutline className="text-lg" />
            <span>Perbarui Nomor Telepon</span>
          </button>
        </div>

        {/* Documents Section */}
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Dokumen</h3>
          <TableAvailabilityDocumentV2 />
        </div>

        {/* Customer Decision Section */}
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Keputusan Customer</h3>
          <ClientFinalDecision />
        </div>

        {/* Notes Section */}
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Catatan</h3>
          <UpdateNotes />
        </div>
      </div>
    );
  }

  private organizationParseByGroup = (org: string) => {
    return getOrganizationByCode(org);
  };
}

// Map Redux state to component props.
const mapStateToProps = (states: TMainReduxStates) => ({
  conversation: states.reducerConversation,
  admin: states.reducerAdmin,
  project: states.reducerProject,
  customer: states.customerReducer,
});

// Map dispatch actions to component props (currently no actions mapped).
const mapDispatchToProps = (dispatch: any) => {
  return {};
};

// Connect ProfileInformation component to Redux store and export.
export default connect(mapStateToProps, mapDispatchToProps)(ProfileInformation);

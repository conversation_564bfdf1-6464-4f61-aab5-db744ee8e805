import React, { useState, useCallback, useEffect } from 'react';
import { Form, Input } from 'semantic-ui-react';
import { EVehicleCondition } from './types/vehicle_condition_types';
import { VariantProduct } from '../../services/types/catalaogueTypes';
import currencyFormat from '../../helpers/currencyFormat';
import { IPromoCode } from '../../services/types/promoServiceTypes';
import SelectPromoCode from '../SelectPromoCode/SelectPromoCode';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { useSelector } from 'react-redux';
import ClientEntity from '../../entities/ClientEntity';
import { getClientProfile } from '../../helpers/clientHelper';
import { ICreateOfferParams } from '../../services/types/offerServiceTypes';
import { autotrimitraServices } from '../../services/autotrimitraServices';
import { offerServices } from '../../services/offerServices';
import moment from 'moment';
import { catalogueServices } from '../../services/catalogue/catalogueServices';
import { blacklistServices } from '../../services/BlackListService';
import { IClientEntityOrderHistory } from '../../entities/types/client-entity-types';
import { doc, Timestamp, writeBatch } from 'firebase/firestore';
import { firestoreIdealVer9 } from '../../services/myFirebase';
import { AxiosError } from 'axios';
import confirmDialog from '../callableDialog/confirmDialog';
import logisticService from '../../services/logistic/logisticService';
import DatePicker from 'react-semantic-ui-datepickers';
import { SemanticDatepickerProps } from 'react-semantic-ui-datepickers/dist/types';
import { IoCarSport, IoWallet, IoCashOutline, IoCalendarOutline } from 'react-icons/io5';

const PurchaseVehicleInformation: React.FC = () => {
  // Redux state
  const conversation = useSelector((state: TMainReduxStates) => state.reducerConversation);
  const admin = useSelector((state: TMainReduxStates) => state.reducerAdmin);
  const customer = useSelector((state: TMainReduxStates) => state.customerReducer);
  const project = useSelector((state: TMainReduxStates) => state.reducerProject.project);

  // Form state
  const [area, setArea] = useState<null | { text: string; value: string }>(null);
  const [promoCode, setPromoCode] = useState<IPromoCode | null>(null);
  const [color, setColor] = useState<null | VariantProduct>(null);
  const [agentCode, setAgentCode] = useState(admin.admin?.amartaVip?.mediatorCode || '');

  // UI state
  const [lastOffer, setLastOffer] = useState<{
    offerCode: string;
    lastRequestAt: Date;
  } | null>(null);
  const [creatingOffer, setCreatingOffer] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [shouldInputIndentCode, setShouldInputIndentCode] = useState(false);
  const [indentCode, setIndentCode] = useState('');
  const [indentDate, setIndentDate] = useState<Date | null>(null);

  // Project validation
  if (!project || project.group !== 'amartamotor') {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
        <h3 className="text-red-800 font-medium mb-2">Project ini tidak bisa mengakses menu ini</h3>
        <p className="text-red-700 text-sm">Menu ini hanya tersedia untuk project Amarta Motor.</p>
      </div>
    );
  }

  const promo = {
    onChange: useCallback(
      (promo: null | IPromoCode) => {
        setPromoCode(promo);
        setAgentCode(admin.admin?.amartaVip?.mediatorCode || '');
      },
      [admin.admin?.amartaVip?.mediatorCode],
    ),
    allowVisible: useCallback(() => {
      return !!area && !!color;
    }, [area, color]),
  };

  const calculateAmount = useCallback(() => {
    let total = color?.price ?? 0;

    if (promoCode) {
      switch (promoCode.discount_type) {
        case 'nominal':
          total -= promoCode.discount_value;
          break;
        case 'percent':
          total -= (total * promoCode.discount_value) / 100;
          break;
      }
    }

    return total;
  }, [color?.price, promoCode]);

  const onSubmit = async () => {
    setCreatingOffer(true);
    try {
      const confirm = await confirmDialog({
        title: 'Konfirmasi',
        content: 'Apakah Anda yakin ingin membuat SPK dan bayar?',
      });
      if (!confirm) {
        setCreatingOffer(false);
        return;
      }

      try {
        await blacklistServices.check(conversation?.chatRoom?.contacts[0] ?? '');
        setCreatingOffer(false);
        setError('Nomor Telepon masuk dalam daftar hitam');
        return;
      } catch (e) {
        // Continue if not blacklisted
      }

      const clients = await conversation.chatRoom?.getClientEntities({
        refresh: true,
      });
      let client!: ClientEntity;
      if (clients && clients.length > 0) {
        client = clients[0];
      }

      const clientProfile = await getClientProfile(client);

      const getVehicle = await autotrimitraServices.getVehicleVariantModel({
        code: color?.variant_code ?? '',
      });

      const idCardOwner = clientProfile.clientData.details.idCardOwner;
      const idCardGuarantor = clientProfile.clientData.details.idCardGuarantor;
      const idCardOrderMaker = clientProfile.clientData.details.idCardOrderMaker;
      const familyRegister = clientProfile.clientData.details.familyRegister;

      let payload: ICreateOfferParams = {
        company: 'amarta',
        source: 'ideal',
        purchase_method: 'cash',
        area: client.profile.area?.text.toLowerCase() ?? '',
        take_vehicle_in_dealer: false,
        agent_code: agentCode,
        phone_number_owner: clientProfile.clientData.details.owner_phone_number ?? '',
        phone_number_order_maker: clientProfile.clientData.details.order_maker_phone_number ?? '',
        home_ownership_status_code: '',
        promo_code: promoCode?.promo_code ?? undefined,
        notes: '',
        id_card_owner: {
          birth_date: idCardOwner?.dateOfBirth
            ? moment(idCardOwner?.dateOfBirth.toDate()).format('YYYY-MM-DD')
            : '',
          birth_place: idCardOwner?.placeOfBirth ?? '',
          full_address: idCardOwner?.fullAddress ?? '',
          full_name: idCardOwner?.fullName ?? '',
          id_card_image: idCardOwner?.idCardImage ?? '',
          id_card_number: idCardOwner?.idCardNumber ?? '',
          marital_status: '',
          occupation_code: '',
        },
        id_card_order_maker: {
          birth_date: idCardOrderMaker?.dateOfBirth
            ? moment(idCardOrderMaker?.dateOfBirth.toDate()).format('YYYY-MM-DD')
            : '',
          birth_place: idCardOrderMaker?.placeOfBirth ?? '',
          full_address: idCardOrderMaker?.fullAddress ?? '',
          full_name: idCardOrderMaker?.fullName ?? '',
          id_card_image: idCardOrderMaker?.idCardImage ?? '',
          id_card_number: idCardOrderMaker?.idCardNumber ?? '',
          marital_status: '',
          occupation_code: '',
        },
        id_card_guarantor: {
          full_name: idCardGuarantor?.fullName ?? '',
          id_card_number: idCardGuarantor?.idCardNumber ?? '',
        },
        family_register_owner: {
          family_register_image: familyRegister?.familyRegisterImage ?? '',
          family_register_number: familyRegister?.familyRegisterNumber || '',
        },
        address_owner: {
          full_address: idCardOwner?.fullAddress || '',
          province_name: idCardOwner?.province.name ?? '',
          province_code: idCardOwner?.province.code ?? '',
          city_name: idCardOwner?.city.name ?? '',
          city_code: idCardOwner?.city.code ?? '',
          district_name: idCardOwner?.district.name ?? '',
          district_code: idCardOwner?.district.code ?? '',
          sub_district_name: idCardOwner?.subDistrict.name ?? '',
          sub_district_code: idCardOwner?.subDistrict.code ?? '',
          zip_code: idCardOwner?.zipCode ?? '',
        },
        address_order_maker: {
          full_address: idCardOrderMaker?.fullAddress || '',
          province_name: idCardOrderMaker?.province.name ?? '',
          province_code: idCardOrderMaker?.province.code ?? '',
          city_name: idCardOrderMaker?.city.name ?? '',
          city_code: idCardOrderMaker?.city.code ?? '',
          district_name: idCardOrderMaker?.district.name ?? '',
          district_code: idCardOrderMaker?.district.code ?? '',
          sub_district_name: idCardOrderMaker?.subDistrict.name ?? '',
          sub_district_code: idCardOrderMaker?.subDistrict.code ?? '',
          zip_code: idCardOrderMaker?.zipCode ?? '',
        },
        address_shipping: {
          full_address: idCardOrderMaker?.fullAddress || '',
          province_name: idCardOrderMaker?.province.name ?? '',
          province_code: idCardOrderMaker?.province.code ?? '',
          city_name: idCardOrderMaker?.city.name ?? '',
          city_code: idCardOrderMaker?.city.code ?? '',
          district_name: idCardOrderMaker?.district.name ?? '',
          district_code: idCardOrderMaker?.district.code ?? '',
          sub_district_name: idCardOrderMaker?.subDistrict.name ?? '',
          sub_district_code: idCardOrderMaker?.subDistrict.code ?? '',
          zip_code: idCardOrderMaker?.zipCode ?? '',
        },
        vehicle: {
          brand_name: getVehicle?.data[0].brand_name ?? '',
          brand_uuid: getVehicle?.data[0].brand_uuid ?? '',
          model_name: getVehicle?.data[0].model_name ?? '',
          model_uuid: getVehicle?.data[0].model_uuid ?? '',
          variant_name: getVehicle?.data[0].variant_name ?? '',
          variant_uuid: getVehicle?.data[0].variant_uuid ?? '',
          variant_code: color?.variant_code ?? '',
          color: color?.color_name ?? '',
          color_code: color?.color_code ?? '',
        },
        mediator_code: admin.admin!.amartaVip?.mediatorCode || '',
        mediator_name: admin.admin!.amartaVip?.mediatorName || '',
        indent_code: indentCode || undefined,
        indent_date: indentDate ? moment(indentDate).format('YYYY-MM-DD') : undefined,
      };

      await client.refresh();
      const createOffer = await offerServices.createOffer(payload);

      const batch = writeBatch(firestoreIdealVer9);

      batch.update(client.ref!, {
        'cash_offer.offer_code': createOffer?.data.offer_code,
        'cash_offer.bill_code': createOffer?.data.bill_code,
        'cash_offer.last_request_at': new Date(),
      });

      if (conversation.chatRoom && conversation.chatRoom.ref) {
        const projectRef = conversation.chatRoom.ref.parent.parent!;
        const labelDoc = doc(projectRef, '/labels/Xx4CclfRD8cfKHwjAKOJ');
        batch.update(conversation.chatRoom.ref, {
          label: labelDoc,
          label_updated_at: Timestamp.now(),
        });
      }

      const bookHistory: IClientEntityOrderHistory = {
        offer_code: createOffer?.data.offer_code ?? '',
        order_code: '',
        created_at: Timestamp.now(),
        source: 'IDEAL',
        survey_order_type: 'vehicle',
        payment_scheme: 'CASH',
        createdByAdminIdeal: {
          ref: admin.admin!.ref,
          name: admin.admin!.name,
          email: admin.admin!.email,
        },
        orderCreatedBy: admin.admin!.email,
      };

      await client.addNewOrderHistory(bookHistory, batch);
      setSuccess('Berhasil membuat offer');
      await batch.commit();
    } catch (e: any) {
      const error = e as AxiosError<any>;
      setError('Terjadi kesalahan pada saat membuat offer :' + error.response?.data.error.message);
    }

    setCreatingOffer(false);
    fetch();
  };

  const fetch = useCallback(async () => {
    const clients = await conversation.chatRoom?.getClientEntities({
      refresh: true,
    });
    let client!: ClientEntity;
    if (clients && clients.length > 0) {
      client = clients[0];
    }

    if (!client.profile.area?.text || !client.dream_vehicle?.variant_code) return;

    const loadVehicle = await catalogueServices.getVariantByAreaAMH({
      area: client.profile.area?.text ?? '',
      variantCode: client.dream_vehicle?.variant_code ?? '',
    });

    const theVehicle = loadVehicle?.data[0];

    if (client.cash_offer) {
      setLastOffer({
        lastRequestAt: client.cash_offer.last_request_at.toDate(),
        offerCode: client.cash_offer.offer_code,
      });
    }

    if (client.profile.area) {
      setArea({
        ...client.profile.area,
      });
    }

    if (!!client.dream_vehicle) {
      setColor(
        client.dream_vehicle.color_code
          ? ({
              variant_code: client.dream_vehicle.variant_code,
              variant_name: client.dream_vehicle.variant_name,
              model_name: client.dream_vehicle.model_name,
              color_code: client.dream_vehicle.color_code,
              color_name: client.dream_vehicle.color_name,
              price: theVehicle?.price ?? 0,
            } as any)
          : null,
      );
    }
  }, [conversation.chatRoom]);

  const checkShouldInputIndentCode = useCallback(() => {
    logisticService
      .getOrderPolicy({
        vehicle_model: color?.model_name || '',
      })
      .then((orderPolicy) => {
        setShouldInputIndentCode(orderPolicy.data.data.indent_code || false);
      })
      .catch(() => {
        setShouldInputIndentCode(false);
      });
  }, [color?.model_name]);

  useEffect(() => {
    fetch().then(() => {
      setTimeout(() => {
        checkShouldInputIndentCode();
      }, 500);
    });
  }, [fetch, checkShouldInputIndentCode]);

  const handleIndentCodeChange = useCallback((_e: any, { value }: { value: string }) => {
    setIndentCode(value);
  }, []);

  const handleIndentDateChange = useCallback((_e: any, data: SemanticDatepickerProps) => {
    setIndentDate(data.value as Date | null);
  }, []);

  if (!area || !color) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
        <h3 className="text-red-800 font-medium mb-2">Tidak Dapat Menentukan Pembelian Tunai</h3>
        <ul className="list-disc pl-5 text-red-700 text-sm">
          <li>
            Pastikan <strong>Area</strong> sudah ditentukan.
          </li>
          <li>
            Pastikan <strong>Kendaraan yang Diinginkan</strong> sudah ditentukan hingga ke warna
            kendaraan.
          </li>
        </ul>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Vehicle Information Card */}
      <div className="bg-white rounded-lg p-6 border border-gray-100">
        <div className="flex items-center gap-3 mb-4">
          <IoCarSport className="text-xl text-blue-600" />
          <h3 className="text-lg font-medium text-gray-900">Kendaraan yang diinginkan</h3>
        </div>
        <div className="space-y-2">
          <p className="text-gray-700">{color?.variant_name}</p>
          <p className="text-gray-600 text-sm">
            {`${color?.color_code} ${color?.color_name}`.toUpperCase()}
          </p>
          <p className="text-gray-600 text-sm">
            Tahun: {customer.client?.dream_vehicle?.year || '-'}
          </p>
          <p className="text-lg font-semibold text-blue-600">{currencyFormat(color?.price ?? 0)}</p>
          {admin.admin?.amartaVip && (
            <div className="mt-4 p-3 bg-green-50 rounded-lg space-y-1">
              <p className="text-sm text-green-700">
                <span className="font-medium">Kode Agen:</span> {admin.admin.amartaVip.mediatorCode}
              </p>
              <p className="text-sm text-green-700">
                <span className="font-medium">Nama Agen:</span> {admin.admin.amartaVip.mediatorName}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Last Offer Information */}
      {lastOffer && (
        <div className="bg-white rounded-lg p-6 border border-gray-100">
          <div className="flex items-center gap-3 mb-4">
            <IoWallet className="text-xl text-green-600" />
            <h3 className="text-lg font-medium text-gray-900">Informasi Penawaran Terakhir</h3>
          </div>
          <div className="space-y-2">
            <p className="text-gray-700">
              Kode: <span className="font-medium">{lastOffer.offerCode}</span>
            </p>
            <div className="flex items-center gap-2 text-gray-600 text-sm">
              <IoCalendarOutline className="text-base" />
              <span>{moment(lastOffer.lastRequestAt).format('DD MMM YYYY - HH:mm')}</span>
            </div>
          </div>
        </div>
      )}

      {/* Main Form */}
      <div className="bg-white rounded-lg p-6 border border-gray-100">
        <Form>
          <div className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <Form.Field>
                <label className="text-sm font-medium text-gray-700">Area</label>
                <div className="mt-1 text-gray-900">{area?.text}</div>
              </Form.Field>

              <Form.Field>
                <label className="text-sm font-medium text-gray-700">
                  Kendaraan yang diinginkan
                </label>
                <div className="mt-1 text-gray-900">
                  {`${color?.variant_name} - ${color?.color_code} ${color?.color_name}`.toUpperCase()}
                </div>
              </Form.Field>

              <Form.Field>
                <label className="text-sm font-medium text-gray-700">OTR Kendaraan</label>
                <div className="mt-1 text-gray-900 font-semibold">
                  {currencyFormat(color?.price ?? 0)}
                </div>
              </Form.Field>
            </div>

            {/* Promo and Payment */}
            <div className="space-y-4">
              <Form.Field>
                <label className="text-sm font-medium text-gray-700">Kode Promo</label>
                {promo.allowVisible() ? (
                  <SelectPromoCode
                    cityGroup={area!.text}
                    vehicleModel={color?.model_name ?? ''}
                    vehicleVariant={color?.variant_code ?? ''}
                    selectedValue={promoCode?.promo_code ?? null}
                    onChange={promo.onChange}
                  />
                ) : (
                  <div className="mt-1 text-gray-500">Pilih Kode Promo</div>
                )}
              </Form.Field>

              <Form.Field>
                <label className="text-sm font-medium text-gray-700">Total Pembayaran</label>
                <div className="mt-1 text-xl font-semibold text-blue-600">
                  {currencyFormat(calculateAmount())}
                </div>
              </Form.Field>
            </div>

            {/* Indent Code Form */}
            {shouldInputIndentCode && (
              <div className="space-y-4 border-t border-gray-100 pt-6">
                <Form.Field>
                  <label className="text-sm font-medium text-gray-700">Kode Indent</label>
                  <Input
                    placeholder="Kode Indent"
                    value={indentCode}
                    onChange={handleIndentCodeChange}
                    className="w-full mt-1"
                  />
                </Form.Field>
                <Form.Field>
                  <label className="text-sm font-medium text-gray-700">Tanggal Indent</label>
                  <DatePicker
                    placeholder="Pilih Tanggal Indent"
                    value={indentDate}
                    onChange={handleIndentDateChange}
                    format="DD-MM-YYYY"
                    className="w-full mt-1"
                  />
                </Form.Field>
              </div>
            )}

            {/* Action Button */}
            <div className="pt-6 border-t border-gray-100">
              <button
                type="button"
                onClick={onSubmit}
                disabled={creatingOffer}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:bg-blue-300 flex items-center justify-center gap-2"
              >
                <IoCashOutline className="text-lg" />
                <span>{creatingOffer ? 'Memproses...' : 'Buat SPK & Bayar'}</span>
              </button>
            </div>
          </div>
        </Form>
      </div>

      {/* Status Messages */}
      {success && (
        <div className="bg-green-50 border border-green-100 rounded-lg p-4 text-green-700">
          {success}
        </div>
      )}

      {error && (
        <div className="bg-red-50 border border-red-100 rounded-lg p-4 text-red-700">{error}</div>
      )}
    </div>
  );
};

export default PurchaseVehicleInformation;

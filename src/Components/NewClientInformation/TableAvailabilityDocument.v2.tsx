import React from 'react';
import { Icon, Segment } from 'semantic-ui-react';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { useDispatch, useSelector } from 'react-redux';
import { mainStore } from '../../redux/reducers';
import modalSetImageAsSlice from '../../redux/modal-capture-image/modalSetImageAs.slice';
import { MdCheckCircle, MdError } from 'react-icons/md';
import { FaIdCard, FaUsers, FaUserFriends, FaCamera } from 'react-icons/fa';
import { HiIdentification } from 'react-icons/hi';
import { TCaptureTarget } from '../../Components/ImageCapture/types/capture-types';

interface DocumentItemProps {
  title: string;
  isAvailable: boolean;
  icon: React.ReactNode;
  onClick?: () => void;
}

const DocumentItem: React.FC<DocumentItemProps> = ({ title, isAvailable, icon, onClick }) => {
  const titleClass = onClick ? 'cursor-pointer hover:text-blue-600' : '';

  return (
    <div
      className={`
                flex items-center justify-between p-4 bg-white rounded-lg
                border border-gray-100 hover:border-gray-200 transition-all
                ${isAvailable ? 'hover:shadow-sm' : ''}
            `}
    >
      <div className="flex items-center gap-3">
        <div className={`text-xl ${isAvailable ? 'text-blue-500' : 'text-gray-400'}`}>{icon}</div>
        <span
          className={`font-medium ${isAvailable ? 'text-gray-700' : 'text-gray-500'} ${titleClass}`}
          onClick={onClick}
        >
          {title}
        </span>
      </div>
      <div>
        {isAvailable ? (
          <MdCheckCircle className="w-5 h-5 text-green-500" />
        ) : (
          <MdError className="w-5 h-5 text-red-400" />
        )}
      </div>
    </div>
  );
};

const TableAvailabilityDocumentV2: React.FC = () => {
  const dispatch = useDispatch();
  const customer = useSelector((state: TMainReduxStates) => state.customerReducer);

  if (customer.fetching) {
    return (
      <Segment>
        <Icon
          name="spinner"
          loading={true}
        />{' '}
        Mohon tunggu ...
      </Segment>
    );
  }

  const handleDocumentClick = (documentTarget: TCaptureTarget) => {
    mainStore.dispatch(
      modalSetImageAsSlice.actions.open({
        type: 'UPDATE',
        documentTarget,
      }),
    );
  };

  const documents: Array<{
    title: string;
    isAvailable: boolean;
    icon: React.ReactNode;
    documentTarget: TCaptureTarget;
  }> = [
    {
      title: 'KTP PEMILIK',
      isAvailable: !!customer.client?.details.idCardOwner?.idCardNumber,
      icon: <FaIdCard />,
      documentTarget: 'idCardOwner',
    },
    {
      title: 'KTP PENJAMIN',
      isAvailable: !!customer.client?.details.idCardGuarantor?.idCardNumber,
      icon: <FaUsers />,
      documentTarget: 'idCardGuarantor',
    },
    {
      title: 'KTP PEMESAN',
      isAvailable: !!customer.client?.details.idCardOrderMaker?.idCardNumber,
      icon: <FaIdCard />,
      documentTarget: 'idCardOrderMaker',
    },
    {
      title: 'KTP PASANGAN PENJAMIN',
      isAvailable: !!customer.client?.details.idCardGuarantorSpouse?.idCardNumber,
      icon: <FaUserFriends />,
      documentTarget: 'idCardGuarantorSpouse',
    },
    {
      title: 'KARTU KELUARGA',
      isAvailable: !!customer.client?.details.familyRegister?.familyRegisterNumber,
      icon: <HiIdentification />,
      documentTarget: 'familyRegister',
    },
    {
      title: 'SELFIE',
      isAvailable: !!customer.client?.details.selfie?.selfieImage,
      icon: <FaCamera />,
      documentTarget: 'selfie',
    },
  ];

  return (
    <div className="space-y-3">
      {documents.map((doc) => (
        <DocumentItem
          key={doc.title}
          title={doc.title}
          isAvailable={doc.isAvailable}
          icon={doc.icon}
          onClick={doc.isAvailable ? () => handleDocumentClick(doc.documentTarget) : undefined}
        />
      ))}
    </div>
  );
};

export default TableAvailabilityDocumentV2;

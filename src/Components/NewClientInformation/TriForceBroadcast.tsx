import React, { Component } from 'react';
import { Button, Checkbox, Form, Segment, Select, TextArea } from 'semantic-ui-react';
import { CheckboxProps } from 'semantic-ui-react/dist/commonjs/modules/Checkbox/Checkbox';
import { DropdownProps } from 'semantic-ui-react/dist/commonjs/modules/Dropdown/Dropdown';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { connect } from 'react-redux';
import { compose } from 'redux';
import moment from 'moment';
import { updateDoc } from 'firebase/firestore';

enum EPurchaseScheme {
  CASH = 'CASH',
  CREDIT = 'CREDIT',
}

interface ITriForceBroadcastReduxProps {
  conversation: TMainReduxStates['reducerConversation'];
}

interface ITriForceBroadcastProps {}

interface ITriForceBroadcastStates {
  allowToBroadcast: boolean;
  purchaseScheme: EPurchaseScheme;
  updating: boolean;

  purchaseInformation: {
    clientId: string;
    clientName: string;
    receivedNotificationAt: Date;
    transactionCode: string;
  } | null;

  notes: string;
}

class TriForceBroadcast extends Component<
  ITriForceBroadcastProps & ITriForceBroadcastReduxProps,
  ITriForceBroadcastStates
> {
  constructor(props: ITriForceBroadcastProps & ITriForceBroadcastReduxProps) {
    super(props);

    this.state = {
      allowToBroadcast: false,
      purchaseScheme: EPurchaseScheme.CREDIT,
      updating: false,
      purchaseInformation: null,
      notes: '',
    };
  }

  allowToBroadCast = {
    onChange: async (event: React.FormEvent<HTMLInputElement>, data: CheckboxProps) => {
      await this.setState({
        allowToBroadcast: data.checked ?? false,
      });
    },
  };

  purchaseScheme = {
    onChange: (event: React.SyntheticEvent<HTMLElement>, data: DropdownProps) => {
      this.setState({
        purchaseScheme: data.value as any,
      });
    },
  };

  notesChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    this.setState({
      notes: event.target.value,
    });
  };

  submit = async () => {
    await this.setState({
      updating: true,
    });

    const clients = await this.props.conversation.chatRoom?.getClientEntities({
      refresh: true,
    });
    if (!clients) return;

    const client = clients[0];

    if (client.ref) {
      await updateDoc(client.ref, {
        broadcast: {
          allow_to_broadcast: this.state.allowToBroadcast,
          purchase_scheme: this.state.purchaseScheme,
          notes: this.state.notes,
          last_update: new Date(),
        },
      });
    }

    await this.setState({
      updating: false,
    });
  };

  fetch = async () => {
    const clients = await this.props.conversation.chatRoom?.getClientEntities({
      refresh: true,
    });
    if (!clients) return;

    const client = clients[0];

    const currentState: ITriForceBroadcastStates = { ...this.state };
    if (client.broadcast) {
      currentState.allowToBroadcast = client.broadcast.allow_to_broadcast ?? false;
      currentState.notes = client.broadcast.notes ?? '';
      currentState.purchaseScheme = (client.broadcast.purchase_scheme ??
        'CREDIT') as EPurchaseScheme;

      if (client.broadcast.is_purchased && !!client.broadcast.client_purchase) {
        const { name, client_id, notification_received_at, transaction_code } =
          client.broadcast.client_purchase;

        currentState.purchaseInformation = {
          clientId: client_id,
          clientName: name,
          receivedNotificationAt: notification_received_at.toDate(),
          transactionCode: transaction_code,
        };
      }
    }

    this.setState({
      ...currentState,
    });
  };

  componentDidMount() {
    this.fetch();
  }

  render() {
    if (this.state.purchaseInformation) {
      return (
        <Segment basic>
          Leads ini sudah dibeli pada tanggal{' '}
          <strong>
            {moment(this.state.purchaseInformation.receivedNotificationAt).format('LLL')}
          </strong>
        </Segment>
      );
    }
    return (
      <Segment basic>
        <Form>
          <Form.Field>
            <Checkbox
              onChange={this.allowToBroadCast.onChange}
              checked={this.state.allowToBroadcast}
              label={'Broadcast Client Ini'}
              disabled={this.state.updating}
            />
          </Form.Field>
          <Form.Field>
            <label>Skema Pembelian</label>
            <Select
              onChange={this.purchaseScheme.onChange}
              value={this.state.purchaseScheme}
              options={[
                {
                  text: 'Kredit',
                  value: 'CREDIT',
                },
                {
                  text: 'Tunai',
                  value: 'CASH',
                },
              ]}
            />
          </Form.Field>

          <Form.Field>
            <label>Catatan</label>
            <TextArea
              value={this.state.notes}
              onChange={this.notesChange}
            />
          </Form.Field>

          <Button
            onClick={this.submit}
            loading={this.state.updating}
          >
            Perbarui
          </Button>
        </Form>
      </Segment>
    );
  }
}

const mapStateToProps = (states: TMainReduxStates) => ({
  conversation: states.reducerConversation,
});

export default compose<React.ComponentType<ITriForceBroadcastProps>>(connect(mapStateToProps))(
  TriForceBroadcast,
);

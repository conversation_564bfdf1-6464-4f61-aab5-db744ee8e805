import React from 'react';
import UpdateNotesEditMode from './UpdateNotesEditMode';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { useSelector } from 'react-redux';
import UpdateNotesPreviewMode from './UpdateNotesPreviewMode';
import { AnimatePresence, motion } from 'framer-motion';

const UpdateNotes: React.FC = () => {
  const mode = useSelector((state: TMainReduxStates) => state.updateNotes.mode);

  return (
    <div>
      <AnimatePresence mode="wait">
        {mode === 'preview' && (
          <motion.div
            key="preview"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.2 }}
          >
            <UpdateNotesPreviewMode />
          </motion.div>
        )}
        {mode === 'edit' && (
          <motion.div
            key="edit"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.2 }}
          >
            <UpdateNotesEditMode />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default UpdateNotes;

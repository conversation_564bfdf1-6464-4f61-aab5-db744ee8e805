import React, { ChangeEvent, useState } from 'react';
import { Button, Form, Icon } from 'semantic-ui-react';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { useSelector } from 'react-redux';
import { Timestamp, updateDoc } from 'firebase/firestore';
import { mainStore } from '../../redux/reducers';
import { fetchCustomerThunk } from '../../redux/customerInfo/customerInfoSlice';
import updateNotesSlice from '../../redux/update-notes/updateNotesSlice';
import { MdSave, MdClose } from 'react-icons/md';

const UpdateNotesEditMode: React.FC = () => {
  const customer = useSelector((state: TMainReduxStates) => state.customerReducer);
  const [textInput, setTextInput] = useState(customer.client?.notes?.text || '');
  const [updating, setUpdating] = useState(false);

  const close = () => {
    mainStore.dispatch(updateNotesSlice.actions.changeMode('preview'));
  };

  const onChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    setTextInput(e.target.value);
  };

  const onSubmit = async () => {
    const { client, ref } = customer;
    if (!client || !ref) return;

    setUpdating(true);

    try {
      await updateDoc(ref, {
        notes: {
          text: textInput,
          updatedAt: Timestamp.now(),
        },
      });

      await mainStore.dispatch(
        fetchCustomerThunk({
          clientDocRef: ref,
        }) as any,
      );
      mainStore.dispatch(updateNotesSlice.actions.changeMode('preview'));
    } catch (error) {
      console.error('Error updating notes:', error);
    } finally {
      setUpdating(false);
    }
  };

  return (
    <div className="p-4">
      <Form>
        <Form.Field>
          <textarea
            value={textInput}
            onChange={onChange}
            placeholder="Tulis catatan..."
            rows={5}
            className="w-full p-3 rounded-lg border border-gray-200 
                            focus:border-blue-400 focus:ring-1 focus:ring-blue-400 
                            transition-all outline-none resize-none
                            placeholder:text-gray-400"
          />
        </Form.Field>

        <div className="flex justify-end gap-2 mt-4">
          <Button
            onClick={close}
            className="!bg-gray-100 !text-gray-700 hover:!bg-gray-200 !transition-colors"
            disabled={updating}
          >
            <div className="flex items-center gap-2">
              <MdClose />
              <span>Batal</span>
            </div>
          </Button>
          <Button
            onClick={onSubmit}
            className="!bg-blue-500 !text-white hover:!bg-blue-600 !transition-colors"
            disabled={updating}
          >
            <div className="flex items-center gap-2">
              {updating ? (
                <Icon
                  name="spinner"
                  loading
                />
              ) : (
                <MdSave />
              )}
              <span>Simpan</span>
            </div>
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default UpdateNotesEditMode;

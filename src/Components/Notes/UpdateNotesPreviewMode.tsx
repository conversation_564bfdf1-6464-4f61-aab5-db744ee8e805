import React, { Component } from 'react';
import { Button, Icon, Segment } from 'semantic-ui-react';
import { mainStore } from '../../redux/reducers';
import updateNotesSlice from '../../redux/update-notes/updateNotesSlice';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { connect } from 'react-redux';
import moment from 'moment';

interface Props {
  customer: TMainReduxStates['customerReducer'];
}

class UpdateNotesPreviewMode extends Component<Props> {
  updateNotes = () => {
    mainStore.dispatch(updateNotesSlice.actions.changeMode('edit'));
  };

  render() {
    if (this.props.customer.fetching) {
      return (
        <Segment>
          <Icon
            name={'spinner'}
            loading={true}
          />{' '}
          Mohon tunggu ...
        </Segment>
      );
    }

    return (
      <div>
        <Segment>{this.props.customer.client?.notes?.text || <i>Belum ada catatan</i>}</Segment>
        <Button onClick={this.updateNotes}>Update Catatan</Button>
        <div
          style={{
            marginTop: '6px',
          }}
        >
          <small>Terakhir diperbarui:</small> <br />
          {this.props.customer.client?.notes?.updatedAt ? (
            <strong>
              {moment(this.props.customer.client.notes.updatedAt.toDate()).format(
                'YYYY-MM-DD HH:mm',
              )}
            </strong>
          ) : (
            <i>Belum pernah</i>
          )}
        </div>
      </div>
    );
  }
}

const mapStateToProps = (states: TMainReduxStates) => {
  return {
    customer: states.customerReducer,
  };
};

export default connect(mapStateToProps)(UpdateNotesPreviewMode);

import React, { useEffect, useState } from 'react';
import { DropdownItemProps, Select } from 'semantic-ui-react';
import { catalogueServices } from '../../services/catalogue/catalogueServices';
import { DropdownProps } from 'semantic-ui-react/dist/commonjs/modules/Dropdown/Dropdown';
import { Area } from '../../services/types/catalaogueTypes';

export interface SelectAreaFromCatalogProps {
  onChange: (cityGroup: string, area: string, province_name: string, province_code: string) => void;
  value?: string | null;
  companyCode?: string | null;
  disabled?: boolean;
  clearable?: boolean;
}

const SelectAreaFromCatalog: React.FC<SelectAreaFromCatalogProps> = ({
  onChange,
  value,
  companyCode,
  disabled,
  clearable = false,
}) => {
  const [cityGroups, setCityGroups] = useState<Area[]>([]);

  const fetch = async () => {
    const fetchCity = await catalogueServices.getAvailableCityGroup({
      company: companyCode || 'amarta',
    });
    const cityGroup = fetchCity.data?.data || [];

    setCityGroups(cityGroup);
  };

  const options = (): DropdownItemProps[] => {
    return cityGroups.map((value) => {
      return {
        text: value.city_group.toUpperCase(),
        value: value.city_group.toUpperCase(),
      };
    });
  };

  useEffect(() => {
    fetch();
  }, [companyCode]);

  const handleChange = (event: React.SyntheticEvent<HTMLElement>, data: DropdownProps) => {
    const find = cityGroups.find((c) => c.city_group === data.value);
    onChange(
      find?.city_group || '',
      find?.area || '',
      find?.province_name || '',
      find?.province_code || '',
    );
  };

  return (
    <Select
      clearable={clearable}
      disabled={disabled}
      search={true}
      options={options()}
      value={value ?? ''}
      placeholder={'Pilih City Group'}
      onChange={handleChange}
    />
  );
};

export default SelectAreaFromCatalog;

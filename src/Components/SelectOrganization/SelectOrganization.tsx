import React, { useEffect, useState } from 'react';
import { Form, Select, DropdownProps } from 'semantic-ui-react';
import { getAllOrganizations, Organization } from '../../helpers/organizationHelper';
import { useSelector } from 'react-redux';
import { TMainReduxStates } from '../../redux/types/redux-types';

// Using Organization interface from helper
export type OrganizationInfo = Organization;

interface SelectOrganizationProps {
  onChange?: (organization: Organization | null) => void;
  value?: string;
  placeholder?: string;
  clearable?: boolean;
}

const SelectOrganization: React.FC<SelectOrganizationProps> = ({
  onChange,
  value,
  clearable = false,
  placeholder = 'Select Organization',
}) => {
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [loading, setLoading] = useState(true);
  const project = useSelector((state: TMainReduxStates) => state.reducerProject.project);

  useEffect(() => {
    // Load organizations using helper function
    setOrganizations(getAllOrganizations());
    setLoading(false);
  }, []);

  const handleChange = (event: React.SyntheticEvent<HTMLElement>, data: DropdownProps) => {
    const selectedOrg = organizations.find((org) => org.organization === data.value) || null;
    onChange?.(selectedOrg);
  };

  const options = () => {
    return organizations.map((org) => ({
      key: org.organization,
      text: org.name,
      value: org.organization,
      disabled: project?.group !== org.group,
    }));
  };

  return (
    <Select
      clearable={clearable}
      options={options()}
      onChange={handleChange}
      value={value || ''}
      placeholder={placeholder}
      loading={loading}
      search
      selection
      fluid
    />
  );
};

export default SelectOrganization;

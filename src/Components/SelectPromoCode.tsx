import React, { Component } from 'react';
import { Form } from 'semantic-ui-react';
import { IGetPromoParams, IPromoCode } from '../services/types/promoServiceTypes';
import { promoService } from '../services/promo/promoServices';

interface Props extends Omit<IGetPromoParams, 'promo_type' | 'vehicle_brand'> {
  onChange?: (p: IPromoCode | null) => void;
  value?: string;
  disabled?: boolean;
}

interface States {
  promoCodes: IPromoCode[];
  fetchingPromoCodes: boolean;
}

class SelectPromoCode extends Component<Props, States> {
  constructor(props: Props) {
    super(props);
    this.state = {
      promoCodes: [],
      fetchingPromoCodes: false,
    };
  }

  onChange = (p: string) => {
    const find = this.state.promoCodes.find((pc) => pc.promo_code === p);
    this.props.onChange?.(find || null);
  };

  fetch = () => {
    this.setState(
      {
        fetchingPromoCodes: true,
        promoCodes: [],
      },
      async () => {
        let data: IPromoCode[] = [];
        try {
          const get = await promoService.getPromo({
            promo_type: 'new_vehicle',
            purchase_method: this.props.purchase_method,
            city_group: this.props.city_group,
            vehicle_model: this.props.vehicle_model,
            vehicle_variant: this.props.vehicle_variant,
            allow_agent: this.props.allow_agent,
          });

          data = get?.data ?? [];
        } catch (e) {}

        this.setState({
          fetchingPromoCodes: false,
          promoCodes: data,
        });
      },
    );
  };

  componentDidMount() {
    this.fetch();
  }

  render() {
    return (
      <Form.Select
        options={[
          ...this.state.promoCodes.map((p) => ({
            text: p.promo_code,
            value: p.promo_code,
          })),
        ]}
        loading={this.state.fetchingPromoCodes}
        placeholder={'Pilih Promo'}
        label={'Kode Promo'}
        onChange={(e, data) => this.onChange(data.value as string)}
        value={this.props.value}
        disabled={this.props.disabled}
        clearable={true}
      />
    );
  }
}

export default SelectPromoCode;

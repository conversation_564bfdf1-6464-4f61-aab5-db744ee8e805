import React, { Component } from 'react';
import { Button, Input, Message } from 'semantic-ui-react';
import { promoService } from '../../services/promo/promoServices';
import { IPromoCode } from '../../services/types/promoServiceTypes';
import { DropdownProps } from 'semantic-ui-react/dist/commonjs/modules/Dropdown/Dropdown';
import { InputOnChangeData } from 'semantic-ui-react/dist/commonjs/elements/Input/Input';

interface ISelectPromoCodeProps {
  cityGroup: string;
  vehicleModel: string;
  vehicleVariant: string;

  selectedValue: string | null;
  onChange: (promoCode: IPromoCode | null) => void;
}

interface ISelectPromoCodeStates {
  promoCodes: IPromoCode[];
  fetching: boolean;
  selectedPromoCode: string;

  editing: boolean;
  error: string;
}

class SelectPromoCode extends Component<ISelectPromoCodeProps, ISelectPromoCodeStates> {
  constructor(props: ISelectPromoCodeProps) {
    super(props);

    this.state = {
      promoCodes: [],
      fetching: false,
      selectedPromoCode: '',

      editing: false,
      error: '',
    };
  }

  fetch = async () => {
    this.setState(
      {
        fetching: true,
      },
      async () => {
        let currentState: ISelectPromoCodeStates = Object.assign({}, this.state);
        try {
          const fetch = await promoService.getPromo({
            promo_type: 'new_vehicle',
            city_group: this.props.cityGroup,
            purchase_method: 'cash',
            vehicle_brand: 'honda',
            vehicle_model: this.props.vehicleModel,
            vehicle_variant: this.props.vehicleVariant,
          });

          currentState.promoCodes = fetch?.data ?? [];
        } catch (e: any) {}

        currentState.fetching = false;
        this.setState({ ...currentState });
      },
    );
  };

  onChange = (event: React.SyntheticEvent<HTMLElement>, data: DropdownProps) => {
    const find = this.state.promoCodes?.find(
      (promoCode) => promoCode.promo_code === (data.value as string),
    );
    this.props.onChange(find ?? null);
  };

  componentDidMount() {
    this.fetch();
  }

  goEdit = () => {
    this.setState({
      selectedPromoCode: this.props.selectedValue ?? '',
      editing: true,
    });
  };

  onTextInputPromoChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    data: InputOnChangeData,
  ) => {
    this.setState({
      selectedPromoCode: data.value,
    });
  };

  onTagClick = (promoCode: IPromoCode) => {
    this.setState({
      selectedPromoCode: promoCode.promo_code,
    });
  };

  onApply = async () => {
    await this.setState({
      fetching: true,
    });

    let currentState = { ...this.state };
    try {
      const get = await promoService.getDetailPromo(this.state.selectedPromoCode, {
        promo_type: 'new_vehicle',
        city_group: this.props.cityGroup,
        purchase_method: 'cash',
        vehicle_brand: 'honda',
        vehicle_model: this.props.vehicleModel,
        vehicle_variant: this.props.vehicleVariant,
      });

      if (get?.data) {
        this.props.onChange(get.data);
        currentState.editing = false;
      } else {
        currentState.error = 'Kode Promo tidak dapat digunakan.';
      }
    } catch (e: any) {
      currentState.error = 'Kode Promo tidak dapat digunakan.';
    }

    currentState.fetching = false;

    await this.setState({
      ...currentState,
    });
  };

  onRemove = () => {
    this.setState({
      selectedPromoCode: '',
    });

    this.props.onChange(null);
  };

  render() {
    if (!this.props.selectedValue || this.state.editing) {
      return (
        <React.Fragment>
          {!this.state.editing && (
            <Button
              onClick={this.goEdit}
              size={'mini'}
            >
              Masukan Kode Promo
            </Button>
          )}
          {this.state.editing && (
            <React.Fragment>
              <Input
                action={
                  <Button
                    onClick={this.onApply}
                    loading={this.state.fetching}
                  >
                    Terapkan
                  </Button>
                }
                placeholder="Masukan Kode Promo"
                value={this.state.selectedPromoCode}
                onChange={this.onTextInputPromoChange}
              />
            </React.Fragment>
          )}
          {this.state.editing && (
            <div className={'promo-wrapper'}>
              <small>
                <strong>Atau Pilih Kode Promo:</strong>
              </small>
              <div className="flex flex-wrap gap-2">
                {this.state.promoCodes.map((value) => {
                  return (
                    <div
                      key={value.promo_code}
                      onClick={(event) => this.onTagClick(value)}
                      className="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded-full text-xs font-medium shadow-sm cursor-pointer transition-colors duration-200"
                    >
                      {value.promo_code}
                    </div>
                  );
                })}
              </div>
            </div>
          )}
          {this.state.error && <Message size={'mini'}>{this.state.error}</Message>}
        </React.Fragment>
      );
    } else {
      return (
        <React.Fragment>
          <div className="bg-green-500 text-white px-3 py-1 rounded-full text-xs font-medium shadow-sm inline-block">
            {this.props.selectedValue}
          </div>
          <Button
            onClick={this.goEdit}
            size={'mini'}
          >
            Ganti
          </Button>
          <Button
            size={'mini'}
            onClick={this.onRemove}
          >
            Lepas
          </Button>
        </React.Fragment>
      );
    }
  }
}

export default SelectPromoCode;

import React, { Component } from 'react';
import { IProvince } from '../../services/types/provinceTypes';
import { ICity } from '../../services/types/cityTypes';
import { IDistrict } from '../../services/types/districtTypes';
import { ISubDistrict } from '../../services/types/subDistrictTypes';
import { autotrimitraServices } from '../../services/autotrimitraServices';
import { DropdownItemProps, Form, Input, Select } from 'semantic-ui-react';
import { DropdownProps } from 'semantic-ui-react/dist/commonjs/modules/Dropdown/Dropdown';

export interface CommonRegionProps {
  code: string;
  name: string;
}

export interface SelectRegionProps {
  withLabel?: boolean;
  required?: boolean;
  province: {
    onChange: (region: CommonRegionProps | null) => void;
    selectedCode?: string;
  };
  city: {
    onChange: (region: CommonRegionProps | null) => void;
    selectedCode?: string;
  };
  district: {
    onChange: (region: CommonRegionProps | null) => void;
    selectedCode?: string;
  };
  subDistrict: {
    onChange: (region: CommonRegionProps | null, zipCode: string | null) => void;
    selectedCode?: string;
  };
  postCode: {
    selectedCode?: string;
    onChange: (zipCode: string | null) => void;
  };
  userLabel?: boolean;
}

export interface SelectRegionStates {
  provinceLoading: boolean;
  cityLoading: boolean;
  districtLoading: boolean;
  subDistrictLoading: boolean;

  provinces: IProvince[];
  cities: ICity[];
  districts: IDistrict[];
  subDistricts: ISubDistrict[];
}

class SelectRegion extends Component<SelectRegionProps, SelectRegionStates> {
  constructor(props: SelectRegionProps) {
    super(props);

    this.state = {
      provinceLoading: false,
      cityLoading: false,
      districtLoading: false,
      subDistrictLoading: false,

      provinces: [],
      cities: [],
      districts: [],
      subDistricts: [],
    };
  }

  private province = {
    fetch: async () => {
      this.setState(
        {
          provinceLoading: true,
        },
        async () => {
          try {
            const getProvince = await autotrimitraServices.getProvince();
            this.setState({
              provinces: getProvince?.data ?? [],
              provinceLoading: false,
            });
          } catch (e: any) {}
        },
      );
    },
    data: (): DropdownItemProps[] => {
      return this.state.provinces.map((value) => ({
        value: value.code,
        text: value.name.toUpperCase(),
        province: { ...value },
      }));
    },
    onChange: async (event: React.SyntheticEvent<HTMLElement>, data: DropdownProps) => {
      const province = this.state.provinces.find((value) => value.code === data.value);
      if (province) {
        this.setState(
          {
            cities: [],
            districts: [],
            subDistricts: [],
          },
          async () => {
            this.props.province.onChange({
              code: province.code,
              name: province.name,
            });
            this.props.city.onChange(null);
            this.props.district.onChange(null);
            this.props.subDistrict.onChange(null, null);
            await this.city.fetch();
          },
        );
      }
    },
  };

  private city = {
    fetch: async (provinceCode?: string) => {
      this.setState(
        {
          cityLoading: true,
        },
        async () => {
          setTimeout(async (args) => {
            if (!this.props.province.selectedCode) throw new Error();
            const getCity = await autotrimitraServices.getCities(
              provinceCode ?? this.props.province.selectedCode,
            );
            this.setState({
              cities: getCity?.data ?? [],
              cityLoading: false,
            });
          }, 1000);
        },
      );
    },
    data: (): DropdownItemProps[] => {
      return this.state.cities.map((value) => ({
        value: value.code,
        text: `${value.administrative_type} ${value.name}`.toUpperCase(),
      }));
    },
    onChange: async (event: React.SyntheticEvent<HTMLElement>, data: DropdownProps) => {
      const city = this.state.cities.find((value) => value.code === data.value);
      if (city) {
        this.setState(
          {
            districts: [],
            subDistricts: [],
          },
          async () => {
            this.props.city.onChange({
              code: city.code,
              name: city.name,
            });
            this.props.district.onChange(null);
            this.props.subDistrict.onChange(null, null);
            await this.district.fetch();
          },
        );
      }
    },
  };

  private district = {
    fetch: async (cityCode?: string) => {
      this.setState(
        {
          districtLoading: true,
        },
        async () => {
          setTimeout(async (args) => {
            if (!this.props.city.selectedCode) throw new Error();
            const getDistricts = await autotrimitraServices.getDistricts(
              cityCode ?? this.props.city.selectedCode,
            );
            this.setState({
              districts: getDistricts?.data ?? [],
              districtLoading: false,
            });
          }, 1000);
        },
      );
    },
    data: (): DropdownItemProps[] => {
      return this.state.districts.map((value) => ({
        value: value.code,
        text: value.name.toUpperCase(),
      }));
    },
    onChange: async (event: React.SyntheticEvent<HTMLElement>, data: DropdownProps) => {
      const district = this.state.districts.find((value) => value.code === data.value);
      if (district) {
        this.setState(
          {
            subDistricts: [],
          },
          async () => {
            this.props.district.onChange({
              code: district.code,
              name: district.name,
            });
            this.props.subDistrict.onChange(null, null);
            await this.subDistrict.fetch();
          },
        );
      }
    },
  };

  private subDistrict = {
    fetch: async (districtCode?: string) => {
      this.setState(
        {
          subDistrictLoading: true,
        },
        async () => {
          setTimeout(async (args) => {
            if (!this.props.district.selectedCode) throw new Error();
            const getSubDistrict = await autotrimitraServices.getSubDistricts(
              districtCode ?? this.props.district.selectedCode,
            );
            this.setState({
              subDistricts: getSubDistrict?.data ?? [],
              subDistrictLoading: false,
            });
          }, 1000);
        },
      );
    },
    data: (): DropdownItemProps[] => {
      return this.state.subDistricts.map((value) => ({
        value: value.code,
        text: value.name.toUpperCase(),
      }));
    },
    onChange: async (event: React.SyntheticEvent<HTMLElement>, data: DropdownProps) => {
      const subDistrict = this.state.subDistricts.find((value) => value.code === data.value);
      if (subDistrict) {
        this.props.subDistrict.onChange(
          {
            code: subDistrict.code,
            name: subDistrict.name,
          },
          subDistrict.postal_code?.[0] ?? '',
        );
      }
    },
  };

  private zipCode = {
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => {
      this.props.postCode.onChange(e.target.value ?? '');
    },
  };

  fetchAll = () =>
    this.province.fetch().then(() => {
      if (this.props.province.selectedCode)
        this.city.fetch(this.props.province.selectedCode).then(() => {
          if (this.props.city.selectedCode)
            this.district.fetch(this.props.city.selectedCode).then(() => {
              if (this.props.district.selectedCode)
                this.subDistrict.fetch(this.props.district.selectedCode).then();
            });
        });
    });

  async componentDidMount() {
    this.fetchAll();
  }

  render() {
    return (
      <React.Fragment>
        <Form.Group widths={'equal'}>
          <Form.Field>
            {(this.props.withLabel || this.props.userLabel) && <label>Provinsi</label>}
            <Select
              loading={this.state.provinceLoading}
              options={this.province.data()}
              onChange={this.province.onChange}
              value={this.props.province?.selectedCode || ''}
              search={true}
              placeholder={'Provinsi'}
            />
          </Form.Field>
          <Form.Field>
            {(this.props.withLabel || this.props.userLabel) && <label>Kota</label>}
            <Select
              loading={this.state.cityLoading}
              options={this.city.data()}
              onChange={this.city.onChange}
              value={this.props.city?.selectedCode || ''}
              search={true}
              placeholder={'Kota/Kabupaten'}
            />
          </Form.Field>
        </Form.Group>
        <Form.Group widths={'equal'}>
          <Form.Field>
            {(this.props.withLabel || this.props.userLabel) && <label>Kecamatan</label>}
            <Select
              loading={this.state.districtLoading}
              options={this.district.data()}
              onChange={this.district.onChange}
              value={this.props.district?.selectedCode || ''}
              search={true}
              placeholder={'Kecamatan'}
            />
          </Form.Field>
          <Form.Field>
            {(this.props.withLabel || this.props.userLabel) && <label>Kelurahan</label>}
            <Select
              loading={this.state.subDistrictLoading}
              options={this.subDistrict.data()}
              onChange={this.subDistrict.onChange}
              value={this.props.subDistrict?.selectedCode || ''}
              search={true}
              placeholder={'Kelurahan'}
            />
          </Form.Field>
        </Form.Group>
        <Form.Field required={this.props.required}>
          {(this.props.withLabel || this.props.userLabel) && <label>Kode Pos</label>}
          <Input>
            <input
              placeholder={'Kode Pos'}
              readOnly={false}
              value={this.props.postCode.selectedCode}
              onChange={this.zipCode.onChange}
            />
          </Input>
        </Form.Field>
      </React.Fragment>
    );
  }
}

export default SelectRegion;

import React, { Component } from 'react';
import { Dropdown, DropdownItemProps, Form, Input } from 'semantic-ui-react';
import {
  IBrandModel,
  IVariant,
  IVehicleModel,
} from '../../services/types/autotrimitra-services-types';
import { autotrimitraServices } from '../../services/autotrimitraServices';
import { DropdownProps } from 'semantic-ui-react/dist/commonjs/modules/Dropdown/Dropdown';
import { InputOnChangeData } from 'semantic-ui-react/dist/commonjs/elements/Input/Input';

interface SelectVehicleStates {
  brands: IBrandModel[];
  models: IVehicleModel[];
  variants: IVariant[];
  fetching: boolean;
}

interface SelectVehicleProps {
  vehicleType?: 'motorcycle' | 'car' | null;
  hideLabel?: boolean;
  useFreeTextVariant?: boolean;
  limit?: number;

  onBrandChange?: (brand: { name: string; uuid: string }) => void;
  onModelChange?: (model: { name: string; uuid: string; category: string }) => void;
  onVariantChange?: (variant: { name: string; uuid: string; code: string }) => void;
  onVariantFreeTextChange?: (text: string) => void;

  brand?: {
    uuid: string;
    name: string;
  };

  model?: {
    uuid: string;
    name: string;
    category: string;
  };
  variant?: {
    uuid: string;
    name: string;
    code: string;
  };
  variantFreeText?: string;
}

class SelectVehicleFromAutoTrimitra extends Component<SelectVehicleProps, SelectVehicleStates> {
  constructor(props: SelectVehicleProps) {
    super(props);

    this.state = {
      brands: [],
      models: [],
      variants: [],
      fetching: false,
    };
  }

  private brand = {
    fetch: async () => {
      this.setState({
        fetching: true,
        brands: [],
        models: [],
        variants: [],
      });
      let brands: IBrandModel[] = [];

      const getBrands = await autotrimitraServices.getVehicleBrand();
      brands = getBrands?.data || [];

      this.setState({
        brands,
        fetching: false,
      });
    },
    onChange: (event: React.SyntheticEvent<HTMLElement>, data: DropdownProps) => {
      const findBrand = this.state.brands.find((value) => value.brand_uuid === data.value);

      if (findBrand) {
        this.setState(
          {
            variants: [],
            models: [],
          },
          () => {
            if (this.props.onBrandChange) {
              this.props.onBrandChange({
                name: findBrand.brand_name,
                uuid: findBrand.brand_uuid,
              });
            }
            setTimeout(() => {
              this.model.fetch();
            }, 200);
          },
        );
      }
    },
    options: (): DropdownItemProps[] => {
      return this.state.brands.map((value) => {
        return {
          value: value.brand_uuid,
          text: value.brand_name.toUpperCase(),
        };
      });
    },
  };

  private model = {
    fetch: async () => {
      if (!this.props.brand?.uuid) return;
      this.setState({
        fetching: true,
        models: [],
        variants: [],
      });

      let models: IVehicleModel[] = [];
      const getModels = await autotrimitraServices.getVehicleModelBrand({
        brandUuid: this.props.brand.uuid,
        category: this.props.vehicleType || undefined,
      });
      models = getModels?.data || [];

      this.setState({
        models,
        fetching: false,
      });
    },
    onChange: (event: React.SyntheticEvent<HTMLElement>, data: DropdownProps) => {
      const findModel = this.state.models.find((value) => value.model_uuid === data.value);
      if (findModel) {
        this.setState(
          {
            variants: [],
          },
          () => {
            if (this.props.onModelChange) {
              this.props.onModelChange({
                uuid: findModel.model_uuid,
                category: findModel.category,
                name: findModel.model_name.toUpperCase(),
              });
            }
            setTimeout(() => {
              this.variant.fetch();
            }, 200);
          },
        );
      }
    },
    options: (): DropdownItemProps[] => {
      return this.state.models.map((value) => {
        return {
          value: value.model_uuid,
          text: `${value.model_name} (${value.category})`.toUpperCase(),
        };
      });
    },
  };

  private variant = {
    onChange: (event: React.SyntheticEvent<HTMLElement>, data: DropdownProps) => {
      const findVariant = this.state.variants.find((value) => value.variant_uuid === data.value);
      if (findVariant) {
        if (this.props.onVariantChange) {
          this.props.onVariantChange({
            code: findVariant.code,
            name: findVariant.variant_name,
            uuid: findVariant.variant_uuid,
          });
        }
      }
    },
    fetch: async () => {
      if (!this.props.model?.uuid) return;

      this.setState({
        variants: [],
        fetching: true,
      });

      const currentState = { ...this.state } as SelectVehicleStates;
      let variants: IVariant[] = [];
      const getVariants = await autotrimitraServices.getVehicleVariantModel({
        modelUuid: this.props.model?.uuid,
      });
      variants = getVariants?.data || [];

      this.setState({
        variants,
        fetching: false,
      });
    },
    options: (): DropdownItemProps[] => {
      return this.state.variants.map((value) => {
        return {
          value: value.variant_uuid,
          text: `${value.variant_name} - ${value.code_vin}`.toUpperCase(),
          description: value.code_vin,
        };
      });
    },
  };

  onFreeTextVariantChange = (e: any, d: InputOnChangeData) => {
    if (this.props.onVariantFreeTextChange) {
      this.props.onVariantFreeTextChange(d.value);
    }
  };

  componentDidMount() {
    const { limit } = this.props;
    if (limit === 1) {
      // Hanya memuat brand
      this.brand.fetch();
    } else if (limit === 2) {
      // Asumsikan brand sudah diberikan melalui props, maka muat model
      this.model.fetch();
    } else if (limit === 3) {
      // Asumsikan brand dan model sudah diberikan melalui props, maka muat variant
      this.variant.fetch();
    } else {
      // Jalur default: load secara berurutan
      this.brand.fetch().then(() => {
        if (this.props.brand?.uuid) {
          this.model.fetch().then(() => {
            if (this.props.model?.uuid) this.variant.fetch();
          });
        }
      });
    }
  }

  render() {
    const { limit } = this.props;

    // Jika limit tertentu diberikan, hanya tampilkan field yang bersangkutan
    if (limit === 1) {
      // Tampilkan hanya Brand
      return (
        <Form.Field required={true}>
          <label>Brand</label>
          <Dropdown
            value={this.props.brand?.uuid || ''}
            onChange={this.brand.onChange}
            options={this.brand.options()}
            placeholder={'Pilih Brand'}
            selection
            fluid
            loading={this.state.fetching}
            search
            disabled={this.state.fetching}
          />
        </Form.Field>
      );
    } else if (limit === 2) {
      // Tampilkan hanya Model
      return (
        <Form.Field required={true}>
          <label>Model</label>
          <Dropdown
            value={this.props.model?.uuid || ''}
            onChange={this.model.onChange}
            options={this.model.options()}
            placeholder={'Pilih Model'}
            selection
            fluid
            loading={this.state.fetching}
            search
            disabled={this.state.fetching}
          />
        </Form.Field>
      );
    } else if (limit === 3) {
      // Tampilkan hanya Variant
      return (
        <Form.Field required={true}>
          <label>Variant (free text)</label>
          {!this.props.useFreeTextVariant ? (
            <Dropdown
              options={this.variant.options()}
              placeholder={'Pilih Variant'}
              selection
              onChange={this.variant.onChange}
              loading={this.state.fetching}
              fluid
              value={this.props.variant?.uuid || ''}
              search
              disabled={this.state.fetching}
            />
          ) : (
            <Input
              placeholder={'Masukan Variant'}
              onChange={this.onFreeTextVariantChange}
              value={this.props.variantFreeText}
            />
          )}
        </Form.Field>
      );
    } else {
      // Default: tampilkan semua field
      return (
        <React.Fragment>
          <Form.Field required={true}>
            <label>Brand</label>
            <Dropdown
              value={this.props.brand?.uuid || ''}
              onChange={this.brand.onChange}
              options={this.brand.options()}
              placeholder={'Pilih Brand'}
              selection
              fluid
              loading={this.state.fetching}
              search
              disabled={this.state.fetching}
            />
          </Form.Field>
          <Form.Field required={true}>
            <label>Model</label>
            <Dropdown
              value={this.props.model?.uuid || ''}
              onChange={this.model.onChange}
              options={this.model.options()}
              placeholder={'Pilih Model'}
              selection
              fluid
              loading={this.state.fetching}
              search
              disabled={this.state.fetching}
            />
          </Form.Field>
          <Form.Field required={true}>
            <label>Variant (free text)</label>
            {!this.props.useFreeTextVariant ? (
              <Dropdown
                options={this.variant.options()}
                placeholder={'Pilih Variant'}
                selection
                onChange={this.variant.onChange}
                loading={this.state.fetching}
                fluid
                value={this.props.variant?.uuid || ''}
                search
                disabled={this.state.fetching}
              />
            ) : (
              <Input
                placeholder={'Masukan Variant'}
                onChange={this.onFreeTextVariantChange}
                value={this.props.variantFreeText}
              />
            )}
          </Form.Field>
        </React.Fragment>
      );
    }
  }
}

export default SelectVehicleFromAutoTrimitra;

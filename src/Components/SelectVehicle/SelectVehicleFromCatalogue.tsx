import React, { Component } from 'react';
import { DropdownItemProps, Form, Select } from 'semantic-ui-react';
import { Model as ModelCatalogue, VariantProduct } from '../../services/types/catalaogueTypes';
import { catalogueServices } from '../../services/catalogue/catalogueServices';
import { DropdownProps } from 'semantic-ui-react/dist/commonjs/modules/Dropdown/Dropdown';
import currencyFormat from '../../helpers/currencyFormat';

export interface ISelectVehicleFromCatalogueStates {
  models: ModelCatalogue[];
  variants: VariantProduct[];
  colors: VariantProduct[];

  fetchingModels: boolean;
  fetchingVariants: boolean;
  fetchingColors: boolean;
}

export interface ISelectVehicleFromCatalogueProps {
  model?: {
    onChange?: (model: ModelCatalogue) => void;
    selected?: string;
  };

  variant?: {
    onChange?: (color: VariantProduct) => void;
    selected?: {
      code: string;
      name: string;
    };
  };

  color?: {
    onChange?: (color: VariantProduct) => void;
    selected?: {
      code: string;
      name: string;
    };
  };

  cityGroup: string;
}

class SelectVehicleFromCatalogue extends Component<
  ISelectVehicleFromCatalogueProps,
  ISelectVehicleFromCatalogueStates
> {
  constructor(props: ISelectVehicleFromCatalogueProps) {
    super(props);

    this.state = {
      models: [],
      variants: [],
      colors: [],

      fetchingModels: false,
      fetchingVariants: false,
      fetchingColors: false,
    };
  }

  private models = {
    onChange: async (event: React.SyntheticEvent<HTMLElement>, data: DropdownProps) => {
      const model = this.state.models?.find(
        (value) => value.model_name.toLowerCase() === data.value,
      );
      if (model) {
        this.setState(
          {
            variants: [],
            colors: [],
          },
          async () => {
            this.props.model?.onChange?.(model);
            await this.variants.fetch(model.model_name);
          },
        );
      }
    },
    fetch: async (area?: string) => {
      return new Promise((resolve) => {
        this.setState(
          {
            fetchingModels: true,
          },
          async () => {
            let currentState: ISelectVehicleFromCatalogueStates = JSON.parse(
              JSON.stringify(this.state),
            );

            const getModels = await catalogueServices.getModelByCityGroup({
              area: area || this.props.cityGroup,
            });

            currentState = {
              ...currentState,
              models: getModels?.data || [],
              fetchingModels: false,
              variants: [],
              colors: [],
            };

            this.setState({ ...currentState }, () => {
              resolve(true);
            });
          },
        );
      });
    },
    options: (): DropdownItemProps[] => {
      return this.state.models?.map((value) => {
        return {
          value: value.model_name.toLowerCase(),
          text: value.model_name.toUpperCase(),
        };
      });
    },
  };

  private variants = {
    fetch: async (modelName?: string) => {
      return new Promise((resolve) => {
        this.setState(
          {
            fetchingVariants: true,
          },
          async () => {
            let currentState = { ...this.state };

            const getVariants = await catalogueServices.getVariantByAreaAMH({
              area: this.props.cityGroup,
              modelName: modelName || this.props.model?.selected || '',
            });

            currentState = {
              ...currentState,
              variants: getVariants?.data ?? [],
              fetchingVariants: false,
            };
            this.setState({ ...currentState }, () => {
              resolve(true);
            });
          },
        );
      });
    },
    onChange: async (event: React.SyntheticEvent<HTMLElement>, data: DropdownProps) => {
      const variant = this.state.variants?.find((value) => value.variant_code === data.value);

      if (variant) {
        this.setState(
          {
            colors: [],
          },
          async () => {
            this.props.variant?.onChange?.(variant);
            await this.colors.fetch(variant.variant_code);
          },
        );
      }
    },
    options: (): DropdownItemProps[] => {
      return this.state.variants?.map((value) => {
        return {
          value: value.variant_code,
          text: `${value.variant_name.toUpperCase()} - ${value.variant_code}`,
          description: currencyFormat(value.price),
        };
      });
    },
  };

  private colors = {
    fetch: async (code?: string) => {
      return new Promise((resolve) => {
        this.setState(
          {
            fetchingColors: true,
          },
          async () => {
            let currentState = Object.assign({}, JSON.parse(JSON.stringify(this.state)));

            const getVariantColors = await catalogueServices.getVariantByAreaAMH({
              area: this.props.cityGroup,
              variantCode: code ?? this.props.variant?.selected?.code,
            });

            currentState.colors = (getVariantColors?.data as VariantProduct[]) ?? [];
            currentState.fetchingColors = false;

            this.setState({ ...currentState }, () => {
              resolve(true);
            });
          },
        );
      });
    },
    options: (): DropdownItemProps[] => {
      return this.state.colors?.map((value) => {
        return {
          value: value.color_code,
          text: `${value.color_name.toUpperCase()} - ${value.color_code}`,
          description: currencyFormat(value.price),
        };
      });
    },
    onChange: async (event: React.SyntheticEvent<HTMLElement>, data: DropdownProps) => {
      const color = this.state.colors?.find((value) => value.color_code === data.value);

      if (color) {
        this.props.color?.onChange?.(color);
      }
    },
  };

  componentDidMount() {
    if (this.props.cityGroup) {
      this.models.fetch(this.props.cityGroup).then(() => {
        if (this.props.model?.selected) {
          this.variants.fetch(this.props.model.selected).then(() => {
            if (this.props.variant?.selected) {
              this.colors.fetch(this.props.variant.selected.code);
            }
          });
        }
      });
    }
  }

  render() {
    return (
      <React.Fragment>
        <Form.Field required={true}>
          <label>Model</label>
          <Select
            search={true}
            options={this.models.options()}
            placeholder={'Model'}
            loading={this.state.fetchingModels}
            onChange={this.models.onChange}
            value={this.props.model?.selected?.toLowerCase() || ''}
          />
        </Form.Field>
        <Form.Field required={true}>
          <label>Variant</label>
          <Select
            search={true}
            options={this.variants.options()}
            onChange={this.variants.onChange}
            placeholder={'Variant'}
            value={this.props.variant?.selected?.code || ''}
            loading={this.state.fetchingVariants}
          />
        </Form.Field>
        <Form.Field required={true}>
          <label>Warna</label>
          {!this.state.fetchingColors && (
            <Select
              options={this.colors.options()}
              value={this.props.color?.selected?.code || ''}
              placeholder={'Warna'}
              onChange={this.colors.onChange}
            />
          )}
        </Form.Field>
      </React.Fragment>
    );
  }
}

export default SelectVehicleFromCatalogue;

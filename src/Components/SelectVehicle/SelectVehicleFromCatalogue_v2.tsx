import React, { useEffect, useState } from 'react';
import { Form, Select } from 'semantic-ui-react';
import { Model as ModelCatalogue, VariantProduct } from '../../services/types/catalaogueTypes';
import { catalogueServices } from '../../services/catalogue/catalogueServices';
import { DropdownProps } from 'semantic-ui-react/dist/commonjs/modules/Dropdown/Dropdown';
import currencyFormat from '../../helpers/currencyFormat';

/**
 * Props untuk komponen SelectVehicleFromCatalogue_v2
 * @property {string} [company] - <PERSON>a perusahaan (opsional)
 * @property {string} cityGroup - Grup kota/wilayah
 * @property {1|2|3} [limit] - Batasan level yang ditampilkan:
 *                            1 = model saja,
 *                            2 = model + varian,
 *                            3 = semua field (default)
 * @property {string} [selectedModel] - Model yang terpilih
 * @property {Function} [onModelChange] - Callback saat model berubah
 * @property {string} [selectedVariant] - Varian yang terpilih
 * @property {Function} [onVariantChange] - Callback saat varian berubah
 * @property {string} [selectedColor] - <PERSON><PERSON> yang terpilih
 * @property {Function} [onColorChange] - Callback saat warna berubah
 */
interface Props {
  company?: string;
  cityGroup: string;
  limit?: 1 | 2 | 3; // Add limit prop: 1 = model only, 2 = model + variant, 3 = all fields

  selectedModel?: string;
  onModelChange?: (model: ModelCatalogue) => void;

  selectedVariant?: string;
  onVariantChange?: (variant: VariantProduct) => void;

  selectedColor?: string;
  onColorChange?: (color: VariantProduct) => void;
}

/**
 * Komponen untuk memilih kendaraan dari katalog dengan 3 level:
 * 1. Model
 * 2. Varian
 * 3. Warna
 *
 * Komponen ini menggunakan Semantic UI untuk tampilan form dan dropdown
 */
const SelectVehicleFromCatalogue_v2 = (props: Props) => {
  // State untuk menyimpan data
  const [models, setModels] = useState<ModelCatalogue[]>([]); // Daftar model
  const [variants, setVariants] = useState<VariantProduct[]>([]); // Daftar varian
  const [colors, setColors] = useState<VariantProduct[]>([]); // Daftar warna

  // State untuk loading indicator
  const [fetchingModels, setFetchingModels] = useState(false);
  const [fetchingVariants, setFetchingVariants] = useState(false);
  const [fetchingColors, setFetchingColors] = useState(false);

  /**
   * Mengambil data model dari API berdasarkan cityGroup dan company
   */
  const fetchModels = async () => {
    setFetchingModels(true);
    try {
      const getModels = await catalogueServices.getModelByCityGroup({
        area: props.cityGroup,
        company: props.company,
      });
      setModels(getModels?.data || []);
    } catch (e) {
      setModels([]);
    } finally {
      setFetchingModels(false);
    }
  };

  /**
   * Mengambil data varian dari API berdasarkan model yang dipilih
   * @param {string} modelName - Nama model yang dipilih
   */
  const fetchVariants = async (modelName: string) => {
    setFetchingVariants(true);
    try {
      const getVariants = await catalogueServices.getVariantByAreaAMH({
        area: props.cityGroup,
        modelName: modelName,
        company: props.company,
      });
      setVariants(getVariants?.data ?? []);
    } catch (e) {
      setVariants([]);
    } finally {
      setFetchingVariants(false);
    }
  };

  /**
   * Mengambil data warna dari API berdasarkan varian yang dipilih
   * @param {string} variantCode - Kode varian yang dipilih
   */
  const fetchColors = async (variantCode: string) => {
    setFetchingColors(true);
    try {
      const getVariantColors = await catalogueServices.getVariantByAreaAMH({
        area: props.cityGroup,
        variantCode: variantCode,
        company: props.company,
      });
      setColors(getVariantColors?.data ?? []);
    } catch (e) {
      setColors([]);
    } finally {
      setFetchingColors(false);
    }
  };

  /**
   * Handler saat model berubah
   * 1. Mencari model yang dipilih
   * 2. Reset data varian dan warna
   * 3. Memanggil callback onModelChange
   * 4. Mengambil data varian untuk model yang dipilih
   */
  const handleModelChange = async (
    event: React.SyntheticEvent<HTMLElement>,
    data: DropdownProps,
  ) => {
    const model = models.find((value) => value.model_name.toLowerCase() === data.value);
    if (model) {
      setVariants([]);
      setColors([]);
      props.onModelChange?.(model);
      await fetchVariants(model.model_name);
    }
  };

  /**
   * Handler saat varian berubah
   * 1. Mencari varian yang dipilih
   * 2. Reset data warna
   * 3. Memanggil callback onVariantChange
   * 4. Mengambil data warna untuk varian yang dipilih
   */
  const handleVariantChange = async (
    event: React.SyntheticEvent<HTMLElement>,
    data: DropdownProps,
  ) => {
    const variant = variants.find((value) => value.variant_code === data.value);
    if (variant) {
      setColors([]);
      props.onVariantChange?.(variant);
      await fetchColors(variant.variant_code);
    }
  };

  /**
   * Handler saat warna berubah
   * 1. Mencari warna yang dipilih
   * 2. Memanggil callback onColorChange
   */
  const handleColorChange = (event: React.SyntheticEvent<HTMLElement>, data: DropdownProps) => {
    const color = colors.find((value) => value.color_code === data.value);
    if (color) {
      props.onColorChange?.(color);
    }
  };

  /**
   * Effect untuk mengambil data awal berdasarkan props yang diberikan
   * - Jika limit = 1: hanya mengambil model
   * - Jika limit = 2: mengambil model dan varian
   * - Default: mengambil semua data (model, varian, dan warna)
   */
  useEffect(() => {
    if (props.cityGroup) {
      if (props.limit === 1) {
        // Only fetch models
        fetchModels();
      } else if (props.limit === 2 && props.selectedModel) {
        // Fetch models and variants
        fetchModels().then(() => {
          if (props.selectedModel) {
            fetchVariants(props.selectedModel);
          }
        });
      } else {
        // Default behavior - fetch all as needed
        fetchModels().then(() => {
          if (props.selectedModel) {
            fetchVariants(props.selectedModel).then(() => {
              if (props.selectedVariant) {
                fetchColors(props.selectedVariant);
              }
            });
          }
        });
      }
    }
  }, [props.cityGroup, props.limit]);

  const modelOptions = models.map((value) => ({
    value: value.model_name.toLowerCase(),
    text: value.model_name.toUpperCase(),
  }));

  const variantOptions = variants.map((value) => ({
    value: value.variant_code,
    text: `${value.variant_name.toUpperCase()} - ${value.variant_code}`,
    description: currencyFormat(value.price),
  }));

  const colorOptions = colors.map((value) => ({
    value: value.color_code,
    text: `${value.color_name.toUpperCase()} - ${value.color_code}`,
    description: currencyFormat(value.price),
  }));

  return (
    <React.Fragment>
      {/* Field Model - selalu ditampilkan */}
      <Form.Field required={true}>
        <label>Model</label>
        <Select
          search={true}
          options={modelOptions}
          placeholder={'Model'}
          loading={fetchingModels}
          onChange={handleModelChange}
          value={props.selectedModel?.toLowerCase() || ''}
        />
      </Form.Field>

      {/* Field Varian - ditampilkan jika limit >= 2 atau tidak ditentukan */}
      {(!props.limit || props.limit >= 2) && (
        <Form.Field required={true}>
          <label>Variant</label>
          <Select
            search={true}
            options={variantOptions}
            onChange={handleVariantChange}
            placeholder={'Variant'}
            value={props.selectedVariant || ''}
            loading={fetchingVariants}
          />
        </Form.Field>
      )}

      {/* Field Warna - ditampilkan jika limit >= 3 atau tidak ditentukan */}
      {(!props.limit || props.limit >= 3) && (
        <Form.Field required={true}>
          <label>Warna</label>
          {!fetchingColors && (
            <Select
              options={colorOptions}
              value={props.selectedColor || ''}
              placeholder={'Warna'}
              onChange={handleColorChange}
            />
          )}
        </Form.Field>
      )}
    </React.Fragment>
  );
};

export default SelectVehicleFromCatalogue_v2;

import React, { Component } from 'react';
import {
  Button,
  Divider,
  Form,
  Message,
  Select,
  Table,
  TableBody,
  TableCell,
  TableRow,
} from 'semantic-ui-react';
import SelectRegionWithLimit from '../SelectRegion/SelectRegionWithLimit';
import { otodisServices } from '../../services/otodisServices';
import { OtodisDealer } from '../../services/types/otodisServiceTypes';
import { CommonRegionProps } from '../SelectRegion/SelectRegion';
import logisticService from '../../services/logistic/logisticService';
import { ResponseGetShipmentCost } from '../../services/logistic/logisticService.types';
import currencyFormat from '../../helpers/currencyFormat';
import { AxiosError } from 'axios';
import { IoRefreshOutline } from 'react-icons/io5';

interface ShipmentCostProps {}

interface ShipmentCostStates {
  otodisDealers: OtodisDealer[];
  dealer: string | null;
  province: CommonRegionProps | null;
  city: CommonRegionProps | null;
  shipmentCost: ResponseGetShipmentCost | null;
  errorMessage: string;
  loading: boolean;
}

class ShipmentCost extends Component<ShipmentCostProps, ShipmentCostStates> {
  constructor(props: ShipmentCostProps) {
    super(props);
    this.state = {
      loading: false,
      otodisDealers: [],
      dealer: null,
      province: null,
      city: null,
      shipmentCost: null,
      errorMessage: '',
    };
  }

  private region = {
    province: {
      onChange: (params: CommonRegionProps | null) =>
        this.setState({
          province: params,
        }),
    },
    city: {
      onChange: (params: CommonRegionProps | null) =>
        this.setState({
          city: params,
        }),
    },
  };

  onSubmit = async () => {
    if (!this.state.city || !this.state.dealer) {
      return;
    }

    this.setState({
      loading: true,
      shipmentCost: null,
      errorMessage: '',
    });

    try {
      const get = await logisticService.getShippingCost({
        city_code: this.state.city.code,
        dealer_code: this.state.dealer,
      });
      const data = get.data;
      this.setState({
        shipmentCost: data,
        loading: false,
      });
    } catch (e) {
      const error = e as AxiosError<any>;
      this.setState({
        errorMessage: error.response?.data.error.message,
        loading: false,
      });
    }
  };

  onDealerChange = (e: any, d: any) => {
    this.setState({
      dealer: d.value,
    });
  };

  fetchDealer = async () => {
    const fetch = await otodisServices.getDealer();
    const dataDealer = fetch?.data || [];
    this.setState({
      otodisDealers: dataDealer,
    });
  };

  total = () => {
    if (!this.state.shipmentCost) {
      return 0;
    }
    const { bpkon, pdi, pelwil, tol } = this.state.shipmentCost;
    return bpkon + pdi + pelwil + tol;
  };

  componentDidMount() {
    this.fetchDealer();
  }

  render() {
    return (
      <div className="space-y-6">
        {/* Dealer Pengiriman Section */}
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100 space-y-6">
          <h3 className="text-lg font-medium text-gray-900">Dealer Pengiriman</h3>
          <Form>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Pilih Dealer</label>
              <Select
                onChange={this.onDealerChange}
                options={this.state.otodisDealers.map((d) => ({
                  value: d.dealer_code,
                  text: d.dealer_name,
                }))}
                placeholder="Pilih Dealer"
                value={this.state.dealer || ''}
                className="w-full"
              />
            </div>
          </Form>
        </div>

        {/* Tujuan Pengiriman Section */}
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100 space-y-6">
          <h3 className="text-lg font-medium text-gray-900">Tujuan Pengiriman</h3>
          <Form>
            <div className="space-y-4">
              <SelectRegionWithLimit
                limit={2}
                province={{
                  onChange: this.region.province.onChange,
                  selectedCode: this.state.province?.code || '',
                }}
                city={{
                  onChange: this.region.city.onChange,
                  selectedCode: this.state.city?.code || '',
                }}
              />
              <button
                onClick={this.onSubmit}
                disabled={!this.state.city || !this.state.dealer || this.state.loading}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:bg-blue-300 flex items-center justify-center gap-2"
              >
                {this.state.loading ? (
                  <>
                    <IoRefreshOutline className="animate-spin" />
                    <span>Loading...</span>
                  </>
                ) : (
                  <span>Hitung Biaya Pengiriman</span>
                )}
              </button>
            </div>
          </Form>
        </div>

        {/* Hasil Section */}
        {this.state.shipmentCost && (
          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100 space-y-6">
            <h3 className="text-lg font-medium text-gray-900">Hasil Perhitungan</h3>
            <div className="grid gap-4">
              <div className="p-4 bg-gray-50 rounded-lg">
                <span className="text-sm text-gray-500">Kota Tujuan</span>
                <div className="font-medium text-gray-900">
                  {this.state.shipmentCost.city_name} - {this.state.shipmentCost.city_code}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="p-4 bg-gray-50 rounded-lg">
                  <span className="text-sm text-gray-500">bpkon</span>
                  <div className="font-medium text-gray-900">
                    {currencyFormat(this.state.shipmentCost.bpkon || 0)}
                  </div>
                </div>

                <div className="p-4 bg-gray-50 rounded-lg">
                  <span className="text-sm text-gray-500">pdi</span>
                  <div className="font-medium text-gray-900">
                    {currencyFormat(this.state.shipmentCost.pdi || 0)}
                  </div>
                </div>

                <div className="p-4 bg-gray-50 rounded-lg">
                  <span className="text-sm text-gray-500">pelwil</span>
                  <div className="font-medium text-gray-900">
                    {currencyFormat(this.state.shipmentCost.pelwil || 0)}
                  </div>
                </div>

                <div className="p-4 bg-gray-50 rounded-lg">
                  <span className="text-sm text-gray-500">tol</span>
                  <div className="font-medium text-gray-900">
                    {currencyFormat(this.state.shipmentCost.tol || 0)}
                  </div>
                </div>
              </div>

              <div className="p-4 bg-blue-50 rounded-lg">
                <span className="text-sm text-blue-500">Total Biaya</span>
                <div className="font-medium text-blue-900">{currencyFormat(this.total())}</div>
              </div>
            </div>
          </div>
        )}

        {/* Error Message */}
        {this.state.errorMessage && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <h4 className="text-red-800 font-medium mb-1">Terjadi Kesalahan</h4>
            <p className="text-red-700 text-sm">{this.state.errorMessage}</p>
          </div>
        )}
      </div>
    );
  }
}

export default ShipmentCost;

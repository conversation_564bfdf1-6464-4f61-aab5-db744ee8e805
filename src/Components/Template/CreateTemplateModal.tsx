import React, { Component } from 'react';
import { But<PERSON>, Form, Modal, Select } from 'semantic-ui-react';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { mainStore } from '../../redux/reducers';
import createTemplateSlice from '../../redux/create-template/create-template-slice';
import { collection, doc, DocumentReference, setDoc, updateDoc } from 'firebase/firestore';
import { fetchTemplateVer2 } from '../../redux/available-templates/action-available-template';
import { DropdownProps } from 'semantic-ui-react/dist/commonjs/modules/Dropdown/Dropdown';

interface Props {
  modal: TMainReduxStates['modalCreateTemplate'];
  project: TMainReduxStates['reducerProject']['project'];
  labels: TMainReduxStates['reducerAvailableLabels'];
}

interface States {
  templateBody: string;
  button1: string;
  button2: string;
  button3: string;
  creating: boolean;
  successModal: boolean;
  label: {
    name: string;
    ref: DocumentReference;
  } | null;
}

class CreateTemplateModal extends Component<Props, States> {
  constructor(props: Props) {
    super(props);

    this.state = {
      creating: false,
      templateBody: '',
      successModal: false,
      button1: '',
      button2: '',
      button3: '',
      label: null,
    };
  }

  change = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    this.setState({
      templateBody: e.target.value,
    });
  };

  changeLabel = async (event: React.SyntheticEvent<HTMLElement>, data: DropdownProps) => {
    if (!data.value) {
      this.setState({
        label: null,
      });
      return;
    }
    const find = this.props.labels.labels.find((l) => l.ref?.id === data.value);
    if (!find) return;
    this.setState({
      label: {
        ref: find.ref!,
        name: find.name,
      },
    });
  };

  changeButtonText = (
    e: React.ChangeEvent<HTMLInputElement>,
    buttonPosition: 'button1' | 'button2' | 'button3',
  ) => {
    const currentState = { ...this.state } as any;
    currentState[buttonPosition as unknown as string] = e.target.value;
    this.setState({
      ...currentState,
    });
  };

  onCreate = async () => {
    this.setState({
      creating: true,
    });

    let type = 'text';
    const text = this.state.templateBody.replace(/\n/g, '<br>');
    if (this.state.button1 || this.state.button2 || this.state.button3) {
      type = 'button';
    }

    if (!text) return;

    if (!this.props.project) return;

    const templateCollection = collection(this.props.project.ref, 'templates');
    if (!this.props.modal.updateMode) {
      try {
        await setDoc(doc(templateCollection), {
          active: true,
          out: 0,
          createdAt: new Date(),
          type: type,
          text,
          button: {
            button1: this.state.button1,
            button2: this.state.button2,
            button3: this.state.button3,
          },
          label: this.state.label,
        });
        this.setState(
          {
            creating: false,
            successModal: true,
          },
          () => {
            mainStore.dispatch(fetchTemplateVer2(this.props.project!.ref) as any);
          },
        );
      } catch (e) {
        this.setState({
          creating: false,
          successModal: false,
        });
      }
    } else {
      const templateDoc = doc(templateCollection, this.props.modal.updateMode.id);

      await updateDoc(templateDoc, {
        text,
        type: type,
        button: {
          button1: this.state.button1,
          button2: this.state.button2,
          button3: this.state.button3,
        },
        label: this.state.label,
      });

      this.setState(
        {
          creating: false,
          successModal: true,
        },
        () => {
          mainStore.dispatch(fetchTemplateVer2(this.props.project!.ref) as any);
        },
      );
    }
  };

  onClose = () => {
    this.setState({
      successModal: false,
    });
    mainStore.dispatch(createTemplateSlice.actions.close());
  };

  componentDidUpdate(prevProps: Readonly<Props>, prevState: Readonly<States>, snapshot?: any) {
    if (prevProps.modal.updateMode?.id !== this.props.modal.updateMode?.id) {
      this.setState({
        templateBody: this.props.modal.updateMode?.text ?? '',
        button1: this.props.modal.updateMode?.button?.button1 || '',
        button2: this.props.modal.updateMode?.button?.button2 || '',
        button3: this.props.modal.updateMode?.button?.button3 || '',
        label: this.props.modal.updateMode?.label || null,
      });
    }
  }

  render() {
    return (
      <React.Fragment>
        <Modal
          open={this.props.modal.open}
          size="small"
          className="!bg-gray-50"
        >
          <div className="p-6">
            {/* Header */}
            <div className="mb-6">
              <h2 className="text-2xl font-semibold text-gray-800">
                {!this.props.modal.updateMode ? 'Tambah Template' : 'Perbarui Template'}
              </h2>
            </div>

            {/* Form */}
            <div className="space-y-6">
              <Form>
                <Form.Field className="!mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Label</label>
                  <Select
                    options={this.props.labels.labels.map((l) => ({
                      text: l.name,
                      value: l.ref?.id,
                    }))}
                    placeholder="Pilih Label"
                    onChange={this.changeLabel}
                    value={this.state.label?.ref.id}
                    clearable={true}
                    className="w-full"
                  />
                </Form.Field>

                <Form.Field className="!mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Masukan Isi Template
                  </label>
                  <Form.TextArea
                    rows={15}
                    onChange={this.change}
                    value={this.state.templateBody}
                    className="!bg-white !border-gray-300 focus:!border-blue-500 focus:!ring-1 focus:!ring-blue-500"
                    placeholder="Tulis isi template disini..."
                  />
                </Form.Field>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Form.Input
                    label={
                      <span className="block text-sm font-medium text-gray-700 mb-2">Tombol 1</span>
                    }
                    placeholder="Masukan Text Pada Tombol"
                    value={this.state.button1}
                    onChange={(e) => this.changeButtonText(e, 'button1')}
                    className="w-full"
                  />
                  <Form.Input
                    label={
                      <span className="block text-sm font-medium text-gray-700 mb-2">Tombol 2</span>
                    }
                    placeholder="Masukan Text Pada Tombol"
                    value={this.state.button2}
                    onChange={(e) => this.changeButtonText(e, 'button2')}
                    className="w-full"
                  />
                  <Form.Input
                    label={
                      <span className="block text-sm font-medium text-gray-700 mb-2">Tombol 3</span>
                    }
                    placeholder="Masukan Text Pada Tombol"
                    value={this.state.button3}
                    onChange={(e) => this.changeButtonText(e, 'button3')}
                    className="w-full"
                  />
                </div>
              </Form>
            </div>

            {/* Footer Actions */}
            <div className="flex justify-end gap-2 pt-6 mt-6 border-t border-gray-200">
              <Button
                onClick={this.onCreate}
                className="!bg-blue-600 !text-white hover:!bg-blue-700"
                loading={this.state.creating}
              >
                {!this.props.modal.updateMode ? 'Tambah' : 'Perbarui'}
              </Button>
              <Button
                onClick={this.onClose}
                className="!bg-gray-800 !text-white hover:!bg-gray-900"
              >
                Batal
              </Button>
            </div>
          </div>
        </Modal>

        {/* Success Modal */}
        <Modal
          open={this.state.successModal}
          size="tiny"
          className="!bg-gray-50"
        >
          <div className="p-6">
            <div className="mb-6">
              <h3 className="text-xl font-semibold text-gray-800">Sukses</h3>
            </div>
            <p className="text-gray-600 mb-6">
              Template berhasil {!this.props.modal.updateMode ? 'ditambahkan' : 'diperbarui'}.
            </p>
            <div className="flex justify-end">
              <Button
                onClick={this.onClose}
                className="!bg-gray-800 !text-white hover:!bg-gray-900"
              >
                OK
              </Button>
            </div>
          </div>
        </Modal>
      </React.Fragment>
    );
  }
}

const mapStateToProps = (states: TMainReduxStates) => {
  return {
    modal: states.modalCreateTemplate,
    project: states.reducerProject.project,
    labels: states.reducerAvailableLabels,
  };
};

export default compose<React.ComponentType<Omit<Props, 'modal' | 'project' | 'labels'>>>(
  connect(mapStateToProps),
)(CreateTemplateModal);

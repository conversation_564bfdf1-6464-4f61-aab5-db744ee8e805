import React from 'react';
import { But<PERSON>, Icon } from 'semantic-ui-react';
import parse from 'html-react-parser';
import MessageTemplateEntity from '../../entities/MessageTemplateEntity';
import ButtonCopy from './ButtonCopy';

interface SettingTemplateCardProps {
  template: MessageTemplateEntity;
  onDelete: (template: MessageTemplateEntity) => void;
  onUpdate: (template: MessageTemplateEntity) => void;
  isLoading: boolean;
}

const SettingTemplateCard: React.FC<SettingTemplateCardProps> = ({
  template,
  onDelete,
  onUpdate,
  isLoading,
}) => {
  const hasInteractiveButtons =
    template.button &&
    template.type === 'button' &&
    [template.button.button1, template.button.button2, template.button.button3].some(
      (btn) => !!btn?.trim(),
    );

  return (
    <div className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 relative">
      {template.welcome_message && (
        <div className="absolute top-0 left-0 right-0 bg-green-500 text-white py-2 px-4 rounded-t-lg flex items-center gap-2">
          <Icon
            name="check circle"
            className="!text-white"
          />
          <span className="text-sm font-medium">Pesan Sapaan</span>
        </div>
      )}

      <div className="p-4 pt-6">
        {/* Header */}
        <div className="space-y-2 mb-4">
          {template.label && (
            <div>
              <div className="bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-medium shadow-sm inline-block">
                {template.label.name}
              </div>
            </div>
          )}
          <div className="flex items-center gap-2 text-gray-500">
            <span className="text-xs">id: {template.ref.id}</span>
            <ButtonCopy
              textToCopy={template.ref.id}
              size="mini"
            />
          </div>
        </div>

        {/* Content */}
        <div className="prose max-h-[400px] overflow-y-auto mb-4 leading-relaxed">
          {parse(template.text)}
        </div>

        {/* Interactive Buttons Section */}
        {hasInteractiveButtons && (
          <div className="border-t border-gray-100 pt-3 mb-4">
            <h4 className="text-sm font-semibold text-gray-700 mb-2">Tombol Interaktif:</h4>
            <div className="space-y-1">
              {[template.button?.button1, template.button?.button2, template.button?.button3]
                .filter((btn) => !!btn?.trim())
                .map((btn, idx) => (
                  <div
                    key={idx}
                    className="text-sm text-gray-600"
                  >
                    {idx + 1}. {btn}
                  </div>
                ))}
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-2 mt-4 border-t border-gray-100 pt-4">
          <Button
            disabled={template.welcome_message}
            size="tiny"
            className={`!min-w-[100px] ${
              template.welcome_message
                ? '!bg-gray-100 !text-gray-400'
                : '!bg-red-100 hover:!bg-red-200 !text-red-700 !border-red-200'
            } flex items-center gap-1`}
            onClick={() => onDelete(template)}
          >
            <Icon
              name="trash"
              className="!mr-1"
            />
            <span>Hapus</span>
          </Button>
          <Button
            size="tiny"
            className="!min-w-[100px] !bg-blue-100 hover:!bg-blue-200 !text-blue-700 !border-blue-200 flex items-center gap-1"
            onClick={() => onUpdate(template)}
            loading={isLoading}
          >
            <Icon
              name="pencil"
              className="!mr-1"
            />
            <span>Edit</span>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default SettingTemplateCard;

import React, { Component } from 'react';
import { Button, Form, Modal, Select } from 'semantic-ui-react';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { connect } from 'react-redux';
import { compose } from 'redux';
import Masonry, { ResponsiveMasonry } from 'react-responsive-masonry';
import { deleteDoc, writeBatch } from 'firebase/firestore';
import { mainStore } from '../../redux/reducers';
import { fetchTemplateVer2 } from '../../redux/available-templates/action-available-template';
import createTemplateSlice from '../../redux/create-template/create-template-slice';
import modalSettingsTemplateSlice from '../../redux/modal-settings-template/modalSettingsTemplateSlice';
import MessageTemplateEntity from '../../entities/MessageTemplateEntity';
import { firestoreIdealVer9 } from '../../services/myFirebase';
import { DropdownProps } from 'semantic-ui-react/dist/commonjs/modules/Dropdown/Dropdown';
import { availableTemplateSlice } from '../../redux/available-templates/reducer-available-template';
import SettingTemplateCard from './SettingTemplateCard';

interface States {
  markAsWelcomeMessageTemplateLoading: boolean;
}

interface OmitProps {
  templates: TMainReduxStates['reducerAvailableTemplate'];
  project: TMainReduxStates['reducerProject']['project'];
  modal: TMainReduxStates['modalSettingsTemplate'];
  labels: TMainReduxStates['reducerAvailableLabels'];
}

interface Props extends OmitProps {}

class SettingTemplateModal extends Component<Props, States> {
  constructor(props: Props) {
    super(props);
    this.state = {
      markAsWelcomeMessageTemplateLoading: false,
    };
  }

  openCreateModal = () => {
    mainStore.dispatch(createTemplateSlice.actions.open());
  };

  deleteTemplate = async (m: MessageTemplateEntity) => {
    if (m.welcome_message) {
      return false;
    }

    if (this.props.project?.ref) {
      await deleteDoc(m.ref);
      mainStore.dispatch(fetchTemplateVer2(this.props.project?.ref) as any);
    }
  };
  closeModal = () => {
    mainStore.dispatch(modalSettingsTemplateSlice.actions.close());
  };

  updateTemplate = async (m: MessageTemplateEntity) => {
    mainStore.dispatch(
      createTemplateSlice.actions.openUpdate({
        text: m.text,
        id: m.ref.id,
        type: m.type,
        button: m.button,
        label: m.label,
      }),
    );
  };

  changeLabel = async (event: React.SyntheticEvent<HTMLElement>, data: DropdownProps) => {
    if (!data.value) {
      mainStore.dispatch(availableTemplateSlice.actions.setFilterLabel(null));
      return;
    }
    const find = this.props.labels.labels.find((l) => l.ref?.id === data.value);
    if (!find) return;
    mainStore.dispatch(availableTemplateSlice.actions.setFilterLabel(find.ref!.id));
  };

  render() {
    return (
      <Modal
        open={this.props.modal.open}
        closeOnDimmerClick={true}
        onClose={this.closeModal}
        className="!bg-gray-50"
      >
        <div className="p-6">
          {/* Header */}
          <div className="mb-6">
            <h2 className="text-2xl font-semibold text-gray-800">Pengaturan Template</h2>
          </div>

          {/* Label Filter */}
          <div className="mb-6">
            <Form>
              <Form.Field className="!mb-0">
                <label className="block text-sm font-medium text-gray-700 mb-2">Label</label>
                <Select
                  options={this.props.labels.labels.map((l) => ({
                    text: l.name,
                    value: l.ref?.id,
                  }))}
                  placeholder="Pilih Label"
                  onChange={this.changeLabel}
                  value={this.props.templates.filter.labelId || ''}
                  clearable={true}
                  className="w-full"
                />
              </Form.Field>
            </Form>
          </div>

          {/* Template Grid */}
          <div className="mb-6">
            <ResponsiveMasonry columnsCountBreakPoints={{ 350: 1, 750: 2, 900: 3, 1400: 3 }}>
              <Masonry gutter="16px">
                {this.props.templates.filteredTemplate?.map((template) => (
                  <SettingTemplateCard
                    key={template.ref.id}
                    template={template}
                    onDelete={this.deleteTemplate}
                    onUpdate={this.updateTemplate}
                    isLoading={this.state.markAsWelcomeMessageTemplateLoading}
                  />
                ))}
              </Masonry>
            </ResponsiveMasonry>
          </div>

          {/* Footer Actions */}
          <div className="flex justify-end gap-2 pt-4 border-t border-gray-200">
            <Button
              onClick={this.openCreateModal}
              className="!bg-blue-600 !text-white hover:!bg-blue-700"
            >
              Tambah Baru
            </Button>
            <Button
              onClick={this.closeModal}
              className="!bg-gray-800 !text-white hover:!bg-gray-900"
            >
              Tutup
            </Button>
          </div>
        </div>
      </Modal>
    );
  }
}

const mapStateToProps = (states: TMainReduxStates) => {
  return {
    templates: states.reducerAvailableTemplate,
    project: states.reducerProject.project,
    modal: states.modalSettingsTemplate,
    labels: states.reducerAvailableLabels,
  };
};

export default compose(connect(mapStateToProps))(SettingTemplateModal);

import React from 'react';
import { Button } from 'semantic-ui-react';
import parse from 'html-react-parser';
import MessageTemplateEntity from '../../entities/MessageTemplateEntity';

interface TemplateCardProps {
  template: MessageTemplateEntity;
  onSendTemplate: (template: MessageTemplateEntity) => void;
}

const TemplateCard: React.FC<TemplateCardProps> = ({ template, onSendTemplate }) => {
  const hasInteractiveButtons =
    template.button &&
    (template.button.button1 || template.button.button2 || template.button.button3);

  return (
    <div className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300">
      <div className="p-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-3">
          <span className="text-xs text-gray-500">id: {template.ref.id}</span>
          {template.label && (
            <div className="bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-medium shadow-sm ml-2">
              {template.label.name}
            </div>
          )}
        </div>

        {/* Content */}
        <div className="prose max-h-[400px] overflow-y-auto mb-4 leading-relaxed">
          {parse(template.text)}
        </div>

        {/* Interactive Buttons Section */}
        {hasInteractiveButtons && (
          <div className="border-t border-gray-100 pt-3 mb-4">
            <h4 className="text-sm font-semibold text-gray-700 mb-2">Tombol Interaktif:</h4>
            <div className="space-y-1">
              {[template.button?.button1, template.button?.button2, template.button?.button3].map(
                (btn, idx) =>
                  btn && (
                    <div
                      key={idx}
                      className="text-sm text-gray-600"
                    >
                      {idx + 1}. {btn}
                    </div>
                  ),
              )}
            </div>
          </div>
        )}

        {/* Action Button */}
        <div className="mt-4">
          <Button
            basic
            size="tiny"
            onClick={() => onSendTemplate(template)}
            className="w-full !bg-blue-50 hover:!bg-blue-100 !text-blue-600 !border-blue-200"
          >
            Kirim
          </Button>
        </div>
      </div>
    </div>
  );
};

export default TemplateCard;

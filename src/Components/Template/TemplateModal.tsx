import React, { Component } from 'react';
import { Button, Form, Modal, Select } from 'semantic-ui-react';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { connect } from 'react-redux';
import MessageTemplateEntity from '../../entities/MessageTemplateEntity';
import { compose } from 'redux';
import customizeMediaQuery, { CustomizeMediaQuery } from '../hoc/CustomizeMediaQuery';
import Masonry, { ResponsiveMasonry } from 'react-responsive-masonry';
import { DropdownProps } from 'semantic-ui-react/dist/commonjs/modules/Dropdown/Dropdown';
import { mainStore } from '../../redux/reducers';
import { availableTemplateSlice } from '../../redux/available-templates/reducer-available-template';
import TemplateCard from './TemplateCard';

interface IAdditionalTemplateModalProps extends CustomizeMediaQuery {
  templates: TMainReduxStates['reducerAvailableTemplate'];
  project: TMainReduxStates['reducerProject']['project'];
  labels: TMainReduxStates['reducerAvailableLabels'];
}

interface TemplateModalProps {
  open: boolean;
  onClose: () => void;
  onSendTemplate: (template: MessageTemplateEntity) => void;
}

class TemplateModal extends Component<TemplateModalProps & IAdditionalTemplateModalProps> {
  changeLabel = async (event: React.SyntheticEvent<HTMLElement>, data: DropdownProps) => {
    if (!data.value) {
      mainStore.dispatch(availableTemplateSlice.actions.setFilterLabel(null));
      return;
    }
    const find = this.props.labels.labels.find((l) => l.ref?.id === data.value);
    if (!find) return;
    mainStore.dispatch(availableTemplateSlice.actions.setFilterLabel(find.ref!.id));
  };

  render() {
    return (
      <Modal
        open={this.props.open}
        closeOnDimmerClick={true}
        onClose={this.props.onClose}
        className="!bg-gray-50"
      >
        <div className="p-6">
          {/* Header */}
          <div className="mb-6">
            <h2 className="text-2xl font-semibold text-gray-800">Kirim Template</h2>
          </div>

          {/* Label Filter */}
          <div className="mb-6">
            <Form>
              <Form.Field className="!mb-0">
                <label className="block text-sm font-medium text-gray-700 mb-2">Label</label>
                <Select
                  options={this.props.labels.labels.map((l) => ({
                    text: l.name,
                    value: l.ref?.id,
                  }))}
                  placeholder="Pilih Label"
                  onChange={this.changeLabel}
                  value={this.props.templates.filter.labelId || ''}
                  clearable={true}
                  className="w-full"
                />
              </Form.Field>
            </Form>
          </div>

          {/* Warning Message */}
          <div className="mb-6 bg-yellow-50 border-l-4 border-yellow-400 p-4">
            <div className="flex">
              <div className="ml-3">
                <h3 className="text-sm font-medium text-yellow-800">Peringatan!!</h3>
                <p className="text-sm text-yellow-700 mt-2">
                  Tombol <strong>Kirim</strong> akan langsung mengirim pesan ke customer tanpa ada
                  pengeditan lagi.
                  <br />
                  Jadi pastikan template yang akan dikirim sudah benar.
                </p>
              </div>
            </div>
          </div>

          {/* Template Grid */}
          <div className="mb-6">
            <ResponsiveMasonry columnsCountBreakPoints={{ 350: 1, 750: 2, 900: 3, 1400: 3 }}>
              <Masonry gutter="16px">
                {this.props.templates.filteredTemplate?.map((template) => (
                  <TemplateCard
                    key={template.ref.id}
                    template={template}
                    onSendTemplate={this.props.onSendTemplate}
                  />
                ))}
              </Masonry>
            </ResponsiveMasonry>
          </div>

          {/* Footer Actions */}
          <div className="flex justify-end pt-4 border-t border-gray-200">
            <Button onClick={this.props.onClose}>Batal</Button>
          </div>
        </div>
      </Modal>
    );
  }
}

const mapStateToProps = (states: TMainReduxStates) => {
  return {
    templates: states.reducerAvailableTemplate,
    project: states.reducerProject.project,
    labels: states.reducerAvailableLabels,
  };
};

export default compose<React.ComponentType<TemplateModalProps>>(
  connect(mapStateToProps),
  customizeMediaQuery,
)(TemplateModal);

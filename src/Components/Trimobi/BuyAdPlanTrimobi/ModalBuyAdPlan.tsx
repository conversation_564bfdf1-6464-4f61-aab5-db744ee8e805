import { useDispatch, useSelector } from 'react-redux';
import { Form, Modal } from 'semantic-ui-react';

import { Button } from 'semantic-ui-react';
import { TMainReduxStates } from '../../../redux/types/redux-types';
import modalSelectPlanSlice from '../../../redux/modal-trimobi/modalSelectPlan.slice';
import { useCallback } from 'react';
import SelectPlanAd from '../SelectPlanAd';
import { AdsOption } from '../../../services/trimobi/trimobi.getPlan.response.types';
import { Package } from '../../../services/trimobi/trimobi.getPlan.response.types';
import confirmDialog from '../../callableDialog/confirmDialog';
import { mainApiServices } from '../../../services/MainApiServices';
import successDialog from '../../callableDialog/successDialog';
import errorDialog from '../../callableDialog/errorDialog';
import PlanDetailCard from './PlanDetailCard';
import currencyFormat from '../../../helpers/currencyFormat';
import { BaseSuccessResponse } from '../../../services/types/mainApiService/mainApiService.types';

interface TrimobiBuyNewPlanResponse {
  checkout: {
    bill_amount: number;
    transaction_id: string;
    bill_id: string;
    payload: {
      code: string;
      payment_issuer: string;
      notes?: string;
    };
    status: string;
  };
  qrisUrl: string;
}

const ModalSelectPlanAd = () => {
  const actions = modalSelectPlanSlice.actions;
  const customer = useSelector((state: TMainReduxStates) => state.customerReducer);
  const project = useSelector((state: TMainReduxStates) => state.reducerProject);
  const modalSelectPlan = useSelector((state: TMainReduxStates) => state.modalSelectPlan);
  const conversation = useSelector((state: TMainReduxStates) => state.reducerConversation);
  const admin = useSelector((state: TMainReduxStates) => state.reducerAdmin);
  const dispatch = useDispatch();

  const onClose = useCallback(() => {
    dispatch(actions.closeModal());
  }, [dispatch, actions]);

  const handleVehicleTypeChange = useCallback(
    (data: string) => {
      dispatch(actions.selectVehicleType(data as 'motorcycle' | 'car'));
    },
    [dispatch, actions],
  );

  // Fungsi untuk handle perubahan plan
  const handleChangePlan = useCallback(
    (plan: Package | null) => {
      dispatch(actions.selectPlan(plan));
    },
    [dispatch, actions],
  );

  // Fungsi untuk handle perubahan quantity ads
  const handleChangeQuantityAds = useCallback(
    (quantity: AdsOption | null) => {
      dispatch(actions.selectQuantityAds(quantity));
    },
    [dispatch, actions],
  );

  const onSubmit = useCallback(async () => {
    const confirm = await confirmDialog({
      title: 'Konfirmasi',
      content: `Tindakan ini akan mengirim pesan whatsapp ke nomor ${
        customer?.client?.contacts.whatsapp || '-'
      } yang berisi QRIS untuk dibayar oleh customer.`,
      okButton: 'Ya',
      cancelButton: 'Tidak',
    });

    if (!confirm) return;

    dispatch(actions.setLoading(true));

    try {
      const createBill = (await mainApiServices.trimobiBuyNewPlan({
        projectId: project?.project?.ref.id || '',
        planCode: modalSelectPlan.selectedPlan?.code || '',
        adsQuantity: modalSelectPlan.quantityAds?.ads || 0,
        customerPhoneNumber: customer?.client?.contacts.whatsapp || '',
        vehicleType: modalSelectPlan.vehicleType || '',
      })) as BaseSuccessResponse<TrimobiBuyNewPlanResponse>;

      const billAmount = createBill.success.data.checkout.bill_amount;

      const messageContent = `Pembelian paket iklan Anda telah berhasil diproses. Detail transaksi:
- ID Transaksi: ${createBill.success.data.checkout.transaction_id}
- ID Tagihan: ${createBill.success.data.checkout.bill_id}
- Jenis Paket: ${createBill.success.data.checkout.payload.code}
- Jumlah Iklan: ${modalSelectPlan.quantityAds?.ads}
- Total Pembayaran: ${currencyFormat(billAmount)}

Silakan segera selesaikan pembayaran dengan memindai QR Code yang terlampir. Pembayaran yang terlambat dapat menyebabkan penundaan aktivasi paket iklan Anda.

Catatan: ${createBill.success.data.checkout.payload.notes || 'Tidak ada catatan'}`;

      await mainApiServices.sendMessageV2({
        adminSessionPath: admin.adminSession?.ref.path || '',
        phoneNumber: customer?.client?.contacts.whatsapp || '',
        roomPath: conversation.chatRoom?.ref.path || '',
        text: messageContent,
        fileUrl: createBill.success.data.qrisUrl,
        fileType: 'image',
      });

      await successDialog({
        title: 'Success',
        content: 'Iklan berhasil dibeli',
        cancelButton: false,
      });
      onClose();
    } catch (err: any) {
      let errorMessage = '';

      if (err?.response?.data?.error?.messages) {
        errorMessage = err.response.data.error.messages;
      } else if (err instanceof Error) {
        errorMessage = err.message;
      } else if (typeof err === 'string') {
        errorMessage = err;
      }

      await errorDialog({
        title: 'Error',
        content: errorMessage,
        cancelButton: false,
      });
    } finally {
      dispatch(actions.setLoading(false));
    }
  }, [
    onClose,
    customer?.client?.contacts.whatsapp,
    dispatch,
    actions,
    project?.project?.ref.id,
    modalSelectPlan.selectedPlan?.code,
    modalSelectPlan.quantityAds?.ads,
    modalSelectPlan.vehicleType,
    conversation.chatRoom?.ref.path,
    admin.adminSession?.ref.path,
  ]);

  return (
    <Modal
      open={modalSelectPlan.isOpen}
      onClose={onClose}
    >
      <Modal.Header>Beli Paket Iklan</Modal.Header>
      <Modal.Content>
        {modalSelectPlan.loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
          </div>
        ) : (
          <>
            <div className="bg-gray-50 p-4 rounded-lg mb-4">
              <div className="flex items-center space-x-4">
                <div className="flex-1">
                  <div className="text-sm font-medium text-gray-500">Nama</div>
                  <div className="font-semibold text-gray-800">
                    {customer?.client?.profile.name || '-'}
                  </div>
                </div>
                <div className="flex-1">
                  <div className="text-sm font-medium text-gray-500">Nomor Telepon</div>
                  <div className="font-semibold text-gray-800">
                    {customer?.client?.contacts.whatsapp || '-'}
                  </div>
                </div>
              </div>
            </div>
            <Form className="mb-5">
              <Form.Field required>
                <label>Jenis Kendaraan</label>
                <Form.Select
                  options={[
                    {
                      key: 'car',
                      text: 'Mobil',
                      value: 'car',
                    },
                    {
                      key: 'motorcycle',
                      text: 'Motor',
                      value: 'motorcycle',
                    },
                  ]}
                  value={modalSelectPlan.vehicleType ?? ''}
                  onChange={(_, data) => handleVehicleTypeChange(data.value as string)}
                  placeholder="Pilih jenis kendaraan"
                />
              </Form.Field>
              {modalSelectPlan.vehicleType && (
                <SelectPlanAd
                  vehicleType={modalSelectPlan.vehicleType}
                  planCode={modalSelectPlan.selectedPlan?.code}
                  quantityAds={modalSelectPlan.quantityAds?.ads}
                  onChangePlan={handleChangePlan}
                  onChangeQuantityAds={handleChangeQuantityAds}
                />
              )}
            </Form>

            {modalSelectPlan.selectedPlan && modalSelectPlan.quantityAds && (
              <PlanDetailCard
                plan={modalSelectPlan.selectedPlan}
                adsPlan={modalSelectPlan.quantityAds}
              />
            )}
          </>
        )}
      </Modal.Content>
      <Modal.Actions>
        <Button
          onClick={onClose}
          content="Cancel"
          disabled={modalSelectPlan.loading}
        />
        <Button
          onClick={onSubmit}
          color="blue"
          content={modalSelectPlan.loading ? 'Memproses...' : 'Konfirmasi'}
          loading={modalSelectPlan.loading}
          disabled={modalSelectPlan.loading}
        />
      </Modal.Actions>
    </Modal>
  );
};

export default ModalSelectPlanAd;

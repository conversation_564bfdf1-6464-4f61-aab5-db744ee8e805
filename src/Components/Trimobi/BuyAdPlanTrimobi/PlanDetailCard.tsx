import { AdsOption, Package } from '../../../services/trimobi/trimobi.getPlan.response.types';
import currencyFormat from '../../../helpers/currencyFormat';

interface PlanDetailCardProps {
  plan: Package;
  adsPlan: AdsOption;
}

const PlanDetailCard = ({ plan, adsPlan }: PlanDetailCardProps) => {
  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-xl font-semibold text-gray-800 mb-4">Detail Paket</h3>
      <div className="space-y-3">
        <div className="flex justify-between">
          <span className="text-gray-600">Nama Paket:</span>
          <span className="font-medium text-gray-800">{plan.name}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-600">Durasi:</span>
          <span className="font-medium text-gray-800">{plan.days} hari</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-600">Jumlah Ads:</span>
          <span className="font-medium text-gray-800">{adsPlan.ads}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-600">Total Harga:</span>
          <span className="font-medium text-blue-600">{currencyFormat(adsPlan.price)}</span>
        </div>
      </div>

      <hr className="my-5 border-gray-200" />

      <div className="space-y-3">
        <h4 className="text-lg font-semibold text-gray-800">Info Paket</h4>
        <ul className="space-y-2 text-gray-600">
          {plan.information.map((info, index) => (
            <li
              key={index}
              className="flex items-start"
            >
              <svg
                className="w-4 h-4 mt-1 mr-2 flex-shrink-0 text-blue-500"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  clipRule="evenodd"
                />
              </svg>
              <span className="text-sm leading-relaxed">{info}</span>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default PlanDetailCard;

import { Form } from 'semantic-ui-react';
import { NumericFormat } from 'react-number-format';
import { useDispatch, useSelector } from 'react-redux';
import { ChangeEvent, useCallback } from 'react';
import UploadImages from './UploadImages.CreateAd';
import modalCreateAdSlice from '../../../redux/modal-trimobi/modalCreateAd.slice';
import { TMainReduxStates } from '../../../redux/types/redux-types';

const AdInformation = () => {
  const actions = modalCreateAdSlice.actions;
  const dispatch = useDispatch();
  const modalCreateAd = useSelector((state: TMainReduxStates) => state.modalCreateAd);

  const onTitleChange = useCallback(
    (e: ChangeEvent<HTMLInputElement>) => {
      dispatch(actions.setTitle(e.target.value));
    },
    [dispatch, actions],
  );

  const onDescriptionChange = useCallback(
    (e: ChangeEvent<HTMLTextAreaElement>) => {
      dispatch(actions.setDescription(e.target.value));
    },
    [dispatch, actions],
  );

  const onPriceChange = useCallback(
    (value: number) => {
      dispatch(actions.setPrice(value));
    },
    [dispatch, actions],
  );

  return (
    <Form>
      <Form.Input
        label="Judul"
        placeholder="Masukkan judul"
        required
        onChange={onTitleChange}
        value={modalCreateAd.title}
      />
      <Form.TextArea
        label="Deskripsi"
        placeholder="Masukkan deskripsi"
        required
        onChange={onDescriptionChange}
        value={modalCreateAd.description}
      />
      <Form.Field required>
        <label>Harga</label>
        <NumericFormat
          customInput={Form.Input}
          placeholder="Masukkan harga yang ditawarkan"
          thousandSeparator="."
          decimalSeparator=","
          prefix="Rp "
          required
          onValueChange={(values) => onPriceChange(values.floatValue || 0)}
          value={modalCreateAd.price}
        />
      </Form.Field>
      <UploadImages
        values={modalCreateAd.images}
        onAddRow={() => {
          dispatch(actions.addImageRow());
        }}
        onDeleteRow={(uuid) => {
          dispatch(actions.deleteImageRow({ uuid }));
        }}
        onChange={(params) => {
          dispatch(actions.updateImageUrl(params));
        }}
      />
    </Form>
  );
};

export default AdInformation;

import { Form } from 'semantic-ui-react';
import { useCallback, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useDispatch } from 'react-redux';
import modalCreateAdSlice from '../../../redux/modal-trimobi/modalCreateAd.slice';
import { TMainReduxStates } from '../../../redux/types/redux-types';
import SelectRegion, { CommonRegionProps } from '../../SelectRegion/SelectRegion';

const BasicInformation = () => {
  const actions = modalCreateAdSlice.actions;
  const dispatch = useDispatch();
  const modalCreateAd = useSelector((state: TMainReduxStates) => state.modalCreateAd);
  const customer = useSelector((state: TMainReduxStates) => state.customerReducer);

  useEffect(() => {
    if (customer) {
      dispatch(actions.setFullName(customer.client?.profile.name || ''));
      dispatch(actions.setPhoneNumber(customer.client?.contacts.whatsapp || ''));
    }
  }, [customer]);

  const onChangeName = useCallback(
    (name: string) => {
      dispatch(actions.setFullName(name));
    },
    [dispatch, actions],
  );

  const onChangeProvince = useCallback(
    (province: CommonRegionProps | null) => {
      dispatch(actions.setProvince(province));
    },
    [dispatch, actions],
  );

  const onChangeCity = useCallback(
    (city: CommonRegionProps | null) => {
      dispatch(actions.setCity(city));
    },
    [dispatch, actions],
  );

  const onChangeDistrict = useCallback(
    (district: CommonRegionProps | null) => {
      dispatch(actions.setDistrict(district));
    },
    [dispatch, actions],
  );

  const onChangeSubDistrict = useCallback(
    (subDistrict: CommonRegionProps | null) => {
      dispatch(actions.setSubDistrict(subDistrict));
    },
    [dispatch, actions],
  );

  const onChangePostCode = useCallback(
    (postCode: string | null) => {
      dispatch(actions.setPostalCode(postCode || ''));
    },
    [dispatch, actions],
  );

  const onChangeAddress = useCallback(
    (address: string) => {
      dispatch(actions.setAddress(address));
    },
    [dispatch, actions],
  );

  return (
    <Form>
      <Form.Input
        label="Nama"
        required
        placeholder="Nama"
        value={modalCreateAd.fullName}
        onChange={(event, data) => onChangeName(data.value)}
      />
      <Form.Input
        label="Nomor Telepon (Hanya baca)"
        required
        placeholder="Nomor Telepon"
        value={modalCreateAd.phoneNumber}
        // onChange={(event, data) => onChangePhoneNumber(data.value)}
        readOnly
      />
      <SelectRegion
        required
        withLabel={true}
        province={{
          onChange: onChangeProvince,
          selectedCode: modalCreateAd.province?.code,
        }}
        city={{
          onChange: onChangeCity,
          selectedCode: modalCreateAd.city?.code,
        }}
        district={{
          onChange: onChangeDistrict,
          selectedCode: modalCreateAd.district?.code,
        }}
        subDistrict={{
          onChange: (region, zipCode) => {
            onChangeSubDistrict(region);
            onChangePostCode(zipCode);
          },
          selectedCode: modalCreateAd.subDistrict?.code,
        }}
        postCode={{
          selectedCode: modalCreateAd.postalCode,
          onChange: onChangePostCode,
        }}
      />
      <Form.TextArea
        label="Alamat"
        required
        value={modalCreateAd.address}
        onChange={(event, data) => onChangeAddress(data.value as string)}
      />
    </Form>
  );
};

export default BasicInformation;

import { But<PERSON>, Icon, Modal } from 'semantic-ui-react';
import { useDispatch, useSelector } from 'react-redux';
import BasicInformation from './BasicInformation.CreateAd';
import { useCallback } from 'react';
import VehicleInformation from './VehicleInformation.CreateAd';
import StepCreateAd from './Step.CreateAd';
import AdInformation from './AdInformation.CreateAd';
import * as yup from 'yup';
import modalCreateAdSlice from '../../../redux/modal-trimobi/modalCreateAd.slice';
import { TMainReduxStates } from '../../../redux/types/redux-types';
import { mainApiServices } from '../../../services/MainApiServices';
import { AdListingRequest } from '../../../services/types/mainApiService/mainApiService.trimobi.types';
import errorDialog from '../../callableDialog/errorDialog';
import successDialog from '../../callableDialog/successDialog';
import SelectPlan from './SelectPlan.CreateAd';

const ModalCreateAdTrimobi = () => {
  const actions = modalCreateAdSlice.actions;
  const modalCreateAd = useSelector((state: TMainReduxStates) => state.modalCreateAd);
  const customer = useSelector((state: TMainReduxStates) => state.customerReducer);
  const conversation = useSelector((state: TMainReduxStates) => state.reducerConversation);
  const admin = useSelector((state: TMainReduxStates) => state.reducerAdmin);
  const dispatch = useDispatch();

  const onClose = useCallback(() => {
    dispatch(actions.setIsOpen(false));
  }, [dispatch, actions]);

  // Submit handler for final form submission
  const submitButton = useCallback(async () => {
    dispatch(actions.setLoading(true));
    dispatch(actions.setErrorMessages(null));
    try {
      const payload: AdListingRequest = {
        planCode: modalCreateAd.plan?.code || '',
        vehicleType: modalCreateAd.vehicleType,
        fullName: modalCreateAd.fullName,
        phoneNumber: modalCreateAd.phoneNumber,
        province: {
          code: modalCreateAd.province?.code || '',
          name: modalCreateAd.province?.name || '',
        },
        city: {
          code: modalCreateAd.city?.code || '',
          name: modalCreateAd.city?.name || '',
        },
        district: {
          code: modalCreateAd.district?.code || '',
          name: modalCreateAd.district?.name || '',
        },
        subDistrict: {
          code: modalCreateAd.subDistrict?.code || '',
          name: modalCreateAd.subDistrict?.name || '',
        },
        address: modalCreateAd.address,
        postalCode: modalCreateAd.postalCode,
        brand: {
          uuid: modalCreateAd.brand?.uuid || '',
          name: modalCreateAd.brand?.name || '',
        },
        model: {
          uuid: modalCreateAd.model?.uuid || '',
          name: modalCreateAd.model?.name || '',
          category: modalCreateAd.model?.category || '',
        },
        variant: modalCreateAd.variant,
        licensePlate: modalCreateAd.licensePlate,
        fuelType: modalCreateAd.fuelType,
        transmission: modalCreateAd.transmission,
        engineCapacity: modalCreateAd.engineCapacity,
        year: modalCreateAd.year,
        mileage: modalCreateAd.mileage,
        completeVehicleDocuments: modalCreateAd.completeVehicleDocuments,
        activeVehicleDocuments: modalCreateAd.activeVehicleDocuments,
        ownerNameMatchDocuments: modalCreateAd.ownerNameMatchDocuments,
        allowBuyerPriceOffer: modalCreateAd.allowBuyerPriceOffer,
        title: modalCreateAd.title,
        description: modalCreateAd.description,
        price: modalCreateAd.price,
        images: modalCreateAd.images.map((image) => ({
          uuid: image.uuid,
          url: image.url || '',
        })),
      };

      const createListing = await mainApiServices.trimobiAddNewListing(payload);
      const url = createListing?.success.data.trimobiUrl;
      dispatch(actions.setLoading(false));
      dispatch(actions.setIsOpen(false));
      await successDialog({
        title: 'Berhasil',
        content: `Berhasil membuat iklan. Link iklan Anda: ${url}`,
        cancelButton: false,
      });
      if (url) {
        const messageContent = `Iklan Anda telah berhasil dibuat. Berikut detail iklan Anda:
- Judul: ${modalCreateAd.title}
- Harga: Rp ${modalCreateAd.price?.toLocaleString()}
- Link Iklan: ${url}

Silakan gunakan link di atas untuk mengelola iklan Anda.`;

        if (url) {
          await mainApiServices.sendMessageV2({
            roomPath: conversation.chatRoom?.ref.path || '',
            adminSessionPath: admin.adminSession?.ref.path || '',
            phoneNumber: customer?.client?.contacts.whatsapp || '',
            text: messageContent,
          });
        }
      }
      onClose();
    } catch (error: any) {
      dispatch(actions.setLoading(false));
      let errorMessage: string | string[] = '';
      if (error?.response?.data?.error?.messages) {
        const messages = error.response.data.error.messages;
        if (
          error.response.status === 422 &&
          error.response.data.error.type === 'UNPROCESSABLE_ENTITY'
        ) {
          errorMessage = [];
          errorMessage = Object.values(messages)
            .map((item: any) => `${item.path}: ${item.msg}`)
            .join('\n');
        } else {
          errorMessage = messages;
        }
      }
      await errorDialog({
        title: 'Error',
        content: errorMessage,
        cancelButton: false,
      });
    }
  }, [
    dispatch,
    actions,
    modalCreateAd.plan?.code,
    modalCreateAd.vehicleType,
    modalCreateAd.fullName,
    modalCreateAd.phoneNumber,
    modalCreateAd.province?.code,
    modalCreateAd.province?.name,
    modalCreateAd.city?.code,
    modalCreateAd.city?.name,
    modalCreateAd.district?.code,
    modalCreateAd.district?.name,
    modalCreateAd.subDistrict?.code,
    modalCreateAd.subDistrict?.name,
    modalCreateAd.address,
    modalCreateAd.postalCode,
    modalCreateAd.brand?.uuid,
    modalCreateAd.brand?.name,
    modalCreateAd.model?.uuid,
    modalCreateAd.model?.name,
    modalCreateAd.model?.category,
    modalCreateAd.variant,
    modalCreateAd.licensePlate,
    modalCreateAd.fuelType,
    modalCreateAd.transmission,
    modalCreateAd.engineCapacity,
    modalCreateAd.year,
    modalCreateAd.mileage,
    modalCreateAd.completeVehicleDocuments,
    modalCreateAd.activeVehicleDocuments,
    modalCreateAd.ownerNameMatchDocuments,
    modalCreateAd.allowBuyerPriceOffer,
    modalCreateAd.title,
    modalCreateAd.description,
    modalCreateAd.price,
    modalCreateAd.images,
    onClose,
    conversation.chatRoom?.ref.path,
    admin.adminSession?.ref.path,
    customer?.client?.contacts.whatsapp,
  ]);

  // Handles next button click and validation for each step
  const nextButton = useCallback(async () => {
    dispatch(actions.setErrorMessages(null));
    try {
      // Step 1: Validasi informasi dasar
      if (modalCreateAd.step === 1) {
        const validationSchema = yup.object({
          fullName: yup
            .string()
            .required('Nama lengkap wajib diisi')
            .min(3, 'Nama lengkap minimal 3 karakter'),
          phoneNumber: yup
            .string()
            .required('Nomor telepon wajib diisi')
            .matches(/^[0-9]+$/, 'Nomor telepon harus berupa angka')
            .min(10, 'Nomor telepon minimal 10 digit')
            .max(15, 'Nomor telepon maksimal 15 digit'),
          province: yup
            .object({
              code: yup.string().required(),
              name: yup.string().required(),
            })
            .required('Provinsi wajib diisi'),
          city: yup
            .object({
              code: yup.string().required(),
              name: yup.string().required(),
            })
            .required('Kota wajib diisi'),
          district: yup
            .object({
              code: yup.string().required(),
              name: yup.string().required(),
            })
            .required('Kecamatan wajib diisi'),
          subDistrict: yup
            .object({
              code: yup.string().required(),
              name: yup.string().required(),
            })
            .required('Kelurahan wajib diisi'),
          postalCode: yup
            .string()
            .required('Kode pos wajib diisi')
            .matches(/^[0-9]+$/, 'Kode pos harus berupa angka')
            .length(5, 'Kode pos harus 5 digit'),
          address: yup
            .string()
            .required('Alamat wajib diisi')
            .min(10, 'Alamat minimal 10 karakter'),
        });

        await validationSchema.validate(
          {
            fullName: modalCreateAd.fullName,
            phoneNumber: modalCreateAd.phoneNumber,
            province: modalCreateAd.province,
            city: modalCreateAd.city,
            district: modalCreateAd.district,
            subDistrict: modalCreateAd.subDistrict,
            postalCode: modalCreateAd.postalCode,
            address: modalCreateAd.address,
          },
          {
            abortEarly: false,
          },
        );
      }
      // Step 2: Validasi informasi kendaraan
      else if (modalCreateAd.step === 2) {
        const validationSchema = yup.object({
          brand: yup
            .object({
              uuid: yup.string().required(),
              name: yup.string().required(),
            })
            .required('Merek wajib diisi'),
          model: yup
            .object({
              uuid: yup.string().required(),
              name: yup.string().required(),
              category: yup.string().required(),
            })
            .required('Model wajib diisi'),
          variant: yup.string().required('Varian wajib diisi').min(2, 'Varian minimal 2 karakter'),
          mileage: yup
            .string()
            .required('Kilometer wajib diisi')
            .matches(/^[0-9]+$/, 'Kilometer harus berupa angka'),
          year: yup
            .string()
            .required('Tahun pembuatan wajib diisi')
            .matches(/^[0-9]+$/, 'Tahun harus berupa angka')
            .length(4, 'Tahun harus 4 digit'),
          licensePlate: yup
            .string()
            .required('Plat nomor wajib diisi')
            .min(4, 'Plat nomor minimal 4 karakter'),
          fuelType: yup.string().required('Bahan bakar wajib diisi'),
          transmission: yup.string().required('Transmisi wajib diisi'),
        });

        await validationSchema.validate(
          {
            brand: modalCreateAd.brand,
            model: modalCreateAd.model,
            variant: modalCreateAd.variant,
            mileage: modalCreateAd.mileage,
            year: modalCreateAd.year,
            licensePlate: modalCreateAd.licensePlate,
            fuelType: modalCreateAd.fuelType,
            transmission: modalCreateAd.transmission,
          },
          { abortEarly: false },
        );
      }
      // Step 3: Validasi informasi iklan
      else if (modalCreateAd.step === 3) {
        const validationSchema = yup.object({
          title: yup
            .string()
            .required('Judul iklan wajib diisi')
            .min(10, 'Judul iklan minimal 10 karakter'),
          description: yup
            .string()
            .required('Deskripsi wajib diisi')
            .min(10, 'Deskripsi minimal 10 karakter'),
          price: yup.number().required('Harga wajib diisi').min(1000, 'Harga minimal Rp 1.000'),
          images: yup
            .array()
            .of(
              yup.object({
                uuid: yup.string().required(),
                url: yup.string().nullable().required('Foto wajib diisi'),
              }),
            )
            .min(1, 'Minimal harus ada 1 foto')
            .required('Foto wajib diisi'),
        });

        await validationSchema.validate(
          {
            title: modalCreateAd.title,
            description: modalCreateAd.description,
            price: modalCreateAd.price,
            images: modalCreateAd.images,
          },
          { abortEarly: false },
        );
      }
      // Step 4: Validasi pilihan plan
      else if (modalCreateAd.step === 4) {
        const validationSchema = yup.object({
          plan: yup.object().required('Plan wajib dipilih'),
        });

        await validationSchema.validate(
          {
            plan: modalCreateAd.plan,
          },
          { abortEarly: false },
        );
      }
    } catch (error) {
      if (error instanceof yup.ValidationError) {
        dispatch(actions.setErrorMessages(error.errors));
      }
      return;
    }

    if (modalCreateAd.step === 4) {
      return submitButton();
    }
    dispatch(actions.setStep(modalCreateAd.step + 1));
  }, [
    dispatch,
    actions,
    modalCreateAd.step,
    modalCreateAd.fullName,
    modalCreateAd.phoneNumber,
    modalCreateAd.province,
    modalCreateAd.city,
    modalCreateAd.district,
    modalCreateAd.subDistrict,
    modalCreateAd.postalCode,
    modalCreateAd.address,
    modalCreateAd.brand,
    modalCreateAd.model,
    modalCreateAd.variant,
    modalCreateAd.mileage,
    modalCreateAd.year,
    modalCreateAd.licensePlate,
    modalCreateAd.fuelType,
    modalCreateAd.transmission,
    modalCreateAd.title,
    modalCreateAd.description,
    modalCreateAd.price,
    modalCreateAd.images,
    modalCreateAd.plan,
    submitButton,
  ]);

  // Handles previous button click to go back one step
  const prevButton = useCallback(() => {
    if (modalCreateAd.step === 1) {
      return;
    }
    dispatch(actions.setStep(modalCreateAd.step - 1));
  }, [dispatch, actions, modalCreateAd.step]);

  // Render modal with dynamic content based on current step
  return (
    <Modal open={modalCreateAd.isOpen}>
      <Modal.Header>Buat Iklan Trimobi</Modal.Header>
      <Modal.Content>
        <div className="bg-gray-50 p-4 rounded-lg mb-4">
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <div className="text-sm font-medium text-gray-500">Nama</div>
              <div className="font-semibold text-gray-800">
                {customer?.client?.profile.name || '-'}
              </div>
            </div>
            <div className="flex-1">
              <div className="text-sm font-medium text-gray-500">Nomor Telepon</div>
              <div className="font-semibold text-gray-800">
                {customer?.client?.contacts.whatsapp || '-'}
              </div>
            </div>
          </div>
        </div>
        <StepCreateAd step={modalCreateAd.step} />
        <div className="mt-4">
          {modalCreateAd.step === 1 && <BasicInformation />}
          {modalCreateAd.step === 2 && <VehicleInformation />}
          {modalCreateAd.step === 3 && <AdInformation />}
          {modalCreateAd.step === 4 && <SelectPlan />}
        </div>

        {modalCreateAd.errorMessages && (
          <div className="bg-red-50 p-4 rounded-lg mb-4 border border-red-100 mt-5">
            <div className="text-red-600 font-medium">
              {Array.isArray(modalCreateAd.errorMessages) ? (
                <ul className="list-disc list-inside space-y-1 pl-4">
                  {modalCreateAd.errorMessages.map((error) => (
                    <li key={error}>{error}</li>
                  ))}
                </ul>
              ) : (
                <div>{modalCreateAd.errorMessages}</div>
              )}
            </div>
          </div>
        )}
      </Modal.Content>
      <Modal.Actions>
        <Button
          onClick={onClose}
          color="red"
          disabled={modalCreateAd.loading}
        >
          Batalkan
        </Button>
        {modalCreateAd.step > 1 && (
          <Button
            onClick={prevButton}
            icon
            labelPosition="left"
            disabled={modalCreateAd.loading}
          >
            <Icon name="arrow left" />
            Sebelumnya
          </Button>
        )}
        {modalCreateAd.step === 4 && (
          <Button
            color="green"
            onClick={nextButton}
            loading={modalCreateAd.loading}
            disabled={modalCreateAd.loading}
          >
            Submit
          </Button>
        )}
        {modalCreateAd.step <= 3 && (
          <Button
            onClick={nextButton}
            icon
            labelPosition="right"
            loading={modalCreateAd.loading}
            disabled={modalCreateAd.loading}
          >
            Selanjutnya
            <Icon name="arrow right" />
          </Button>
        )}
      </Modal.Actions>
    </Modal>
  );
};

export default ModalCreateAdTrimobi;

import { Form, Icon, List } from 'semantic-ui-react';
import { useDispatch, useSelector } from 'react-redux';
import modalCreateAdSlice from '../../../redux/modal-trimobi/modalCreateAd.slice';
import { useCallback, useEffect } from 'react';
import { TMainReduxStates } from '../../../redux/types/redux-types';
import { mainApiServices } from '../../../services/MainApiServices';
import { AdsPackage } from '../../../services/types/mainApiService/mainApiService.amartavip.types';
import errorDialog from '../../callableDialog/errorDialog';

const SelectPlan = () => {
  const actions = modalCreateAdSlice.actions;
  const dispatch = useDispatch();
  const modalCreateAd = useSelector((state: TMainReduxStates) => state.modalCreateAd);

  const fetchPurchasedPlans = useCallback(async () => {
    try {
      const plans = await mainApiServices.trimobiGetPurchasedPlan();
      if (Array.isArray(plans.data)) {
        dispatch(actions.setPurchasedPlans(plans.data));
      }
    } catch (error: any) {
      await errorDialog({
        title: 'Gagal',
        content: 'Gagal mengambil data paket iklan',
        cancelButton: false,
      });
    }
  }, [dispatch, actions]);

  useEffect(() => {
    fetchPurchasedPlans();
  }, [fetchPurchasedPlans]);

  const onSelectPlan = useCallback(
    (plan: AdsPackage) => {
      dispatch(actions.setPlan(plan));
    },
    [dispatch, actions],
  );

  return (
    <Form>
      <div className="py-4">
        <h3 className="mb-4 text-xl font-semibold">Pilih Paket Iklan Anda</h3>
        {!modalCreateAd.purchasedPlans || modalCreateAd.purchasedPlans.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-8">
            <Icon
              name="box"
              size="huge"
              className="text-gray-400 mb-4"
            />
            <p className="text-gray-600 text-lg">Tidak ada paket iklan tersedia</p>
          </div>
        ) : (
          <List
            divided
            relaxed
            selection
          >
            {modalCreateAd.purchasedPlans.map((plan) => (
              <List.Item
                key={plan.code}
                onClick={() => onSelectPlan(plan)}
                className={`
                                    p-4 transition-all duration-200 ease-in-out
                                    hover:shadow-md hover:scale-[1.01]
                                    cursor-pointer rounded-lg
                                    ${
                                      modalCreateAd.plan?.code === plan.code
                                        ? 'bg-blue-50 border-l-4 border-blue-500'
                                        : 'hover:bg-gray-50'
                                    }
                                `}
              >
                <List.Icon
                  name="box"
                  size="large"
                  verticalAlign="middle"
                  className={`
                                        ${
                                          modalCreateAd.plan?.code === plan.code
                                            ? 'text-blue-500'
                                            : 'text-gray-600'
                                        }
                                    `}
                />
                <List.Content>
                  <List.Header
                    as="h4"
                    className={`
                                            text-lg font-medium
                                            ${
                                              modalCreateAd.plan?.code === plan.code
                                                ? 'text-blue-700'
                                                : 'text-gray-800'
                                            }
                                        `}
                  >
                    {plan.name}
                  </List.Header>
                  <List.Description className="mt-2 space-y-2">
                    <p className="text-gray-600">Kode Paket: {plan.code}</p>
                    <div className="flex flex-wrap gap-2">
                      <span className="px-2 py-1 text-sm bg-gray-100 text-gray-700 rounded-full">
                        Sisa Iklan: {plan.ads_quantity - plan.ads_used}
                      </span>
                      {plan.category &&
                        plan.category.length > 0 &&
                        plan.category.map((category: string, index: number) => (
                          <span
                            key={index}
                            className="px-2 py-1 text-sm bg-gray-100 text-gray-700 rounded-full"
                          >
                            {category}
                          </span>
                        ))}
                    </div>
                  </List.Description>
                </List.Content>
                {modalCreateAd.plan?.code === plan.code && (
                  <List.Content floated="right">
                    <Icon
                      name="check circle"
                      size="large"
                      className="text-blue-500"
                    />
                  </List.Content>
                )}
              </List.Item>
            ))}
          </List>
        )}
      </div>
    </Form>
  );
};

export default SelectPlan;

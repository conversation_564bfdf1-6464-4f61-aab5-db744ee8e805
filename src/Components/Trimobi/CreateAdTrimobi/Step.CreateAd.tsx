import { Icon, Step } from 'semantic-ui-react';

interface StepCreateAdProps {
  step: number;
}

const StepCreateAd = ({ step }: StepCreateAdProps) => {
  return (
    <Step.Group size="mini">
      <Step
        active={step === 1}
        completed={step > 1}
      >
        <Icon name={step > 1 ? 'check' : 'user'} />
        <Step.Content>
          <Step.Title>Informasi Pengiklan</Step.Title>
          <Step.Description>Data pemilik mobil</Step.Description>
        </Step.Content>
      </Step>
      <Step
        active={step === 2}
        completed={step > 2}
      >
        <Icon name={step > 2 ? 'check' : 'car'} />
        <Step.Content>
          <Step.Title>Spesifikasi Mobil</Step.Title>
          <Step.Description>Detail kendaraan</Step.Description>
        </Step.Content>
      </Step>
      <Step
        active={step === 3}
        completed={step > 3}
      >
        <Icon name={step > 3 ? 'check' : 'image'} />
        <Step.Content>
          <Step.Title>Detail Iklan</Step.Title>
          <Step.Description>Judu<PERSON>, harga & foto</Step.Description>
        </Step.Content>
      </Step>
      <Step
        active={step === 4}
        completed={step > 4}
      >
        <Icon name={step > 4 ? 'check' : 'box'} />
        <Step.Content>
          <Step.Title>Pilih Paket</Step.Title>
          <Step.Description>Paket yang tersedia</Step.Description>
        </Step.Content>
      </Step>
    </Step.Group>
  );
};

export default StepCreateAd;

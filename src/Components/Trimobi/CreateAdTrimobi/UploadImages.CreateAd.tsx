import { Button, Icon } from 'semantic-ui-react';
import UploadPhoto from './UploadPhoto.CreateAd';

interface UploadImagesProps {
  onAddRow?: () => void;
  values?: { uuid: string; url: string | null }[];
  onDeleteRow?: (uuid: string) => void;
  onChange?: (params: { uuid: string; url: string }) => void;
}

const UploadImages = (props: UploadImagesProps) => {
  return (
    <div>
      {props.values?.map((value, index) => (
        <div
          key={index}
          className="relative group mt-5"
        >
          <UploadPhoto
            label={`Foto ${index + 1}`}
            required
            showDeleteButton
            value={value.url}
            onDelete={() => props.onDeleteRow?.(value.uuid)}
            onChange={(file, url) => {
              props.onChange?.({ uuid: value.uuid, url });
            }}
          />
        </div>
      ))}

      {props.values?.length !== 5 && (
        <div className="mt-4">
          <Button
            fluid
            size="small"
            type="button"
            icon
            labelPosition="left"
            onClick={props.onAddRow}
          >
            <Icon name="plus" />
            Tambah Foto
          </Button>
        </div>
      )}
    </div>
  );
};

export default UploadImages;

import apisauce from 'apisauce';
import { ChangeEvent, useRef, useState } from 'react';
import { Button, Form, Icon } from 'semantic-ui-react';
import randomNumber from '../../../helpers/randomNumber';

interface UploadPhotoProps {
  onChange?: (file: File, url: string) => void;
  value?: string | null;
  label?: string;
  required?: boolean;
  showDeleteButton?: boolean;
  onDelete?: () => void;
}

async function upload(params: {
  payload: {
    documentName: string;
    documentType: string;
    source: string;
    image: File;
  };
  onSuccess: (url: string) => void;
  onError: (error: any) => void;
}) {
  const base = apisauce.create({
    baseURL: 'https://hbxhwzeej9.execute-api.ap-southeast-1.amazonaws.com/v1/upload-document/',
  });

  const formData = new FormData();
  formData.append('document_name', params.payload.documentName);
  formData.append('document_type', params.payload.documentType);
  formData.append('source', params.payload.source);
  formData.append('image', params.payload.image);

  try {
    let res = await base.post<{ data: { url_document: string } }>('/document-data', formData);
    if (res.ok && res.data) {
      const imageUrl = res.data.data.url_document;
      params.onSuccess(imageUrl);
    } else {
      params.onError(res.data);
    }
  } catch (err) {
    params.onError(err);
  }
}

const UploadPhoto = ({
  onChange,
  value,
  label,
  required = false,
  showDeleteButton = false,
  onDelete,
}: UploadPhotoProps) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleClick = () => {
    inputRef.current?.click();
  };

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file || !onChange) return;

    setLoading(true);
    setError(null);

    // Langsung upload ketika file dipilih
    upload({
      payload: {
        documentName: randomNumber(10).toString(),
        documentType: 'general',
        source: 'trimobi',
        image: file,
      },
      onSuccess: (url) => {
        onChange(file, url);
        setLoading(false);
      },
      onError: (err) => {
        setError('Gagal mengupload gambar');
        setLoading(false);
        console.error('Upload error:', err);
      },
    });
  };

  const getPreviewUrl = () => {
    if (!value) return null;
    if (typeof value === 'string') return value;
    return URL.createObjectURL(value);
  };

  const previewUrl = getPreviewUrl();

  return (
    <Form.Field required={required}>
      {label && <label>{label}</label>}
      <div
        onClick={!loading ? handleClick : undefined}
        className={`relative border-2 border-dashed border-gray-300 rounded-lg p-4 text-center cursor-pointer hover:border-blue-500 transition-colors ${
          loading ? 'opacity-50 cursor-wait' : ''
        }`}
      >
        {previewUrl ? (
          <div className="relative w-full aspect-video">
            <img
              src={previewUrl}
              alt="Preview"
              className="w-full h-full object-cover rounded-lg"
            />
          </div>
        ) : (
          <div className="py-8">
            <div className="text-gray-500 mb-2">
              {loading ? 'Mengupload...' : 'Klik untuk upload foto'}
            </div>
            <div className="text-sm text-gray-400">Format: JPG, PNG</div>
          </div>
        )}

        {showDeleteButton && (
          <Button
            size="mini"
            color="red"
            className="absolute top-2 right-2 shadow-lg"
            onClick={(e) => {
              e.stopPropagation();
              onDelete?.();
            }}
          >
            <Icon name="trash" />
            Hapus
          </Button>
        )}
      </div>

      {error && <div className="text-red-500 text-sm mt-2">{error}</div>}

      <input
        ref={inputRef}
        type="file"
        accept="image/*"
        onChange={handleChange}
        className="hidden"
        disabled={loading}
      />
    </Form.Field>
  );
};

export default UploadPhoto;

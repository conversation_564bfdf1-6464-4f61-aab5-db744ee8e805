import { CheckboxProps, Form } from 'semantic-ui-react';
import SelectVehicleFromAutoTrimitra from '../../SelectVehicle/SelectVehicleFromAutoTrimitra';
import { useDispatch, useSelector } from 'react-redux';
import React, { useCallback } from 'react';
import { TMainReduxStates } from '../../../redux/types/redux-types';
import modalCreateAdSlice from '../../../redux/modal-trimobi/modalCreateAd.slice';

const VehicleInformation = () => {
  const actions = modalCreateAdSlice.actions;
  const dispatch = useDispatch();
  const modalCreateAd = useSelector((state: TMainReduxStates) => state.modalCreateAd);

  const onBrandChange = useCallback(
    (brand: { name: string; uuid: string }) => {
      dispatch(actions.setBrand(brand));
    },
    [dispatch, actions],
  );

  const onModelChange = useCallback(
    (model: { name: string; uuid: string; category: string }) => {
      dispatch(actions.setModel(model));
    },
    [dispatch, actions],
  );

  const onVariantChange = useCallback(
    (variant: string) => {
      dispatch(actions.setVariant(variant));
    },
    [dispatch, actions],
  );

  const onMileageChange = useCallback(
    (mileage: string) => {
      dispatch(actions.setMileage(mileage));
    },
    [dispatch, actions],
  );

  const onYearChange = useCallback(
    (year: string) => {
      dispatch(actions.setYear(year));
    },
    [dispatch, actions],
  );

  const onLicensePlateChange = useCallback(
    (licensePlate: string) => {
      dispatch(actions.setLicensePlate(licensePlate));
    },
    [dispatch, actions],
  );

  const onFuelTypeChange = useCallback(
    (fuelType: string) => {
      dispatch(actions.setFuelType(fuelType));
    },
    [dispatch, actions],
  );

  const onTransmissionChange = useCallback(
    (transmission: string) => {
      dispatch(actions.setTransmission(transmission));
    },
    [dispatch, actions],
  );

  const onCompleteVehicleDocumentsChange = useCallback(
    (e: React.FormEvent<HTMLInputElement>, data: CheckboxProps) => {
      dispatch(actions.setCompleteVehicleDocuments(data.checked || false));
    },
    [dispatch, actions],
  );

  const onActiveVehicleDocumentsChange = useCallback(
    (e: React.FormEvent<HTMLInputElement>, data: CheckboxProps) => {
      dispatch(actions.setActiveVehicleDocuments(data.checked || false));
    },
    [dispatch, actions],
  );

  const onOwnerNameMatchDocumentsChange = useCallback(
    (e: React.FormEvent<HTMLInputElement>, data: CheckboxProps) => {
      dispatch(actions.setOwnerNameMatchDocuments(data.checked || false));
    },
    [dispatch, actions],
  );

  const onAllowBuyerPriceOfferChange = useCallback(
    (e: React.FormEvent<HTMLInputElement>, data: CheckboxProps) => {
      dispatch(actions.setAllowBuyerPriceOffer(data.checked || false));
    },
    [dispatch, actions],
  );

  return (
    <Form>
      <SelectVehicleFromAutoTrimitra
        onBrandChange={onBrandChange}
        onModelChange={onModelChange}
        onVariantFreeTextChange={onVariantChange}
        brand={modalCreateAd.brand || undefined}
        model={modalCreateAd.model || undefined}
        variantFreeText={modalCreateAd.variant || undefined}
        useFreeTextVariant={true}
      />
      <Form.Input
        label="Kilometer Kendaraan"
        placeholder="Masukkan kilometer kendaraan"
        type="number"
        required
        value={modalCreateAd.mileage || ''}
        onChange={(e, data) => onMileageChange(data.value)}
      />
      <Form.Select
        label="Tahun Pembuatan"
        placeholder="Pilih tahun pembuatan"
        options={Array.from({ length: new Date().getFullYear() - 1959 }, (_, i) => ({
          key: 1960 + i,
          value: (1960 + i).toString(),
          text: (1960 + i).toString(),
        })).reverse()}
        required
        value={modalCreateAd.year || ''}
        onChange={(e, data) => onYearChange(data.value as string)}
      />
      <Form.Input
        label="Plat Nomor"
        placeholder="Masukkan plat nomor kendaraan"
        required
        value={modalCreateAd.licensePlate || ''}
        onChange={(e, data) => onLicensePlateChange(data.value)}
      />
      <Form.Select
        label="Bahan Bakar"
        options={[
          { key: 'bensin', value: 'gasoline', text: 'Bensin' },
          { key: 'solar', value: 'solar', text: 'Solar' },
          { key: 'listrik', value: 'electric', text: 'Listrik' },
          { key: 'hybrid', value: 'hybrid', text: 'Hybrid' },
        ]}
        placeholder="Pilih bahan bakar"
        required
        value={modalCreateAd.fuelType || ''}
        onChange={(e, data) => onFuelTypeChange(data.value as string)}
      />
      <Form.Select
        label="Transmisi"
        options={[
          { key: 'manual', value: 'manual', text: 'Manual' },
          { key: 'automatic', value: 'automatic', text: 'Automatic' },
          { key: 'cvt', value: 'cvt', text: 'CVT' },
        ]}
        placeholder="Pilih transmisi"
        required
        value={modalCreateAd.transmission || ''}
        onChange={(e, data) => onTransmissionChange(data.value as string)}
      />
      <Form.Group grouped>
        <Form.Checkbox
          label="BPKB & STNK kendaraan lengkap"
          checked={modalCreateAd.completeVehicleDocuments || false}
          onChange={onCompleteVehicleDocumentsChange}
        />
        <Form.Checkbox
          label="BPKB dan STNK masih aktif"
          checked={modalCreateAd.activeVehicleDocuments || false}
          onChange={onActiveVehicleDocumentsChange}
        />
        <Form.Checkbox
          label="Nama Pemilik Sesuai STNK"
          checked={modalCreateAd.ownerNameMatchDocuments || false}
          onChange={onOwnerNameMatchDocumentsChange}
        />
        <Form.Checkbox
          label="Izinkan calon pembeli memberikan penawaran harga"
          checked={modalCreateAd.allowBuyerPriceOffer || false}
          onChange={onAllowBuyerPriceOfferChange}
        />
      </Form.Group>
    </Form>
  );
};

export default VehicleInformation;

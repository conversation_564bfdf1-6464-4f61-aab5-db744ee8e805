import { useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { <PERSON><PERSON>, <PERSON>dal, Tab } from 'semantic-ui-react';
import { TMainReduxStates } from '../../../redux/types/redux-types';
import modalPaymentStatusSlice from '../../../redux/modal-trimobi/modalPaymentStatus.slice';
import { mainApiServices } from '../../../services/MainApiServices';
import PaymentStatusPane from './PaymentStatus/PaymentStatusPane';
import MyPlanPane from './MyPlan/MyPlanPane';

const ModalTrimobiMenu = () => {
  const dispatch = useDispatch();
  const actions = modalPaymentStatusSlice.actions;
  const modalTrimobi = useSelector((state: TMainReduxStates) => state.modalPaymentStatus);

  const onClose = useCallback(() => {
    dispatch(actions.closeModal());
  }, [dispatch, actions]);

  const fetchPaymentStatus = useCallback(async () => {
    dispatch(actions.setLoading(true));
    try {
      const response = await mainApiServices.trimobiGetPlanCheckoutStatus({
        condition: modalTrimobi.filter,
        transaction_id: '',
      });
      dispatch(actions.setPaymentStatusList(response.data));
    } catch (error) {
      console.error('Error fetching payment status:', error);
      dispatch(actions.setErrorMessage('Gagal memuat data status pembayaran'));
    } finally {
      dispatch(actions.setLoading(false));
    }
  }, [dispatch, actions, modalTrimobi.filter]);

  useEffect(() => {
    if (modalTrimobi.isOpen) {
      fetchPaymentStatus().then();
    }
  }, [modalTrimobi.isOpen, modalTrimobi.filter, fetchPaymentStatus]);

  const panes = [
    {
      menuItem: 'Paket Saya',
      render: () => <MyPlanPane />,
    },
    {
      menuItem: 'Status Pembayaran',
      render: () => <PaymentStatusPane />,
    },
  ];

  return (
    <Modal
      open={modalTrimobi.isOpen}
      onClose={onClose}
      size="large"
    >
      <Modal.Header>Menu Trimobi</Modal.Header>
      <Modal.Content>
        <Tab panes={panes} />
      </Modal.Content>
      <Modal.Actions>
        <Button onClick={onClose}>Tutup</Button>
      </Modal.Actions>
    </Modal>
  );
};

export default ModalTrimobiMenu;

import { useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { TMainReduxStates } from '../../../../redux/types/redux-types';
import { mainApiServices } from '../../../../services/MainApiServices';
import modalPaymentStatusSlice from '../../../../redux/modal-trimobi/modalPaymentStatus.slice';
import MyPlanTable from './MyPlanTable';

const MyPlanPane = () => {
  const dispatch = useDispatch();
  const actions = modalPaymentStatusSlice.actions;
  const { loading, myPlanList } = useSelector(
    (state: TMainReduxStates) => state.modalPaymentStatus,
  );

  const fetchMyPlan = useCallback(async () => {
    dispatch(actions.setLoading(true));
    try {
      const response = await mainApiServices.trimobiGetPurchasedPlan();
      dispatch(actions.setMyPlanList(response.data));
    } catch (error) {
      console.error('Error fetching my plan:', error);
      dispatch(actions.setErrorMessage('Gagal memuat data paket'));
    } finally {
      dispatch(actions.setLoading(false));
    }
  }, [dispatch, actions]);

  useEffect(() => {
    fetchMyPlan();
  }, [fetchMyPlan]);

  return (
    <div className="p-4">
      <MyPlanTable
        data={myPlanList}
        loading={loading}
      />
    </div>
  );
};

export default MyPlanPane;

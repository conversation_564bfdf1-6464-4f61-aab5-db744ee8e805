import { Icon } from 'semantic-ui-react';
import { AdsPackage } from '../../../../services/types/mainApiService/mainApiService.amartavip.types';

interface MyPlanTableProps {
  data: AdsPackage[];
  loading: boolean;
}

const MyPlanTable = ({ data, loading }: MyPlanTableProps) => {
  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-gray-500">
        <Icon
          name="search"
          size="huge"
        />
        <p className="mt-4 text-lg">Tidak ada paket yang aktif</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {data.map((item) => (
        <div
          key={item.code}
          className="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow duration-200"
        >
          <div className="flex justify-between items-start mb-3">
            <div className="space-y-1">
              <div className="text-gray-600 text-sm flex items-center gap-2">
                <Icon name="box" />
                Kode Paket
              </div>
              <div className="font-medium">{item.code}</div>
            </div>
            <div className="flex flex-wrap gap-1">
              {item.category.map((cat, index) => (
                <div
                  key={index}
                  className="bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-sm font-medium"
                >
                  {cat.toUpperCase()}
                </div>
              ))}
            </div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            <div>
              <div className="text-gray-600 text-sm flex items-center gap-2">
                <Icon name="tag" />
                Nama Paket
              </div>
              <div>{item.name}</div>
            </div>
            <div>
              <div className="text-gray-600 text-sm flex items-center gap-2">
                <Icon name="chart bar" />
                Sisa Iklan
              </div>
              <div>{item.ads_quantity - item.ads_used} iklan</div>
            </div>
            <div>
              <div className="text-gray-600 text-sm flex items-center gap-2">
                <Icon name="clock" />
                Masa Berlaku
              </div>
              <div>{item.days} hari</div>
            </div>
            <div className="md:col-span-3">
              <div className="text-gray-600 text-sm flex items-center gap-2">
                <Icon name="info circle" />
                Deskripsi
              </div>
              <div className="text-sm mt-1">{item.caption}</div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default MyPlanTable;

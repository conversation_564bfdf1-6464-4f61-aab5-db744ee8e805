import { useDispatch, useSelector } from 'react-redux';
import { Dropdown, Tab } from 'semantic-ui-react';
import { TMainReduxStates } from '../../../../redux/types/redux-types';
import modalPaymentStatusSlice from '../../../../redux/modal-trimobi/modalPaymentStatus.slice';
import PaymentStatusTable from './PaymentStatusTable';

const PaymentStatusPane = () => {
  const dispatch = useDispatch();
  const actions = modalPaymentStatusSlice.actions;
  const { loading, filter, paymentStatusList } = useSelector(
    (state: TMainReduxStates) => state.modalPaymentStatus,
  );

  return (
    <Tab.Pane>
      <div className="mb-4 space-y-4">
        <div className="bg-blue-50 p-4 rounded-lg">
          <h3 className="font-semibold text-blue-800 mb-2">
            Status Pembayaran Paket Iklan Trimobi
          </h3>
          <p className="text-gray-600 text-sm">
            Menu ini menampilkan status pembayaran untuk pembelian paket iklan Trimobi. Anda dapat
            memfilter status pembayaran menggunakan dropdown di bawah ini.
          </p>
        </div>
        <div className="flex justify-end">
          <Dropdown
            selection
            options={[
              { key: 'unpaid', text: 'Belum Lunas', value: 'unpaid' },
              { key: 'paid', text: 'Lunas', value: 'paid' },
            ]}
            value={filter}
            onChange={(_, data) => dispatch(actions.setFilter(data.value as 'unpaid' | 'paid'))}
          />
        </div>
      </div>
      <PaymentStatusTable
        data={paymentStatusList}
        loading={loading}
      />
    </Tab.Pane>
  );
};

export default PaymentStatusPane;

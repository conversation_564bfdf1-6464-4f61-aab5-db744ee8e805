import { Table, Icon, Button } from 'semantic-ui-react';
import { format } from 'date-fns';
import { PlanCheckoutStatusItem } from '../../../../services/types/mainApiService/mainApiService.amartavip.types';

interface PaymentStatusTableProps {
  data: PlanCheckoutStatusItem[];
  loading: boolean;
}

const PaymentStatusTable = ({ data, loading }: PaymentStatusTableProps) => {
  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-gray-500">
        <Icon
          name="search"
          size="huge"
        />
        <p className="mt-4 text-lg">Tidak ada data</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {data.map((item, index) => (
        <div
          key={item.transaction_id}
          className="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow duration-200"
        >
          <div className="flex justify-between items-start mb-3">
            <div className="space-y-1">
              <div className="text-gray-600 text-sm flex items-center gap-2">
                <Icon name="hashtag" />
                ID Transaksi
              </div>
              <div className="font-medium">{item.transaction_id}</div>
            </div>
            <div
              className={`px-3 py-1 rounded-full text-xs font-medium shadow-sm ml-2 ${
                item.status === 'PAID' ? 'bg-green-500 text-white' : 'bg-yellow-500 text-white'
              }`}
            >
              {item.status === 'PAID' ? 'Lunas' : 'Belum Lunas'}
            </div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            <div>
              <div className="text-gray-600 text-sm flex items-center gap-2">
                <Icon name="calendar" />
                Tanggal
              </div>
              <div>{format(new Date(item.created_time), 'dd MMM yyyy HH:mm')}</div>
            </div>
            <div>
              <div className="text-gray-600 text-sm flex items-center gap-2">
                <Icon name="box" />
                Kode Paket
              </div>
              <div>{item.payload.code}</div>
            </div>
            <div>
              <div className="text-gray-600 text-sm flex items-center gap-2">
                <Icon name="chart bar" />
                Jumlah Iklan
              </div>
              <div>{item.payload.ads_option_qty}</div>
            </div>
            <div className="md:col-span-2">
              <div className="text-gray-600 text-sm flex items-center gap-2">
                <Icon name="money bill alternate" />
                Total Harga
              </div>
              <div className="font-semibold text-lg">
                Rp {item.bill_amount.toLocaleString('id-ID')}
              </div>
            </div>
            <div className="flex items-end justify-end">
              <Button.Group size="tiny">
                <Button icon>
                  <Icon name="refresh" />
                </Button>
              </Button.Group>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default PaymentStatusTable;

import { useCallback, useState } from 'react';
import { Form } from 'semantic-ui-react';
import trimobiServices from '../../services/trimobi/trimobi.services';
import { AdsOption, Package } from '../../services/trimobi/trimobi.getPlan.response.types';
import { useEffect } from 'react';

interface SelectPlanAdProps {
  vehicleType?: string;
  showLabel?: boolean;
  required?: boolean;
  onChangePlan?: (plan: Package) => void;
  onChangeQuantityAds?: (quantityAds: AdsOption) => void;
  planCode?: string;
  quantityAds?: number;
  readOnlySelectPlan?: boolean;
  readOnlySelectQuantityAds?: boolean;
}

const SelectPlanAd = (props: SelectPlanAdProps) => {
  const {
    vehicleType,
    showLabel = true,
    onChangePlan,
    onChangeQuantityAds,
    required = false,
    planCode,
    quantityAds,
    readOnlySelectPlan = false,
    readOnlySelectQuantityAds = false,
  } = props;
  const [availablePlans, setAvailablePlans] = useState<Package[]>([]);
  const [availableQuantityAds, setAvailableQuantityAds] = useState<AdsOption[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    trimobiServices
      .getPlan({
        company: 'trimobi'.toUpperCase(),
        category: vehicleType?.toUpperCase() || undefined,
        condition: 'used'.toUpperCase(),
        internal: true,
      })
      .then((response) => {
        const data = response?.data ?? [];
        setAvailablePlans(data);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [vehicleType]);

  useEffect(() => {
    if (planCode) {
      const plan = availablePlans.find((plan) => plan.code === planCode);
      if (plan) {
        setAvailableQuantityAds(plan.ads_option);
      }
    } else {
      setAvailableQuantityAds([]);
    }
  }, [vehicleType, planCode, availablePlans]);

  const onPlanChange = useCallback(
    (code: string) => {
      const plan = availablePlans.find((plan) => plan.code === code);
      if (plan) {
        onChangePlan?.(plan);
      }
    },
    [availablePlans, onChangePlan],
  );

  const onQuantityAdsChange = useCallback(
    (quantityAds: number) => {
      const option = availableQuantityAds.find((option) => option.ads === quantityAds);
      if (option) {
        onChangeQuantityAds?.(option);
      }
    },
    [availableQuantityAds, onChangeQuantityAds],
  );

  return (
    <div>
      <Form.Field required={required}>
        {showLabel && <label>Pilih Paket</label>}
        {readOnlySelectPlan ? (
          <p>{availablePlans.find((plan) => plan.code === planCode)?.name || '-'}</p>
        ) : (
          <Form.Select
            placeholder="Pilih Paket"
            options={availablePlans.map((plan) => ({
              key: plan.code,
              text: plan.name,
              value: plan.code,
            }))}
            onChange={(_, { value }) => {
              onPlanChange(value as string);
            }}
            loading={loading}
            value={planCode || ''}
          />
        )}
      </Form.Field>
      <Form.Field required={required}>
        {showLabel && <label>Jumlah Ads</label>}
        {readOnlySelectQuantityAds ? (
          <p>{quantityAds || '-'}</p>
        ) : (
          <Form.Select
            placeholder="Pilih Jumlah Ads"
            options={availableQuantityAds.map((option) => ({
              key: option.ads,
              text: option.ads,
              value: option.ads,
            }))}
            onChange={(_, { value }) => {
              onQuantityAdsChange(value as number);
            }}
            loading={loading}
            value={quantityAds || ''}
          />
        )}
      </Form.Field>
    </div>
  );
};

export default SelectPlanAd;

import { Button, Message, Modal } from 'semantic-ui-react';
import { mainApiServices } from '../../../services/MainApiServices';
import modalUpgradePlanTrimobiSlice from '../../../redux/modal-trimobi/modalUpgradePlan.slice';
import { useCallback, useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { TMainReduxStates } from '../../../redux/types/redux-types';
import StepGroup from './StepGroup.UpdatePlan';
import Step1 from './Step1.UpdatePlan';
import Step2 from './Step2.UpdatePlan';
import { AdsPackage } from '../../../services/types/mainApiService/mainApiService.amartavip.types';
import * as Yup from 'yup';
import successDialog from '../../callableDialog/successDialog';
import errorDialog from '../../callableDialog/errorDialog';
import confirmDialog from '../../callableDialog/confirmDialog';
import currencyFormat from '../../../helpers/currencyFormat';

const ModalUpgradePlanTrimobi = () => {
  const dispatch = useDispatch();
  const actions = modalUpgradePlanTrimobiSlice.actions;
  const { myPurchasedPlan, selectedPlan, loading, step, quantityAds, errorMessage, isOpen } =
    useSelector((state: TMainReduxStates) => state.modalUpgradePlan);
  const admin = useSelector((state: TMainReduxStates) => state.reducerAdmin);

  const project = useSelector((state: TMainReduxStates) => state.reducerProject);
  const customer = useSelector((state: TMainReduxStates) => state.customerReducer);
  const conversation = useSelector((state: TMainReduxStates) => state.reducerConversation);

  useEffect(() => {
    if (isOpen) {
      dispatch(actions.setLoading(true));
      mainApiServices.trimobiGetPurchasedPlan().then((res) => {
        dispatch(actions.setMyPurchasedPlan(res.data));
        dispatch(actions.setLoading(false));
      });
    }
  }, [isOpen]);

  const onClose = useCallback(() => {
    dispatch(actions.closeModal());
  }, [dispatch, actions]);

  const validationSchema = useMemo(() => {
    return {
      step1: Yup.object({
        selectedPlan: Yup.object().required('Paket iklan wajib dipilih'),
      }).required('Pilih paket iklan terlebih dahulu'),
      step2: Yup.object({
        quantityAds: Yup.number()
          .required('Jumlah iklan wajib diisi')
          .min(1, 'Jumlah iklan minimal 1'),
      }).required('Jumlah iklan wajib diisi'),
    };
  }, []);

  const onPrevious = useCallback(() => {
    if (step === 1) {
      return;
    }
    dispatch(actions.setStep(step - 1));
    dispatch(actions.clearErrorMessage());
  }, [step, dispatch, actions]);

  const onSelectPlan = useCallback(
    (plan: AdsPackage) => {
      dispatch(actions.setSelectedPlan(plan));
    },
    [dispatch, actions],
  );

  const onSubmit = useCallback(async () => {
    const confirmed = await confirmDialog({
      title: 'Konfirmasi',
      content: 'Apakah anda yakin ingin membeli paket iklan ini?',
      okButton: 'Ya',
      cancelButton: 'Tidak',
    });

    if (!confirmed) {
      return;
    }
    dispatch(actions.setLoading(true));
    try {
      const createBill = await mainApiServices.trimobiUpgradePlan({
        projectId: project?.project?.ref.id || '',
        planCode: selectedPlan?.code || '',
        adsQuantity: quantityAds?.ads || 0,
        customerPhoneNumber: customer.client?.contacts.whatsapp || '',
      });

      const billAmount = createBill?.success.data.checkout.bill_amount;

      const messageContent = `Pembelian paket iklan Anda telah berhasil diproses. Detail transaksi:
- ID Transaksi: ${createBill?.success.data.checkout.transaction_id}
- ID Tagihan: ${createBill?.success.data.checkout.bill_id}
- Jenis Paket: ${createBill?.success.data.checkout.payload.code}
- Jumlah Iklan: ${quantityAds?.ads}
- Total Pembayaran: ${currencyFormat(billAmount)}

Silakan segera selesaikan pembayaran dengan memindai QR Code yang terlampir. Pembayaran yang terlambat dapat menyebabkan penundaan aktivasi paket iklan Anda.

Catatan: ${createBill?.success.data.checkout.payload.notes || 'Tidak ada catatan'}`;

      await mainApiServices.sendMessageV2({
        roomPath: conversation.chatRoom?.ref.path || '',
        text: messageContent,
        adminSessionPath: admin.adminSession?.ref.path || '',
        phoneNumber: customer.client?.contacts.whatsapp || '',
        fileUrl: createBill?.success.data.qrisUrl,
        fileType: 'image',
      });

      await successDialog({
        title: 'Berhasil',
        content: 'Berhasil membeli paket iklan',
        cancelButton: false,
      });
      onClose();
    } catch (error: any) {
      let errorMessage = 'Terjadi kesalahan saat memproses permintaan';

      if (error?.response?.data?.error?.messages) {
        errorMessage = error.response.data.error.messages;
      } else if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      }

      await errorDialog({
        title: 'Gagal',
        content: errorMessage,
        cancelButton: false,
      });
    } finally {
      dispatch(actions.setLoading(false));
    }
  }, [
    project.project?.ref.id,
    selectedPlan?.code,
    quantityAds?.ads,
    customer.client?.contacts.whatsapp,
    dispatch,
    actions,
    onClose,
    conversation.chatRoom?.ref.path,
    admin.adminSession?.ref.path,
  ]);

  const onNext = useCallback(async () => {
    dispatch(actions.clearErrorMessage());
    try {
      if (step === 1) {
        await validationSchema['step1'].validate({
          selectedPlan: selectedPlan,
        });
      } else if (step === 2) {
        await validationSchema['step2'].validate({
          quantityAds: quantityAds?.ads || 0,
        });
        return onSubmit();
      }

      dispatch(actions.setStep(step + 1));
    } catch (error) {
      if (error instanceof Yup.ValidationError) {
        dispatch(actions.setErrorMessage(error.message));
      } else {
        console.error('Unexpected error:', error);
        dispatch(actions.setErrorMessage('Terjadi kesalahan yang tidak terduga'));
      }
    }
  }, [step, validationSchema, selectedPlan, onSubmit, dispatch, actions, quantityAds?.ads]);

  return (
    <Modal
      open={isOpen}
      onClose={onClose}
    >
      <Modal.Header>Update Paket Iklan</Modal.Header>
      <Modal.Content>
        {customer.client && (
          <div className="bg-gray-50 p-4 rounded-lg mb-4">
            <div className="flex items-center space-x-4">
              <div className="flex-1">
                <div className="text-sm font-medium text-gray-500">Nama</div>
                <div className="font-semibold text-gray-800">
                  {customer?.client?.profile.name || '-'}
                </div>
              </div>
              <div className="flex-1">
                <div className="text-sm font-medium text-gray-500">Nomor Telepon</div>
                <div className="font-semibold text-gray-800">
                  {customer?.client?.contacts.whatsapp || '-'}
                </div>
              </div>
            </div>
          </div>
        )}
        <StepGroup currentStep={step} />

        {step === 1 && (
          <Step1
            myPurchasedPlan={myPurchasedPlan}
            selectedPlan={selectedPlan}
            onSelectPlan={onSelectPlan}
          />
        )}

        {step === 2 && <Step2 />}

        {errorMessage && (
          <Message
            negative
            className="mt-4"
          >
            <Message.Header>Error</Message.Header>
            <p>{errorMessage}</p>
          </Message>
        )}
      </Modal.Content>
      <Modal.Actions>
        <Button
          color="red"
          icon="times"
          content="Batal"
          onClick={onClose}
          disabled={loading}
        />
        {step > 1 && (
          <Button
            onClick={onPrevious}
            icon="arrow left"
            labelPosition="left"
            content="Sebelumnya"
            disabled={loading}
          />
        )}
        {step < 2 && (
          <Button
            onClick={onNext}
            icon="arrow right"
            labelPosition="right"
            content="Selanjutnya"
            primary
            disabled={loading}
            loading={loading}
          />
        )}
        {step === 2 && (
          <Button
            onClick={onNext}
            icon="check"
            labelPosition="right"
            content="Submit"
            color="green"
            disabled={loading}
            loading={loading}
          />
        )}
      </Modal.Actions>
    </Modal>
  );
};

export default ModalUpgradePlanTrimobi;

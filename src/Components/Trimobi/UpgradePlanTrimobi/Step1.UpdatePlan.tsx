import { Icon, List } from 'semantic-ui-react';
import { AdsPackage } from '../../../services/types/mainApiService/mainApiService.amartavip.types';

interface Step1Props {
  myPurchasedPlan: AdsPackage[] | null;
  selectedPlan: AdsPackage | null;
  onSelectPlan: (plan: AdsPackage) => void;
}

const Step1 = ({ myPurchasedPlan, selectedPlan, onSelectPlan }: Step1Props) => {
  return (
    <div className="py-4">
      <h3 className="mb-4 text-xl font-semibold">Pilih Paket Iklan Anda</h3>
      {!myPurchasedPlan || myPurchasedPlan.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-8">
          <Icon
            name="box"
            size="huge"
            className="text-gray-400 mb-4"
          />
          <p className="text-gray-600 text-lg">Tidak ada paket iklan tersedia</p>
        </div>
      ) : (
        <List
          divided
          relaxed
          selection
        >
          {myPurchasedPlan.map((plan) => (
            <List.Item
              key={plan.code}
              onClick={() => onSelectPlan(plan)}
              className={`
                                p-4 transition-all duration-200 ease-in-out
                                hover:shadow-md hover:scale-[1.01]
                                cursor-pointer rounded-lg
                                ${
                                  selectedPlan?.code === plan.code
                                    ? 'bg-blue-50 border-l-4 border-blue-500'
                                    : 'hover:bg-gray-50'
                                }
                            `}
            >
              <List.Icon
                name="box"
                size="large"
                verticalAlign="middle"
                className={`
                                    ${
                                      selectedPlan?.code === plan.code
                                        ? 'text-blue-500'
                                        : 'text-gray-600'
                                    }
                                `}
              />
              <List.Content>
                <List.Header
                  as="h4"
                  className={`
                                        text-lg font-medium
                                        ${
                                          selectedPlan?.code === plan.code
                                            ? 'text-blue-700'
                                            : 'text-gray-800'
                                        }
                                    `}
                >
                  {plan.name}
                </List.Header>
                <List.Description className="mt-2 space-y-2">
                  <p className="text-gray-600">Kode Paket: {plan.code}</p>
                  {plan.category && plan.category.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {plan.category.map((category, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 text-sm bg-gray-100 text-gray-700 rounded-full"
                        >
                          {category}
                        </span>
                      ))}
                    </div>
                  )}
                </List.Description>
              </List.Content>
              {selectedPlan?.code === plan.code && (
                <List.Content floated="right">
                  <Icon
                    name="check circle"
                    size="large"
                    className="text-blue-500"
                  />
                </List.Content>
              )}
            </List.Item>
          ))}
        </List>
      )}
    </div>
  );
};

export default Step1;

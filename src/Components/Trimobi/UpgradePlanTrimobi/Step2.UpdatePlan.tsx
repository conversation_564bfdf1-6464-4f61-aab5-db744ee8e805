import { useDispatch, useSelector } from 'react-redux';
import { TMainReduxStates } from '../../../redux/types/redux-types';
import { AdsPackage } from '../../../services/types/mainApiService/mainApiService.amartavip.types';
import SelectPlanAd from '../SelectPlanAd';
import { Form } from 'semantic-ui-react';
import { useCallback } from 'react';
import { AdsOption } from '../../../services/trimobi/trimobi.getPlan.response.types';
import currencyFormat from '../../../helpers/currencyFormat';
import modalUpgradePlanTrimobiSlice from '../../../redux/modal-trimobi/modalUpgradePlan.slice';

const Step2 = () => {
  const modalUpdatePlan = useSelector((state: TMainReduxStates) => state.modalUpgradePlan);
  const selectedPlan = modalUpdatePlan.selectedPlan as AdsPackage | null;
  const actions = modalUpgradePlanTrimobiSlice.actions;
  const dispatch = useDispatch();

  const onChangeQuantityAds = useCallback(
    (quantityAds: AdsOption) => {
      dispatch(actions.setQuantityAds(quantityAds));
    },
    [dispatch, actions],
  );

  return (
    <div className="py-4">
      {selectedPlan && (
        <div className="bg-white p-6 rounded-lg shadow-md mb-5">
          <h2 className="text-2xl font-bold mb-4">Detail Paket Iklan</h2>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="font-medium">Kode Paket:</span>
              <span>{selectedPlan.code}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Nama Paket:</span>
              <span>{selectedPlan.name}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Kategori:</span>
              <div className="flex flex-wrap gap-2">
                {selectedPlan.category.map((cat, index) => (
                  <span
                    key={index}
                    className="bg-gray-100 px-2 py-1 rounded text-sm"
                  >
                    {cat}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
      <h3 className="mb-4 text-xl font-semibold">
        <Form>
          <SelectPlanAd
            required={true}
            planCode={selectedPlan?.code}
            readOnlySelectPlan={true}
            onChangeQuantityAds={onChangeQuantityAds}
            quantityAds={modalUpdatePlan.quantityAds?.ads}
          />
        </Form>
      </h3>

      {modalUpdatePlan.quantityAds && (
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-xl font-bold mb-4">Detail Iklan</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="font-medium">Jumlah Iklan:</span>
              <span>{modalUpdatePlan.quantityAds.ads}</span>
            </div>
            <div className="flex justify-between font-bold">
              <span>Total Harga:</span>
              <span>
                {currencyFormat(
                  modalUpdatePlan.quantityAds.ads * modalUpdatePlan.quantityAds.price,
                )}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Step2;

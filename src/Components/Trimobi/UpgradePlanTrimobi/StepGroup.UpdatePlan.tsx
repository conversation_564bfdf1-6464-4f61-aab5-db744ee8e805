import { Icon, Step } from 'semantic-ui-react';

interface StepGroupProps {
  currentStep: number;
}

const StepGroup = ({ currentStep }: StepGroupProps) => {
  return (
    <Step.Group size="small">
      <Step active={currentStep === 1}>
        <Icon name="box" />
        <Step.Content>
          <Step.Title>Pilih <PERSON>et</Step.Title>
        </Step.Content>
      </Step>
      <Step active={currentStep === 2}>
        <Icon name="hashtag" />
        <Step.Content>
          <Step.Title>Pilih <PERSON>tas</Step.Title>
        </Step.Content>
      </Step>
    </Step.Group>
  );
};

export default StepGroup;

import { confirmable, createConfirmation, ConfirmDialogProps } from 'react-confirm';
import { Button, Modal } from 'semantic-ui-react';
import { Component } from 'react';

export interface SimpleDialogProps {
  title: string;
  content: string;

  okButton?: false | string;
  cancelButton?: false | string;
}

type IMySimpleDialogProps = ConfirmDialogProps<SimpleDialogProps, boolean>;

class ConfirmDialog extends Component<IMySimpleDialogProps> {
  render() {
    return (
      <Modal
        size={'tiny'}
        open={this.props.show}
        className="!bg-white rounded-lg shadow-xl"
      >
        {this.props.title && (
          <Modal.Header className="!border-none !pb-2 !pt-6 !px-6">
            <h3 className="text-xl font-semibold text-gray-800 text-center">{this.props.title}</h3>
          </Modal.Header>
        )}
        <Modal.Content className="!border-none !px-6 !py-4">
          <div className="text-center text-gray-600">{this.props.content}</div>
        </Modal.Content>
        <Modal.Actions className="!border-none !px-6 !pb-6 !pt-2 flex justify-center gap-3">
          {this.props.cancelButton !== false && (
            <Button
              onClick={(event) => this.props.proceed(false)}
              className="!bg-gray-100 hover:!bg-gray-200 !text-gray-700"
            >
              {this.props.cancelButton ?? 'Cancel'}
            </Button>
          )}
          {this.props.okButton !== false && (
            <Button
              onClick={(event) => this.props.proceed(true)}
              className="!bg-blue-600 hover:!bg-blue-700 !text-white"
            >
              {this.props.okButton ?? 'Confirm'}
            </Button>
          )}
        </Modal.Actions>
      </Modal>
    );
  }
}

const MyDialogConfirmable = confirmable(ConfirmDialog);

const _confirmDialog = createConfirmation(MyDialogConfirmable);

const confirmDialog = (
  options?: Omit<IMySimpleDialogProps, 'dismiss' | 'proceed' | 'cancel' | 'show'>,
) => {
  return _confirmDialog({ ...(options as any) });
};

export default confirmDialog;

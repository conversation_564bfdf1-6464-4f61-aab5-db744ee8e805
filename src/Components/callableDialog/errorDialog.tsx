import { confirmable, createConfirmation, ConfirmDialog } from 'react-confirm';
import { Button, Icon, Modal } from 'semantic-ui-react';

export interface SimpleDialogProps {
  title: string;
  content: string | string[] | React.ReactNode;
  okButton?: false | string;
  cancelButton?: false | string;
}

const ErrorDialog: ConfirmDialog<SimpleDialogProps, boolean> = ({
  show,
  proceed,
  title,
  content,
  okButton,
  cancelButton,
}) => {
  return (
    <Modal
      size="mini"
      open={show}
      className="!bg-white rounded-xl shadow-2xl"
    >
      {title && (
        <Modal.Header className="!border-none !pb-2 !pt-6 !px-6">
          <div className="flex items-center gap-2">
            <Icon
              name="exclamation triangle"
              className="!text-red-600"
            />
            <h3 className="text-xl font-semibold text-red-600">{title}</h3>
          </div>
        </Modal.Header>
      )}
      <Modal.Content className="!border-none !px-6 !py-4">
        {Array.isArray(content) ? (
          <div className="bg-red-50 p-4 rounded-lg border border-red-100">
            <ul className="space-y-2">
              {content.map((line, i) => (
                <li
                  key={i}
                  className="flex text-sm text-red-700"
                >
                  <span className="text-red-500 mr-2">•</span>
                  <span className="flex-1">{line}</span>
                </li>
              ))}
            </ul>
          </div>
        ) : (
          <div className="text-gray-700">{content}</div>
        )}
      </Modal.Content>
      <Modal.Actions className="!flex !items-center !justify-end !gap-3 !p-6 !bg-gray-50 !border-t !border-gray-200 !rounded-b-xl">
        {cancelButton !== false && (
          <Button
            onClick={() => proceed(false)}
            className="!bg-white !text-gray-700 !border !border-gray-300"
          >
            {cancelButton ?? 'CANCEL'}
          </Button>
        )}
        {okButton !== false && (
          <Button
            onClick={() => proceed(true)}
            className="!bg-red-600 !text-white hover:!bg-red-700"
          >
            {okButton ?? 'OK'}
          </Button>
        )}
      </Modal.Actions>
    </Modal>
  );
};

const MyDialogConfirmable = confirmable(ErrorDialog);
const _errorDialog = createConfirmation(MyDialogConfirmable);

export const errorDialog = (options: SimpleDialogProps) => {
  return _errorDialog({ ...options });
};

export default errorDialog;

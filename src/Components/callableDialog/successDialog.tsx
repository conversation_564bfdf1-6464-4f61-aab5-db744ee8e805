import { confirmable, createConfirmation, ConfirmDialogProps } from 'react-confirm';
import { Button, Icon, Modal } from 'semantic-ui-react';
import { Component } from 'react';

export interface SimpleDialogProps {
  title: string;
  content: string;

  okButton?: false | string;
  cancelButton?: false | string;
}

type IMySimpleDialogProps = ConfirmDialogProps<SimpleDialogProps, boolean>;

class SuccessDialog extends Component<IMySimpleDialogProps> {
  render() {
    return (
      <Modal
        size={'tiny'}
        open={this.props.show}
      >
        {this.props.title && (
          <Modal.Header className="!text-xl !font-bold !text-green-600">
            <Icon name="check circle" /> {this.props.title}
          </Modal.Header>
        )}
        <Modal.Content>{this.props.content}</Modal.Content>
        <Modal.Actions>
          {this.props.cancelButton !== false && (
            <Button onClick={(event) => this.props.proceed(true)}>
              {this.props.cancelButton ?? 'CANCEL'}
            </Button>
          )}
          {this.props.okButton !== false && (
            <Button onClick={(event) => this.props.proceed(false)}>
              {this.props.okButton ?? 'OK'}
            </Button>
          )}
        </Modal.Actions>
      </Modal>
    );
  }
}

const MyDialogConfirmable = confirmable(SuccessDialog);

const _successDialog = createConfirmation(MyDialogConfirmable);

const successDialog = (
  options?: Omit<IMySimpleDialogProps, 'dismiss' | 'proceed' | 'cancel' | 'show'>,
) => {
  return _successDialog({ ...(options as any) });
};

export default successDialog;

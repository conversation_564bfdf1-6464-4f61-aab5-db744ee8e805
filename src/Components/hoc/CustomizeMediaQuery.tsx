import React from 'react';
import { useMediaQuery } from 'react-responsive';

export interface IMediaQueries {
  isBigScreen: boolean;
  isTabletOrMobile: boolean;
  isPortrait: boolean;
  isRetina: boolean;
  isMobile: boolean;
}

export interface CustomizeMediaQuery {
  mediaQueries: IMediaQueries;
}

function customizeMediaQuery<P extends CustomizeMediaQuery>(
  WrappedComponent: React.ComponentType<P>,
) {
  const WrapperComponent = (props: P) => {
    const isDesktopOrLaptop = useMediaQuery({
      query: '(min-width: 1224px)',
    });
    const isBigScreen = useMediaQuery({ query: '(min-width: 1824px)' });
    const isTabletOrMobile = useMediaQuery({ query: '(max-width: 1224px)' });
    const isMobile = useMediaQuery({ query: '(max-width: 768px)' });
    const isPortrait = useMediaQuery({ query: '(orientation: portrait)' });
    const isRetina = useMediaQuery({ query: '(min-resolution: 2dppx)' });

    const mediaQueries = {
      isDesktopOrLaptop,
      isBigScreen,
      isTabletOrMobile,
      isMobile,
      isPortrait,
      isRetina,
    };
    return (
      <WrappedComponent
        {...props}
        mediaQueries={mediaQueries}
      />
    );
  };

  return WrapperComponent;
}

export default customizeMediaQuery;

import AdminDocument from '../../../entities/AdminDocument';
import { TMainReduxStates } from '../../../redux/types/redux-types';
import { DocumentReference } from 'firebase/firestore';

export interface IWithAuthReduxDispatchProps {
  setAdmin: (adminUid: string) => void;
  setProject: (project: DocumentReference) => void;
}

export interface IWithAuthProps extends IWithAuthReduxDispatchProps, IWithAuthReduxStatesProps {}

export interface IWithAuthReduxStatesProps {
  admin: TMainReduxStates['reducerAdmin'];
  project: TMainReduxStates['reducerProject'];
}

export interface IWithAuthStates {
  authenticated: boolean;
  authenticating: boolean;
}

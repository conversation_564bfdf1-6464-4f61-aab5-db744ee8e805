import React from 'react';
import { Redirect } from 'react-router-dom';
import authServices from '../../services/firebase/AuthServices';
import { IWithAuthProps, IWithAuthStates } from './types/with-auth-types';
import { connect } from 'react-redux';
import moment from 'moment';
import { setProject } from '../../redux/project/action-project';
import { ThunkDispatch } from 'redux-thunk';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { DocumentReference } from 'firebase/firestore';
import { setAdminThunk } from '../../redux/admin/admin.slice';

export default function withAuth<P>(Component: any) {
  class InnerHoc extends React.Component<IWithAuthProps, IWithAuthStates> {
    constructor(props: IWithAuthProps) {
      super(props);

      this.state = {
        authenticated: false,
        authenticating: true,
      };
    }

    checkAuth = async () => {
      this.setState(
        {
          authenticating: true,
        },
        async () => {
          let currentState: IWithAuthStates = { ...this.state };

          const auth = await authServices.checkAuth();
          currentState.authenticating = false;

          if (auth) {
            currentState.authenticated = true;
            if (auth.uid) {
              this.props.setAdmin(auth.uid);
            }

            localStorage.setItem('last-active', moment().toISOString());
          }

          this.setState({ ...currentState });
        },
      );
    };

    componentDidMount = () => {
      this.checkAuth().then();
    };

    render = () => {
      const { setAdmin, setProject, admin, project, ...rest } = this.props;
      if (this.state.authenticating) {
        return <span>Authenticating...</span>;
      } else {
        if (this.state.authenticated) {
          if (!this.props.admin.admin?.ref || !this.props.project.project?.ref) {
            return <span>Fetching Admin Data & Project...</span>;
          }

          return <Component {...(rest as P)} />;
        } else {
          return <Redirect to={'/login'} />;
        }
      }
    };
  }

  const mapStateToProps = (states: TMainReduxStates) => ({
    admin: states.reducerAdmin,
    project: states.reducerProject,
  });

  const mapDispatchToProps = (dispatch: ThunkDispatch<TMainReduxStates, void, any>) => ({
    setAdmin: (adminUid: string) => dispatch(setAdminThunk({ adminUid: adminUid })),
    setProject: (project: DocumentReference) => dispatch(setProject(project)),
  });

  return connect(mapStateToProps, mapDispatchToProps)(InnerHoc);
}

import React, { createContext, useContext, useRef, ReactNode } from 'react';

interface ScrollContainerContextType {
  scrollContainerRef: React.RefObject<HTMLDivElement>;
}

const ScrollContainerContext = createContext<ScrollContainerContextType | undefined>(undefined);

export const ScrollContainerProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  return (
    <ScrollContainerContext.Provider value={{ scrollContainerRef }}>
      {children}
    </ScrollContainerContext.Provider>
  );
};

export const useScrollContainer = (): ScrollContainerContextType => {
  const context = useContext(ScrollContainerContext);
  if (context === undefined) {
    throw new Error('useScrollContainer must be used within a ScrollContainerProvider');
  }
  return context;
};

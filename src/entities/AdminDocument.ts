import { IAdminDocument } from './types/admin-entity-types';
import { FirestoreDataConverter, Timestamp, DocumentReference } from 'firebase/firestore';

export default class AdminDocument implements IAdminDocument {
  public static converter: FirestoreDataConverter<AdminDocument> = {
    fromFirestore(snapshot, options) {
      const d = snapshot.data(options)!;
      return new AdminDocument({
        uuid: snapshot.id,
        email: d.email,
        ref: snapshot.ref,
        phone_number: d.phone_number,
        active: d.active,
        address: d.address,
        count_clients: d.count_clients,
        created_time: d.created_time,
        doc_department: d.doc_department,
        doc_project: d.doc_project,
        project: d.project || null,
        department: d.department || null,
        level: d.level,
        name: d.name,
        amartaVip: d.amartaVip,
        admin_rank: d.admin_rank || 99,
      });
    },
    toFirestore(modelObject) {
      return {
        phone_number: modelObject.phone_number,
        active: modelObject.active,
        address: modelObject.address,
        count_clients: modelObject.count_clients,
        created_time: modelObject.created_time,
        doc_department: modelObject.doc_department,
        doc_project: modelObject.doc_project,
        project: modelObject.project || null,
        department: modelObject.department || null,
        level: modelObject.level,
        name: modelObject.name,
        email: modelObject.email,
        amartaVip: modelObject.amartaVip,
        admin_rank: modelObject.admin_rank || 99,
      };
    },
  };
  public uuid!: string;
  public email!: string;
  public active!: boolean;
  public address!: string;
  public count_clients!: number;
  public created_time!: Timestamp;
  public doc_department!: null | DocumentReference;
  public doc_project!: DocumentReference;
  public project!: IAdminDocument['project'];
  public department!: IAdminDocument['department'];
  public level!: IAdminDocument['level'];
  public name!: string;
  public phone_number!: string;
  public admin_rank!: number;
  public amartaVip!: IAdminDocument['amartaVip'];
  public ref!: DocumentReference;

  constructor(params: IAdminDocument) {
    this.active = params.active;
    this.address = params.address;
    this.count_clients = params.count_clients;
    this.created_time = params.created_time;
    this.doc_department = params.doc_department;
    this.doc_project = params.doc_project;
    this.project = params.project || null;
    this.department = params.department || null;
    this.level = params.level;
    this.name = params.name;
    this.email = params.email;
    this.phone_number = params.phone_number;
    this.amartaVip = params.amartaVip || null;
    this.admin_rank = params.admin_rank || 99;
    this.ref = params.ref;
  }
}

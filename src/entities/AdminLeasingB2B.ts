import { DocumentReference, FirestoreDataConverter } from 'firebase/firestore';

export interface IAdminLeasingB2BModel {
  ref: DocumentReference;
  email: string;
  full_name: string;
  leasing_company: {
    code: string;
    name: string;
    ref: DocumentReference;
  };
}

export default class AdminLeasingB2B implements IAdminLeasingB2BModel {
  static converter: FirestoreDataConverter<AdminLeasingB2B> = {
    fromFirestore(snapshot, options) {
      const data = snapshot.data(options)! as IAdminLeasingB2BModel;

      const adminLeasing = new AdminLeasingB2B();
      adminLeasing.ref = snapshot.ref;
      adminLeasing.leasing_company = data.leasing_company;
      adminLeasing.full_name = data.full_name;
      adminLeasing.email = data.email;

      return adminLeasing;
    },
    toFirestore(modelObject: AdminLeasingB2B) {
      return {
        email: modelObject.email,
        full_name: modelObject.full_name,
        leasing_company: modelObject.leasing_company,
      };
    },
  };
  public ref!: IAdminLeasingB2BModel['ref'];
  public email!: IAdminLeasingB2BModel['email'];
  public full_name!: IAdminLeasingB2BModel['full_name'];
  public leasing_company!: IAdminLeasingB2BModel['leasing_company'];
}

import { AdminLogSessionEntityTypes } from './types/admin-log-session-entity-types';
import { FirestoreDataConverter, DocumentReference } from 'firebase/firestore';

export default class AdminLoginSessionEntity implements AdminLogSessionEntityTypes {
  static converter: FirestoreDataConverter<AdminLoginSessionEntity> = {
    fromFirestore(snapshot, options) {
      const data = snapshot.data(options)! as AdminLoginSessionEntity;
      const adminLoginSessionEntity = new AdminLoginSessionEntity();

      adminLoginSessionEntity.admin = data.admin;
      adminLoginSessionEntity.messages = data.messages ?? [];
      adminLoginSessionEntity.sent_to_bigquery_at = data.sent_to_bigquery_at ?? null;
      adminLoginSessionEntity.analytic_data = data.analytic_data;
      adminLoginSessionEntity.last_heartbeat = data.last_heartbeat;
      adminLoginSessionEntity.auto_end_session_at = data.auto_end_session_at;
      adminLoginSessionEntity.signed_in_at = data.signed_in_at;
      adminLoginSessionEntity.ref = snapshot.ref;

      return adminLoginSessionEntity;
    },
    toFirestore(modelObject) {
      const data = {
        admin: modelObject.admin,
        analytic_data: modelObject.analytic_data ?? null,
        messages: modelObject.messages ?? [],
        auto_end_session_at: modelObject.auto_end_session_at,
        signed_in_at: modelObject.signed_in_at,
        last_heartbeat: modelObject.last_heartbeat,
        sent_to_bigquery_at: modelObject.sent_to_bigquery_at ?? null,
      };
      return { ...data };
    },
  };
  public admin!: AdminLogSessionEntityTypes['admin'];
  public last_heartbeat!: AdminLogSessionEntityTypes['last_heartbeat'];
  public signed_in_at!: AdminLogSessionEntityTypes['signed_in_at'];
  public analytic_data!: AdminLogSessionEntityTypes['analytic_data'];
  public auto_end_session_at!: AdminLogSessionEntityTypes['auto_end_session_at'];
  public messages!: AdminLogSessionEntityTypes['messages'];
  public ref!: DocumentReference;
  public sent_to_bigquery_at!: AdminLogSessionEntityTypes['sent_to_bigquery_at'];
}

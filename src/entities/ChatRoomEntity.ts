import { IChatRoomEntity, IChatRoomEntityFields } from './types/chat-room-entity-types';
import 'firebase/firestore';
import ClientEntity from './ClientEntity';
import DepartmentEntity from './DeparmentEntity';
import {
  collection,
  doc,
  DocumentReference,
  FirestoreDataConverter,
  getDoc,
  Timestamp,
  updateDoc,
} from 'firebase/firestore';

export default class ChatRoomEntity implements IChatRoomEntity {
  public static converter: FirestoreDataConverter<ChatRoomEntity> = {
    fromFirestore: (snapshot, options) => {
      const data = snapshot.data(options)! as IChatRoomEntityFields;
      const entity = new ChatRoomEntity({
        ref: snapshot.ref,
        blockReason: data.blockReason || null,
        blocked: data.blocked || false,
        clients: data.clients,
        contacts: data.contacts,
        created_at: data.created_at,
        doc_department: data?.doc_department || null,
        cityGroup: data.cityGroup || null,
        cityGroupUpdatedAt: data.cityGroupUpdatedAt || null,
        cityGroupUpdatedBy: data.cityGroupUpdatedBy || null,
        organization: data.organization || null,
        organizationGroup: data.organizationGroup || null,
        organizationUpdatedAt: data.organizationUpdatedAt || null,
        organizationUpdatedBy: data.organizationUpdatedBy || null,
        dream_vehicle: data.dream_vehicle || null,
        headers: data.headers,
        label: data.label || null,
        label_updated_at: data.label_updated_at || null,
        last_inbound: data.last_inbound || null,
        last_message_type: data.last_message_type || null,
        recent_chat: data.recent_chat,
        wait_for_answer: data.wait_for_answer || null,
        exclusive_admin: data.exclusive_admin || null,
        agent_ai_reply: data.agent_ai_reply || false,
        pinnedMessages: data.pinnedMessages || [],
      });

      entity.ref = snapshot.ref;

      return entity;
    },
    toFirestore: (modelObject) => ({
      ref: modelObject.ref,
      blockReason: modelObject.blockReason || null,
      blocked: modelObject.blocked || false,
      clients: modelObject.clients,
      contacts: modelObject.contacts,
      created_at: modelObject.created_at,
      doc_department: modelObject?.doc_department ?? null,
      cityGroup: modelObject.cityGroup || null,
      cityGroupUpdatedAt: modelObject.cityGroupUpdatedAt || null,
      cityGroupUpdatedBy: modelObject.cityGroupUpdatedBy || null,
      organization: modelObject.organization || null,

      organizationGroup: modelObject.organizationGroup || null,
      dream_vehicle: modelObject.dream_vehicle || null,
      headers: modelObject.headers,
      label: modelObject.label || null,
      label_updated_at: modelObject.label_updated_at || null,
      last_inbound: modelObject.last_inbound || null,
      last_message_type: modelObject.last_message_type || null,
      recent_chat: modelObject.recent_chat,
      wait_for_answer: modelObject.wait_for_answer || null,
      exclusive_admin: modelObject.exclusive_admin || null,
      agent_ai_reply: modelObject.agent_ai_reply || false,
      pinnedMessages: modelObject.pinnedMessages || [],
    }),
  };
  public ref: IChatRoomEntity['ref'];
  public blocked: IChatRoomEntity['blocked'];
  public blockReason: IChatRoomEntity['blockReason'];
  public clients: IChatRoomEntity['clients'];
  public contacts: IChatRoomEntity['contacts'];
  public created_at: IChatRoomEntity['created_at'];
  public doc_department: IChatRoomEntity['doc_department'];
  public cityGroup: IChatRoomEntity['cityGroup'];
  public cityGroupUpdatedAt: IChatRoomEntity['cityGroupUpdatedAt'];
  public cityGroupUpdatedBy: IChatRoomEntity['cityGroupUpdatedBy'];
  public organization: IChatRoomEntity['organization'];
  public organizationUpdatedAt: IChatRoomEntity['organizationUpdatedAt'];
  public organizationUpdatedBy: IChatRoomEntity['organizationUpdatedBy'];
  public organizationGroup: IChatRoomEntity['organizationGroup'];
  public dream_vehicle: IChatRoomEntity['dream_vehicle'];
  public headers: IChatRoomEntity['headers'];
  public label: IChatRoomEntity['label'];
  public label_updated_at: IChatRoomEntity['label_updated_at'];
  public last_inbound: IChatRoomEntity['last_inbound'];
  public last_message_type: IChatRoomEntity['last_message_type'];
  public recent_chat: IChatRoomEntity['recent_chat'];
  public wait_for_answer: IChatRoomEntity['wait_for_answer'];
  public exclusive_admin: IChatRoomEntity['exclusive_admin'];
  public agent_ai_reply: IChatRoomEntity['agent_ai_reply'];
  public pinnedMessages: IChatRoomEntity['pinnedMessages'];

  private _clientEntities!: Array<ClientEntity>;
  private _department!: DepartmentEntity;

  constructor(params: IChatRoomEntity) {
    const {
      ref,
      blockReason,
      blocked,
      clients,
      contacts,
      created_at,
      doc_department,
      cityGroup,
      organization,
      organizationUpdatedAt,
      organizationUpdatedBy,
      organizationGroup,
      dream_vehicle,
      headers,
      label,
      label_updated_at,
      last_inbound,
      last_message_type,
      recent_chat,
      wait_for_answer,
      exclusive_admin,
      agent_ai_reply,
      pinnedMessages,
    } = params;

    this.ref = ref;
    this.blockReason = blockReason;
    this.blocked = blocked;
    this.clients = clients;
    this.contacts = contacts;
    this.created_at = created_at;
    this.doc_department = doc_department;
    this.cityGroup = cityGroup;
    this.organization = organization;
    this.organizationUpdatedAt = organizationUpdatedAt;
    this.organizationUpdatedBy = organizationUpdatedBy;
    this.organizationGroup = organizationGroup;
    this.dream_vehicle = dream_vehicle;
    this.headers = headers;
    this.label = label;
    this.label_updated_at = label_updated_at;
    this.last_inbound = last_inbound;
    this.last_message_type = last_message_type;
    this.recent_chat = recent_chat;
    this.wait_for_answer = wait_for_answer;
    this.exclusive_admin = exclusive_admin;
    this.agent_ai_reply = agent_ai_reply;
    this.pinnedMessages = pinnedMessages;
  }

  get projectRef(): DocumentReference | null {
    return this.ref?.parent?.parent ?? null;
  }

  async getClientEntities(params?: { refresh?: boolean }): Promise<Array<ClientEntity>> {
    let entities: Array<ClientEntity> = [];
    const refresh = async () => {
      for (const client of this.clients) {
        const get = await getDoc(client.withConverter(ClientEntity.converter));
        if (get.exists()) {
          entities.push(get.data()!);
        }
      }
    };

    this._clientEntities = entities;
    if (params?.refresh) {
      await refresh();
    } else if (!this._clientEntities) {
      await refresh();
    }

    return this._clientEntities;
  }

  public async updateDepartment(department: string | DocumentReference | null) {
    if (!this.projectRef) return;
    let departmentRef!: DocumentReference | null;

    if (typeof department == 'string') {
      departmentRef = doc(collection(this.projectRef, 'departments'), department);
    } else if (!department) {
      departmentRef = null;
    } else {
      departmentRef = department;
    }

    await updateDoc(this.ref, {
      doc_department: department,
    });

    this.doc_department = departmentRef;
  }

  public async updateLabel(label: DocumentReference | false) {
    await updateDoc(this.ref, {
      label: !label ? null : label,
      label_updated_at: Timestamp.now(),
    });

    this.label = !label ? null : label;
  }
}

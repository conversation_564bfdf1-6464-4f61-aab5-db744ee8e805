import {
  EContact,
  IClientEntity,
  IClientEntityOrderHistory,
  IClientInfo,
} from './types/client-entity-types';
import {
  DocumentReference,
  FirestoreDataConverter,
  getDoc,
  Timestamp,
  updateDoc,
  WriteBatch,
} from 'firebase/firestore';

export default class ClientEntity implements IClientEntity {
  static converter: FirestoreDataConverter<ClientEntity> = {
    fromFirestore(snapshot, options) {
      const data: IClientEntity = snapshot.data(options)! as IClientEntity;
      return new ClientEntity(
        {
          developer: data.developer || false,
          profile: data.profile,
          details: data.details || {
            owner_phone_number: '',
            guarantor_phone_number: '',
            order_maker_phone_number: '',
          },
          dream_vehicle: data.dream_vehicle || null,
          contacts: data.contacts,
          created_time: data.created_time,
          survey: data?.survey
            ? {
                ...data.survey,
                credit_scheme: data.survey?.credit_scheme || null,
              }
            : null,
          survey_loan: data?.survey_loan || null,
          cash_offer: data?.cash_offer || null,
          freeLeadsStatus: data.freeLeadsStatus || null,
          acquiredLeadsStatus: data.acquiredLeadsStatus || null,
          broadcast: data.broadcast || {
            allow_to_broadcast: false,
            client_purchase: null,
            is_purchased: false,
          },
          order_histories: data.order_histories || [],
          owned_vehicle: data.owned_vehicle || [],
          leads: data.leads,
          notes: data.notes || null,
          finalDecision: data.finalDecision || null,
        },
        snapshot.ref,
      );
    },
    toFirestore(modelObject) {
      const data = {
        developer: modelObject.developer || false,
        created_time: modelObject.created_time!,
        contacts: modelObject.contacts!,
        details: modelObject.details || {
          owner_phone_number: '',
          guarantor_phone_number: '',
          order_maker_phone_number: '',
        },
        dream_vehicle: modelObject.dream_vehicle || null,
        profile: modelObject.profile!,
        survey: modelObject.survey || null,
        survey_loan: modelObject.survey_loan || null,
        cash_offer: modelObject.cash_offer || null,
        freeLeadsStatus: modelObject.freeLeadsStatus || null,
        acquiredLeadsStatus: modelObject.acquiredLeadsStatus || null,
        broadcast: modelObject.broadcast || {
          allow_to_broadcast: false,
          purchase_scheme: 'CREDIT',
          client_purchase: null,
          is_purchased: false,
          notes: '',
        },
        order_histories: modelObject.order_histories || [],
        ownedVehicle: modelObject.owned_vehicle || [],
        leads: modelObject.leads || null,
        notes: modelObject.notes || null,
        finalDecision: modelObject.freeLeadsStatus || null,
      };
      console.log(modelObject.notes);
      return { ...data };
    },
  };
  public developer: boolean;
  public created_time: Timestamp;
  public profile: IClientInfo;
  public contacts: Partial<Record<EContact, 'string' | null>>;
  public survey: IClientEntity['survey'];
  public survey_loan: IClientEntity['survey_loan'];
  public dream_vehicle: IClientEntity['dream_vehicle'];
  public ref?: DocumentReference;
  public details: IClientEntity['details'];
  public cash_offer: IClientEntity['cash_offer'];
  public freeLeadsStatus: IClientEntity['freeLeadsStatus'];
  public acquiredLeadsStatus: IClientEntity['acquiredLeadsStatus'];
  public broadcast: IClientEntity['broadcast'];
  public order_histories: IClientEntity['order_histories'];
  public leads: IClientEntity['leads'];
  public notes: IClientEntity['notes'];
  public finalDecision: IClientEntity['finalDecision'];
  public owned_vehicle: IClientEntity['owned_vehicle'];

  constructor(params: IClientEntity, ref?: DocumentReference) {
    this.developer = params.developer;
    this.profile = params.profile;
    this.created_time = params.created_time;
    this.contacts = params.contacts;
    this.survey = params.survey;
    this.dream_vehicle = params.dream_vehicle;
    this.survey_loan = params.survey_loan;
    this.details = params.details;
    this.cash_offer = params.cash_offer;
    this.broadcast = params.broadcast;
    this.freeLeadsStatus = params.freeLeadsStatus;
    this.acquiredLeadsStatus = params.acquiredLeadsStatus;
    this.order_histories = params.order_histories;
    this.leads = params.leads || null;
    this.notes = params.notes || null;
    this.finalDecision = params.finalDecision || null;
    this.owned_vehicle = params.owned_vehicle || [];

    if (ref) this.ref = ref;
  }

  public addNewOrderHistory = async (params: IClientEntityOrderHistory, batch?: WriteBatch) => {
    const orderHistories = [...this.order_histories];
    orderHistories.push(params);

    if (this.ref) {
      if (batch) {
        batch.update(this.ref, {
          order_histories: orderHistories,
        });
      }

      await updateDoc(this.ref, {
        order_histories: orderHistories,
      });
    }
  };

  refresh = async () => {
    if (!this.ref) throw new Error('Ref is Empty!');
    const client = await getDoc(this.ref?.withConverter(ClientEntity.converter));
    if (client.exists()) {
      const data = client.data()!;

      this.developer = data.developer;
      this.profile = data.profile;
      this.created_time = data.created_time;
      this.contacts = data.contacts;
      this.survey = data.survey;
      this.dream_vehicle = data.dream_vehicle;
      this.details = data.details;
      this.cash_offer = data.cash_offer;
      this.broadcast = data.broadcast;
      this.order_histories = data.order_histories;
      this.notes = data.notes;
      this.freeLeadsStatus = data.freeLeadsStatus;
      this.acquiredLeadsStatus = data.acquiredLeadsStatus;
      this.order_histories = data.order_histories;
      this.owned_vehicle = data.owned_vehicle;
    }
  };
}

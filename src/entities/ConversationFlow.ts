import { ConversationFlowDocument } from './types/conversation-flow.types';
import { FirestoreDataConverter, Timestamp } from 'firebase/firestore';
import {
  IConversationFlowMessage,
  IConversationFlowStartAt,
} from '../redux/add-conversation-flow/addConversationFlow.slice';

class ConversationFlow implements ConversationFlowDocument {
  public static converter: FirestoreDataConverter<ConversationFlow> = {
    fromFirestore: (snapshot): ConversationFlow => {
      const data = snapshot.data();
      return new ConversationFlow({
        messages: data.messages,
        startAt: data.startAt,
        createdAt: data.createdAt,
        updatedAt: data.updatedAt,
        active: data.active || false,
        referralSourceId: data.referralSourceId || null,
        ref: snapshot.ref,
      });
    },
    toFirestore: (modelObject) => {
      return {
        messages: modelObject.messages,
        startAt: modelObject.startAt,
        createdAt: modelObject.createdAt,
        updatedAt: modelObject.updatedAt,
        active: modelObject.active,
        ref: modelObject.ref,
        referralSourceId: modelObject.referralSourceId || null,
      };
    },
  };
  createdAt: Timestamp;
  messages: IConversationFlowMessage[];
  startAt: IConversationFlowStartAt;
  updatedAt: Timestamp;
  active: boolean;
  referralSourceId: ConversationFlowDocument['referralSourceId'];
  ref: ConversationFlowDocument['ref'];

  constructor(params: {
    createdAt: Timestamp;
    messages: IConversationFlowMessage[];
    startAt: IConversationFlowStartAt;
    updatedAt: Timestamp;
    active: boolean;
    ref: ConversationFlowDocument['ref'];
    referralSourceId: ConversationFlowDocument['referralSourceId'];
  }) {
    this.createdAt = params.createdAt;
    this.messages = params.messages;
    this.startAt = params.startAt;
    this.updatedAt = params.updatedAt;
    this.active = params.active;
    this.referralSourceId = params.referralSourceId || null;
    this.ref = params.ref;
  }
}

export default ConversationFlow;

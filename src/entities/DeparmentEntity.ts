import { IDepartmentEntity, IDepartmentFields } from './types/department-entity-types';
import 'firebase/firestore';
import { DocumentReference, FirestoreDataConverter } from 'firebase/firestore';

export default class DepartmentEntity implements IDepartmentEntity {
  public static converter: FirestoreDataConverter<DepartmentEntity> = {
    fromFirestore(snapshot, options): DepartmentEntity {
      const data: IDepartmentFields = snapshot.data(options)! as IDepartmentFields;
      const entity = new DepartmentEntity({
        ...data,
      });

      entity.ref = snapshot.ref;

      return entity;
    },
    toFirestore(modelObject) {
      const data = {
        description: modelObject.description,
        active: modelObject.active,
        name: modelObject.name,
      };
      return { ...data };
    },
  };

  public active!: boolean;
  public description!: string | null;
  public name!: string;
  public ref!: DocumentReference;

  constructor(params: IDepartmentFields) {
    this.active = params.active;
    this.description = params.description;
    this.name = params.name;
  }
}

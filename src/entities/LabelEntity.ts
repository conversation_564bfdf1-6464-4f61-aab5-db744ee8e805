import 'firebase/firestore';
import { ILabelEntity } from './types/label-entity-types';
import { DocumentReference, FirestoreDataConverter } from 'firebase/firestore';

export default class LabelEntity implements ILabelEntity {
  static converter: FirestoreDataConverter<LabelEntity> = {
    fromFirestore(snapshot, options): LabelEntity {
      const data: ILabelEntity = snapshot.data(options)! as ILabelEntity;
      return new LabelEntity(
        {
          name: data.name,
          description: data.description,
          active: data.active,
          group: data.group,
        },
        snapshot.ref,
      );
    },
    toFirestore(modelObject) {
      const data = {
        name: modelObject.name,
        description: modelObject.description,
        active: modelObject.active,
        group: modelObject.group,
      };
      return { ...data };
    },
  };
  public active!: boolean;
  public group!: ILabelEntity['group'];
  public description!: string | null;
  public name!: string;
  public ref?: DocumentReference;

  constructor(props: ILabelEntity, ref?: DocumentReference) {
    this.active = props.active;
    this.description = props.description;
    this.group = props.group;
    this.name = props.name;
    this.ref = ref ?? undefined;
  }
}

import { IFirestoreMessageEntity, TStatuses } from './types/message-entities-types';
import { DocumentReference, FirestoreDataConverter } from 'firebase/firestore';

export default class MessageEntity implements IFirestoreMessageEntity {
  public static converter: FirestoreDataConverter<MessageEntity> = {
    toFirestore(modelObject) {
      return {
        statuses: modelObject.statuses,
        message: modelObject.message,
        origin: modelObject.origin,
      };
    },
    fromFirestore(snapshot, options) {
      const data = snapshot.data(options)!;
      return new MessageEntity(
        {
          statuses: data.statuses,
          message: data.message,
          origin: data.origin,
          error: data.error,
        },
        snapshot.ref,
      );
    },
  };
  public origin!: IFirestoreMessageEntity['origin'];
  public message!: IFirestoreMessageEntity['message'];
  public statuses?: TStatuses;
  public error?: IFirestoreMessageEntity['error'];
  public ref!: DocumentReference;

  constructor(params: IFirestoreMessageEntity, ref: DocumentReference) {
    this.origin = params.origin;
    this.message = params.message;
    this.statuses = params.statuses;
    if (params.error) this.error = params.error;
    this.ref = ref;
  }
}

import { ITemplateMessageDocument } from './types/message-template-entity-types';
import 'firebase/firestore';
import { DocumentReference, FirestoreDataConverter } from 'firebase/firestore';

export default class MessageTemplateEntity implements ITemplateMessageDocument {
  static converter: FirestoreDataConverter<MessageTemplateEntity> = {
    fromFirestore(snapshot, options) {
      const data: ITemplateMessageDocument = snapshot.data(options)! as ITemplateMessageDocument;
      const messageEntity = new MessageTemplateEntity();
      messageEntity.text = data.text;
      messageEntity.welcome_message = data.welcome_message ?? false;
      messageEntity.ref = snapshot.ref;
      messageEntity.type = data.type || 'text';
      messageEntity.button = data.button || undefined;
      messageEntity.label = data.label || null;
      return messageEntity;
    },
    toFirestore(modelObject) {
      const data = {
        text: modelObject.text ?? '',
        welcome_message: modelObject.welcome_message ?? false,
        type: modelObject.type || 'text',
        button: modelObject.button || null,
        label: modelObject.label || null,
      };
      return { ...data };
    },
  };
  public ref!: DocumentReference;
  public text!: ITemplateMessageDocument['text'];
  public type!: ITemplateMessageDocument['type'];
  public button?: ITemplateMessageDocument['button'];
  public welcome_message!: ITemplateMessageDocument['welcome_message'];
  public label!: ITemplateMessageDocument['label'];
}

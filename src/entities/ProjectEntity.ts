import { ProjectEntityTypes } from './types/project-entity-types';
import { FirestoreDataConverter } from 'firebase/firestore';

export default class ProjectEntity implements ProjectEntityTypes {
  static converter: FirestoreDataConverter<ProjectEntity> = {
    fromFirestore(snapshot, options) {
      const data = snapshot.data(options);
      const project = new ProjectEntity();
      project.null_department_reply_message = data.null_department_reply_message ?? false;
      project.null_department_visibility = data.null_department_visibility ?? false;
      project.phone_number_visibility = data.phone_number_visibility ?? false;
      project.blocked_number_visibility = data.blocked_number_visibility ?? false;
      project.legal_name = data.legal_name;
      project.provider = data.provider;
      project.cityGroupCompany = data.cityGroupCompany;
      project.phone_number = data.phone_number || '';
      project.group = data.group || '';
      project.brands = data.brands || [];
      project.ref = snapshot.ref;

      return project;
    },
    toFirestore(modelObject) {
      return {
        phone_number_visibility: modelObject.phone_number_visibility,
        null_department_visibility: modelObject.null_department_visibility,
        null_department_reply_message: modelObject.null_department_reply_message,
        blocked_number_visibility: modelObject.blocked_number_visibility,
        legal_name: modelObject.legal_name,
        provider: modelObject.provider,
        cityGroupCompany: modelObject.cityGroupCompany,
        phone_number: modelObject.phone_number,
        group: modelObject.group,
        brands: modelObject.brands,
      };
    },
  };
  public null_department_reply_message!: boolean;
  public null_department_visibility!: boolean;
  public phone_number_visibility!: boolean;
  public blocked_number_visibility!: boolean;
  public legal_name!: string;
  public provider!: string;
  public cityGroupCompany!: ProjectEntityTypes['cityGroupCompany'];
  public phone_number!: string;
  public ref!: ProjectEntityTypes['ref'];
  public group!: string;
  public brands!: ProjectEntityTypes['brands'];
}

import firestore, { FirestoreDataConverter } from 'firebase/firestore';

interface Variable {
  type: string;
  name: string;
}

class ProviderTemplate<T = Date> {
  public static converter: FirestoreDataConverter<ProviderTemplate> = {
    toFirestore(modelObject: ProviderTemplate<Date>): firestore.DocumentData {
      return {
        body: modelObject.body,
        createdAt: modelObject.createdAt,
        variables: modelObject.variables,
      };
    },
    fromFirestore(
      snapshot: firestore.QueryDocumentSnapshot<firestore.DocumentData>,
    ): ProviderTemplate {
      const data = snapshot.data();
      return new ProviderTemplate({
        id: snapshot.id,
        ref: snapshot.ref,
        body: data.body,
        variables: data.variables || [],
        createdAt: data.createdAt.toDate as Date,
      });
    },
  };
  public ref: firestore.DocumentReference;
  public id: string;
  public body: string;
  public createdAt: T;
  public variables: Variable[];

  constructor(params: {
    ref: firestore.DocumentReference;
    id: string;
    body: string;
    createdAt: T;
    variables: Variable[];
  }) {
    this.ref = params.ref;
    this.id = params.id;
    this.body = params.body;
    this.createdAt = params.createdAt;
    this.variables = params.variables;
  }
}

export default ProviderTemplate;

import { DocumentReference, Timestamp } from 'firebase/firestore';

export interface IAdminDocument {
  admin_rank: number;
  uuid: string;
  active: boolean;
  address: string;
  count_clients: number;
  created_time: Timestamp;
  doc_department: null | DocumentReference;
  doc_project: DocumentReference;
  project: {
    name: string;
    provider: string;
  };
  department: {
    name: string;
  } | null;
  level: 'admin' | 'owner';
  name: string;
  email: string;
  phone_number: string;
  ref: DocumentReference;
  amartaVip: {
    mediatorName: string;
    mediatorCode: string;
  } | null;
}

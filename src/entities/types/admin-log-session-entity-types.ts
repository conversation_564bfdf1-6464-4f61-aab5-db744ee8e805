import 'firebase/firestore';
import { DocumentReference, Timestamp } from 'firebase/firestore';
import { IFirestoreMessageEntity } from './message-entities-types';

export interface AdminLogSessionEntityTypes {
  last_heartbeat: Timestamp | Date;
  auto_end_session_at: Timestamp | Date;
  signed_in_at: Timestamp | Date;
  admin: {
    name: string;
    email: string;
    ref: DocumentReference;
  };
  analytic_data: any;
  messages: Pick<IFirestoreMessageEntity, 'message' | 'origin'>[];
  sent_to_bigquery_at: Timestamp | Date | null;
}

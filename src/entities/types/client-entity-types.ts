import 'firebase/firestore';
import firestore, { Timestamp } from 'firebase/firestore';
import { EMaritalStatus } from '../../Components/ImageCapture/types/marital-status-types';
import { EFamilyEducation } from './family-register-entity-types';
export interface IClientAddress {
  address: string;
  province: { name: string; code: string };
  city: { name: string; code: string };
  district: { name: string; code: string };
  sub_district: { name: string; code: string };
  postal_code: string;
}

export interface IClientEntity {
  developer: boolean;
  contacts: Partial<Record<EContact, 'string' | null>>;
  created_time: Timestamp;
  profile: IClientInfo;
  dream_vehicle: {
    updated_at?: Timestamp;
    condition?: 'new' | 'used' | null;
    license_plate?: string;
    brand_name?: string;
    brand_uuid?: string;
    model_name: string;
    model_uuid?: string;
    model_category: string;
    variant_code: string;
    variant_name: string;
    variant_uuid?: string;
    variant_free_text?: string;
    color_name?: string | null;
    color_code?: string | null;
    year?: string | null;
    mileage?: string;
    buy_time: Timestamp;
    price?: number | null;
  } | null;
  details: {
    owner_phone_number: string;
    order_maker_phone_number: string;
    guarantor_phone_number: string;
    idCardOwner: IdCard | null;
    idCardGuarantor: IdCard | null;
    idCardOrderMaker: IdCard | null;
    idCardGuarantorSpouse: IdCard | null;
    familyRegister: IFamilyRegister | null;
    selfie: ISelfie | null;
  };
  cash_offer: {
    offer_code: string;
    last_request_at: Timestamp;
  } | null;
  survey_loan: {
    last_send: Timestamp | null;
    offer_code: string;
    order_code: string;
    scheme?: {
      tenor: number;
      amount: number;
    } | null;
  } | null;
  survey: {
    last_send?: Timestamp | null;
    offer_code?: string;
    credit_scheme: {
      down_payment: number;
      down_payment_discount: number;
      installment: number;
      tenor: number;
      surveyTime: null | Timestamp;
      surveyGmapUrl: null | string;
      surveyGeo: { lat: number; long: number } | null;
      discountTenor?: number;
      discountInstallment?: number;
      dealCode: string;
      priceListSource: string;
      lastUpdate: null | Timestamp;
      selectedLeasingCode: null | string;
      leasingAdminId: null | string;
    } | null;
  } | null;

  freeLeadsStatus: {
    acquired?: boolean;
    agentCode?: string;
    agentName?: string;
    acquiredAt?: Timestamp | null;
    submitted: boolean;
    createdAt: Timestamp | null;
    organization?: string | null;
  } | null;

  acquiredLeadsStatus: {
    acquired: boolean;
    agentName: string;
    agentCode: string;
    acquiredAt: Timestamp;
    organization: string;
  } | null;

  // Sudah tidak digunakan
  broadcast: {
    allow_to_broadcast: boolean;
    purchase_scheme: 'CREDIT' | 'CASH';
    is_purchased: boolean;
    client_purchase: {
      client_id: string;
      name: string;
      notification_received_at: Timestamp;
      transaction_code: string;
    } | null;
    notes?: string;
  };

  order_histories: IClientEntityOrderHistory[];
  leads: {
    [key: string]: any;
  } | null;
  notes?: {
    updatedAt: Timestamp;
    text: string;
  } | null;

  finalDecision: {
    result: string;
    updatedAt: Timestamp;
    followUp: {
      dateTime: Timestamp;
      message: {
        template: string;
        variables: [];
      };
    } | null;
  } | null;
  owned_vehicle: IOwnedVehicle[];
}

export interface IOwnedVehicle {
  id: string;
  brand_name: string;
  brand_uuid: string;
  model_name: string;
  model_uuid: string;
  model_category: string;
  variant_free_text: string;
  year: string;
  license_plate: string;
  mileage: string;
}

export interface IClientEntityOrderHistory {
  offer_code: string;
  order_code: string;
  created_at: Timestamp;
  survey_order_type: 'vehicle' | 'loan';
  payment_scheme: 'CASH' | 'CREDIT';
  source: 'IDEAL' | 'TRIFORCE';
  orderCreatedBy?: string;
  createdByAdminIdeal?: {
    name: string;
    email: string;
    ref: firestore.DocumentReference;
  } | null;
  lastUpdateDataToOtodis?: {
    success: boolean;
    updatedAt: Timestamp;
    offerCode: string;
    dataUpdate?: any;
  } | null;
  lastUpdateDataPromoCodeToOtodis?: {
    success: boolean;
    updatedAt: Timestamp;
    offerCode: string;
    dataUpdate?: any;
  } | null;
  createBill?: {
    success: boolean;
    updatedAt: Timestamp;
    offerCode: string;
    dataUpdate?: any;
  };
}

export interface IClientInfo {
  name: string;
  temporary_name?: string;
  phone_number: string;
  area?: { text: string; value: string } | null;
  organization?: string;
  organization_group?: string;
}

export interface IGeoFields {
  name: string;
  code: string;
}

export interface IdCard<Date = firestore.Timestamp> {
  idCardImage: string;
  idCardNumber: string;
  fullName: string;
  birthMother: string;
  placeOfBirth: string;
  dateOfBirth: Date;

  occupation: string;
  occupationCode: string;

  maritalStatus: string;
  maritalStatusCode: EMaritalStatus | null;

  lastEducation: string;
  lastEducationCode: EFamilyEducation | null;

  fullAddress: string;
  zipCode: string;
  province: IGeoFields;
  city: IGeoFields;
  district: IGeoFields;
  subDistrict: IGeoFields;
  hamlet: string;
  neighbourhood: string;

  domicileFullAddress: string;
  domicileZipCode: string;
  domicileProvince: IGeoFields;
  domicileCity: IGeoFields;
  domicileDistrict: IGeoFields;
  domicileSubDistrict: IGeoFields;
  domicileHamlet: string;
  domicileNeighbourhood: string;
}

export interface IFamilyRegister {
  familyRegisterNumber: string;
  familyRegisterImage: string;
}

export interface ISelfie {
  selfieImage: string;
}

export type EContact = 'whatsapp';

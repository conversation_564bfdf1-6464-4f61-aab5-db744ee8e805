import firestore from 'firebase/firestore';
import {
  IConversationFlowMessage,
  IConversationFlowStartAt,
} from '../../redux/add-conversation-flow/addConversationFlow.slice';

export interface ConversationFlowDocument<
  REF = firestore.DocumentReference,
  DATE = firestore.Timestamp,
> {
  createdAt: DATE;
  updatedAt: DATE;
  startAt: IConversationFlowStartAt;
  messages: IConversationFlowMessage[];
  active: boolean;
  referralSourceId: {
    dealCode: string;
    sourceId: string;
  };
  ref: REF;
}

export enum EFamilyEducation {
  NO_SCHOOL = 'NO_SCHOOL',
  DID_NOT_FINISH_ELEMENTARY_SCHOOL = 'DID_NOT_FINISH_ELEMENTARY_SCHOOL',
  GRADUATED_ELEMENTARY_SCHOOL = 'GRADUATED_ELEMENTARY_SCHOOL',
  JUNIOR_HIGH_SCHOOL = 'JUNIOR_HIGH_SCHOOL',
  HIGH_SCHOOL = 'HIGH_SCHOOL',
  DIPLOMA_1_2 = 'DIPLOMA_1_2',
  DIPLOMA_3 = 'DIPLOMA_3',
  DIPLOMA_4_OR_BACHELOR = 'DIPLOMA_4_OR_BACHELOR',
  MASTER = 'MASTER',
  DOCTOR = 'DOCTOR',
}

export enum EFamilyRelation {
  HEADS_OF_FAMILY = 'HEADS_OF_FAMILY',
  HUSBAND = 'HUSBAND',
  WIFE = 'WIFE',
  CHILD = 'CHILD',
  SON_IN_LAW = 'SON_IN_LAW',
  GRANDCHILD = 'GRANDCHILD',
  PARENTS = 'PARENTS',
  IN_LAWS = 'IN_LAWS',
  ANOTHER_FAMILY = 'ANOTHER_FAMILY',
  HELPER = 'HELPER',
  OTHER = 'OTHER',
}

import { DocumentReference, Timestamp } from 'firebase/firestore';

export interface IFirestoreMessageEntity {
  origin: I<PERSON><PERSON><PERSON>;
  message: {
    id: string;
    audio?: IFileContext;
    document?: IFileContext;
    image?: IFileContext;
    video?: IFileContext;
    voice?: IFileContext;
    text?: TextContext;
    button?: IButtonContext;
    location?: ILocationContext;
    sticker?: IStickerContext;
    contacts?: ITypeMessageContacts[];
    context?: IReplyContext;
    unixtime: number;
    timestamp: Timestamp;
    type: EMessageContext;
    direction: EMessageDirection;
    referral: IReferralMessageContext;
    interactive?: {
      header?: {
        type: 'text' | 'video' | 'image' | 'document';
        text?: string;
        video?: IFileContext;
        image?: IFileContext;
        document?: IFileContext;
      };
      body?: {
        text: string;
      };
      footer?: {
        text: string;
      };

      button_reply?: {
        id: string;
        title: string;
      };
      list_reply?: {
        id: string;
        title: string;
      };
      type?: 'button_reply' | 'list_reply';
    };
  };
  statuses?: TStatuses;
  error?: {
    code: number;
    href: string;
    title: string;
  };
}

interface IOrigin {
  display_name: string;
  id: string;
  reference: DocumentReference;
}

export type EMessageDirection = 'IN' | 'OUT';

export interface IFirestoreClientEntity {
  wa_id: string;
  name: string;
  phone_number: string;
  created_time: Date;
}

export type TStatuses = Record<EStatuses, Timestamp | null>;

export type EStatuses = 'sent' | 'delivered' | 'read' | 'failed';

export type EMessageContext =
  | 'audio'
  | 'document'
  | 'image'
  | 'location'
  | 'system'
  | 'text'
  | 'video'
  | 'voice'
  | 'context'
  | 'sticker'
  | 'button'
  | 'interactive'
  | 'contacts';

export interface IFileContext {
  filename?: string;
  id?: string;
  link?: string;
  mime_type?: string;
  sha256?: string;
  caption?: string;
  saved_link?: string; // Bukan dari Qiscus, Buatan Sendiri
}

export interface ILocationContext {
  address: string;
  latitude: number;
  longitude: number;
  name: string;
}

export interface TextContext {
  body: string;
}

export interface IWebhookContact {
  profile: {
    name: string;
  };
  wa_id: string;
}

export interface IStickerContext {
  id: string;
  metadata: {
    emojis: string[];
    'is-first-party-sticker': number;
    'sticker-pack-id': string;
  };
  mime_type: string;
  sha256: string;
}

export interface IReplyContext {
  from: string;
  id: string;
}

export interface IButtonContext {
  text: string;
}

export interface IReferralMessageContext {
  source_id: string;
  media_type: string;
  body: string;
  source_url: string;
  video_url: string;
  thumbnail_url: string;
  headline: string;
  source_type: string;
}

export interface ITypeMessageContacts {
  name: {
    formatted_name: string;
  };
  phones: {
    wa_id: string;
  }[];
}

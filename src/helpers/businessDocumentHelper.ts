import { EBusinessDocument } from '../Components/ImageCapture/types/business-document-types';

export default class BusinessDocumentHelper {
  public static toIndonesianFormatter(params: EBusinessDocument | keyof EBusinessDocument) {
    switch (params) {
      case EBusinessDocument.NIB:
        return 'NOMOR INDUK BERUSAHA';
      case EBusinessDocument.SIUP:
        return 'SURAT IZIN USAHA PERDAGANGAN';
      case EBusinessDocument.NPWP:
        return 'NOMOR PENGENAL WAJIB PAJAK';
      case EBusinessDocument.SKDU:
        return 'SURAT KETERANGAN DOMISILI USAHA';
      case EBusinessDocument.SITU:
        return 'SURAT IZIN TEMPAT USAHA';
      case EBusinessDocument.SIUI:
        return 'SURAT IZIN USAHA INDUSTRI';
      case EBusinessDocument.TDP:
        return 'TANDA DAFTAR PERUSAHAAN';
      case EBusinessDocument.TDI:
        return 'TANDA DAFTAR INDUSTRI';
      case EBusinessDocument.SK_MENKEH:
        return 'SURAT KEPUTUSAN MENKUMHAM';
      default:
        return '';
    }
  }
}

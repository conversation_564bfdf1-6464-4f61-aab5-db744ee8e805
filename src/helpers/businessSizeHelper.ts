import { EBusinessSize } from '../Components/ImageCapture/types/place_of_business_types';

export default class BusinessSizeHelper {
  public static toIndonesianFormatter(params: EBusinessSize | keyof EBusinessSize) {
    switch (params) {
      case EBusinessSize.U01:
        return 'MIKRO';
      case EBusinessSize.U02:
        return 'MENENGAH';
      case EBusinessSize.U03:
        return 'BESAR';
      default:
        return '';
    }
  }
}

import { EBusinessType } from '../Components/ImageCapture/types/place_of_business_types';

export default class BusinessTypeHelper {
  public static toIndonesianFormatter(params: EBusinessType | keyof EBusinessType) {
    switch (params) {
      case EBusinessType.U11:
        return 'ECERAN OFFLINE';
      case EBusinessType.U12:
        return 'ECERAN ONLINE';
      case EBusinessType.U13:
        return 'GROSIR ';
      case EBusinessType.U14:
        return 'PRODUKSI';
      case EBusinessType.U15:
        return 'JASA';
      default:
        return '';
    }
  }
}

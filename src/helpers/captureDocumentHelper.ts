import { ImageCaptureTypes } from '../Components/ImageCapture/types/capture-types';

export default class CaptureDocumentHelper {
  public static toIndonesianFormatter(params: ImageCaptureTypes | keyof ImageCaptureTypes) {
    switch (params) {
      case ImageCaptureTypes.ID_CARD_OWNER:
        return 'KTP PEMILIK';
      case ImageCaptureTypes.ID_CARD_GUARANTOR:
        return 'KTP PENJAMIN';
      case ImageCaptureTypes.ID_CARD_GUARANTOR_SPOUSE:
        return 'KTP PASANGAN PENJAMIN';
      case ImageCaptureTypes.ID_CARD_ORDER_MAKER:
        return 'KTP PEMESAN';
      case ImageCaptureTypes.FAMILY_REGISTER:
        return 'KARTU KELUARGA';
      case ImageCaptureTypes.INCOME_DOC:
        return 'DOKUMEN PENGHASILAN';
      case ImageCaptureTypes.PLACE_TO_STAY:
        return 'TEMPAT TINGGAL';
      case ImageCaptureTypes.PLACE_OF_BUSINESS:
        return 'TEMPAT USAHA';
      case ImageCaptureTypes.BUSINESS_DOCUMENT:
        return '<PERSON><PERSON><PERSON><PERSON> BISNIS';
      case ImageCaptureTypes.SELFIE:
        return 'SELFIE';
      case ImageCaptureTypes.OTHER_DOCUMENT:
        return 'DOKUMEN LAIN';
      default:
        return '';
    }
  }
}

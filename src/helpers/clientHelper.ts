import ClientEntity from '../entities/ClientEntity';
import { DocumentReference, getDoc } from 'firebase/firestore';

export async function getClientProfile(clientRef: DocumentReference | ClientEntity) {
  let clientData!: ClientEntity;
  if (clientRef instanceof ClientEntity) {
    clientData = clientRef;
  } else {
    const getClient = await getDoc(clientRef.withConverter(ClientEntity.converter));
    clientData = getClient.data()!;
  }

  return {
    clientData,
  };
}

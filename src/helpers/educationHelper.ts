import { EFamilyEducation } from '../entities/types/family-register-entity-types';

export default class EducationHelper {
  public static toIndonesianFormatter(params: EFamilyEducation | keyof EFamilyEducation): string {
    switch (params) {
      case EFamilyEducation.NO_SCHOOL:
        return 'TIDAK / BELUM SEKOLAH';
      case EFamilyEducation.DID_NOT_FINISH_ELEMENTARY_SCHOOL:
        return 'BELUM TAMAT SD/SEDERAJAT';
      case EFamilyEducation.GRADUATED_ELEMENTARY_SCHOOL:
        return 'TAMAT SD / SEDERAJAT';
      case EFamilyEducation.JUNIOR_HIGH_SCHOOL:
        return 'SLTP/SEDERAJAT';
      case EFamilyEducation.HIGH_SCHOOL:
        return 'SLTA/SEDERAJAT	';
      case EFamilyEducation.DIPLOMA_1_2:
        return 'DIPLOMA I / II';
      case EFamilyEducation.DIPLOMA_3:
        return 'AKADEMI/ DIPLOMA III/S. MUDA';
      case EFamilyEducation.DIPLOMA_4_OR_BACHELOR:
        return 'DIPLOMA IV/ STRATA';
      case EFamilyEducation.MASTER:
        return 'STRATA II';
      case EFamilyEducation.DOCTOR:
        return 'STRATA III';
      default:
        return '';
    }
  }
}

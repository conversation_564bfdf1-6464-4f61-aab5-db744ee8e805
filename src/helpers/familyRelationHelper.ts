import { EFamilyRelation } from '../entities/types/family-register-entity-types';

export default class FamilyRelationHelper {
  public static toIndonesianFormatter(params: EFamilyRelation | keyof EFamilyRelation) {
    switch (params) {
      case EFamilyRelation.HEADS_OF_FAMILY:
        return 'KEPALA KELUARGA';
      case EFamilyRelation.HUSBAND:
        return 'SUAMI';
      case EFamilyRelation.WIFE:
        return 'ISTRI';
      case EFamilyRelation.CHILD:
        return 'ANAK';
      case EFamilyRelation.SON_IN_LAW:
        return 'MENANTU';
      case EFamilyRelation.GRANDCHILD:
        return 'CUCU';
      case EFamilyRelation.PARENTS:
        return 'ORANG TUA';
      case EFamilyRelation.IN_LAWS:
        return 'MERTUA';
      case EFamilyRelation.ANOTHER_FAMILY:
        return 'KELUARGA LAIN';
      case EFamilyRelation.HELPER:
        return 'PEMBANTU';
      case EFamilyRelation.OTHER:
        return 'LAINNYA';
      default:
        return '';
    }
  }
}

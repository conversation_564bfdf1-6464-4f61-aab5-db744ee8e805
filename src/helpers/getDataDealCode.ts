import { catalogueServices } from '../services/catalogue/catalogueServices';
import { DealCodeResponse } from '../services/types/offerServiceTypes';

interface DownPayment {
  downPayment: number;
  tenors: Tenor[];
}

interface Tenor {
  tenor: number;
  installment: number;
  fincoCode: string;
}

export interface IGetDataDealCodeHelper {
  modelName: string;
  originalCreditScheme: DealCodeResponse['data']['credit'];
  fincoId: string;
  downPayments: DownPayment[];
  variantCodes: string[];
  cityGroup: string;
  dealCode: string;
  promoCode: {
    code: string;
    discount: number;
  };
}

const getDataDealCode = async (dealCode: string): Promise<IGetDataDealCodeHelper> => {
  const data = await catalogueServices.getDealCode(dealCode);

  const promoCode = data.promo_codes.length > 0 ? data.promo_codes[0] : '';
  const promoAmount = data.total_promo_discount;

  const cityGroup = data.area[0].toUpperCase();
  const variantCodes = data.vehicle.variant_custom.map((v) => v.variant_code);
  const modelName = data.vehicle.model_name.toUpperCase();

  const _downPayments: DownPayment[] = [];

  const availableDownPayments: number[] = [];
  data.credit.forEach((_dp) => {
    if (availableDownPayments.indexOf(_dp.dp_amount) === -1)
      availableDownPayments.push(_dp.dp_amount);
  });

  availableDownPayments.forEach((dp) => {
    const _downPayment_tmp: DownPayment = {
      downPayment: dp,
      tenors: [],
    };

    const filteredDownPayments = data.credit.filter((c) => {
      let keep = false;
      if (c.dp_amount === dp) keep = true;
      return keep;
    });

    let availableTenors: number[] = [];

    filteredDownPayments.forEach((c) => {
      c.tenor.forEach((t) => {
        const _t = parseInt(t);
        if (availableTenors.indexOf(_t) === -1) {
          availableTenors.push(_t);
        }
      });
    });

    availableTenors.forEach((t) => {
      const findInstallment = data.credit.find((v) => {
        return v.dp_amount === _downPayment_tmp.downPayment && parseInt(v.tenor[0]) === t;
      });

      if (findInstallment)
        _downPayment_tmp.tenors.push({
          fincoCode: findInstallment?.finco_code || '',
          installment: findInstallment?.installment_amount || 0,
          tenor: t,
        });
    });

    _downPayments.push(_downPayment_tmp);
  });

  return {
    cityGroup,
    dealCode,
    downPayments: _downPayments,
    originalCreditScheme: data.credit,
    modelName: modelName,
    promoCode: {
      code: promoCode,
      discount: promoAmount,
    },
    variantCodes: variantCodes,
    fincoId: data.default_finco_id,
  };
};

export default getDataDealCode;

import { createBrowserHistory } from 'history';
import authServices from '../services/firebase/AuthServices';
import { mainStore } from '../redux/reducers';
import 'firebase/firestore';
import { updateDoc } from 'firebase/firestore';
import adminSlice from '../redux/admin/admin.slice';

export const myHistory = createBrowserHistory();

class Pooling {
  private mainPoolingRef?: NodeJS.Timeout;

  public setPool = (pooling: NodeJS.Timeout) => {
    this.mainPoolingRef = pooling;
  };

  public removePooling = () => {
    if (this.mainPoolingRef) clearInterval(this.mainPoolingRef);
  };
}

export const poolingClass = new Pooling();

export const onLogout = async () => {
  poolingClass.removePooling();
  const adminReducer = mainStore.getState().reducerAdmin;

  if (adminReducer.adminSession?.ref) {
    updateDoc(adminReducer.adminSession.ref, {
      auto_end_session_at: new Date(),
    });
  }

  myHistory.push('/login');
  await authServices.signOut();
  mainStore.dispatch(adminSlice.actions.unsetAdmin());
  window.location.reload();
};

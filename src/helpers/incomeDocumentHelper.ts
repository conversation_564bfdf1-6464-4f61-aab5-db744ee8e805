import { EIncomeType } from '../Components/ImageCapture/types/income_document_types';

export default class IncomeDocumentHelper {
  public static toIndonesianFormatter(params: EIncomeType | keyof EIncomeType) {
    switch (params) {
      case EIncomeType.I01:
        return 'SINGLE TETAP';
      case EIncomeType.I02:
        return 'SINGLE TIDAK TETAP';
      case EIncomeType.I03:
        return 'DUAL TETAP';
      case EIncomeType.I04:
        return 'DUAL TIDAK TETAP';
      case EIncomeType.I05:
        return 'DUAL CAMPUR';
      case EIncomeType.I06:
        return 'MULTI TETAP';
      case EIncomeType.I07:
        return 'MULTI TIDAK TETAP';
      case EIncomeType.I08:
        return 'MULTI CAMPUR';
      default:
        return '';
    }
  }
}

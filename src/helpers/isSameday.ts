import moment, { Moment } from 'moment';

export default function isSameDay(date: Moment | Date | number, dateDay?: Moment) {
  const todayMoment = dateDay ?? moment();
  const startOfToday = todayMoment.clone().startOf('day');
  const endOfToday = todayMoment.clone().endOf('day');
  let targetMoment: Moment | null = null;

  if (typeof date === 'number') {
    targetMoment = moment.unix(date);
  } else {
    targetMoment = moment(date);
  }

  if (targetMoment.isBetween(startOfToday, endOfToday)) {
    return true;
  } else {
    return false;
  }
}

import { EMaritalStatus } from '../Components/ImageCapture/types/marital-status-types';

export default class MaritalStatusHelper {
  public static toIndonesianFormatter(params: EMaritalStatus | keyof EMaritalStatus) {
    switch (params) {
      case EMaritalStatus.SINGLE:
        return 'BELUM KAWIN';
      case EMaritalStatus.MARRIED:
        return 'SUDAH KAWIN';
      case EMaritalStatus.DIVORCED:
        return 'CERAI';
      case EMaritalStatus.WIDOW_WIDOWER:
        return 'CERAI MATI';
      default:
        return '';
    }
  }
}

class PhoneNumberPatternModel {
  private readonly _id: string;
  private readonly _regex: RegExp;
  private readonly _enabled: boolean;
  private readonly _sanitizer: (value: string) => string;
  private readonly _mask: (value: string) => string;

  get id(): string {
    return this._id;
  }

  get regex(): RegExp {
    return this._regex;
  }
  get sanitizer(): (value: string) => string {
    return this._sanitizer;
  }

  get mask(): (value: string) => string {
    return this._mask;
  }

  get enabled(): boolean {
    return this._enabled;
  }

  constructor(params: {
    id: string;
    regex: RegExp;
    sanitizer: (value: string) => string;
    mask: (value: string) => string;
    enabled?: boolean;
  }) {
    this._id = params.id;
    this._regex = params.regex;
    this._sanitizer = params.sanitizer;
    this._mask = params.mask;
    if (params.enabled === undefined) {
      this._enabled = true;
    } else {
      this._enabled = params.enabled;
    }
  }
}

export default PhoneNumberPatternModel;

import PhoneNumberPatternModel from './PhoneNumberPattern.model';
import randomNumber from '../randomNumber';
import phoneNumberPatterns from './phoneNumberPatterns';
import checkTextContainsPhoneNumber from './checkTextContainsPhoneNumber';

export interface PlaceholderAddress {
  placeholderId: string;
  originalValue: string;
  pattern: PhoneNumberPatternModel;
}

export function appendHtmlToPhoneNumber(
  text: string,
  options?: {
    mask?: boolean;
  },
) {
  // console.log("B", checkTextContainsPhoneNumber(text), text);
  if (!checkTextContainsPhoneNumber(text)) {
    return text;
  }

  const addressingPlaceholders: PlaceholderAddress[] = [];

  let mutableText = text;
  for (const pattern of phoneNumberPatterns) {
    if (!pattern.enabled) continue;
    const matches = text.match(pattern.regex);
    // console.log(
    //     matches,
    //     pattern.id,
    //     text
    // )
    if (matches) {
      for (const match of matches) {
        const placeholderId = `_PHONE_NUMBER_${randomNumber(3)}_`;
        mutableText = mutableText.replace(match, placeholderId);
        addressingPlaceholders.push({
          originalValue: match,
          placeholderId: placeholderId,
          pattern: pattern,
        });
      }
    }
  }

  // console.log(addressingPlaceholders);

  for (const addressingPlaceholder of addressingPlaceholders) {
    let content = addressingPlaceholder.originalValue;
    if (options?.mask) {
      content = addressingPlaceholder.pattern.mask(content);
    }
    const sanitized = addressingPlaceholder.pattern.sanitizer(addressingPlaceholder.originalValue);
    mutableText = mutableText.replace(
      addressingPlaceholder.placeholderId,
      `<span sanitized="${sanitized}" original="${addressingPlaceholder.originalValue}" class="mask-phone-number">${content}</span>`,
    );
  }

  return mutableText;
}

import phoneNumberPatterns from './phoneNumberPatterns';

const debug = '6289658779012';

function checkTextContainsPhoneNumber(text: string) {
  let result = false;
  for (const pattern of phoneNumberPatterns) {
    if (pattern.enabled) {
      const testMatch = pattern.regex.test(text);
      if (testMatch) {
        result = true;
      }
    }
    pattern.regex.lastIndex = 0;
  }
  return result;
}

export default checkTextContainsPhoneNumber;

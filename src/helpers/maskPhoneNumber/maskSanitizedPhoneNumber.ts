import { PlaceholderAddress } from './appendHtmlToPhoneNumber.prototype.ver2';
import phoneNumberPatterns from './phoneNumberPatterns';
import randomNumber from '../randomNumber';
import checkTextContainsPhoneNumber from './checkTextContainsPhoneNumber';

function maskPhoneNumbers(text: string) {
  if (!checkTextContainsPhoneNumber(text)) {
    return text;
  }
  const addressingPlaceholders: PlaceholderAddress[] = [];

  let mutableText = text;

  for (const pattern of phoneNumberPatterns) {
    if (!pattern.enabled) continue;
    const matches = text.match(pattern.regex);
    if (matches) {
      for (const match of matches) {
        const placeholderId = `_PHONE_NUMBER_${randomNumber(4)}_`;
        mutableText = mutableText.replace(match, placeholderId);
        addressingPlaceholders.push({
          originalValue: match,
          placeholderId: placeholderId,
          pattern: pattern,
        });
      }
    }
  }

  for (const addressingPlaceholder of addressingPlaceholders) {
    mutableText = mutableText.replace(
      addressingPlaceholder.placeholderId,
      `${addressingPlaceholder.pattern.mask(addressingPlaceholder.originalValue)}`,
    );
  }
  return mutableText;
}

function maskSanitizedPhoneNumber(
  phoneNumber: string,
  startDigits: number = 3,
  endDigits: number = 4,
) {
  const cleanedNumber = phoneNumber.replace(/\D+/g, '');
  const firstPart = cleanedNumber.slice(0, startDigits);
  const lastPart = cleanedNumber.slice(-endDigits);
  const maskedPart = cleanedNumber.slice(startDigits, -endDigits).replace(/[0-9]/g, '*');
  return `${firstPart}${maskedPart}${lastPart}`;
}

export { maskPhoneNumbers };
export default maskSanitizedPhoneNumber;

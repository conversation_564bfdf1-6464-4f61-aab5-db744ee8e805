import PhoneNumberPatternModel from './PhoneNumberPattern.model';

const phoneNumberPatterns: PhoneNumberPatternModel[] = [
  /*
        6285763705624
        085763705624
    */
  new PhoneNumberPatternModel({
    id: 'phoneNumber-9637336850',
    regex: /(?:^|\s)(\+62|62|0)8[1-9][0-9]{8,11}(?=\s|$)/g,
    sanitizer: (phoneNumber) => {
      const cleanedNumber = phoneNumber.replace(/^0/, '').replace(/\D+/g, '');
      return cleanedNumber.startsWith('62') ? cleanedNumber : `62${cleanedNumber}`;
    },
    mask: (phoneNumber) => {
      const cleanedNumber = phoneNumber.replace(/\D+/g, '');
      const firstPart = cleanedNumber.slice(0, 3);
      const lastPart = cleanedNumber.slice(-4);
      const maskedPart = cleanedNumber.slice(3, -4).replace(/[0-9]/g, '*');
      return `${firstPart}${maskedPart}${lastPart}`;
    },
  }),
  /*
        85763705624
    */
  new PhoneNumberPatternModel({
    id: 'phoneNumber-8050603222',
    regex: /\b8[1-9][0-9]{8,11}\b/g,
    sanitizer: (phoneNumber) => {
      return `62${phoneNumber}`;
    },
    mask: (phoneNumber) => {
      const cleanedNumber = phoneNumber.replace(/\D+/g, '');
      const firstPart = cleanedNumber.slice(0, 3);
      const lastPart = cleanedNumber.slice(-4);
      const maskedPart = cleanedNumber.slice(3, -4).replace(/[0-9]/g, '*');
      return `${firstPart}${maskedPart}${lastPart}`;
    },
  }),
  /*
        0857-6370-5624
        857-6370-5624
        62-857-6370-5624
        62 857-6370-5624
        62857-6370-5624
   */
  new PhoneNumberPatternModel({
    id: 'phoneNumber-3397623004',
    regex: /(0|62)?([ \-])?8[0-9]{2}-[0-9]{4}-[0-9]{3,4}/g,
    sanitizer: (phoneNumber) => {
      const cleanedNumber = phoneNumber.replace(/^0/, '').replace(/\D+/g, '');
      return cleanedNumber.startsWith('62') ? cleanedNumber : `62${cleanedNumber}`;
    },
    mask: (phoneNumber) => {
      const parts = phoneNumber.split('-');
      const middlePart = parts[1];
      const maskLength = middlePart.length;
      const mask = '*'.repeat(maskLength);
      return `${parts[0]}-${mask}-${parts[2]}`;
    },
  }),
  /*
        0 8 5 7 6 3 7 0 5 6 2 4
        6 2 8 5 7 6 3 7 0 5 6 2 4
    */
  new PhoneNumberPatternModel({
    id: 'phoneNumber-1321276480',
    regex: /(?:0 |6 |8 )([0-9] ){9,13}[0-9]/g,
    sanitizer: (phoneNumber) => {
      const cleanedNumber = phoneNumber.replace(/^0/, '').replace(/\D+/g, '');
      return cleanedNumber.startsWith('62') ? cleanedNumber : `62${cleanedNumber}`;
    },
    mask: (phoneNumber) => {
      const digits = phoneNumber.split(' ').map((digit) => digit.trim());
      const maskStart = 3;
      const maskEnd = digits.length - 4;
      const mask = '*'.repeat(maskEnd - maskStart);
      const maskedDigits = [
        ...digits.slice(0, maskStart),
        ...mask.split(''),
        ...digits.slice(maskEnd),
      ];
      return maskedDigits.join(' ');
    },
    enabled: false,
  }),
];

export default phoneNumberPatterns;

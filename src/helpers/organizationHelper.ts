import organizationData from '../config/organization.json';

// Interface untuk mendefinisikan struktur data Organization
export interface Organization {
  organization: string;
  name: string;
  companyCode: string;
  brand: string;
  leads_price: number;
  group: string;
}

// Type guard untuk memastikan data sesuai dengan interface Organization
function isValidOrganization(obj: any): obj is Organization {
  return (
    typeof obj === 'object' &&
    typeof obj.organization === 'string' &&
    typeof obj.name === 'string' &&
    typeof obj.companyCode === 'string' &&
    typeof obj.brand === 'string' &&
    typeof obj.leads_price === 'number' &&
    typeof obj.group === 'string'
  );
}

// Validasi dan cast data dari JSON
const organizations: Organization[] = organizationData.filter(isValidOrganization);

/**
 * Mendapatkan semua data organisasi
 * @returns Array dari semua organisasi
 */
export const getAllOrganizations = (): Organization[] => {
  return organizations;
};

/**
 * Mencari organisasi berdasarkan organization code
 * @param organizationCode - Kode organisasi yang dicari
 * @returns Organisasi yang ditemukan atau undefined
 */
export const getOrganizationByCode = (organizationCode: string): Organization | undefined => {
  return organizations.find((org) => org.organization === organizationCode);
};

/**
 * Mencari organisasi berdasarkan company code
 * @param companyCode - Kode perusahaan yang dicari
 * @returns Organisasi yang ditemukan atau undefined
 */
export const getOrganizationByCompanyCode = (companyCode: string): Organization | undefined => {
  return organizations.find((org) => org.companyCode === companyCode);
};

/**
 * Mendapatkan semua organisasi berdasarkan brand
 * @param brand - Brand yang dicari
 * @returns Array organisasi dengan brand yang sesuai
 */
export const getOrganizationsByBrand = (brand: string): Organization[] => {
  return organizations.filter((org) => org.brand.toLowerCase() === brand.toLowerCase());
};

/**
 * Mendapatkan semua organisasi berdasarkan group
 * @param group - Group yang dicari
 * @returns Array organisasi dengan group yang sesuai
 */
export const getOrganizationsByGroup = (group: string): Organization[] => {
  return organizations.filter((org) => org.group.toLowerCase() === group.toLowerCase());
};

/**
 * Mendapatkan harga leads dari organisasi tertentu
 * @param organizationCode - Kode organisasi
 * @returns Harga leads atau null jika organisasi tidak ditemukan
 */
export const getLeadsPrice = (organizationCode: string): number | null => {
  const organization = getOrganizationByCode(organizationCode);
  return organization ? organization.leads_price : null;
};

/**
 * Mendapatkan nama organisasi berdasarkan organization code
 * @param organizationCode - Kode organisasi
 * @returns Nama organisasi atau null jika tidak ditemukan
 */
export const getOrganizationName = (organizationCode: string): string | null => {
  const organization = getOrganizationByCode(organizationCode);
  return organization ? organization.name : null;
};

/**
 * Mengecek apakah organization code valid/ada
 * @param organizationCode - Kode organisasi yang akan dicek
 * @returns true jika organisasi ada, false jika tidak
 */
export const isValidOrganizationCode = (organizationCode: string): boolean => {
  return organizations.some((org) => org.organization === organizationCode);
};

/**
 * Mendapatkan semua brand yang tersedia
 * @returns Array dari semua brand unik
 */
export const getAllBrands = (): string[] => {
  const brands = organizations.map((org) => org.brand);
  return Array.from(new Set(brands));
};

/**
 * Mendapatkan semua group yang tersedia
 * @returns Array dari semua group unik
 */
export const getAllGroups = (): string[] => {
  const groups = organizations.map((org) => org.group);
  return Array.from(new Set(groups));
};

/**
 * Mencari organisasi berdasarkan nama (case insensitive)
 * @param name - Nama organisasi yang dicari
 * @returns Array organisasi yang namanya mengandung kata kunci
 */
export const searchOrganizationsByName = (name: string): Organization[] => {
  const searchTerm = name.toLowerCase();
  return organizations.filter((org) => org.name.toLowerCase().includes(searchTerm));
};

/**
 * Mendapatkan company code berdasarkan organization code
 * @param organizationCode - Kode organisasi
 * @returns Company code atau null jika tidak ditemukan
 */
export const getCompanyCodeByOrganization = (organizationCode: string): string | null => {
  const organization = getOrganizationByCode(organizationCode);
  return organization ? organization.companyCode : null;
};

// Export default untuk kemudahan import
export default {
  getAllOrganizations,
  getOrganizationByCode,
  getOrganizationByCompanyCode,
  getOrganizationsByBrand,
  getOrganizationsByGroup,
  getLeadsPrice,
  getOrganizationName,
  isValidOrganizationCode,
  getAllBrands,
  getAllGroups,
  searchOrganizationsByName,
  getCompanyCodeByOrganization,
};

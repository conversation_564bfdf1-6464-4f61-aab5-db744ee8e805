import { EPlaceToStayStatus } from '../Components/ImageCapture/types/place_to_stay_types';

export default class PlaceToStayStatusHelper {
  public static toIndonesianFormatter(params: EPlaceToStayStatus | keyof EPlaceToStayStatus) {
    switch (params) {
      case EPlaceToStayStatus.H01:
        return 'RUMAH SENDIRI';
      case EPlaceToStayStatus.H02:
        return 'RUMAH KELUARGA';
      case EPlaceToStayStatus.H03:
        return 'RUMAH DINAS';
      case EPlaceToStayStatus.H04:
        return 'RUMAH KONTRAK';
      case EPlaceToStayStatus.H05:
        return 'RUMAH KOS';
      case EPlaceToStayStatus.H06:
        return 'RUMAH STATUS KREDIT (KPR)';
      default:
        return '';
    }
  }
}

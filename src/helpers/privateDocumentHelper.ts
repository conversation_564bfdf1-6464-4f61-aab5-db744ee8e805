import { EPrivateDocument } from '../Components/ImageCapture/types/private-document-types';

export default class PrivateDocumentHelper {
  public static toIndonesianFormatter(params: EPrivateDocument | keyof EPrivateDocument) {
    switch (params) {
      case EPrivateDocument.SURAT_NIKAH:
        return 'SURAT NIKAH';
      case EPrivateDocument.BUKU_NIKAH:
        return 'BUKU NIKAH';
      case EPrivateDocument.SURAT_KETERANGAN_KERJA:
        return 'SURAT KETERANGAN KERJA';
      case EPrivateDocument.SLIP_GAJI:
        return 'SLIP GAJI';
      case EPrivateDocument.KARTU_BPJS_KESEHATAN:
        return 'KARTU BPJS KESEHATAN';
      case EPrivateDocument.KARTU_BPJS_TK:
        return 'KARTU BPJS TK';
      case EPrivateDocument.SIM:
        return 'SIM (SURAT IZIN MENGEMUDI)';
      case EPrivateDocument.PASSPORT:
        return 'PASSPORT';
      case EPrivateDocument.SURAT_AKTA_CERAI:
        return 'SURAT/AKTA CERAI';
      case EPrivateDocument.KARTU_TASPEN:
        return 'KARTU TASPEN';
      case EPrivateDocument.NPWP:
        return 'NPWP';
      case EPrivateDocument.SPT_PPH_21:
        return 'SPT PPH 21';
      case EPrivateDocument.COVER_BUKU_TABUNGAN:
        return 'COVER BUKU TABUNGAN';
      case EPrivateDocument.REKENING_KORAN:
        return 'REKENING KORAN';
      case EPrivateDocument.PBB:
        return 'PBB (PAJAK BUMI & BANGUNAN)';
      case EPrivateDocument.KITAS:
        return 'KITAS(KARTU IZIN TINGGAL TERBATAS)';
      case EPrivateDocument.KITAP:
        return 'KITAP(KARTU IZIN TINGGAL TETAP)';
      case EPrivateDocument.SIP:
        return 'SIP (SURAT IZIN PRAKTEK)';
      case EPrivateDocument.STR:
        return 'STR KESEHATAN (SURAT TANDA REGISTRASI)';
      default:
        return '';
    }
  }
}

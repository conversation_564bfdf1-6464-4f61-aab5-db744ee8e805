function randomNumber(digits: number = 6): number {
  if (digits <= 0) return 0; // Jika digit kurang atau sama dengan 0, kembalikan 0

  // Dapatkan waktu saat ini dalam milidetik
  const now = Date.now();

  // Buat string acak
  const randomString = Math.random().toString(36).substr(2, 10);

  // Gabungkan angka acak dengan waktu dalam milidetik dan string acak
  const combinedString = `${randomString}${now}`;

  // Ambil angka acak dari rentang yang ditentukan
  const min = Math.pow(10, digits - 1);
  const max = Math.pow(10, digits) - 1;
  const randomPart = Math.floor(Math.random() * (max - min + 1)) + min;

  // Gabungkan angka acak dengan string gabungan
  const uniqueNumber = parseInt(`${randomPart}${combinedString}`);

  // Jika panjang lebih dari yang diing<PERSON>an, potong dari awal
  const uniqueNumberStr = uniqueNumber.toString().slice(0, digits);

  return parseInt(uniqueNumberStr);
}

export default randomNumber;

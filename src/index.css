@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: 'Roboto', sans-serif;
}

.main-container-chat {
  height: calc(100dvh - 61px);
}

.container-contact-list {
  overflow-y: auto;
}

.container-contact-list .date {
  float: right;
}

.as-link {
  color: #0d71bb;
}

.as-link:hover {
  text-decoration: underline;
  cursor: pointer;
}

.promo-wrapper {
  box-shadow:
    rgb(204, 219, 232) 3px 3px 6px 0 inset,
    rgba(255, 255, 255, 0.5) -3px -3px 6px 1px inset;
  padding: 8px;
  margin-top: 8px;
}

.header-active-conversation {
  background: #e1f4f3;
}

.chat-box {
  overflow-y: auto;
  overflow-x: hidden;
  height: calc(100dvh - 180px);
  max-height: calc(100dvh - 180px);
  padding-bottom: 80px; /* Tambahkan padding untuk mengakomodasi chat input */
}

.chat-text-input-container {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10;
  background-color: white;
  margin-top: auto;
}

/* Untuk mobile */
@media (max-width: 768px) {
  .chat-box {
    height: calc(100dvh - 180px);
    max-height: calc(100dvh - 180px);
  }

  .chat-text-input-container {
    position: fixed; /* Gunakan fixed untuk mobile */
    bottom: 0;
    left: 0;
    right: 0;
  }
}

/* Tambahkan backdrop blur effect untuk memberikan efek visual yang lebih baik */
.chat-text-input-container::before {
  content: '';
  position: absolute;
  top: -10px;
  left: 0;
  right: 0;
  height: 10px;
  background: linear-gradient(to top, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
  pointer-events: none;
}

/* Chat item dropdown styles */
.chat-item-dropdown {
  z-index: 1000;
}

/* Hide the default Semantic UI dropdown caret/arrow */
.chat-item-dropdown .ui.dropdown > .dropdown.icon,
.chat-item-dropdown .ui.dropdown > .search.icon,
.chat-item-dropdown .ui.dropdown > .delete.icon,
.chat-item-dropdown .ui.dropdown > .icon {
  display: none !important;
}

/* Ensure the trigger button doesn't have extra spacing for the caret */
.chat-item-dropdown .ui.dropdown > .text {
  padding-right: 0 !important;
}

.chat-item-dropdown .ui.dropdown .menu {
  z-index: 1001 !important;
  border: 1px solid #e5e7eb !important;
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
}

.chat-item-dropdown .ui.dropdown .menu .item {
  border-radius: 0 !important;
  transition: background-color 0.15s ease-in-out !important;
}

.chat-item-dropdown .ui.dropdown .menu .item:hover {
  background-color: #f9fafb !important;
}

.chat-item-dropdown .ui.dropdown .menu .item.disabled {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
}

.chat-item-dropdown .ui.dropdown .menu .item.disabled:hover {
  background-color: transparent !important;
}

/* Ensure the dropdown trigger button is perfectly centered */
.chat-item-dropdown .ui.dropdown {
  display: inline-block !important;
}

.chat-item-dropdown .ui.dropdown > .default.text,
.chat-item-dropdown .ui.dropdown > .text {
  display: none !important;
}

/* Remove any default padding/margin that might affect centering */
.chat-item-dropdown .ui.dropdown > .dropdown.icon:before {
  content: none !important;
}

/* Mobile-specific styles for chat item dropdown */
@media (max-width: 768px) {
  .chat-item-dropdown .ui.dropdown .menu {
    min-width: 150px !important;
    font-size: 14px !important;
  }

  .chat-item-dropdown .ui.dropdown .menu .item {
    padding: 12px 16px !important;
    min-height: 44px !important; /* Better touch target size */
  }

  /* Ensure dropdown appears above other mobile elements */
  .chat-item-dropdown {
    z-index: 1050;
  }

  .chat-item-dropdown .ui.dropdown .menu {
    z-index: 1051 !important;
  }
}

.chat-text-box {
  background: whitesmoke;
  flex: 0 1 auto;
  padding: 8px 8px;
  transition: all 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.icon-image-failed-fetch {
  color: #a0a0a0;
}

.template-collections {
  margin-top: 4px;
  overflow: auto;
  white-space: nowrap;
}

.template-label {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  max-width: 200px;
}

.wrapper-chat-item {
  display: flex;
  gap: 6px;
}

.wrapper-chat-item.out-wrapper {
  flex-direction: row-reverse;
}

.chat-item {
  margin-bottom: 16px;
  border-radius: 8px;
  padding: 8px;
  max-width: 667px;
}

.chat-item.out {
  color: white;
  background-color: #3e66f0;
  float: right;
}

.chat-item-date {
  color: #9c9c9c;
  font-size: 12px;
}

.chat-item.in {
  float: left;
  background-color: #ecf0f1;
}

a.hyperlink-render-ref {
  text-decoration: none;
  color: black;
}

.render-ref:hover {
  cursor: pointer;
}

.render-ref {
  background: #ddd;
  padding: 8px;
  border-radius: 2px;
  margin-bottom: 8px;
  border-left: 3px solid #008080;
}

.render-ref > .title {
  display: block;
  font-weight: bold;
}

.render-ref > .source-id {
  margin-top: 6px;
  display: block;
  font-size: 12px;
}

.render-ref > .body {
  display: -webkit-box;
  -webkit-line-clamp: 2; /* Batasan jumlah baris */
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-clamp: 2;
}

.unsupported-message-wrapper {
  background: #ddd;
  padding: 8px;
  border-radius: 2px;
  margin-bottom: 8px;
  border-left: 3px solid red;
}

.contact-chat-item {
  background: #ddd;
  padding: 8px;
  border-radius: 2px;
  margin-bottom: 8px;
  border-left: 3px solid #3252c0;
  display: flex;
  gap: 14px;

  align-items: center;
}

.conversation-box {
  width: 100%;
}

.send-promo-item-info-wrapper > div:nth-child(2) {
  font-weight: bold;
}

.order-histories-content-wrapper {
  display: flex;
  gap: 8px;
  flex-direction: column;
}

.order-histories-item-row {
  display: flex;
  gap: 6px;
  flex-direction: row;
}

div.order-histories-item-row > div:nth-child(1) {
  font-weight: 500;
  flex: 1;
}

div.order-histories-item-row > div:nth-child(2) {
  text-align: left;
  flex: 2;
}

.offer-detail-section-title {
  font-size: 16px;
  font-weight: bold;
}

.offer-detail-section-description {
  margin-top: 8px;
  display: flex;
  flex-direction: column;
}

.offer-detail-item-description-row {
  display: flex;
  flex-direction: row;
}

.offer-detail-item-description-row > :nth-child(1) {
  font-weight: bold;
  flex-basis: 30%;
}
.offer-detail-item-description-row > :nth-child(2) {
  flex-basis: 70%;
}
.offer-detail-item-description-row > :nth-child(2):before {
  content: ': ';
}

.btn-price-list-add-row {
  margin: 8px 0 0 0 !important;
}
.empty-price-list-row {
  margin: 8px 0;
  padding: 8px 0;
}

.selectable-installment {
  cursor: pointer;
}

.selectable-installment.selected {
  font-weight: bolder;
}

.admin-list-content-item {
}

@keyframes highlight-animation {
  0%,
  100% {
    background-color: transparent;
  }
  50% {
    background-color: rgba(59, 130, 246, 0.4);
  }
}

.highlight-message {
  animation: highlight-animation 1.5s ease-in-out 1;
  transform-origin: center;
}

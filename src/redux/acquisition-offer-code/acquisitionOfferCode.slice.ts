import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { IGetOfferData } from '../../services/types/offerServiceTypes';
import { TMainReduxStates } from '../types/redux-types';
import { offerServices } from '../../services/offerServices';

interface IAcquisitionOfferCodeState {
  open: boolean;
  submitting: boolean;
  searchingDataOffer: boolean;
  dataOffer: IGetOfferData | null;
  offerCode: string;
  poNumber: string;
}

const initialState: IAcquisitionOfferCodeState = {
  open: false,
  submitting: false,
  searchingDataOffer: false,
  dataOffer: null,
  offerCode: '',
  poNumber: '',
};

export const submitSearchOfferCodeByPOThunk = createAsyncThunk(
  'submitSearchOfferCodeByPOThunk',
  async (arg, thunkAPI) => {
    const state = thunkAPI.getState() as TMainReduxStates;
    const poNumber = state.acquisitionOfferCodeSlice.poNumber;

    try {
      const get = await offerServices.getDataByPoNumber(poNumber);
      if (get?.data) {
        return get.data;
      }
    } catch (e) {
      throw e;
    }
  },
);

const acquisitionOfferCodeSlice = createSlice({
  name: 'acquisitionOfferCodeSlice',
  reducers: {
    open: (s, a: { payload: { offerCode: string } }) => {
      s.open = true;
      s.offerCode = a.payload.offerCode;
    },
    close: () => {
      return {
        ...initialState,
      };
    },
    changePoNumber: (s, a: { payload: string }) => {
      s.poNumber = a.payload;
    },
    setSubmitStatus: (s, a: { payload: boolean }) => {
      s.submitting = a.payload;
    },
  },
  initialState: {
    ...initialState,
  },
  extraReducers: (builder) => {
    builder.addCase(submitSearchOfferCodeByPOThunk.fulfilled, (state, action) => {
      state.dataOffer = action.payload || null;
      state.searchingDataOffer = false;
    });

    builder.addCase(submitSearchOfferCodeByPOThunk.pending, (state) => {
      state.searchingDataOffer = true;
      state.dataOffer = null;
    });

    builder.addCase(submitSearchOfferCodeByPOThunk.rejected, (state) => {
      state.searchingDataOffer = false;
    });
  },
});

export default acquisitionOfferCodeSlice;

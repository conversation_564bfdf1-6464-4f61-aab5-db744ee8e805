import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { v4 as uuidV4 } from 'uuid';

export type IConversationFlowStartAt = 'leadsCredit' | 'referralSourceId' | 'newCustomer';
export type IConversationFlowMessageType =
  | 'text'
  | 'priceList'
  | 'button'
  | 'mediaTrimitraArt'
  | 'internalTemplate';
export type IConversationFlowMessageOnCustomerReplied = 'saveName';

export interface IConversationFlowMessage {
  parent: string | null;
  uuid: string;
  type: IConversationFlowMessageType | null;
  text: {
    body: string;
  };
  priceList: {
    text: string;
  };
  button: {
    text: string;
    buttons: { id: string; text: string }[];
  };
  mediaTrimitraArt: {
    artCode: string;
    text: string;
  };
  onReplied: IConversationFlowMessageOnCustomerReplied | null;
  internalTemplate: {
    templateId: string;
  };
}

export const rowMessageInitConversationFlow: IConversationFlowMessage = {
  parent: null,
  uuid: uuidV4(),
  onReplied: null,
  type: 'text',
  text: {
    body: '',
  },
  button: {
    buttons: [
      { text: '', id: '' },
      { text: '', id: '' },
      { text: '', id: '' },
    ],
    text: '',
  },
  priceList: {
    text: '',
  },
  internalTemplate: {
    templateId: '',
  },
  mediaTrimitraArt: {
    artCode: '',
    text: '',
  },
};

const initStates: IConversationFlowStates = {
  modalOpen: false,
  modalSubmitting: false,

  topicName: '',
  startAt: 'referralSourceId',
  referralSourceId: {
    sourceId: '',
    dealCode: '',
  },

  messages: [rowMessageInitConversationFlow],
};

interface IConversationFlowStates {
  modalOpen: boolean;
  modalSubmitting: boolean;
  referralSourceId: {
    dealCode: string;
    sourceId: string;
  };

  topicName: string;
  startAt: IConversationFlowStartAt | null;
  messages: IConversationFlowMessage[];
}

const addConversationFlowSlice = createSlice({
  name: 'addConversationFlowSlice',
  reducers: {
    setSubmittingStatus: (s, a: { payload: boolean }) => {
      s.modalSubmitting = a.payload;
    },
    open: (state) => {
      state.modalOpen = true;
    },
    duplicate: (
      s,
      a: PayloadAction<{
        prefilled: {
          startAt: IConversationFlowStates['startAt'];
          messages: IConversationFlowStates['messages'];
          referralSourceId?: {
            dealCode: string;
          };
        };
      }>,
    ) => {
      s.modalOpen = true;
      if (a.payload.prefilled) {
        const payload = a.payload.prefilled;
        s.startAt = payload.startAt;
        s.messages = payload.messages;

        if (payload.referralSourceId?.dealCode) {
          s.referralSourceId.dealCode = payload.referralSourceId.dealCode;
        }
      }
    },
    changeTopicName: (state, action: { payload: string }) => {
      state.topicName = action.payload;
    },
    changeReferralSourceId: (state, action: { payload: string }) => {
      state.referralSourceId.sourceId = action.payload;
    },
    changeDealCodeReferralSource: (state, action: { payload: string }) => {
      state.referralSourceId.dealCode = action.payload;
    },
    changeRunAt: (state, action: { payload: IConversationFlowStartAt | null }) => {
      if (!action.payload) {
        state.startAt = null;
      } else {
        state.startAt = action.payload;
      }
    },
    changeInternalTemplateText: (
      s,
      a: {
        payload: {
          index: number;
          text: string;
        };
      },
    ) => {
      const index = a.payload.index;
      s.messages[index].internalTemplate.templateId = a.payload.text;
    },
    changeMessageTextType: (state, action: { payload: { index: number; text: string } }) => {
      const index = action.payload.index;
      const text = action.payload.text;
      switch (state.messages[index].type) {
        case 'text':
          state.messages[index].text = {
            body: action.payload.text,
          };
          break;
        case 'button':
          state.messages[index].button.text = text;
          break;
        case 'priceList':
          state.messages[index].priceList.text = text;
          break;
        case 'mediaTrimitraArt':
          state.messages[index].mediaTrimitraArt.text = text;
          break;
      }
    },
    close: () => {
      return {
        ...initStates,
      };
    },
    addRow: (state) => {
      let parent: null | string = null;

      if (state.messages.length > 0) {
        parent = state.messages[state.messages.length - 1].uuid;
      }

      state.messages.push({
        ...rowMessageInitConversationFlow,
        uuid: uuidV4(),
        parent: parent,
      });
    },
    deleteRow: (state, action: { payload: { index: number } }) => {
      const nextRow = state.messages[action.payload.index + 1];
      const prevRow = state.messages[action.payload.index - 1];
      if (action.payload.index > 0) {
        if (nextRow) {
          nextRow.parent = prevRow.uuid;
        }
      } else if (action.payload.index === 0) {
        if (nextRow) {
          nextRow.parent = null;
        }
      }
      state.messages = state.messages.filter((v, i) => {
        return i !== action.payload.index;
      });
    },
    changeMessageType: (
      state,
      action: {
        payload: {
          index: number;
          value: IConversationFlowMessage['type'];
        };
      },
    ) => {
      const { index, value } = action.payload;
      state.messages[index].type = value;
    },

    onChangeButtonInteractiveText: (
      state,
      action: {
        payload: {
          index: number;
          indexButton: number;
          text: string;
        };
      },
    ) => {
      const { index, text, indexButton } = action.payload;
      state.messages[index].button.buttons[indexButton].text = text;
    },

    onChangeButtonInteractiveKey: (
      state,
      action: {
        payload: {
          index: number;
          indexButton: number;
          key: string;
        };
      },
    ) => {
      const { index, key, indexButton } = action.payload;
      state.messages[index].button.buttons[indexButton].id = key;
    },
    changeMediatrimitraArt: (
      state,
      action: { payload: { index: number; artCode: string; text: string } },
    ) => {
      const { index, artCode, text } = action.payload;
      state.messages[index].mediaTrimitraArt.artCode = artCode;
      state.messages[index].mediaTrimitraArt.text = text;
    },
  },
  initialState: {
    ...initStates,
  } as IConversationFlowStates,
});

export default addConversationFlowSlice;

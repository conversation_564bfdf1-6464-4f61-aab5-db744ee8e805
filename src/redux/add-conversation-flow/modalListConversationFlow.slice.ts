import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import ConversationFlowEntity from '../../entities/ConversationFlow';
import ConversationFlow from '../../entities/ConversationFlow';
import firestore, { collection, getDocs, orderBy, query } from 'firebase/firestore';
import { mainStore } from '../reducers';

interface States {
  open: boolean;

  conversationFlows: ConversationFlowEntity[];
}

const initState: States = {
  open: false,
  conversationFlows: [],
};

export const getConversationFlowThunk = createAsyncThunk(
  'getConversationFlowThunk',
  async (projectRef: firestore.DocumentReference, thunkAPI) => {
    const conversationFlowCollection = collection(projectRef, 'conversation_flow');
    const fetchQuery = query(conversationFlowCollection, orderBy('createdAt', 'desc'));
    const get = await getDocs(fetchQuery.withConverter(ConversationFlow.converter));
    const conversationFlows: ConversationFlowEntity[] = [];

    get.forEach((result) => {
      const data = result.data();
      conversationFlows.push(data);
    });

    return conversationFlows;
  },
);

const modalListConversationFlowSlice = createSlice({
  name: 'modalListConversationFlowSlice',
  reducers: {
    open: (s) => {
      s.open = true;
    },
    close: () => {
      return {
        ...initState,
      };
    },
  },
  extraReducers: (builder) => {
    builder.addCase(getConversationFlowThunk.fulfilled, (state, action) => {
      state.conversationFlows = action.payload;
    });
  },
  initialState: {
    ...initState,
  },
});

export default modalListConversationFlowSlice;

import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { v4 as uuidV4 } from 'uuid';
import {
  IConversationFlowMessage,
  IConversationFlowStartAt,
  rowMessageInitConversationFlow,
} from './addConversationFlow.slice';
import firestore, { collection, doc, getDoc } from 'firebase/firestore';
import ConversationFlow from '../../entities/ConversationFlow';
import { ConversationFlowDocument } from '../../entities/types/conversation-flow.types';

const initStates: IConversationFlowStates = {
  modalOpen: false,
  modalSubmitting: false,

  topicName: '',
  startAt: 'newCustomer',

  messages: [rowMessageInitConversationFlow],
  active: false,
  ref: null,
  referralSourceId: {
    dealCode: '',
    sourceId: '',
  },
};

interface IConversationFlowStates {
  modalOpen: boolean;
  modalSubmitting: boolean;

  topicName: string;
  startAt: IConversationFlowStartAt | null;
  messages: IConversationFlowMessage[];
  active: boolean;
  ref: ConversationFlowDocument['ref'] | null;
  referralSourceId: {
    dealCode: string;
    sourceId: string;
  };
}

export const getConversationFlowToUpdate = createAsyncThunk(
  'getConversationFlowToUpdate',
  async (params: { conversationFlowId: string; projectRef: firestore.DocumentReference }) => {
    const conversationFlowCollection = collection(params.projectRef, 'conversation_flow');

    const conversationFlowDoc = doc(conversationFlowCollection, params.conversationFlowId);

    try {
      const get = await getDoc(conversationFlowDoc.withConverter(ConversationFlow.converter));
      if (!get.exists()) throw new Error('updateConversationFlow');
      let conversationFlow: ConversationFlow = get.data();
      return conversationFlow;
    } catch (e) {
      console.log(e);
      throw e;
    }
  },
);

const updateConversationFlowSlice = createSlice({
  name: 'updateConversationFlowSlice',

  extraReducers: (builder) => {
    builder.addCase(getConversationFlowToUpdate.fulfilled, (state, action) => {
      const data = action.payload;
      state.messages = data.messages;
      state.startAt = data.startAt;
      state.modalOpen = true;
      state.active = data.active;
      state.ref = data.ref;
      state.referralSourceId = {
        dealCode: data.referralSourceId?.dealCode || '',
        sourceId: data.referralSourceId?.sourceId || '',
      };
    });
  },

  reducers: {
    open: (state) => {
      state.modalOpen = true;
    },
    changeTopicName: (state, action: { payload: string }) => {
      state.topicName = action.payload;
    },
    changeRunAt: (state, action: { payload: IConversationFlowStartAt | null }) => {
      if (!action.payload) {
        state.startAt = null;
      } else {
        state.startAt = action.payload;
      }
    },
    changeReferralSourceId: (state, action: { payload: string }) => {
      state.referralSourceId.sourceId = action.payload;
    },
    changeDealCodeReferralSource: (state, action: { payload: string }) => {
      state.referralSourceId.dealCode = action.payload;
    },
    changeInternalTemplateText: (
      s,
      a: {
        payload: {
          index: number;
          text: string;
        };
      },
    ) => {
      const index = a.payload.index;
      s.messages[index].internalTemplate.templateId = a.payload.text;
    },
    changeMessageTextType: (state, action: { payload: { index: number; text: string } }) => {
      const index = action.payload.index;
      const text = action.payload.text;
      switch (state.messages[index].type) {
        case 'text':
          state.messages[index].text = {
            body: action.payload.text,
          };
          break;
        case 'button':
          state.messages[index].button.text = text;
          break;
        case 'priceList':
          state.messages[index].priceList.text = text;
          break;
        case 'mediaTrimitraArt':
          state.messages[index].mediaTrimitraArt.text = text;
          break;
      }
    },
    close: () => {
      return {
        ...initStates,
      };
    },
    addRow: (state) => {
      let parent: null | string = null;

      if (state.messages.length > 0) {
        parent = state.messages[state.messages.length - 1].uuid;
      }

      state.messages.push({
        ...rowMessageInitConversationFlow,
        uuid: uuidV4(),
        parent: parent || null,
      });
    },
    deleteRow: (state, action: { payload: { index: number } }) => {
      const nextRow = state.messages[action.payload.index + 1];
      const prevRow = state.messages[action.payload.index - 1];
      if (action.payload.index > 0) {
        if (nextRow) {
          nextRow.parent = prevRow.uuid;
        }
      } else if (action.payload.index === 0) {
        if (nextRow) {
          nextRow.parent = null;
        }
      }
      state.messages = state.messages.filter((v, i) => {
        return i !== action.payload.index;
      });
    },
    changeMessageType: (
      state,
      action: {
        payload: {
          index: number;
          value: IConversationFlowMessage['type'];
        };
      },
    ) => {
      const { index, value } = action.payload;
      state.messages[index].type = value;
    },

    onChangeButtonInteractiveText: (
      state,
      action: {
        payload: {
          index: number;
          indexButton: number;
          text: string;
        };
      },
    ) => {
      const { index, text, indexButton } = action.payload;
      state.messages[index].button.buttons[indexButton].text = text;
    },

    onChangeButtonInteractiveKey: (
      state,
      action: {
        payload: {
          index: number;
          indexButton: number;
          key: string;
        };
      },
    ) => {
      const { index, key, indexButton } = action.payload;
      state.messages[index].button.buttons[indexButton].id = key;
    },
    changeActiveCheckbox: (s, a: { payload: boolean }) => {
      s.active = a.payload;
    },
    changeMediatrimitraArt: (
      state,
      action: { payload: { index: number; artCode: string; text: string } },
    ) => {
      const { index, artCode, text } = action.payload;
      state.messages[index].mediaTrimitraArt.artCode = artCode;
      state.messages[index].mediaTrimitraArt.text = text;
    },
  },
  initialState: {
    ...initStates,
  } as IConversationFlowStates,
});

export default updateConversationFlowSlice;

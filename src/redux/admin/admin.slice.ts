import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';
import AdminDocument from '../../entities/AdminDocument';
import AdminLoginSessionEntity from '../../entities/AdminLoginSessionEntity';
import {
  collection,
  doc,
  getDoc,
  getDocs,
  limit,
  query,
  setDoc,
  Timestamp,
  where,
} from 'firebase/firestore';
import { firestoreIdealVer9 } from '../../services/myFirebase';
import { v4 } from 'uuid';
import moment from 'moment/moment';
import DepartmentEntity from '../../entities/DeparmentEntity';
import recentChatSlice from '../recent-chats/recentChatSlice';
import { setProject } from '../project/action-project';

export interface IAdminStates {
  admin: AdminDocument | null;
  fetching: boolean;
  adminSession: AdminLoginSessionEntity | null;
}

const initAdminStates: IAdminStates = {
  admin: null,
  fetching: false,
  adminSession: null,
};

export const setAdminThunk = createAsyncThunk(
  'setAdminThunk',
  async (params: { adminUid: string }, thunkAPI) => {
    const { actions } = adminSlice;

    const dispatch = thunkAPI.dispatch;
    dispatch(actions.setLoadingStatus(true));

    const { adminUid } = params;
    const adminRef = doc(collection(firestoreIdealVer9, 'admins'), adminUid).withConverter(
      AdminDocument.converter,
    );
    const getAdmin = await getDoc(adminRef);
    const dataAdmin = getAdmin.data()!;

    const sessionLogCollection = collection(firestoreIdealVer9, 'admin_logs');

    const findLastSessionQuery = query(
      sessionLogCollection.withConverter(AdminLoginSessionEntity.converter),
      where('admin.ref', '==', adminRef),
      where('auto_end_session_at', '>', Timestamp.now()),
      limit(1),
    );
    const findLastSession = await getDocs(findLastSessionQuery);

    let sessionLog!: AdminLoginSessionEntity;

    if (findLastSession.size === 1) {
      findLastSession.forEach((result) => {
        sessionLog = result.data();
      });
    } else {
      sessionLog = new AdminLoginSessionEntity();
      sessionLog.admin = {
        name: dataAdmin.name,
        email: dataAdmin.email,
        ref: adminRef,
      };
      sessionLog.ref = doc(sessionLogCollection, v4());
      sessionLog.signed_in_at = Timestamp.now();
      sessionLog.sent_to_bigquery_at = null;
    }

    sessionLog.analytic_data = null;
    sessionLog.last_heartbeat = Timestamp.now();
    sessionLog.auto_end_session_at = Timestamp.fromDate(
      moment()
        .add(parseInt(process.env.REACT_APP_TIMEOUT_IDLE as any), 'minutes')
        .toDate(),
    );

    let letRefUpdate = sessionLog.ref.withConverter(AdminLoginSessionEntity.converter);

    setDoc(letRefUpdate, { ...sessionLog });

    dispatch(actions.setLoadingStatus(false));

    dispatch(
      actions.setAdmin({
        admin: dataAdmin,
        session: sessionLog,
      }),
    );

    if (dataAdmin.level === 'admin') {
      if (dataAdmin.doc_department) {
        const getDepartment = await getDoc(
          dataAdmin.doc_department.withConverter(DepartmentEntity.converter),
        );

        if (getDepartment.exists()) {
          const dataDepartment = getDepartment.data();
          dispatch(recentChatSlice.actions.setFilterDepartment(dataDepartment));
        }
      } else {
        dispatch(recentChatSlice.actions.setFilterDepartment('NO_DEPARTMENT'));
      }
    }

    dispatch(setProject(dataAdmin.doc_project!) as any);
  },
);

const adminSlice = createSlice({
  name: 'adminSlice',
  reducers: {
    setLoadingStatus: (s, a: PayloadAction<boolean>) => {
      s.fetching = a.payload;
    },
    setAdmin: (
      s,
      a: PayloadAction<{
        admin: AdminDocument;
        session: AdminLoginSessionEntity;
      }>,
    ) => {
      s.admin = a.payload.admin;
      s.adminSession = a.payload.session;
    },
    unsetAdmin: () => {
      return {
        ...initAdminStates,
      };
    },
  },
  initialState: {
    ...initAdminStates,
  },
});

export default adminSlice;

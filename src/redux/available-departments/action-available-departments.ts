import { ThunkAction } from 'redux-thunk';
import { TMainReduxStates } from '../types/redux-types';
import { AnyAction } from 'redux';
import 'firebase/firestore';
import DepartmentEntity from '../../entities/DeparmentEntity';
import {
  EActionAvailableDepartment,
  TSetDepartmentAction,
} from '../types/available-department-types';
import { collection, DocumentReference, getDocs } from 'firebase/firestore';

export function setDepartment(departments: DepartmentEntity[]): TSetDepartmentAction {
  return {
    data: departments,
    type: EActionAvailableDepartment.SET_DEPARTMENT,
  };
}

export const fetchDepartments =
  (
    projectRef: DocumentReference,
    callback?: () => void,
  ): ThunkAction<void, TMainReduxStates, unknown, AnyAction> =>
  async (dispatch) => {
    const departmentCollections = collection(projectRef, 'departments');
    const get = await getDocs(departmentCollections.withConverter(DepartmentEntity.converter));
    const departments: DepartmentEntity[] = [];
    get.forEach((result) => departments.push(result.data()));
    dispatch(setDepartment(departments));
    callback?.();
  };

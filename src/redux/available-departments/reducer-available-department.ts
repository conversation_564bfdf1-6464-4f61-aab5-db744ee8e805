import {
  EActionAvailableDepartment,
  IAvailableDepartmentStates,
  TAvailableDepartmentActions,
} from '../types/available-department-types';

export const initReducerAvailableDepartmentStates: IAvailableDepartmentStates = {
  fetching: false,
  departments: [],
};

export default function reducerAvailableDepartment(
  state: IAvailableDepartmentStates = initReducerAvailableDepartmentStates,
  actions: TAvailableDepartmentActions,
) {
  const action = actions.type;
  let currentStates = { ...state };
  switch (action) {
    case EActionAvailableDepartment.SET_DEPARTMENT:
      currentStates = {
        departments: actions.data ?? [],
        fetching: false,
      };
      break;
    case EActionAvailableDepartment.FETCH_DEPARTMENT:
      currentStates = {
        departments: [],
        fetching: true,
      };
      break;
  }

  return currentStates;
}

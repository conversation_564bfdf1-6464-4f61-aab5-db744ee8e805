import { ThunkAction } from 'redux-thunk';
import { TMainReduxStates } from '../types/redux-types';
import { AnyAction } from 'redux';
import 'firebase/firestore';
import LabelEntity from '../../entities/LabelEntity';
import { EActionAvailableLabel, TSetLabelAction } from '../types/available-labels-types';
import { collection, DocumentReference, getDocs, orderBy, query, where } from 'firebase/firestore';

export function setLabel(labels: LabelEntity[]): TSetLabelAction {
  return {
    data: labels,
    type: EActionAvailableLabel.SET_LABEL,
  };
}

export const fetchLabels =
  (projectRef: DocumentReference): ThunkAction<void, TMainReduxStates, unknown, AnyAction> =>
  async (dispatch) => {
    const templateCollection = collection(projectRef, 'labels');

    const q = query(
      templateCollection.withConverter(LabelEntity.converter),
      where('active', '==', true),
      orderBy('order', 'asc'),
    );
    const get = await getDocs(q);

    const labels: LabelEntity[] = [];

    get.forEach((result) => labels.push(result.data()));

    dispatch(setLabel(labels));
  };

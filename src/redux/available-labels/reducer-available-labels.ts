import {
  EActionAvailableLabel,
  IAvailableLabelStates,
  TAvailableLabelActions,
} from '../types/available-labels-types';

export const initReducerAvailableLabelsStates: IAvailableLabelStates = {
  fetching: false,
  labels: [],
};

export default function reducerAvailableLabels(
  state: IAvailableLabelStates = initReducerAvailableLabelsStates,
  actions: TAvailableLabelActions,
) {
  const action = actions.type;
  let currentStates = { ...state };
  switch (action) {
    case EActionAvailableLabel.SET_LABEL:
      currentStates = {
        fetching: false,
        labels: actions.data ?? [],
      };
      break;
    case EActionAvailableLabel.FETCH_LABEL:
      currentStates = {
        fetching: true,
        labels: [],
      };
      break;
  }

  return currentStates;
}

import MessageTemplateEntity from '../../entities/MessageTemplateEntity';
import 'firebase/firestore';
import { collection, DocumentReference, getDocs, orderBy, query } from 'firebase/firestore';
import { createAsyncThunk } from '@reduxjs/toolkit';
import { availableTemplateSlice } from './reducer-available-template';

export const fetchTemplateVer2 = createAsyncThunk(
  'fetchTemplateVer2',
  async (projectRef: DocumentReference, thunkAPI) => {
    thunkAPI.dispatch(availableTemplateSlice.actions.fetchTemplate());

    const templateCollection = collection(projectRef, 'templates');

    const get = await getDocs(
      query(
        templateCollection.withConverter(MessageTemplateEntity.converter),
        orderBy('createdAt', 'desc'),
      ),
    );

    const templates: MessageTemplateEntity[] = [];

    get.forEach((result) => templates.push(result.data()));

    thunkAPI.dispatch(availableTemplateSlice.actions.setTemplate(templates));
  },
);

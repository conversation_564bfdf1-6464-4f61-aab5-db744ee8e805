import { IAvailableTemplatesStates } from '../types/available-templates-types';
import { createSlice } from '@reduxjs/toolkit';
import MessageTemplateEntity from '../../entities/MessageTemplateEntity';

export const initReducerAvailableTemplateStates: IAvailableTemplatesStates = {
  fetching: false,
  templates: [],
  filter: {
    labelId: null,
  },
  filteredTemplate: [],
};

const filter = (state: IAvailableTemplatesStates) => {
  let filteredTemplate = [...state.templates];
  if (state.filter.labelId) {
    filteredTemplate = filteredTemplate.filter((t) => t.label?.ref.id === state.filter.labelId);
  }

  state.filteredTemplate = filteredTemplate;
};

export const availableTemplateSlice = createSlice({
  name: 'availableTemplateSlice',
  reducers: {
    setTemplate: (state, action: { payload: MessageTemplateEntity[] }) => {
      state.templates = action.payload;
      state.fetching = false;
      state.filteredTemplate = action.payload;
    },
    fetchTemplate: (state) => {
      state.fetching = true;
      state.templates = [];
    },
    setFilterLabel: (state, action: { payload: null | string }) => {
      state.filter.labelId = action.payload;
      filter(state);
    },
  },
  initialState: {
    ...initReducerAvailableTemplateStates,
  },
});

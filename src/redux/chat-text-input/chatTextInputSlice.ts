import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export type MediaSource = 'local' | 'url' | 'whatsappMediaId';
export type MediaType = 'image' | 'video' | 'audio' | 'document';

interface IChatTextInputState {
  sending: boolean;
  htmlContentEditable: string;
  templateModalVisibility: boolean;
  errorMessage: string;

  file: {
    source: MediaSource;
    whatsappMediaId?: {
      id: string;
      previewUrl?: string;
      type: MediaType;
    };
    url?: {
      url: string;
      name: string;
      type: MediaType;
    };
    local?: {
      file: File;
      name: string;
      type: MediaType;
    };
  } | null;
}

const initialState: IChatTextInputState = {
  sending: false,
  htmlContentEditable: '',
  templateModalVisibility: false,
  errorMessage: '',
  file: null,
};

const chatTextInputSlice = createSlice({
  name: 'chatTextInput',
  initialState,
  reducers: {
    setSending: (state, action: PayloadAction<boolean>) => {
      state.sending = action.payload;
    },
    setHtmlContentEditable: (state, action: PayloadAction<string>) => {
      state.htmlContentEditable = action.payload;
    },
    setTemplateModalVisibility: (state, action: PayloadAction<boolean>) => {
      state.templateModalVisibility = action.payload;
    },
    setErrorMessage: (state, action: PayloadAction<string>) => {
      state.errorMessage = action.payload;
    },
    setFile: (state, action: PayloadAction<IChatTextInputState['file']>) => {
      state.file = action.payload;
    },
    resetState: () => initialState,
  },
});

export default chatTextInputSlice;

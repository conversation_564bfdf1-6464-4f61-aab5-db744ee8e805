import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { IConversationStates } from '../types/conversation-type';
import MessageEntity from '../../entities/MessageEntity';
import ChatRoomEntity from '../../entities/ChatRoomEntity';
import { fetchCustomerThunk } from '../customerInfo/customerInfoSlice';
import { onSnapshot, Unsubscribe, updateDoc } from 'firebase/firestore';
import FirestoreServices from '../../services/firebase/FirestoreServices';
import { TMainReduxStates } from '../types/redux-types';

const chatRoomSliceInitStates: IConversationStates = {
  conversations: [],
  chatRoom: null,
  fetching: false,
  listenerActive: false,
};

let firestoreListenerMap: Unsubscribe;

export const startListenChatRoomMessages = createAsyncThunk(
  'selectChatRoomThunk',
  async (chatRoom: ChatRoomEntity, thunkAPI) => {
    const state = thunkAPI.getState() as TMainReduxStates;

    if (state.reducerConversation.listenerActive) return;

    const clientRef = chatRoom.clients[0];
    await thunkAPI.dispatch(
      fetchCustomerThunk({
        clientDocRef: clientRef,
      }),
    );
    thunkAPI.dispatch(chatRoomSlice.actions.setFetchingState(false));

    firestoreListenerMap = onSnapshot(
      FirestoreServices.roomConversations(chatRoom).query,
      (snapshot) => {
        const results: MessageEntity[] = [];
        snapshot.forEach((result) => {
          if (result.exists()) {
            const data = result.data();
            results.push(data);
          }
        });
        thunkAPI.dispatch(chatRoomSlice.actions.loadMessages(results));
      },
    );
    thunkAPI.dispatch(chatRoomSlice.actions.setListenerActive(true));
  },
);

export const stopListenChatRoomMessages = createAsyncThunk(
  'stopChatRoomMessagesListener',
  async (_, thunkAPI) => {
    if (firestoreListenerMap) {
      firestoreListenerMap();
    }
    thunkAPI.dispatch(chatRoomSlice.actions.clearChatRoomAndMessages());
    thunkAPI.dispatch(chatRoomSlice.actions.setListenerActive(false));
  },
);

export const changeListenerChatRoomMessages = createAsyncThunk(
  'changeChatRoomMessagesListener',
  async (chatRoom: ChatRoomEntity, thunkAPI) => {
    await thunkAPI.dispatch(stopListenChatRoomMessages());

    thunkAPI.dispatch(chatRoomSlice.actions.setFetchingState(true));
    thunkAPI.dispatch(chatRoomSlice.actions.loadChatRoom(chatRoom));

    await thunkAPI.dispatch(startListenChatRoomMessages(chatRoom));
  },
);

export const stopConversationFlow = createAsyncThunk(
  'stopConversationFlowThunk',
  async (chatRoom: ChatRoomEntity, thunkAPI) => {
    updateDoc(chatRoom.ref, {
      wait_for_answer: null,
    });
  },
);

const chatRoomSlice = createSlice({
  name: 'chatRoomSlice',
  initialState: {
    ...chatRoomSliceInitStates,
  } satisfies IConversationStates,
  reducers: {
    setListenerActive: (state, action: { payload: boolean }) => {
      state.listenerActive = action.payload;
    },
    clearChatRoomAndMessages: (state) => {
      return { ...chatRoomSliceInitStates };
    },
    loadChatRoom: (
      state,
      action: {
        payload: ChatRoomEntity;
      },
    ) => {
      state.chatRoom = action.payload;
    },
    loadMessages: (state, action: { payload: MessageEntity[] }) => {
      state.conversations = action.payload;
    },
    setFetchingState: (state, action: { payload: boolean }) => {
      state.fetching = action.payload || false;
    },
  },
});

export default chatRoomSlice;

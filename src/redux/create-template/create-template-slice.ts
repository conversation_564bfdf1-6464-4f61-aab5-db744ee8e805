import { createSlice } from '@reduxjs/toolkit';
import { DocumentReference } from 'firebase/firestore';

interface State {
  open: boolean;
  updateMode: {
    text: string;
    id: string;
    button?: {
      button1: string;
      button2: string;
      button3: string;
    };
    label: {
      name: string;
      ref: DocumentReference;
    } | null;
  } | null;
}

const createTemplateSlice = createSlice({
  name: 'createTemplate',
  initialState: {
    open: false,
    updateMode: null,
  } as State,
  reducers: {
    open: (state) => {
      state.open = true;
    },
    close: (state) => {
      state.open = false;
      state.updateMode = null;
    },

    openUpdate: (
      state,
      action: {
        payload: {
          text: string;
          id: string;
          type: string;
          button?: {
            button1: string;
            button2: string;
            button3: string;
          };
          label: {
            ref: DocumentReference;
            name: string;
          } | null;
        };
      },
    ) => {
      state.open = true;
      state.updateMode = action.payload;
    },
  },
});

export default createTemplateSlice;

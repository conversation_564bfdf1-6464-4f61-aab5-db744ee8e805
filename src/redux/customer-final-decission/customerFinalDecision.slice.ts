import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import ProviderTemplate from '../../entities/ProviderTemplate';
import { collection, getDocs, query, where } from 'firebase/firestore';
import { firestoreIdealVer9 } from '../../services/myFirebase';

type TFinalDecision = 'noResult' | 'order' | 'needToFollowUp';

interface CustomerFinalDecisionStates {
  modalOpen: boolean;
  submitting: boolean;

  errorSubmittingMessage: string;

  finalDecision: TFinalDecision | null;
  followUp: {
    date: Date | null;
    time: string;
    message: {
      template: ProviderTemplate | null;
      _template: string;
      variables: string[];
    };
  };
  availableTemplates: ProviderTemplate[];
}

const initState: CustomerFinalDecisionStates = {
  availableTemplates: [],
  modalOpen: false,
  submitting: false,

  errorSubmittingMessage: '',

  finalDecision: null,
  followUp: {
    date: null,
    time: '',
    message: {
      template: null,
      _template: '',
      variables: [],
    },
  },
};

export const fetchTemplateFinalDecision = createAsyncThunk(
  'customerFinalDecision/fetchTemplateFinalDecision',
  async () => {
    const templateCollection = collection(
      firestoreIdealVer9,
      '/projects/v8GGopG1v89nd46aajEX',
      'provider_templates',
    );

    const get = await getDocs(
      query(
        templateCollection.withConverter(ProviderTemplate.converter),
        where('idealInitMessage', '==', true),
      ),
    );

    const t: ProviderTemplate[] = [];

    get.forEach((result) => {
      const data = result.data();
      t.push(data);
    });

    return t;
  },
);

const customerFinalDecisionSlice = createSlice({
  name: 'customerFinalDecission',
  reducers: {
    modalOpen: (
      state,
      action: {
        payload: {
          finalDecision: TFinalDecision | null;
          followUp?: CustomerFinalDecisionStates['followUp'] | null;
        };
      },
    ) => {
      state.modalOpen = true;
      state.finalDecision = action.payload.finalDecision;
      if (action.payload.finalDecision === 'needToFollowUp' && action.payload.followUp) {
        state.followUp = action.payload.followUp;
      }
    },
    close: (state) => {
      return {
        ...initState,
      };
    },
    setErrorMessage: (state, action: { payload: string }) => {
      state.submitting = false;
      state.errorSubmittingMessage = action.payload;
    },
    setLoading: (state, action: { payload: boolean }) => {
      state.submitting = action.payload;
      if (action.payload) {
        state.errorSubmittingMessage = '';
      }
    },
    changeFinalDecision: (state, action: { payload: TFinalDecision }) => {
      state.finalDecision = action.payload;
    },
    changeFollowUpDate: (state, action: { payload: Date | null }) => {
      state.followUp.date = action.payload;
    },
    changeFollowUpTime: (state, action: { payload: string }) => {
      state.followUp.time = action.payload;
    },
    changeFollowUpTemplateMessage: (state, action: { payload: ProviderTemplate }) => {
      state.followUp.message.template = action.payload;
      state.followUp.message.variables = action.payload.variables.map(() => {
        return '';
      });
    },
    changeFollowUpTemplateVariable: (
      state,
      action: { payload: { index: number; value: string } },
    ) => {
      state.followUp.message.variables[action.payload.index] = action.payload.value;
    },
  },
  initialState: {
    ...initState,
  } as CustomerFinalDecisionStates,

  extraReducers: (builder) => {
    builder.addCase(fetchTemplateFinalDecision.fulfilled, (state, action) => {
      state.availableTemplates = action.payload;
      if (state.followUp.message._template) {
        state.followUp.message.template =
          action.payload.find((t) => t.id === state.followUp.message._template) || null;
      }
    });
  },
});

export default customerFinalDecisionSlice;

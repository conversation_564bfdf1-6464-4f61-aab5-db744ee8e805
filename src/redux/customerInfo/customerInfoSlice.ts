import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { IClientEntity } from '../../entities/types/client-entity-types';
import { DocumentReference, getDoc } from 'firebase/firestore';
import ClientEntity from '../../entities/ClientEntity';

interface CustomerInfoStates {
  fetching: boolean;
  client: IClientEntity | null;
  ref: DocumentReference | null;
}

export const fetchCustomerThunk = createAsyncThunk(
  'customer/fetch',
  async (params: { clientDocRef: DocumentReference; cb?: Function }) => {
    const documentRef = params.clientDocRef;
    const get = await getDoc(documentRef.withConverter(ClientEntity.converter));

    const client = get.data();

    if (!client) return Promise.reject('Client not Found');

    return {
      clientData: client,
      cb: params.cb,
    };
  },
);

const customerInfoSlice = createSlice({
  name: 'customerInfoSlice',
  initialState: {
    fetching: false,
    client: null,
  } as CustomerInfoStates,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(fetchCustomerThunk.pending, (state) => {
      state.fetching = true;
    });

    builder.addCase(fetchCustomerThunk.fulfilled, (state, { payload }) => {
      state.fetching = false;
      state.ref = payload.clientData.ref ?? null;
      state.client = {
        developer: payload.clientData.developer,
        profile: payload.clientData.profile,
        details: payload.clientData.details ?? {
          owner_phone_number: '',
          guarantor_phone_number: '',
          order_maker_phone_number: '',
        },
        dream_vehicle: payload.clientData.dream_vehicle ?? null,
        contacts: payload.clientData.contacts,
        created_time: payload.clientData.created_time,
        survey_loan: payload.clientData.survey_loan || null,
        survey: payload.clientData?.survey
          ? {
              ...payload.clientData.survey,
              credit_scheme: payload.clientData.survey?.credit_scheme ?? null,
            }
          : null,
        cash_offer: payload.clientData?.cash_offer ?? null,
        freeLeadsStatus: payload.clientData.freeLeadsStatus ?? null,
        acquiredLeadsStatus: payload.clientData.acquiredLeadsStatus ?? null,
        broadcast: payload.clientData.broadcast ?? {
          allow_to_broadcast: false,
          client_purchase: null,
          is_purchased: false,
        },
        order_histories: payload.clientData.order_histories,
        leads: payload.clientData.leads,
        notes: payload.clientData.notes || null,
        finalDecision: payload.clientData.finalDecision,
        owned_vehicle: payload.clientData.owned_vehicle || [],
      };

      setTimeout(() => {
        payload.cb?.();
      }, 100);
    });
  },
});

export default customerInfoSlice;

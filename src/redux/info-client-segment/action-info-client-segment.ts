import {
  TCloseInfoClientSegment,
  TOpenInfoClientSegment,
  TToggleInfoClientSegment,
} from '../types/info-client-segment';

export function openInfoSegment(): TOpenInfoClientSegment {
  return {
    type: 'OPEN_INFO_CLIENT_SEGMENT',
    data: void 0,
  };
}

export function closeInfoSegment(): TCloseInfoClientSegment {
  return {
    type: 'CLOSE_INFO_CLIENT_SEGMENT',
    data: void 0,
  };
}

export function toggleInfoSegment(): TToggleInfoClientSegment {
  return {
    type: 'TOGGLE_INFO_CLIENT_SEGMENT',
    data: void 0,
  };
}

export type TClientInfoSegmentActionFunctions =
  | ReturnType<typeof openInfoSegment>
  | ReturnType<typeof closeInfoSegment>
  | ReturnType<typeof toggleInfoSegment>;

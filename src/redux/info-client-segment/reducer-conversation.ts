import { IInfoClientSegment } from '../types/info-client-segment';
import { TClientInfoSegmentActionFunctions } from './action-info-client-segment';

const initInfoClientSegment: IInfoClientSegment = {
  visible: false,
};

export default function reducerInfoClientSegment(
  state: IInfoClientSegment = { ...initInfoClientSegment },
  action: TClientInfoSegmentActionFunctions,
): IInfoClientSegment {
  let currentState = { ...state };

  switch (action.type) {
    case 'OPEN_INFO_CLIENT_SEGMENT':
      currentState.visible = true;
      break;
    case 'CLOSE_INFO_CLIENT_SEGMENT':
      currentState.visible = false;
      break;
    case 'TOGGLE_INFO_CLIENT_SEGMENT':
      currentState.visible = !currentState.visible;
      break;
  }

  return currentState;
}

import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface LightboxState {
  isOpen: boolean;
  imageUrl: string | null;
}

const initialState: LightboxState = {
  isOpen: false,
  imageUrl: null,
};

const lightboxSlice = createSlice({
  name: 'lightbox',
  initialState,
  reducers: {
    openLightbox: (state, action: PayloadAction<string>) => {
      state.isOpen = true;
      state.imageUrl = action.payload;
    },
    closeLightbox: (state) => {
      state.isOpen = false;
      state.imageUrl = null;
    },
  },
});

export const { openLightbox, closeLightbox } = lightboxSlice.actions;
export default lightboxSlice;

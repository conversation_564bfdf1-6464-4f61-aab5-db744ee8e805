import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { IOwnedVehicle } from '../../entities/types/client-entity-types';
import ownedVehicle from '../../Components/NewClientInformation/OwnedVehicle/OwnedVehicle';

interface States {
  open: boolean;
  loading: boolean;
  submitStatus: 'none' | 'submitting' | 'success' | 'error';
  errorMessages: string;
  ownedVehicle: IOwnedVehicle | null;
}

const initStates: States = {
  open: false,
  errorMessages: '',
  submitStatus: 'none',
  loading: false,
  ownedVehicle: null,
};

const modalLoanConfirmSlice = createSlice({
  name: 'modalConfirmSlice',
  reducers: {
    open: (s, a: PayloadAction<IOwnedVehicle>) => {
      s.open = true;
      s.ownedVehicle = a.payload;
    },
    close: (s) => {
      return {
        ...initStates,
      };
    },
    setSubmitStatus: (s, a: PayloadAction<States['submitStatus']>) => {
      switch (a.payload) {
        case 'submitting':
          s.loading = true;
          s.submitStatus = 'submitting';
          s.errorMessages = '';
          break;
        case 'error':
          s.loading = false;
          s.submitStatus = 'error';
          break;
        case 'success':
          s.loading = false;
          s.submitStatus = 'success';
          s.errorMessages = '';
          break;
      }
    },
    setErrorMessages: (s, a: PayloadAction<string>) => {
      s.errorMessages = a.payload;
    },
  },
  initialState: {
    ...initStates,
  },
});

export default modalLoanConfirmSlice;

import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface States {
  open: boolean;
  submitting: boolean;
  result: 'none' | 'success' | 'error';
  errorMessages: string;
}

const initStates: States = {
  open: false,
  submitting: false,
  result: 'none',
  errorMessages: '',
};

const modalAcquisitionAsLeads = createSlice({
  name: 'modalAcquisitionAsLeads',
  reducers: {
    open: (state: States, action: PayloadAction<void>) => {
      state.open = true;
    },
    close: (state) => {
      return {
        ...initStates,
      };
    },
    setSubmittingStatus: (state: States, action: PayloadAction<void>) => {
      state.submitting = true;
      state.result = 'none';
      state.errorMessages = '';
    },
    setErrorResult: (state: States, action: PayloadAction<string>) => {
      state.submitting = false;
      state.result = 'error';
      state.errorMessages = 'Gagal: ' + action.payload;
    },
  },
  initialState: {
    ...initStates,
  },
});

export default modalAcquisitionAsLeads;

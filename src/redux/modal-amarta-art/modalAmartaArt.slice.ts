import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { amartaArtServices } from '../../services/amartaArt.services';
import { ArtItem } from '../../services/types/amartaArt.services.types';

export interface ModalAmartaArtState {
  isOpen: boolean;
  selectedArtCategory: 'feed' | 'feed_promo' | null;
  selectedBrand: { name: string; uuid: string } | null;
  feedData: ArtItem[];
  pagination: {
    total: number;
    start: number;
    length: number;
    currentPage: number;
    totalPages: number;
  };
  isLoading: boolean;
  error: string | null;
}

const initialState: ModalAmartaArtState = {
  isOpen: false,
  selectedArtCategory: null,
  selectedBrand: null,
  feedData: [],
  pagination: {
    total: 0,
    start: 0,
    length: 10,
    currentPage: 1,
    totalPages: 0,
  },
  isLoading: false,
  error: null,
};

// Async thunk untuk mengambil feed data
export const fetchFeed = createAsyncThunk(
  'modalAmartaArt/fetchFeed',
  async (
    { art_category, brand_uid }: { art_category: 'feed' | 'feed_promo'; brand_uid: string },
    { rejectWithValue },
  ) => {
    try {
      const response = await amartaArtServices.getFeed({
        art_category: art_category,
        brand_uid: brand_uid,
        start: 0,
        length: 50,
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  },
);

export const fetchImageDetails = createAsyncThunk(
  'modalAmartaArt/fetchImageDetails',
  async (artCode: string) => {
    const response = await amartaArtServices.getMediaByArtCode(artCode);
    return response.data;
  },
);

const modalAmartaArtSlice = createSlice({
  name: 'modalAmartaArt',
  initialState,
  reducers: {
    openModal(state) {
      state.isOpen = true;
    },
    closeModal(state) {
      state.isOpen = false;
    },
    setSelectedArtCategory(state, action: PayloadAction<'feed' | 'feed_promo'>) {
      state.selectedArtCategory = action.payload;
    },
    setSelectedBrand(state, action: PayloadAction<{ name: string; uuid: string }>) {
      state.selectedBrand = action.payload;
    },
    setErrorMessage(state, action) {
      state.error = action.payload;
    },
    resetErrorState(state) {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchFeed.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(fetchFeed.fulfilled, (state, action) => {
        state.feedData = action.payload.data;
        state.pagination = {
          total: action.payload.pagination.total,
          start: action.payload.pagination.start,
          length: action.payload.pagination.length,
          currentPage: action.payload.pagination.current_page,
          totalPages: action.payload.pagination.total_pages,
        };
        state.isLoading = false;
      })
      .addCase(fetchFeed.rejected, (state, action) => {
        state.error = action.payload as string;
        state.isLoading = false;
      });
  },
});

export const {
  openModal,
  closeModal,
  setSelectedArtCategory,
  setSelectedBrand,
  setErrorMessage,
  resetErrorState,
} = modalAmartaArtSlice.actions;

export default modalAmartaArtSlice.reducer;

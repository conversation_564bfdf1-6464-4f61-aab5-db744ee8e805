import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';
import { OrderDataBbn } from '../../services/logistic/logisticService.types';
import logisticService from '../../services/logistic/logisticService';

interface States {
  open: boolean;
  bbn: OrderDataBbn | null;
  loading: boolean;
  errorMessages: string;
}

const states: States = {
  open: false,
  bbn: null,
  loading: false,
  errorMessages: '',
};

export const openModalCheckBbnThunk = createAsyncThunk(
  'modalOpenBbnThunk',
  async (engineNumber: string) => {
    try {
      const get = await logisticService.getBbnByEngineNumber(engineNumber);
      if (get.success.data.length > 0) {
        return get.success.data[0];
      } else {
        throw 'Not Found';
      }
    } catch (e) {
      throw 'Not Found';
    }
  },
);

const modalCheckBbnSlice = createSlice({
  name: 'modalBbnSlice',
  reducers: {
    open: (s, a: PayloadAction<OrderDataBbn>) => {
      s.open = true;
      s.bbn = a.payload;
    },
    close: () => {
      return {
        ...states,
      };
    },
  },
  initialState: {
    ...states,
  },
  extraReducers: (builder) => {
    builder.addCase(openModalCheckBbnThunk.pending, (state) => {
      state.loading = true;
      state.bbn = null;
      state.errorMessages = '';
    });

    builder.addCase(openModalCheckBbnThunk.fulfilled, (state, action) => {
      state.open = true;
      state.loading = false;
      state.bbn = action.payload;
    });

    builder.addCase(openModalCheckBbnThunk.rejected, (state, action) => {
      state.loading = false;
      state.bbn = null;
      state.errorMessages = 'Not Found';
    });
  },
});

export default modalCheckBbnSlice;

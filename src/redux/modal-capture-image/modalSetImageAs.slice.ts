import { createSlice } from '@reduxjs/toolkit';
import { TCaptureTarget } from '../../Components/ImageCapture/types/capture-types';

interface InitState {
  open: boolean;
  type: 'SET' | 'UPDATE';
  documentTarget: TCaptureTarget | null;
  urlImageToSet: string | null;
}

const initialState: InitState = {
  open: false,
  type: 'SET',
  urlImageToSet: null,
  documentTarget: null,
};

const modalSetImageAsSlice = createSlice({
  name: 'modalSetImageAs',
  initialState: {
    ...initialState,
  },
  reducers: {
    open: (
      s,
      a: {
        payload: {
          type: InitState['type'];
          documentTarget?: TCaptureTarget;
          urlImageToSet?: string;
        };
      },
    ) => {
      s.open = true;
      s.type = a.payload.type;

      if (a.payload.documentTarget) {
        s.documentTarget = a.payload.documentTarget;
      }
      if (a.payload.urlImageToSet) {
        s.urlImageToSet = a.payload.urlImageToSet;
      }
    },
    close: () => {
      return {
        ...initialState,
      };
    },
    setImageCaptureTypes: (s, a: { payload: TCaptureTarget }) => {
      s.documentTarget = a.payload;
    },
  },
});

export default modalSetImageAsSlice;

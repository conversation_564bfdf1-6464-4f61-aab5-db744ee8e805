import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import MessageEntity from '../../entities/MessageEntity';

interface States {
  open: boolean;
  messageEntity: null | MessageEntity;
}

const initState: States = {
  open: false,
  messageEntity: null,
};

const modalChatDebugSlice = createSlice({
  name: 'modalChatDebug',
  initialState: {
    open: false,
    messageEntity: null,
  } as States,
  reducers: {
    open: (s, a: PayloadAction<MessageEntity>) => {
      s.open = true;
      s.messageEntity = a.payload;
    },
    close: () => {
      return {
        ...initState,
      };
    },
  },
});

export default modalChatDebugSlice;

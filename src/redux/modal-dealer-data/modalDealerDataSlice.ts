import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { IGetDealerDataResponse } from '../../services/types/catalaogueTypes';

interface ModalDealerDataState {
  open: boolean;
  fetching: boolean;
  dealerData: IGetDealerDataResponse | null;
  errorMessage: string;
}

const initialState: ModalDealerDataState = {
  open: false,
  fetching: false,
  dealerData: null,
  errorMessage: '',
};

const modalDealerDataSlice = createSlice({
  name: 'modalDealerData',
  initialState: {
    ...initialState,
  },
  reducers: {
    open: (state) => {
      state.open = true;
    },
    close: (state) => {
      state.open = false;
    },
    setFetching: (state, action: PayloadAction<boolean>) => {
      state.fetching = action.payload;
    },
    setDealerData: (state, action: PayloadAction<IGetDealerDataResponse>) => {
      state.dealerData = action.payload;
    },
    setErrorMessage: (state, action: PayloadAction<string>) => {
      state.errorMessage = action.payload;
    },
    resetState: (state) => {
      Object.assign(state, initialState);
    },
  },
});

export default modalDealerDataSlice;

import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { IGetOfferSuccessResponse } from '../../services/types/offerServiceTypes';
import { offerServices } from '../../services/offerServices';
import { IClientEntityOrderHistory } from '../../entities/types/client-entity-types';

interface States {
  open: boolean;
  offerCodeToView: string | null;
  dataOfferCode: IGetOfferSuccessResponse['data'] | null;
  fetching: boolean;
  errorMessage: string | null;
  orderHistory: IClientEntityOrderHistory | null;

  creatingBill: boolean;
  sendingPaymentReceipt: boolean;
}

const initState: States = {
  open: false,
  offerCodeToView: null,
  dataOfferCode: null,
  fetching: false,
  errorMessage: null,
  orderHistory: null,

  creatingBill: false,
  sendingPaymentReceipt: false,
};

const modalDetailOfferSlice = createSlice({
  name: 'modalDetailOffer',
  initialState: { ...initState } as States,
  reducers: {
    open: (state, action: { payload: { open: boolean; data?: IClientEntityOrderHistory } }) => {
      if (action.payload.open) {
        state.open = action.payload.open;
        if (action.payload.data) {
          state.orderHistory = action.payload.data;
        } else {
          state.orderHistory = null;
        }
      } else {
        return {
          ...initState,
        };
      }
    },
    setCreatingBillLoading: (s, a: { payload: boolean }) => {
      s.creatingBill = a.payload;
    },
    setSendPaymentReceiptLoading: (s, a: { payload: boolean }) => {
      s.sendingPaymentReceipt = a.payload;
    },
  },
  extraReducers: (builder) => {
    builder.addCase(modalDetailOfferFetch.pending, (state) => {
      state.fetching = true;
      state.errorMessage = null;
    });
    builder.addCase(modalDetailOfferFetch.fulfilled, (state, action) => {
      state.fetching = false;
      state.offerCodeToView = action.payload.transaction_code;
      state.dataOfferCode = action.payload;
      state.errorMessage = null;
    });
    builder.addCase(modalDetailOfferFetch.rejected, (state, action) => {
      state.fetching = false;
      state.offerCodeToView = null;
      state.dataOfferCode = null;
      state.errorMessage = action.error.message || null;
    });
  },
});

export const modalDetailOfferFetch = createAsyncThunk(
  'modalDetailOffer/fetchOfferCode',
  async (offerCode: string, thunkAPI) => {
    try {
      const get = await offerServices.getDataByOfferCode(offerCode);
      if (get?.data) {
        return get.data;
      }
      throw new Error(
        'Kode Offer Tidak ditemukan. Mungkin karena nomor offer ini belum ada di Offer.',
      );
    } catch (e) {
      throw new Error(
        'Kode Offer Tidak ditemukan. Mungkin karena nomor offer ini belum ada di Offer.',
      );
    }
  },
);

export default modalDetailOfferSlice;

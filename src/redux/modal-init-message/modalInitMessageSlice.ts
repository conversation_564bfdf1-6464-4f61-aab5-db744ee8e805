import { createSlice } from '@reduxjs/toolkit';

interface States {
  open: boolean;
  preContact: null | {
    name: string;
    phoneNumber: string;
  };
}

const modalInitMessageSlice = createSlice({
  name: 'modalInitMessage',
  reducers: {
    open: (states, action) => {
      states.open = action.payload.open;
      if (action.payload.open) {
        if (action.payload.preContact) {
          states.preContact = action.payload.preContact;
        }
      } else {
        states.preContact = null;
      }
    },
  },
  initialState: {
    open: false,
    preContact: null,
  } as States,
});

export default modalInitMessageSlice;

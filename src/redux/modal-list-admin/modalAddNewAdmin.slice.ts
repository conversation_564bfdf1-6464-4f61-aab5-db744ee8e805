import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import ProjectEntity from '../../entities/ProjectEntity';
import { collection, doc, getDocs, query, where } from 'firebase/firestore';
import { firestoreIdealVer9 } from '../../services/myFirebase';
import DepartmentEntity from '../../entities/DeparmentEntity';

interface IModalAddNewAdmin {
  open: boolean;
  loading: boolean;
  errorMessage: string;

  name: string;
  email: string;
  password: string;
  role: string;
  selectedProjectId: string | null;
  selectedDepartmentId: string | null;

  availableProjects: ProjectEntity[];
  availableDepartments: DepartmentEntity[];
}

const initStates: IModalAddNewAdmin = {
  open: false,
  loading: false,
  errorMessage: '',

  name: '',
  email: '',
  password: '',
  role: 'owner',
  selectedDepartmentId: null,
  selectedProjectId: null,

  availableProjects: [],
  availableDepartments: [],
};

export const modalAddNewAdminFetchAvailableProjects = createAsyncThunk(
  'modalAddNewAdminSlice/fetchProjects',
  async (arg, thunkAPI) => {
    const projectCollection = collection(firestoreIdealVer9, 'projects').withConverter(
      ProjectEntity.converter,
    );
    const q = query(projectCollection, where('active', '==', true));
    const get = await getDocs(q);

    const projects: ProjectEntity[] = [];

    get.forEach((result) => {
      projects.push(result.data());
    });

    return projects;
  },
);

export const modalAddNewAdminFetchAvailableDepartment = createAsyncThunk(
  'modalAddNewAdminSlice/fetchDepartment',
  async (projectId: string, thunkAPI) => {
    const projectCollection = collection(firestoreIdealVer9, 'projects');
    const projectDoc = doc(projectCollection, projectId);
    const departmentCollection = collection(projectDoc, 'departments').withConverter(
      DepartmentEntity.converter,
    );
    const getDepartment = await getDocs(departmentCollection);

    const departments: DepartmentEntity[] = [];

    getDepartment.forEach((result) => {
      departments.push(result.data());
    });

    return departments;
  },
);

const modalAddNewAdminSlice = createSlice({
  name: 'modalAddNewAdmin',
  reducers: {
    open: (state) => {
      state.open = true;
    },
    close: (state) => {
      return { ...initStates };
    },
    setErrorMessage: (state, action: { payload: string }) => {
      state.errorMessage = action.payload;
    },
    setLoading: (state, action: { payload: boolean }) => {
      state.loading = action.payload;
    },
    setName: (state, action: { payload: string }) => {
      state.name = action.payload;
    },
    setEmail: (state, action: { payload: string }) => {
      state.email = action.payload;
    },
    setPassword: (state, action: { payload: string }) => {
      state.password = action.payload;
    },
    setRole: (state, action: { payload: string }) => {
      state.role = action.payload;
    },
    setSelectedProject: (state, action: { payload: string }) => {
      state.selectedProjectId = action.payload;
    },
    setSelectedDepartment: (state, action: { payload: string }) => {
      state.selectedDepartmentId = action.payload || null;
    },
  },
  initialState: {
    ...initStates,
  },
  extraReducers: (builder) => {
    builder.addCase(modalAddNewAdminFetchAvailableProjects.fulfilled, (state, action) => {
      state.availableProjects = action.payload;
    });

    builder.addCase(modalAddNewAdminFetchAvailableDepartment.fulfilled, (state, action) => {
      state.availableDepartments = action.payload;
    });
  },
});

export default modalAddNewAdminSlice;

import AdminDocument from '../../entities/AdminDocument';
import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { collection, getDocs, orderBy, query } from 'firebase/firestore';
import { firestoreIdealVer9 } from '../../services/myFirebase';

export interface ModalListAdminStates {
  open: boolean;
  loading: boolean;
  adminList: AdminDocument[];
  filteredAdminList: AdminDocument[];
  filter: {
    emailOrName: string;
  };
}

const initState: ModalListAdminStates = {
  open: false,
  loading: false,
  adminList: [],
  filteredAdminList: [],
  filter: {
    emailOrName: '',
  },
};

export const fetchAdminListThunk = createAsyncThunk(
  'fetchAdminListThunk',
  async (arg, thunkAPI) => {
    const collectionAdmins = collection(firestoreIdealVer9, 'admins');
    const queries = query(collectionAdmins, orderBy('created_time', 'desc'));

    const get = await getDocs(queries.withConverter(AdminDocument.converter));

    const adminDocuments: AdminDocument[] = [];

    get.forEach((result) => {
      adminDocuments.push(result.data());
    });

    thunkAPI.dispatch(modalListAdminSlice.actions.setAdminList(adminDocuments));

    thunkAPI.dispatch(modalListAdminSlice.actions.filterClient());
  },
);

const modalListAdminSlice = createSlice({
  name: 'modalListAdminSlice',
  reducers: {
    open: (state) => {
      state.open = true;
    },
    close: (state) => {
      return { ...initState };
    },
    setAdminList: (state, action: { payload: AdminDocument[] }) => {
      state.adminList = action.payload;
    },
    setFilterEmail: (state, action: { payload: string }) => {
      state.filter.emailOrName = action.payload;
    },
    filterClient: (state) => {
      state.filteredAdminList = state.adminList;

      const filterEmail = state.filter.emailOrName.toLowerCase();
      if (filterEmail) {
        state.filteredAdminList = state.filteredAdminList.filter((a) => {
          return (
            a.email.toLowerCase().includes(filterEmail) ||
            a.name.toLowerCase().includes(filterEmail)
          );
        });
      }
    },
  },
  initialState: {
    ...initState,
  },
  extraReducers: (builder) => {
    builder.addCase(fetchAdminListThunk.pending, (state) => {
      state.loading = true;
    });
  },
});

export default modalListAdminSlice;

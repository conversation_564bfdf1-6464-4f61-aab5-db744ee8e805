import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { VariantProduct } from '../../services/types/catalaogueTypes';
import b2bServices from '../../services/b2b/b2bServices';
import { IPriceListJsonData } from '../../services/types/TabularPriceList.types';
import { IPromoCode } from '../../services/types/promoServiceTypes';

interface State {
  open: boolean;
  priceListState: 'none' | 'not_found' | 'available' | 'fetching';
  priceList: IPriceListJsonData | null;
  vehicle: {
    cityGroup: string;
    variant: VariantProduct | null;
  } | null;

  selected: {
    [key: string]: {
      downPayment: number;
      tenor: number;
      installment: number;
    };
  };

  promoType: 'none' | 'promoCode' | 'nominalInput';
  selectedPromoCode: null | IPromoCode;
  nominalInputDiscount: number | null;
}

export const fetchPriceList = createAsyncThunk(
  'priceListSimple/fetch',
  async (arg: { cityGroup: string; modelName: string; variantCode: string }) => {
    const get = await b2bServices.getPriceList({
      cityGroup: arg.cityGroup,
      modelName: arg.modelName,
      variantCode: arg.variantCode,
    });
    return get.data;
  },
);

const modalPriceListSliceSimple = createSlice({
  name: 'modalPriceListSimple',
  initialState: {
    open: false,
    priceListState: 'none',
    priceList: null,
    vehicle: null,
    selected: {},
    promoType: 'none',
    selectedPromoCode: null,
    nominalInputDiscount: 0,
  } as State,
  reducers: {
    reset: (s) => {
      s.priceList = null;
      s.selected = {};
      s.vehicle = null;
      s.priceListState = 'none';
      s.promoType = 'none';
      s.selectedPromoCode = null;
      s.nominalInputDiscount = 0;
    },
    open: (s) => {
      s.open = true;
    },
    close: (s) => {
      s = {
        open: false,
        priceListState: 'none',
        priceList: null,
        vehicle: null,
        selected: {},
        promoType: 'none',
        nominalInputDiscount: 0,
        selectedPromoCode: null,
      };

      return s;
    },
    setVehicle: (s, action: { payload: { variant: VariantProduct; cityGroup: string } }) => {
      s.vehicle = {
        cityGroup: action.payload.cityGroup,
        variant: action.payload.variant,
      };
    },

    addSelected: (
      s,
      action: {
        payload: {
          downPayment: number;
          installment: number;
          tenor: number;
          cellKey: string;
        };
      },
    ) => {
      const { cellKey, installment, tenor, downPayment } = action.payload;
      s.selected[cellKey] = {
        downPayment: downPayment,
        tenor: tenor,
        installment: installment,
      };
    },
    removeSelected: (s, action: { payload: string }) => {
      delete s.selected[action.payload];
    },
    setDiscountType: (state, action) => {
      state.promoType = action.payload;
    },
    setPromoCode: (s, action: { payload: IPromoCode | null }) => {
      s.selectedPromoCode = action.payload;
    },
    setNominalPromo: (state, action: { payload: number | null }) => {
      state.nominalInputDiscount = action.payload;
    },
  },

  extraReducers: (builder) => {
    builder.addCase(fetchPriceList.pending, (state) => {
      state.priceListState = 'fetching';
      state.priceList = null;
    });
    builder.addCase(fetchPriceList.fulfilled, (state, action) => {
      state.priceListState = 'available';
      state.priceList = action.payload;
    });
    builder.addCase(fetchPriceList.rejected, (state) => {
      state.priceListState = 'not_found';
      state.priceList = null;
    });
  },
});

export default modalPriceListSliceSimple;

import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { VariantProduct } from '../../services/types/catalaogueTypes';
import { IPriceListJsonData } from '../../services/types/TabularPriceList.types';
import b2bServices from '../../services/b2b/b2bServices';
import moment from 'moment';
import randomNumber from '../../helpers/randomNumber';

interface PriceListRow {
  downPayment: number | null;
  installments: Installment[];
  key: string;
}

export interface Installment {
  tenor: number;
  installment: number;
  discountInstallment: number | null;
}

interface TenorRow {
  tenor: number;
  discount: number | null;
}

interface State {
  openPriceListModalTabular: boolean;

  open: boolean;
  vehicle: {
    cityGroup: string;
    variant: VariantProduct | null;
  } | null;
  normalPriceList: PriceListRow[];
  priceList: IPriceListJsonData | null;
  priceListState: 'none' | 'not_found' | 'available' | 'fetching';
  title: string;
  subTitle: string;
  expiredAt: Date | null;

  selectedTenor: TenorRow[];
}

const defaultExpiredAt = moment().add(15, 'days').toDate();

export const fetchPriceList = createAsyncThunk(
  'priceList/fetch',
  async (arg: { cityGroup: string; modelName: string; variantCode: string }) => {
    const get = await b2bServices.getPriceList({
      cityGroup: arg.cityGroup,
      modelName: arg.modelName,
      variantCode: arg.variantCode,
    });
    return get.data;
  },
);

const modalPriceListSlice = createSlice({
  name: 'modalPriceListSlice',
  extraReducers: (builder) => {
    builder.addCase(fetchPriceList.pending, (state) => {
      state.priceListState = 'fetching';
      state.priceList = null;
    });
    builder.addCase(fetchPriceList.fulfilled, (state, action) => {
      state.priceListState = 'available';
      state.priceList = action.payload;
    });
    builder.addCase(fetchPriceList.rejected, (state) => {
      state.priceListState = 'not_found';
      state.priceList = null;
    });
  },
  reducers: {
    openPriceListModalTabular: (s) => {
      s.openPriceListModalTabular = true;
    },
    closePriceListModalTabular: (s) => {
      s.openPriceListModalTabular = false;
    },
    open: (state) => {
      state.open = true;
    },
    close: (state) => {
      state = {
        openPriceListModalTabular: false,
        open: false,
        vehicle: null,
        normalPriceList: [],
        priceList: null,
        priceListState: 'none',
        title: '',
        subTitle: '',
        selectedTenor: [],
        expiredAt: defaultExpiredAt,
      };

      return state;
    },
    deleteRow: (state, action: { payload: { uidRow: string } }) => {
      state.normalPriceList = state.normalPriceList.filter((v) => v.key !== action.payload.uidRow);
    },
    addRow: (state) => {
      const uid = randomNumber().toString();
      state.normalPriceList.push({
        installments: state.selectedTenor.map((t) => {
          return {
            tenor: t.tenor,
            installment: 0,
            discountInstallment: null,
          };
        }),
        downPayment: null,
        key: uid,
      });
    },

    clearRowAndPriceList: (state) => {
      state.normalPriceList = [];
      state.priceList = null;
      state.priceListState = 'none';
      state.vehicle = null;
      state.openPriceListModalTabular = false;
      state.selectedTenor = [];
    },

    setTitle: (state, action: { payload: string }) => {
      state.title = action.payload;
    },
    setSubTitle: (state, action: { payload: string }) => {
      state.subTitle = action.payload;
    },

    setPriceList: (
      state,
      action: {
        payload: {
          state: State['priceListState'];
          priceList?: State['priceList'];
        };
      },
    ) => {
      state.priceListState = action.payload.state;
      state.priceList = action.payload.priceList || null;
    },

    setValueDownPayment: (state, action: { payload: { value: number; uidRow: string } }) => {
      const { value, uidRow } = action.payload;
      const index = state.normalPriceList.findIndex((v) => v.key === uidRow);
      state.normalPriceList[index].downPayment = value;
    },

    autoFillInstallmentAllDp: (s) => {
      for (const normalPriceListElement of s.normalPriceList) {
        for (const installment of normalPriceListElement.installments) {
          const findInstallment = s.priceList?.formatted.find(
            (p) => p.tenor === installment.tenor && normalPriceListElement.downPayment === p.dp,
          );
          installment.installment = findInstallment?.installment || 0;
        }
      }
    },

    setVehicle: (s, action: { payload: { variant: VariantProduct; cityGroup: string } }) => {
      s.vehicle = {
        cityGroup: action.payload.cityGroup,
        variant: action.payload.variant,
      };
    },

    setAvailableTenor: (
      state,
      action: {
        payload: {
          value: number[];
        };
      },
    ) => {
      const tenors: TenorRow[] = [];
      for (const s of action.payload.value) {
        const oldTenor = state.selectedTenor.find((t) => t.tenor === s);
        tenors.push({
          tenor: s,
          discount: oldTenor?.discount || 0,
        });
      }
      state.selectedTenor = tenors;

      for (const normalPriceListElement of state.normalPriceList) {
        const newInstallment: Installment[] = [];
        for (const tenor of tenors) {
          const oldInstallment = normalPriceListElement.installments.find(
            (oldInstallment) => oldInstallment.tenor === tenor.tenor,
          );

          newInstallment.push({
            tenor: tenor.tenor,
            installment: oldInstallment?.installment || 0,
            discountInstallment: oldInstallment?.discountInstallment || 0,
          });
        }
        normalPriceListElement.installments = newInstallment;
      }
    },

    setValueDiscountTenor: (s, action: { payload: { tenor: number; value: number | null } }) => {
      const { tenor, value } = action.payload;
      const findIndex = s.selectedTenor.findIndex((t) => t.tenor === tenor);
      if (findIndex > -1) {
        s.selectedTenor[findIndex].discount = value;
      }
    },

    setValueDiscountInstallment: (
      s,
      action: {
        payload: { uidRow: string; value: number; tenor: number };
      },
    ) => {
      const { uidRow, value } = action.payload;
      const index = s.normalPriceList.findIndex((v) => v.key === uidRow);
      const indexOfInstallment = s.normalPriceList[index].installments.findIndex(
        (i) => i.tenor === action.payload.tenor,
      );
      s.normalPriceList[index].installments[indexOfInstallment].discountInstallment = value;
    },

    setExpiredAt: (s, action: { payload: Date }) => {
      s.expiredAt = action.payload;
    },
  },
  initialState: {
    openPriceListModalTabular: false,
    open: false,
    vehicle: null,
    normalPriceList: [],
    highlightPriceList: [],
    priceList: null,
    priceListState: 'none',
    title: '',
    subTitle: '',
    selectedTenor: [],
    expiredAt: defaultExpiredAt,
  } as State,
});

export default modalPriceListSlice;

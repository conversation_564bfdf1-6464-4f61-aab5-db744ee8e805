import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { TMainReduxStates } from '../types/redux-types';

// Tipe state untuk modal profile
export interface IModalProfileState {
  isOpen: boolean;
  loading: boolean;
  error: string | null;
}

// Initial state dengan tipe yang jelas
const initialState: IModalProfileState = {
  isOpen: false,
  loading: false,
  error: null,
};

/**
 * Slice untuk mengelola state modal profile
 * - Mengontrol visibility modal
 * - Menangani loading state saat operasi async
 * - Menyimpan error messages jika ada
 */
const modalProfileSlice = createSlice({
  name: 'modalProfile',
  initialState,
  reducers: {
    // Membuka modal dan reset state sebelumnya
    setIsOpen: (state, action: PayloadAction<boolean>) => {
      state.isOpen = action.payload;
      // Reset error saat membuka modal
      if (action.payload) {
        state.error = null;
      }
    },
    // Untuk operasi async bisa ditambahkan di extraReducers
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

// Selector untuk mengakses state
export const selectModalProfile = (state: TMainReduxStates) => state.modalProfile;

// Export slice sebagai default
export default modalProfileSlice;

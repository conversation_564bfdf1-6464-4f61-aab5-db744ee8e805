import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Area, Model, VariantProduct } from '../../services/types/catalaogueTypes';

export interface ModalSelectCatalogueState {
  visible: boolean;
  fetchingArea: boolean;
  fetchingModel: boolean;
  organization: string | null;
  areas: Area[];
  models: Model[];
  variants: VariantProduct[];
  selectedCityGroup: string | null;
  selectedModel: { name: string } | null;
  selectedImagePreview: string | null;
  variantFilter: { name: string; code: string } | null;
}

const initialState: ModalSelectCatalogueState = {
  visible: false,
  fetchingArea: false,
  fetchingModel: false,
  organization: null,
  areas: [],
  models: [],
  variants: [],
  selectedCityGroup: null,
  selectedModel: null,
  selectedImagePreview: null,
  variantFilter: null,
};

const modalSelectCatalogueSlice = createSlice({
  name: 'modalSelectCatalogue',
  initialState: { ...initialState },
  reducers: {
    setOpen: (
      state,
      action: PayloadAction<
        | {
            preFilled?: {
              selectedOrganization: string | null;
              selectedCityGroup: string | null;
              selectedModel: { name: string } | null;
              variantFilter: { name: string; code: string } | null;
            };
          }
        | undefined
      >,
    ) => {
      state.visible = true;
      if (action.payload?.preFilled) {
        state.organization = action.payload.preFilled.selectedOrganization;
        state.selectedCityGroup = action.payload.preFilled.selectedCityGroup;
        state.selectedModel = action.payload.preFilled.selectedModel;
        state.variantFilter = action.payload.preFilled.variantFilter;
      }
    },
    setClose: (state) => {
      return { ...initialState };
    },
    setFetchingArea: (state, action: PayloadAction<boolean>) => {
      state.fetchingArea = action.payload;
    },
    setFetchingModel: (state, action: PayloadAction<boolean>) => {
      state.fetchingModel = action.payload;
    },
    setAreas: (state, action: PayloadAction<Area[]>) => {
      state.areas = action.payload;
    },
    setModels: (state, action: PayloadAction<Model[]>) => {
      state.models = action.payload;
    },
    setVariants: (state, action: PayloadAction<VariantProduct[]>) => {
      state.variants = action.payload;
    },
    setOrganization: (state, action: PayloadAction<string | null>) => {
      state.organization = action.payload;
      state.selectedCityGroup = null;
      state.selectedModel = null;
      state.variantFilter = null;
      state.models = [];
      state.variants = [];
    },
    setSelectedCityGroup: (state, action: PayloadAction<string | null>) => {
      state.selectedCityGroup = action.payload;
      // Reset related fields when city group changes
      state.selectedModel = null;
      state.variantFilter = null;
      state.models = [];
      state.variants = [];
    },
    setSelectedModel: (state, action: PayloadAction<{ name: string } | null>) => {
      state.selectedModel = action.payload;
      state.variants = [];
      state.variantFilter = null;
    },
    setSelectedImagePreview: (state, action: PayloadAction<string | null>) => {
      state.selectedImagePreview = action.payload;
    },
    setVariantFilter: (state, action: PayloadAction<{ name: string; code: string } | null>) => {
      state.variantFilter = action.payload;
    },
    resetState: (state) => {
      return initialState;
    },
  },
});

export default modalSelectCatalogueSlice;

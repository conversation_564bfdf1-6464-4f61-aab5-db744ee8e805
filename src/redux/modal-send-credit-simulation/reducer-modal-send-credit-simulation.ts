import {
  EActionModalCreditSimulation,
  IModalSendCreditSimulationStates,
  TActionModalCreditSimulation,
} from '../types/modal-send-credit-simulation';

const initState: IModalSendCreditSimulationStates = {
  visible: false,
};

export default function modalSendCreditSimulationReducer(
  states: IModalSendCreditSimulationStates = initState,
  action: TActionModalCreditSimulation,
) {
  let currentStates = { ...states };

  switch (action.type) {
    case EActionModalCreditSimulation.SET_VISIBILITY_MODAL_SEND_CREDIT_SIMULATION:
      currentStates.visible = action.data;
      break;
  }

  return currentStates;
}

import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Area, Model } from '../../services/types/catalaogueTypes';
import { IPromoCode } from '../../services/types/promoServiceTypes';

interface ModalSendPromoState {
  open: boolean;

  fetchingArea: boolean;
  fetchingModel: boolean;
  fetchingPromoCodes: boolean;

  areas: Area[];
  models: Model[];

  selectedCityGroup: string | null;
  selectedModel: Model | null;
  selectedImagePreview: string | null;
  purchaseMethod: 'cash' | 'credit' | null;

  promoCodes: IPromoCode[];
}

const initialState: ModalSendPromoState = {
  open: false,
  fetchingArea: false,
  fetchingModel: false,
  fetchingPromoCodes: false,

  areas: [],
  models: [],

  selectedCityGroup: null,
  selectedModel: null,
  selectedImagePreview: null,
  purchaseMethod: null,

  promoCodes: [],
};

const modalSendPromoSlice = createSlice({
  name: 'modalSendPromo',
  initialState: {
    ...initialState,
  },
  reducers: {
    open: (state) => {
      state.open = true;
    },
    setFetchingArea: (state, action: PayloadAction<boolean>) => {
      state.fetchingArea = action.payload;
    },
    setFetchingModel: (state, action: PayloadAction<boolean>) => {
      state.fetchingModel = action.payload;
    },
    setFetchingPromoCodes: (state, action: PayloadAction<boolean>) => {
      state.fetchingPromoCodes = action.payload;
    },
    setAreas: (state, action: PayloadAction<Area[]>) => {
      state.areas = action.payload;
    },
    setModels: (state, action: PayloadAction<Model[]>) => {
      state.models = action.payload;
    },
    setSelectedCityGroup: (state, action: PayloadAction<string | null>) => {
      state.selectedCityGroup = action.payload;
    },
    setSelectedModel: (state, action: PayloadAction<Model | null>) => {
      state.selectedModel = action.payload;
    },
    setPurchaseMethod: (state, action: PayloadAction<'cash' | 'credit' | null>) => {
      state.purchaseMethod = action.payload;
    },
    setPromoCodes: (state, action: PayloadAction<IPromoCode[]>) => {
      state.promoCodes = action.payload;
    },
    resetState: (state) => {
      return {
        ...initialState,
      };
    },
  },
});

export default modalSendPromoSlice;

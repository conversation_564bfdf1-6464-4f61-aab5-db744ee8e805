import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Model } from '../../services/types/catalaogueTypes';
import { CommonRegionProps } from '../../Components/SelectRegion/SelectRegion';

interface State {
  open: boolean;
  errorMessages: string[];
  loading: boolean;

  organization: string | null;
  cityGroup: string | null;
  model: Model | null;
  province: CommonRegionProps | null;
  city: CommonRegionProps | null;
}

const initState: State = {
  open: false,
  errorMessages: [],
  loading: false,

  organization: null,
  cityGroup: null,
  model: null,
  province: null,
  city: null,
};

const modalSendToFreeLeadsSlice = createSlice({
  name: 'modalSendFreeLeads',
  initialState: {
    ...initState,
  } as State,
  reducers: {
    open: (state) => {
      state.open = true;
    },
    close: () => {
      return {
        ...initState,
      };
    },
    setOrganization: (state, action: { payload: string | null }) => {
      state.organization = action.payload;
      state.cityGroup = null;
      state.model = null;
    },
    setCityGroup: (state, action: { payload: string | null }) => {
      state.cityGroup = action.payload;
      state.model = null;
    },
    setModel: (state, action: { payload: Model | null }) => {
      state.model = action.payload;
    },
    pushErrorMessage: (state, action: { payload: string }) => {
      state.errorMessages.push(action.payload);
    },
    setErrorMessages: (state, action: { payload: string[] }) => {
      state.errorMessages = action.payload;
    },
    setLoading: (state, action: { payload: boolean }) => {
      state.loading = action.payload;
    },
    clearErrorMessage: (state) => {
      state.errorMessages = [];
    },
    setProvince: (state, action: PayloadAction<CommonRegionProps | null>) => {
      state.province = action.payload;
      state.city = null;
    },
    setCity: (state, action: PayloadAction<CommonRegionProps | null>) => {
      state.city = action.payload;
    },
  },
});

export default modalSendToFreeLeadsSlice;

import { createSlice } from '@reduxjs/toolkit';

interface State {
  open: boolean;
}

const modalSettingsTemplate = createSlice({
  name: 'modalSettingsTemplate',
  extraReducers: undefined,
  initialState: {
    open: false,
  } as State,
  reducers: {
    open: (state) => {
      state.open = true;
    },
    close: (state) => {
      state.open = false;
    },
  },
});
export default modalSettingsTemplate;

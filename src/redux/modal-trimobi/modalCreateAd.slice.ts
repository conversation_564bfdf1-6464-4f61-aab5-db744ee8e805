import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { CommonRegionProps } from '../../Components/SelectRegion/SelectRegion';
import { AdsOption, Package } from '../../services/trimobi/trimobi.getPlan.response.types';
import randomNumber from '../../helpers/randomNumber';
import { AdsPackage } from '../../services/types/mainApiService/mainApiService.amartavip.types';

interface State {
  isOpen: boolean;
  step: number;
  errorMessages: string | string[] | null;
  loading: boolean;

  vehicleType: string;
  plan: AdsPackage | null;
  quantityAds: AdsOption | null;
  purchasedPlans: AdsPackage[];

  fullName: string;
  phoneNumber: string;
  province: CommonRegionProps | null;
  city: CommonRegionProps | null;
  district: CommonRegionProps | null;
  subDistrict: CommonRegionProps | null;
  address: string;
  postalCode: string;

  brand: {
    uuid: string;
    name: string;
  } | null;
  model: {
    uuid: string;
    name: string;
    category: string;
  } | null;
  variant: string;
  licensePlate: string;
  fuelType: string;
  transmission: string;
  engineCapacity: string;
  year: string;
  mileage: string;

  completeVehicleDocuments: boolean; // bpkb & stnk kendaraan lengkap
  activeVehicleDocuments: boolean; // bpkb dan STNK masih aktif
  ownerNameMatchDocuments: boolean; // Name Pemilik sesuai stnk
  allowBuyerPriceOffer: boolean; // Izinkan calon pembeli memberikan penawaran harga

  title: string;
  description: string;
  price: number;
  images: {
    uuid: string;
    url: string | null;
  }[];
}

const initialState: State = {
  isOpen: false,
  step: 1,
  errorMessages: null,
  loading: false,

  vehicleType: 'motorcycle',
  plan: null,
  quantityAds: null,
  purchasedPlans: [],

  fullName: '',
  phoneNumber: '',
  province: null,
  city: null,
  district: null,
  subDistrict: null,
  address: '',
  postalCode: '',

  brand: null,
  model: null,
  variant: '',
  licensePlate: '',
  fuelType: '',
  transmission: '',
  engineCapacity: '',
  year: '',
  mileage: '',

  completeVehicleDocuments: false,
  activeVehicleDocuments: false,
  ownerNameMatchDocuments: false,
  allowBuyerPriceOffer: false,

  title: '',
  description: '',
  price: 0,
  images: [
    {
      uuid: randomNumber(10).toString(),
      url: null,
    },
  ],
};

export const modalCreateAdSlice = createSlice({
  name: 'modalCreateAd',
  initialState,
  reducers: {
    setIsOpen: (state, action: PayloadAction<boolean>) => {
      if (!action.payload) {
        return { ...initialState };
      } else {
        state.isOpen = action.payload;
      }
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setStep: (state, action: PayloadAction<number>) => {
      state.step = action.payload;
    },
    setErrorMessages: (state, action: PayloadAction<string | string[] | null>) => {
      state.errorMessages = action.payload;
    },
    setVehicleType: (state, action: PayloadAction<string>) => {
      state.vehicleType = action.payload;
      state.model = null;
      state.variant = '';
    },
    setPlan: (state, action: PayloadAction<AdsPackage | null>) => {
      state.plan = action.payload;
      state.quantityAds = null;
    },
    setQuantityAds: (state, action: PayloadAction<AdsOption | null>) => {
      state.quantityAds = action.payload;
    },
    setPurchasedPlans: (state, action: PayloadAction<AdsPackage[]>) => {
      state.purchasedPlans = action.payload;
    },
    setFullName: (state, action: PayloadAction<string>) => {
      state.fullName = action.payload;
    },
    setPhoneNumber: (state, action: PayloadAction<string>) => {
      state.phoneNumber = action.payload;
    },
    setProvince: (state, action: PayloadAction<CommonRegionProps | null>) => {
      state.province = action.payload;
      state.city = null;
      state.district = null;
      state.subDistrict = null;
    },
    setCity: (state, action: PayloadAction<CommonRegionProps | null>) => {
      state.city = action.payload;
      state.district = null;
      state.subDistrict = null;
    },
    setDistrict: (state, action: PayloadAction<CommonRegionProps | null>) => {
      state.district = action.payload;
      state.subDistrict = null;
    },
    setSubDistrict: (state, action: PayloadAction<CommonRegionProps | null>) => {
      state.subDistrict = action.payload;
    },
    setPostalCode: (state, action: PayloadAction<string>) => {
      state.postalCode = action.payload;
    },
    setAddress: (state, action: PayloadAction<string>) => {
      state.address = action.payload;
    },
    setBrand: (state, action: PayloadAction<{ uuid: string; name: string } | null>) => {
      state.brand = action.payload;
      state.model = null;
    },
    setModel: (
      state,
      action: PayloadAction<{
        uuid: string;
        name: string;
        category: string;
      } | null>,
    ) => {
      state.model = action.payload;
      state.vehicleType = action.payload?.category || '';
    },
    setVariant: (state, action: PayloadAction<string>) => {
      state.variant = action.payload;
    },
    setLicensePlate: (state, action: PayloadAction<string>) => {
      state.licensePlate = action.payload;
    },
    setFuelType: (state, action: PayloadAction<string>) => {
      state.fuelType = action.payload;
    },
    setTransmission: (state, action: PayloadAction<string>) => {
      state.transmission = action.payload;
    },
    setEngineCapacity: (state, action: PayloadAction<string>) => {
      state.engineCapacity = action.payload;
    },
    setYear: (state, action: PayloadAction<string>) => {
      state.year = action.payload;
    },
    setMileage: (state, action: PayloadAction<string>) => {
      state.mileage = action.payload;
    },
    setCompleteVehicleDocuments: (state, action: PayloadAction<boolean>) => {
      state.completeVehicleDocuments = action.payload;
    },
    setActiveVehicleDocuments: (state, action: PayloadAction<boolean>) => {
      state.activeVehicleDocuments = action.payload;
    },
    setOwnerNameMatchDocuments: (state, action: PayloadAction<boolean>) => {
      state.ownerNameMatchDocuments = action.payload;
    },
    setAllowBuyerPriceOffer: (state, action: PayloadAction<boolean>) => {
      state.allowBuyerPriceOffer = action.payload;
    },
    setTitle: (state, action: PayloadAction<string>) => {
      state.title = action.payload;
    },
    setDescription: (state, action: PayloadAction<string>) => {
      state.description = action.payload;
    },
    setPrice: (state, action: PayloadAction<number>) => {
      state.price = action.payload;
    },
    addImageRow: (state) => {
      state.images.push({
        uuid: randomNumber(10).toString(),
        url: null,
      });
    },
    deleteImageRow: (state, action: PayloadAction<{ uuid: string }>) => {
      state.images = state.images.filter((image) => image.uuid !== action.payload.uuid);
      if (state.images.length === 0) {
        state.images.push({
          uuid: randomNumber(10).toString(),
          url: null,
        });
      }
    },
    updateImageUrl: (state, action: PayloadAction<{ uuid: string; url: string }>) => {
      const image = state.images.find((image) => image.uuid === action.payload.uuid);
      if (image) {
        image.url = action.payload.url;
      }
    },
  },
});

export default modalCreateAdSlice;

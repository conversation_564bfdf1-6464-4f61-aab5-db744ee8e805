import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import {
  PlanCheckoutStatusItem,
  AdsPackage,
} from '../../services/types/mainApiService/mainApiService.amartavip.types';

interface IModalPaymentStatus {
  isOpen: boolean;
  loading: boolean;
  errorMessage: string | null;
  filter: 'unpaid' | 'paid';
  paymentStatusList: PlanCheckoutStatusItem[];
  myPlanList: AdsPackage[];
}

const initialState: IModalPaymentStatus = {
  isOpen: false,
  loading: false,
  errorMessage: null,
  filter: 'unpaid',
  paymentStatusList: [],
  myPlanList: [],
};

const modalPaymentStatusSlice = createSlice({
  name: 'modalPaymentStatus',
  initialState,
  reducers: {
    setIsOpen: (state, action: PayloadAction<boolean>) => {
      state.isOpen = action.payload;
      if (!action.payload) {
        state.errorMessage = null;
        state.paymentStatusList = [];
        state.myPlanList = [];
      }
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setErrorMessage: (state, action: PayloadAction<string | null>) => {
      state.errorMessage = action.payload;
    },
    setFilter: (state, action: PayloadAction<'unpaid' | 'paid'>) => {
      state.filter = action.payload;
    },
    setPaymentStatusList: (state, action: PayloadAction<PlanCheckoutStatusItem[]>) => {
      state.paymentStatusList = action.payload;
    },
    setMyPlanList: (state, action: PayloadAction<AdsPackage[]>) => {
      state.myPlanList = action.payload;
    },
    closeModal: (state) => {
      state.isOpen = false;
      state.errorMessage = null;
      state.paymentStatusList = [];
      state.myPlanList = [];
    },
  },
});

export default modalPaymentStatusSlice;

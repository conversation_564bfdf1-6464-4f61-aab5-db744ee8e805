import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { AdsOption, Package } from '../../services/trimobi/trimobi.getPlan.response.types';

interface ModalSelectPlanState {
  isOpen: boolean;
  vehicleType: 'motorcycle' | 'car' | null;
  selectedPlan: Package | null;
  quantityAds: AdsOption | null;
  loading: boolean;
  error: string | null;
}

const initialState: ModalSelectPlanState = {
  isOpen: false,
  vehicleType: null,
  selectedPlan: null,
  quantityAds: null,
  loading: false,
  error: null,
};

const modalSelectPlanSlice = createSlice({
  name: 'modalSelectPlan',
  initialState: { ...initialState },
  reducers: {
    // Membuka modal
    openModal: (state) => {
      state.isOpen = true;
    },

    // Menutup modal
    closeModal: (state) => {
      return { ...initialState };
    },

    // Set loading
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },

    // Memilih jenis kendaraan
    selectVehicleType: (state, action: PayloadAction<'motorcycle' | 'car'>) => {
      state.vehicleType = action.payload;
      state.selectedPlan = null;
      state.quantityAds = null;
    },

    // Memilih paket iklan
    selectPlan: (state, action: PayloadAction<Package | null>) => {
      state.selectedPlan = action.payload;
      state.quantityAds = null;
    },

    // Memilih jumlah iklan
    selectQuantityAds: (state, action: PayloadAction<AdsOption | null>) => {
      state.quantityAds = action.payload;
    },

    // Reset pilihan
    resetSelection: (state) => {
      state.selectedPlan = null;
      state.quantityAds = null;
    },
  },
  extraReducers: (builder) => {
    // ... bisa ditambahkan async thunk actions jika diperlukan ...
  },
});

// Export reducer
export default modalSelectPlanSlice;

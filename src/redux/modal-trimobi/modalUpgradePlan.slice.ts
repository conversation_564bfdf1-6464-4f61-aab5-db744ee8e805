import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { AdsPackage } from '../../services/types/mainApiService/mainApiService.amartavip.types';
import { AdsOption } from '../../services/trimobi/trimobi.getPlan.response.types';

interface ModalUpgradePlanState {
  isOpen: boolean;
  myPurchasedPlan: AdsPackage[];
  selectedPlan: AdsPackage | null;
  loading: boolean;
  step: number;
  quantityAds: AdsOption | null;
  errorMessage: string;
}

const initialState: ModalUpgradePlanState = {
  isOpen: false,
  myPurchasedPlan: [],
  selectedPlan: null,
  loading: false,
  step: 1,
  quantityAds: null,
  errorMessage: '',
};

const modalUpgradePlanTrimobiSlice = createSlice({
  name: 'modalUpgradePlanTrimobi',
  initialState: {
    ...initialState,
  },
  reducers: {
    openModal: (state) => {
      state.isOpen = true;
    },
    closeModal: () => {
      return { ...initialState };
    },
    setMyPurchasedPlan: (state, action: PayloadAction<AdsPackage[]>) => {
      state.myPurchasedPlan = action.payload;
    },
    setSelectedPlan: (state, action: PayloadAction<AdsPackage>) => {
      state.selectedPlan = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setStep: (state, action: PayloadAction<number>) => {
      state.step = action.payload;
    },
    nextStep: (state) => {
      state.step += 1;
    },
    prevStep: (state) => {
      state.step -= 1;
    },
    setQuantityAds: (state, action: PayloadAction<AdsOption>) => {
      state.quantityAds = action.payload;
    },
    setErrorMessage: (state, action: PayloadAction<string>) => {
      state.errorMessage = action.payload;
    },
    clearErrorMessage: (state) => {
      state.errorMessage = '';
    },
  },
});

export default modalUpgradePlanTrimobiSlice;

import { createSlice } from '@reduxjs/toolkit';
import { IPromoCode } from '../../services/types/promoServiceTypes';

interface States {
  open: boolean;
  updating: boolean;
  errorMessage: string;
  success: boolean;
  promoCode: IPromoCode | null;
}

const modalUpdateDataPromoCodeToOtodisSlice = createSlice({
  name: 'modalUpdateDataPromoCodeToOtodisSlice',
  reducers: {
    open: (state) => {
      state.open = true;
    },
    close: (state) => {
      state.open = false;
      state.promoCode = null;
    },
    setPromoCode: (state, action: { payload: IPromoCode | null }) => {
      state.promoCode = action.payload;
    },

    setProcessUpdating: (state) => {
      state.updating = true;
      state.errorMessage = '';
      state.success = false;
    },
    setProcessFinishSuccess: (state) => {
      state.updating = false;
      state.errorMessage = '';
      state.success = true;
    },
    setProcessUpdatingError: (state, action: { payload: string }) => {
      state.updating = false;
      state.errorMessage = action.payload;
    },
  },
  initialState: {
    open: false,
    promoCode: null,
    success: false,
  } as States,
});

export default modalUpdateDataPromoCodeToOtodisSlice;

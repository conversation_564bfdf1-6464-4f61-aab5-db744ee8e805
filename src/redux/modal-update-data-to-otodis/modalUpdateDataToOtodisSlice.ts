import { createSlice } from '@reduxjs/toolkit';
import { CommonRegionProps } from '../../Components/SelectRegion/SelectRegion';

interface States {
  open: boolean;
  updating: boolean;
  errorMessage: string;
  success: boolean;

  shippingAddress: {
    fullAddress: string;
    zipCode: string;
    province: CommonRegionProps | null;
    city: CommonRegionProps | null;
    district: CommonRegionProps | null;
    subDistrict: CommonRegionProps | null;
    neighbourhood: string;
    hamlet: string;
    latitude: string | null;
    longitude: string | null;
  };
  customerCodAmount: number | null;
}

const modalUpdateDataToOtodisSlice = createSlice({
  name: 'modalUpdateDataToOtodisSlice',
  reducers: {
    open: (state) => {
      state.open = true;
    },
    close: (state) => {
      state.open = false;
    },

    setAddress: (state, action: { payload: string }) => {
      state.shippingAddress.fullAddress = action.payload;
    },
    setProvince: (state, action: { payload: CommonRegionProps | null }) => {
      state.shippingAddress.province = action.payload;
    },
    setCity: (state, action: { payload: CommonRegionProps | null }) => {
      state.shippingAddress.city = action.payload;
    },
    setDistrict: (state, action: { payload: CommonRegionProps | null }) => {
      state.shippingAddress.district = action.payload;
    },
    setSubDistrict: (state, action: { payload: CommonRegionProps | null }) => {
      state.shippingAddress.subDistrict = action.payload;
    },
    setZipCode: (state, action: { payload: string }) => {
      state.shippingAddress.zipCode = action.payload;
    },
    setHamlet: (state, action: { payload: string }) => {
      state.shippingAddress.hamlet = action.payload;
    },
    setNeighbourhood: (state, action: { payload: string }) => {
      state.shippingAddress.neighbourhood = action.payload;
    },
    setLongitude: (state, action: { payload: string | null }) => {
      state.shippingAddress.longitude = action.payload;
    },
    setLatitude: (state, action: { payload: string | null }) => {
      state.shippingAddress.latitude = action.payload;
    },
    setCustomerCodAmount: (state, action: { payload: number | null }) => {
      state.customerCodAmount = action.payload;
    },

    setProcessUpdating: (state) => {
      state.updating = true;
      state.errorMessage = '';
      state.success = false;
    },
    setProcessFinishSuccess: (state) => {
      state.updating = false;
      state.errorMessage = '';
      state.success = true;
    },
    setProcessUpdatingError: (state, action: { payload: string }) => {
      state.updating = false;
      state.errorMessage = action.payload;
    },
  },
  initialState: {
    open: false,
    shippingAddress: {
      fullAddress: '',
      zipCode: '',
      province: null,
      city: null,
      district: null,
      subDistrict: null,
      neighbourhood: '',
      hamlet: '',
      latitude: null,
      longitude: null,
    },
    customerCodAmount: null,
    success: false,
  } as States,
});

export default modalUpdateDataToOtodisSlice;

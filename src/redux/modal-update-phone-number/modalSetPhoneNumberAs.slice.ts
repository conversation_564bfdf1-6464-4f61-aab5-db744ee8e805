import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import firestore from 'firebase/firestore';

interface States {
  open: boolean;
  phoneNumber: string;
  setPhoneNumberAs: 'guarantor' | 'owner' | 'orderMaker' | null;
  clientRef: firestore.DocumentReference | null;
  submitResults: 'success' | 'error' | null;
  errorMessages: string;
  submitting: boolean;
}

const initialState: States = {
  open: false,
  phoneNumber: '',
  setPhoneNumberAs: null,
  clientRef: null,
  submitResults: null,
  errorMessages: '',
  submitting: false,
};

const modalSetPhoneNumberAsSlice = createSlice({
  name: 'modalSetPhoneNumber',
  initialState: {
    ...initialState,
  },
  reducers: {
    open: (s, a: PayloadAction<{ phoneNumber: string }>) => {
      s.open = true;
      s.phoneNumber = a.payload.phoneNumber;
    },
    changeType: (s, a: PayloadAction<States['setPhoneNumberAs']>) => {
      s.setPhoneNumberAs = a.payload;
    },
    close: () => {
      return {
        ...initialState,
      };
    },

    setSubmitState: (s, a: PayloadAction<boolean>) => {
      s.submitting = a.payload;
      if (a.payload) {
        s.submitResults = null;
        s.errorMessages = '';
      }
    },

    setSubmitSuccess: (s) => {
      s.submitResults = 'success';
      s.errorMessages = '';
      s.submitting = false;
    },

    setSubmitError: (s, a: PayloadAction<{ errorMessage: string }>) => {
      s.submitResults = 'error';
      s.errorMessages = a.payload.errorMessage;
      s.submitting = false;
    },
  },
});

export default modalSetPhoneNumberAsSlice;

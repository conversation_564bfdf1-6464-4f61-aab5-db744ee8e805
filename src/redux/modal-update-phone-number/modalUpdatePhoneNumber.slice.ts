import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface InitState {
  open: boolean;
  phoneNumberOwner: string;
  phoneNumberGuarantor: string;
  phoneNumberOrderMaker: string;

  updating: boolean;

  submitResults: 'success' | 'error' | null;
  errorMessages: string;
}

const initState: InitState = {
  open: false,

  phoneNumberOwner: '',
  phoneNumberGuarantor: '',
  phoneNumberOrderMaker: '',

  updating: false,

  submitResults: null,
  errorMessages: '',
};

const modalUpdatePhoneNumberSlice = createSlice({
  name: 'modalUpdatePhoneNumberSlice',
  reducers: {
    open: (
      state,
      action: PayloadAction<{
        phoneNumberOwner: string;
        phoneNumberGuarantor: string;
        phoneNumberOrderMaker: string;
      }>,
    ) => {
      state.open = true;
      state.phoneNumberOwner = action.payload.phoneNumberOwner;
      state.phoneNumberGuarantor = action.payload.phoneNumberGuarantor;
      state.phoneNumberOrderMaker = action.payload.phoneNumberOrderMaker;
    },
    close: () => {
      return {
        ...initState,
      };
    },
    setUpdating: (state, action: PayloadAction<boolean>) => {
      state.updating = action.payload;
    },
    setPhoneNumberOwner: (state, action: PayloadAction<string>) => {
      state.phoneNumberOwner = action.payload;
    },
    setPhoneNumberGuarantor: (state, action: PayloadAction<string>) => {
      state.phoneNumberGuarantor = action.payload;
    },
    setPhoneNumberOrderMaker: (state, action: PayloadAction<string>) => {
      state.phoneNumberOrderMaker = action.payload;
    },
    setSubmitResult: (
      s,
      a: PayloadAction<{
        submitResults: InitState['submitResults'];
        errorMessages?: InitState['errorMessages'];
      }>,
    ) => {
      s.submitResults = a.payload.submitResults;
      if (a.payload.errorMessages) {
        s.errorMessages = a.payload.errorMessages;
      }
    },
  },
  initialState: {
    ...initState,
  },
});

export default modalUpdatePhoneNumberSlice;

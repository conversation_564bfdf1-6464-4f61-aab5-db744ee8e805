import { createSlice } from '@reduxjs/toolkit';

interface States {
  open: boolean;
}

const initState = {
  open: false,
};

const modalBlockPhoneNumberSlice = createSlice({
  name: 'modalBlockPhoneNumber',
  initialState: {
    open: false,
  } as States,
  reducers: {
    open: (s) => {
      s.open = true;
    },
    close: () => {
      return {
        ...initState,
      };
    },
  },
});

export default modalBlockPhoneNumberSlice;

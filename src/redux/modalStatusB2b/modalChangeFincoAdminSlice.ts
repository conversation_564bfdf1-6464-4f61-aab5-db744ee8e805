import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import AdminLeasingB2B from '../../entities/AdminLeasingB2B';
import { collection, getDocs, query, where } from 'firebase/firestore';
import { firestoreB2BVer9 } from '../../services/myFirebase';

interface ILeasingCompany {
  name: string;
  code: string;
  disabled: boolean;
}

interface States {
  open: boolean;
  offerCodeToUpdate: string;

  leasingList: ILeasingCompany[];
  leasingAdminList: AdminLeasingB2B[];

  selectedLeasing: string | null;
  selectedLeasingAdmin: string | null;

  updating: boolean;
  errorMessage: string;
}

const initState: States = {
  open: false,
  offerCodeToUpdate: '',

  leasingList: [],
  leasingAdminList: [],
  selectedLeasing: null,
  selectedLeasingAdmin: null,

  updating: false,
  errorMessage: '',
};

export const fetchLeasingChangeAdminLeasing = createAsyncThunk(
  'fetchLeasingChangeAdminLeasing',
  async (arg, thunkAPI) => {
    const getLeasing = await getDocs(collection(firestoreB2BVer9, 'leasing_companies'));
    const listLeasing: ILeasingCompany[] = [];
    getLeasing.forEach((result) => {
      const data = result.data();
      listLeasing.push({
        code: result.ref.id,
        name: data.name,
        disabled: !data.order_source_permission?.e_survey || false,
      });
    });

    return listLeasing;
  },
);

export const fetchAdminLeasingChangeAdminLeasing = createAsyncThunk(
  'fetchAdminLeasingChangeAdminLeasing',
  async (actualLeasingCode: string, thunkAPI) => {
    const collections = collection(firestoreB2BVer9, 'admins');
    const q = query(
      collections.withConverter(AdminLeasingB2B.converter),
      where('leasing_company.code', '==', actualLeasingCode),
      where('active', '==', true),
    );
    const get = await getDocs(q);

    const adminLeasing: AdminLeasingB2B[] = [];
    get.forEach((result) => {
      const data = result.data();
      adminLeasing.push(data);
    });

    return adminLeasing;
  },
);

const modalChangeFincoAdminSlice = createSlice({
  name: 'modalChangeFincoAdminSlice',
  reducers: {
    open: (s, a: { payload: string }) => {
      s.open = true;
      s.offerCodeToUpdate = a.payload;
    },
    close: (s) => {
      return {
        ...initState,
      };
    },
    onLeasingChange: (s, a: { payload: string }) => {
      const find = s.leasingList.find((l) => l.code === a.payload);
      if (find) {
        s.selectedLeasing = a.payload;
        s.selectedLeasingAdmin = null;
      }
    },
    onAdminLeasingChange: (s, a: { payload: string }) => {
      const find = s.leasingAdminList.find((admin) => admin.ref.id === a.payload);
      if (find) {
        s.selectedLeasingAdmin = a.payload;
      }
    },
    setLoadingStatus: (s, a: { payload: boolean }) => {
      s.updating = a.payload;
    },
    setErrorMessage: (s, a: { payload: string }) => {
      s.errorMessage = a.payload;
    },
  },
  extraReducers: (builder) => {
    builder.addCase(fetchLeasingChangeAdminLeasing.fulfilled, (state, action) => {
      state.leasingList = action.payload;
    });
    builder.addCase(fetchAdminLeasingChangeAdminLeasing.fulfilled, (state, action) => {
      state.leasingAdminList = action.payload;
    });
  },
  initialState: {
    ...initState,
  } as States,
});

export default modalChangeFincoAdminSlice;

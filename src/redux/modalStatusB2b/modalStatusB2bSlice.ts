import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { ISuccessResponseGetSurveyByOfferCode } from '../../services/b2b/b2bServices.types';
import b2bServices from '../../services/b2b/b2bServices';
import { IClientEntityOrderHistory } from '../../entities/types/client-entity-types';

interface State {
  open: boolean;
  fetching: boolean;
  dataStatusB2b: ISuccessResponseGetSurveyByOfferCode | null;
  orderHistory: IClientEntityOrderHistory | null;
  errorMessage: string | null;
}

export const fetchStatusB2b = createAsyncThunk('statusB2b/fetch', async (offerCode: string) => {
  const get = await b2bServices.getStatusSurveyByOfferCode(offerCode);
  return get.data;
});

const modalStatusB2bSlice = createSlice({
  name: 'modalStatusB2b',
  reducers: {
    open: (s, a: { payload: IClientEntityOrderHistory }) => {
      s.open = true;
      s.orderHistory = a.payload;
    },
    close: (s) => {
      s.open = false;
      s.dataStatusB2b = null;
      s.errorMessage = null;
      s.fetching = false;
    },
  },
  initialState: {
    open: false,
    fetching: false,
    dataStatusB2b: null,
    errorMessage: null,
    orderHistory: null,
  } as State,
  extraReducers: (builder) => {
    builder.addCase(fetchStatusB2b.pending, (state) => {
      state.fetching = true;
    });
    builder.addCase(fetchStatusB2b.fulfilled, (state, action) => {
      state.fetching = false;
      state.dataStatusB2b = action.payload;
    });
    builder.addCase(fetchStatusB2b.rejected, (state) => {
      state.fetching = false;
      state.errorMessage = 'Data survey di b2b tidak dapat ditemukan.';
    });
  },
});

export default modalStatusB2bSlice;

import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ILicensePlateRegistrationCode } from '../../Components/LicensePlate/LicensePlateSelectCityCode';

interface States {
  brandUuid: string;
  brandName: string;
  modelUuid: string;
  modelName: string;
  modelCategory: string;
  variantFreeText: string;
  mileage: string;
  year: string;
  licensePlateRegistrationCode: string;
  licensePlateRegistrationCodeDetails: ILicensePlateRegistrationCode | null;
  licensePlateNumber: string;
  licensePlateSeries: string;

  open: boolean;
  loading: boolean;
  errorMessages: string;
}

const states: States = {
  brandName: '',
  brandUuid: '',
  errorMessages: '',
  loading: false,
  mileage: '',
  modelCategory: '',
  modelName: '',
  modelUuid: '',
  open: false,
  variantFreeText: '',
  year: '',

  licensePlateNumber: '',
  licensePlateSeries: '',
  licensePlateRegistrationCode: '',
  licensePlateRegistrationCodeDetails: null,
};

const modalAddNewOwnedVehicleSlice = createSlice({
  name: 'modalAddNewOwnedVehicleSlice',
  reducers: {
    open: (s) => {
      s.open = true;
    },

    close: () => {
      return {
        ...states,
      };
    },

    setErrorMessages: (s, a: PayloadAction<string>) => {
      s.errorMessages = a.payload;
    },

    setLoading: (s, a: PayloadAction<boolean>) => {
      s.loading = a.payload;
    },

    setBrand: (s, a: PayloadAction<{ name: string; uuid: string }>) => {
      s.brandName = a.payload.name;
      s.brandUuid = a.payload.uuid;
    },

    setModel: (s, a: PayloadAction<{ name: string; uuid: string; category: string }>) => {
      s.modelName = a.payload.name;
      s.modelUuid = a.payload.uuid;
      s.modelCategory = a.payload.category;
    },

    setVariantFreeText: (s, a: PayloadAction<string>) => {
      s.variantFreeText = a.payload;
    },

    setYear: (s, a: PayloadAction<string>) => {
      s.year = a.payload;
    },

    setMileage: (s, a: PayloadAction<string>) => {
      s.mileage = a.payload;
    },

    setLicensePlateRegistrationCode: (s, a: PayloadAction<ILicensePlateRegistrationCode>) => {
      s.licensePlateRegistrationCode = a.payload.code;
      s.licensePlateRegistrationCodeDetails = a.payload;
    },

    setLicensePlateNumber: (s, a: PayloadAction<string>) => {
      s.licensePlateNumber = a.payload;
    },

    setLicensePlateSeries: (s, a: PayloadAction<string>) => {
      s.licensePlateSeries = a.payload;
    },
  },
  initialState: {
    ...states,
  },
});

export default modalAddNewOwnedVehicleSlice;

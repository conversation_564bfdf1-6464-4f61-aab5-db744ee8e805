import { TProjectSetByClassAction, TProjectSetFetchAction } from '../types/redux-project-types';
import 'firebase/firestore';
import { ThunkAction } from 'redux-thunk';
import { TMainReduxStates } from '../types/redux-types';
import { AnyAction } from 'redux';
import ProjectEntity from '../../entities/ProjectEntity';
import { DocumentReference, getDoc } from 'firebase/firestore';

export function setProjectClass(entity: ProjectEntity): TProjectSetByClassAction {
  return {
    type: 'SET_PROJECT_BY_CLASS',
    data: entity,
  };
}

export function setProjectFetching(fetching: boolean): TProjectSetFetchAction {
  return {
    type: 'SET_PROJECT_FETCHING',
    data: fetching,
  };
}

export const setProject =
  (projectRef: DocumentReference): ThunkAction<void, TMainReduxStates, unknown, AnyAction> =>
  async (dispatch) => {
    dispatch(setProjectFetching(true));
    const get = await getDoc(projectRef.withConverter(ProjectEntity.converter));
    if (get.exists()) {
      dispatch(setProjectClass(get.data()!));
    } else {
      dispatch(setProjectFetching(false));
    }
  };

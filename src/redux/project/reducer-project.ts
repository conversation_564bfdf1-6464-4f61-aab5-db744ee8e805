import { IProjectStates, TProjectActions } from '../types/redux-project-types';

const projectReduxInitState: IProjectStates = {
  project: null,
  fetching: false,
};

export default function reducerProject(
  state: IProjectStates = projectReduxInitState,
  data: TProjectActions,
) {
  let currentState = { ...state };

  switch (data.type) {
    case 'SET_PROJECT_FETCHING':
      currentState.fetching = data.data;
      break;
    case 'SET_PROJECT_BY_CLASS':
      currentState.fetching = false;
      currentState.project = data.data;
      break;
    case 'REMOVE_PROJECT':
      currentState.project = null;
      break;
  }

  return currentState;
}

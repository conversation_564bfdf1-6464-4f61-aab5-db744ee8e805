import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import ChatRoomEntity from '../../entities/ChatRoomEntity';
import { onSnapshot, Unsubscribe } from 'firebase/firestore';
import { TMainReduxStates } from '../types/redux-types';
import FirestoreServices from '../../services/firebase/FirestoreServices';
import {
  ChatRoomStates,
  ChatRoomStatesDepartment,
  ChatRoomStatesLabel,
} from '../types/recent-chat-types';
import chatRoomSlice from '../conversations/chatRoomSlice';

let firestoreListenerMap: Unsubscribe;

const initRecentChatStates: ChatRoomStates = {
  fetchedRoomsFromServer: [],
  fetching: false,
  listenerActive: false,
  filters: {
    label: 'ALL_LABELS',
    department: 'ALL_DEPARTMENTS',
    showBlockedNumber: true,
    phoneNumber: null,
    limit: 100,
  },
};

export const startListenChatRoomList = createAsyncThunk(
  'fetchChatRoomThunk',
  async (_, thunkAPI) => {
    const state = thunkAPI.getState() as TMainReduxStates;

    if (state.recentChat.listenerActive) return;

    const statesRecentChat = state.recentChat;
    const statesProject = state.reducerProject;

    const query = FirestoreServices.queryGetChatRoomVer2({
      projectRef: statesProject.project!.ref,
      filters: {
        label: statesRecentChat.filters.label,
        department: statesRecentChat.filters.department,
        phoneNumber: statesRecentChat.filters.phoneNumber || undefined,
        limit: statesRecentChat.filters.limit,
      },
    });

    firestoreListenerMap = onSnapshot(query, (snapshot) => {
      snapshot.docChanges().forEach((change) => {
        const data = change.doc.data();
        if (change.type === 'modified') {
          const mainReducers = thunkAPI.getState() as TMainReduxStates;
          const chatRoomReducer = mainReducers.reducerConversation;
          const chatRoomData = data;

          if (chatRoomData.ref.id === chatRoomReducer.chatRoom?.ref.id) {
            thunkAPI.dispatch(chatRoomSlice.actions.loadChatRoom(chatRoomData));
          }
        }
      });
      let chatRooms: Array<ChatRoomEntity> = [];
      snapshot.forEach((result) => {
        const chatRoom = result.data();
        if (!chatRoom.blocked) {
          chatRooms.push(chatRoom);
        }
      });
      thunkAPI.dispatch(recentChatSlice.actions.loadChatRoomsFromServer(chatRooms));
    });
    thunkAPI.dispatch(recentChatSlice.actions.setListenerActive(true));
  },
);

export const stopListenChatRoomList = createAsyncThunk(
  'stopChatRoomListener',
  async (_, thunkAPI) => {
    if (firestoreListenerMap) {
      firestoreListenerMap();
    }
    thunkAPI.dispatch(recentChatSlice.actions.setListenerActive(false));
  },
);

export const refreshListenChatRoomList = createAsyncThunk(
  'refreshChatRoomListener',
  async (_, thunkAPI) => {
    thunkAPI.dispatch(stopListenChatRoomList());
    thunkAPI.dispatch(startListenChatRoomList());
  },
);

const recentChatSlice = createSlice({
  name: 'recentChatSlice',
  reducers: {
    setFilterDepartment: (state, action: { payload: ChatRoomStatesDepartment | undefined }) => {
      state.filters.department = action.payload;
    },

    setFilterLabel: (state, action: { payload: ChatRoomStatesLabel | undefined }) => {
      state.filters.label = action.payload;
    },

    setFilterPhoneNumber: (state, action: { payload: string | null }) => {
      state.filters.phoneNumber = action.payload;
    },

    addFilterLimit: (state, action: { payload: number }) => {
      state.filters.limit = (state.filters.limit || 0) + (action.payload || 0);
    },

    setFetchingStatus: (state, action: { payload: boolean }) => {
      state.fetching = action.payload;
    },
    loadChatRoomsFromServer: (state, action: { payload: ChatRoomEntity[] }) => {
      state.fetchedRoomsFromServer = action.payload;
    },
    addChatRoomFromServer: (state, action: { payload: ChatRoomEntity }) => {
      // Add or update
      const index = state.fetchedRoomsFromServer.findIndex(
        (room) => room.ref.id === action.payload.ref.id,
      );
      if (index === -1) {
        state.fetchedRoomsFromServer.push(action.payload);
      } else {
        state.fetchedRoomsFromServer[index] = action.payload;
      }
    },
    removeChatRoomFromServer: (state, action: { payload: ChatRoomEntity }) => {
      state.fetchedRoomsFromServer = state.fetchedRoomsFromServer.filter(
        (room) => room.ref.id !== action.payload.ref.id,
      );
    },
    clearChatRoomsFromServer: (state) => {
      state.fetchedRoomsFromServer = [];
    },
    setListenerActive: (state, action: { payload: boolean }) => {
      state.listenerActive = action.payload;
    },
  },
  initialState: {
    ...initRecentChatStates,
  } satisfies ChatRoomStates,
});

export default recentChatSlice;

import { applyMiddleware, combineReducers, compose, createStore } from 'redux';
import { thunk } from 'redux-thunk';
import lightboxSlice from './lightbox/lightbox.slice';
import { availableTemplateSlice } from './available-templates/reducer-available-template';
import reducerAvailableLabels from './available-labels/reducer-available-labels';
import reducerAvailableDepartment from './available-departments/reducer-available-department';
import reducerProject from './project/reducer-project';
import modalSendCreditSimulationReducer from './modal-send-credit-simulation/reducer-modal-send-credit-simulation';
import reducerInfoClientSegment from './info-client-segment/reducer-conversation';
import createTemplateSlice from './create-template/create-template-slice';
import modalSettingsTemplate from './modal-settings-template/modalSettingsTemplateSlice';
import modalSendToFreeLeadsSlice from './modal-send-to-free-leads/modalSendToFreeLeadsSlice';
import customerInfoSlice from './customerInfo/customerInfoSlice';
import modalInitMessageSlice from './modal-init-message/modalInitMessageSlice';
import modalDetailOfferSlice from './modal-detail-offer/modalDetailOfferSlice';
import updateNotesSlice from './update-notes/updateNotesSlice';
import modalPriceListSlice from './modal-price-list/modalPriceListSlice';
import modalStatusB2bSlice from './modalStatusB2b/modalStatusB2bSlice';
import modalPriceListSliceSimple from './modal-price-list-simple/modalPriceListSliceSimple';
import modalUpdateDataToOtodisSlice from './modal-update-data-to-otodis/modalUpdateDataToOtodisSlice';
import modalUpdateDataPromoCodeToOtodisSlice from './modal-update-data-to-otodis/modalUpdateDataPromoCodeToOtodisSlice';
import chatRoomSlice from './conversations/chatRoomSlice';
import recentChatSlice from './recent-chats/recentChatSlice';
import addConversationFlowSlice from './add-conversation-flow/addConversationFlow.slice';
import customerFinalDecisionSlice from './customer-final-decission/customerFinalDecision.slice';
import modalListAdminSlice from './modal-list-admin/modalListAdmin.slice';
import modalAddNewAdminSlice from './modal-list-admin/modalAddNewAdmin.slice';
import modalListConversationFlowSlice from './add-conversation-flow/modalListConversationFlow.slice';
import acquisitionOfferCodeSlice from './acquisition-offer-code/acquisitionOfferCode.slice';
import modalChangeFincoAdminSlice from './modalStatusB2b/modalChangeFincoAdminSlice';
import updateConversationFlowSlice from './add-conversation-flow/updateConversationFlow.slice';
import modalBlockPhoneNumberSlice from './modalBlockPhoneNumber/modalBlockPhoneNumber.slice';
import stockCheckSlice from './stock-check/stock-check.slice';
import modalSetImageAsSlice from './modal-capture-image/modalSetImageAs.slice';
import modalUpdatePhoneNumberSlice from './modal-update-phone-number/modalUpdatePhoneNumber.slice';
import modalSetPhoneNumberAsSlice from './modal-update-phone-number/modalSetPhoneNumberAs.slice';
import modalAcquisitionAsLeadsSlice from './modal-acquisition-as-leads/modalAcquisitionAsLeads.slice';
import modalLoanConfirmSlice from './loan/modalLoanConfirm.slice';
import modalAddNewOwnedVehicleSlice from './owned-vehicle/modalAddNewOwnedVehicle.slice';
import modalCheckBbnSlice from './modal-bbn/modalCheckBbnSlice';
import adminSlice from './admin/admin.slice';
import modalCreateAdSlice from './modal-trimobi/modalCreateAd.slice';
import modalSelectPlanSlice from './modal-trimobi/modalSelectPlan.slice';
import modalPaymentStatusSlice from './modal-trimobi/modalPaymentStatus.slice';
import modalUpgradePlanTrimobiSlice from './modal-trimobi/modalUpgradePlan.slice';
import modalAmartaArtReducer from './modal-amarta-art/modalAmartaArt.slice';
import chatTextInputSlice from './chat-text-input/chatTextInputSlice';
import modalSelectCatalogueSlice from './modal-select-catalogue/modalSelectCatalogueSlice';
import modalSendPromoSlice from './modal-send-promo/modalSendPromoSlice';
import modalProfileSlice from './modal-profile/modalProfile.slice';
import modalChatDebugSlice from './modal-chat-debug/modalChatDebug.slice';
import modalDealerDataSlice from './modal-dealer-data/modalDealerDataSlice';

export const mainReducers = combineReducers({
  lightbox: lightboxSlice.reducer,
  recentChat: recentChatSlice.reducer,
  reducerConversation: chatRoomSlice.reducer,
  reducerAdmin: adminSlice.reducer,
  reducerAvailableTemplate: availableTemplateSlice.reducer,
  reducerAvailableLabels,
  reducerAvailableDepartment,
  reducerProject,
  modalSetImageAs: modalSetImageAsSlice.reducer,
  modalSendCreditSimulationReducer,
  reducerInfoClientSegment,
  modalCreateTemplate: createTemplateSlice.reducer,
  modalSettingsTemplate: modalSettingsTemplate.reducer,
  modalSendToFreeLeads: modalSendToFreeLeadsSlice.reducer,
  modalAcquisitionAsLeads: modalAcquisitionAsLeadsSlice.reducer,
  customerReducer: customerInfoSlice.reducer,
  modalInitMessageReducer: modalInitMessageSlice.reducer,
  modalDetailOfferCode: modalDetailOfferSlice.reducer,
  updateNotes: updateNotesSlice.reducer,
  modalSendPriceList: modalPriceListSlice.reducer,
  modalSendPriceListSimple: modalPriceListSliceSimple.reducer,
  modalStatusB2b: modalStatusB2bSlice.reducer,
  modalChangeFincoAdminB2b: modalChangeFincoAdminSlice.reducer,
  modalUpdateDataToOtodis: modalUpdateDataToOtodisSlice.reducer,
  modalUpdateDataPromoCodeToOtodis: modalUpdateDataPromoCodeToOtodisSlice.reducer,
  modalAddConversationFlow: addConversationFlowSlice.reducer,
  modalUpdateConversationFlow: updateConversationFlowSlice.reducer,
  modalListConversationFlow: modalListConversationFlowSlice.reducer,
  modalUpdateCustomerFinalDecision: customerFinalDecisionSlice.reducer,
  modalAdminList: modalListAdminSlice.reducer,
  modalAddAdmin: modalAddNewAdminSlice.reducer,
  acquisitionOfferCodeSlice: acquisitionOfferCodeSlice.reducer,
  modalBlockPhoneNumber: modalBlockPhoneNumberSlice.reducer,
  modalStockCheck: stockCheckSlice.reducer,
  modalUpdatePhoneNumber: modalUpdatePhoneNumberSlice.reducer,
  modalSetPhoneNumberAs: modalSetPhoneNumberAsSlice.reducer,
  modalLoanConfirm: modalLoanConfirmSlice.reducer,
  modalAddNewOwnedVehicle: modalAddNewOwnedVehicleSlice.reducer,
  modalBbn: modalCheckBbnSlice.reducer,
  modalCreateAd: modalCreateAdSlice.reducer,
  modalSelectPlan: modalSelectPlanSlice.reducer,
  modalUpgradePlan: modalUpgradePlanTrimobiSlice.reducer,
  modalPaymentStatus: modalPaymentStatusSlice.reducer,
  modalAmartaArt: modalAmartaArtReducer,
  chatTextInput: chatTextInputSlice.reducer,
  modalSelectCatalogue: modalSelectCatalogueSlice.reducer,
  modalSendPromo: modalSendPromoSlice.reducer,
  modalProfile: modalProfileSlice.reducer,
  modalChatDebug: modalChatDebugSlice.reducer,
  modalDealerData: modalDealerDataSlice.reducer,
});
const composeEnhancers = (window as any).__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ || compose;

const enhancer = composeEnhancers(applyMiddleware(thunk));

export const mainStore = createStore(mainReducers, enhancer);

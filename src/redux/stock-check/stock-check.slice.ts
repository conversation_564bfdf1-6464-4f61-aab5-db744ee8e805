import { createSlice } from '@reduxjs/toolkit';
import { GetAvailabilityMultiCityGroupResponse } from '../../services/types/catalaogueTypes';

interface States {
  open: boolean;
  fetching: boolean;
  viewStockMode: 'dealer' | 'cityGroup';
  stockAvailabilityAllDealer: { dealerName: string; count: number }[];
  stockAvailabilityByCityGroup: GetAvailabilityMultiCityGroupResponse[];

  cityGroup: string;
  vehicle: {
    variant: {
      name: string;
      code: string;
    };
    color: {
      name: string;
      code: string;
    };
    year: string;
  } | null;
}

const initStates: States = {
  fetching: false,
  open: false,
  stockAvailabilityAllDealer: [],
  stockAvailabilityByCityGroup: [],
  viewStockMode: 'dealer',
  vehicle: null,
  cityGroup: '',
};

const stockCheckSlice = createSlice({
  name: 'stockCheck',
  initialState: {
    ...initStates,
  } as States,
  reducers: {
    open: (
      s,
      action: {
        payload: {
          variant: {
            name: string;
            code: string;
          };
          color: {
            name: string;
            code: string;
          };
          year: string;
          cityGroup: string;
        };
      },
    ) => {
      s.open = true;
      s.vehicle = {
        variant: action.payload.variant,
        color: action.payload.color,
        year: action.payload.year,
      };
      s.cityGroup = action.payload.cityGroup;
    },
    setLoadingState: (s, a: { payload: boolean }) => {
      s.fetching = a.payload;
    },
    setStockAllDealer: (s, a: { payload: States['stockAvailabilityAllDealer'] }) => {
      s.stockAvailabilityAllDealer = a.payload;
    },
    setStockCityGroup: (s, a: { payload: States['stockAvailabilityByCityGroup'] }) => {
      s.stockAvailabilityByCityGroup = a.payload;
    },
    onChangeViewStockMode: (s, a: { payload: States['viewStockMode'] }) => {
      s.viewStockMode = a.payload;
    },
    close: () => {
      return {
        ...initStates,
      };
    },
  },
});

export default stockCheckSlice;

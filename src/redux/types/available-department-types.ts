import { BaseAction } from './redux-types';
import DepartmentEntity from '../../entities/DeparmentEntity';

export interface IAvailableDepartmentStates {
  departments: DepartmentEntity[];
  fetching: boolean;
}

export enum EActionAvailableDepartment {
  FETCH_DEPARTMENT = 'FETCH_DEPARTMENT',
  SET_DEPARTMENT = 'SET_DEPARTMENT',
}

export type TSetDepartmentAction = BaseAction<
  EActionAvailableDepartment,
  EActionAvailableDepartment.SET_DEPARTMENT,
  DepartmentEntity[]
>;
export type TFetchDepartmentAction = BaseAction<
  EActionAvailableDepartment,
  EActionAvailableDepartment.FETCH_DEPARTMENT,
  null
>;

export type TAvailableDepartmentActions = TSetDepartmentAction | TFetchDepartmentAction;

import { BaseAction } from './redux-types';
import LabelEntity from '../../entities/LabelEntity';

export interface IAvailableLabelStates {
  labels: LabelEntity[];
  fetching: boolean;
}

export enum EActionAvailableLabel {
  FETCH_LABEL = 'FETCH_LABEL',
  SET_LABEL = 'SET_LABEL',
}

export type TSetLabelAction = BaseAction<
  EActionAvailableLabel,
  EActionAvailableLabel.SET_LABEL,
  LabelEntity[]
>;
export type TFetchLabelAction = BaseAction<
  EActionAvailableLabel,
  EActionAvailableLabel.FETCH_LABEL,
  null
>;

export type TAvailableLabelActions = TSetLabelAction | TFetchLabelAction;

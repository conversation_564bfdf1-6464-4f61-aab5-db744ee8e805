import { BaseAction } from './redux-types';

export interface IInfoClientSegment {
  visible: boolean;
}

export type TInfoClientSegmentActionTypes =
  | 'OPEN_INFO_CLIENT_SEGMENT'
  | 'CLOSE_INFO_CLIENT_SEGMENT'
  | 'TOGGLE_INFO_CLIENT_SEGMENT';

export type TOpenInfoClientSegment = BaseAction<
  TInfoClientSegmentActionTypes,
  'OPEN_INFO_CLIENT_SEGMENT',
  void
>;
export type TCloseInfoClientSegment = BaseAction<
  TInfoClientSegmentActionTypes,
  'CLOSE_INFO_CLIENT_SEGMENT',
  void
>;
export type TToggleInfoClientSegment = BaseAction<
  TInfoClientSegmentActionTypes,
  'TOGGLE_INFO_CLIENT_SEGMENT',
  void
>;

export type TInfoClientSegmentActions =
  | TOpenInfoClientSegment
  | TCloseInfoClientSegment
  | TToggleInfoClientSegment;

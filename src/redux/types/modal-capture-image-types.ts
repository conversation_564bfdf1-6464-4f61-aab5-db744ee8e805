import { BaseAction } from './redux-types';
import { ImageCaptureTypes } from '../../Components/ImageCapture/types/capture-types';

export interface IModalImageCaptureStates {
  visible: boolean;
  imageUrl?: string;
  set: 'UPDATE' | 'SET';
  target?: ImageCaptureTypes;
  optional?: {
    closeAfterSuccess?: boolean;
  };
}

export enum EModalImageCaptureActions {
  SET_VISIBILITY = 'SET_VISIBILITY',
}

export type TSetModalImageCaptureVisibility = BaseAction<
  EModalImageCaptureActions,
  EModalImageCaptureActions.SET_VISIBILITY,
  {
    visible: boolean;
    imageUrl?: string;
    set?: 'SET' | 'UPDATE';
    target?: ImageCaptureTypes;
  }
>;

export type TModalImageCaptureActions = TSetModalImageCaptureVisibility;

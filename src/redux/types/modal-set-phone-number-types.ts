import { BaseAction } from './redux-types';

export interface IModalSetPhoneNumberStates {
  visible: boolean;
  phoneNumber: string;
}

export enum EModalSetPhoneNumberActions {
  OPEN = 'OPEN',
  CLOSE = 'CLOSE',
}

export type TOpenModalPhoneNumber = BaseAction<
  EModalSetPhoneNumberActions,
  EModalSetPhoneNumberActions.OPEN,
  Omit<IModalSetPhoneNumberStates, 'visible'>
>;
export type TCloseModalPhoneNumber = BaseAction<
  EModalSetPhoneNumberActions,
  EModalSetPhoneNumberActions.CLOSE,
  null
>;

export type TModalSetPhoneNumberActions = TOpenModalPhoneNumber | TCloseModalPhoneNumber;

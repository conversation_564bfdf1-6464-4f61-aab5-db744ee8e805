import { BaseAction } from './redux-types';
import ChatRoomEntity from '../../entities/ChatRoomEntity';
import DepartmentEntity from '../../entities/DeparmentEntity';
import LabelEntity from '../../entities/LabelEntity';

export interface IChatRoomStates {
  rooms: ChatRoomEntity[];
  filteredRooms: ChatRoomEntity[];
  fetching: boolean;
  limitDays?: number | null;
  filters: {
    label: string | false;
    department: DepartmentEntity | 'ALL_DEPARTMENT' | 'NO_DEPARTMENT';
    showBlockedNumber: boolean;
  };
}

export interface ChatRoomStates {
  fetchedRoomsFromServer: ChatRoomEntity[];
  fetching: boolean;
  listenerActive: boolean;
  filters: {
    label?: ChatRoomStatesLabel;
    department?: ChatRoomStatesDepartment;
    showBlockedNumber?: boolean;
    phoneNumber?: string | null;
    limit?: number;
  };
}

export type ChatRoomStatesLabel = LabelEntity | 'ALL_LABELS' | 'NO_LABEL';
export type ChatRoomStatesDepartment = DepartmentEntity | 'ALL_DEPARTMENTS' | 'NO_DEPARTMENT';

export type TRecentChatActionType =
  | 'INIT_FETCH_RECENT_CHAT'
  | 'UPDATE_RECENT_CHAT'
  | 'UPDATE_FILTERED_RECENT_CHAT'
  | 'SET_FETCHING_STATUS_RECENT_CHAT'
  | 'SET_FILTER_LABEL'
  | 'SET_DAY_LIMIT'
  | 'SET_FILTER_RECENT_CHAT';

export type TRecentChatSetFetchingStatus = BaseAction<
  TRecentChatActionType,
  'SET_FETCHING_STATUS_RECENT_CHAT',
  boolean
>;
export type TRecentChatInitFetch = BaseAction<
  TRecentChatActionType,
  'INIT_FETCH_RECENT_CHAT',
  void
>;

export type TUpdateRecentChat = BaseAction<
  TRecentChatActionType,
  'UPDATE_RECENT_CHAT',
  ChatRoomEntity[]
>;
export type TUpdateFilteredRecentChat = BaseAction<
  TRecentChatActionType,
  'UPDATE_FILTERED_RECENT_CHAT',
  ChatRoomEntity[]
>;

export type TUpdateFilterRecentChat = BaseAction<
  TRecentChatActionType,
  'SET_FILTER_RECENT_CHAT',
  IChatRoomStates['filters']
>;
export type TUpdateDayLimitRecentChat = BaseAction<TRecentChatActionType, 'SET_DAY_LIMIT', number>;

export type TRecentChatAction =
  | TRecentChatInitFetch
  | TRecentChatSetFetchingStatus
  | TUpdateRecentChat
  | TUpdateFilteredRecentChat
  | TUpdateDayLimitRecentChat
  | TUpdateFilterRecentChat;

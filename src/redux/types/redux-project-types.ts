import ProjectEntity from '../../entities/ProjectEntity';
import { BaseAction } from './redux-types';

export interface IProjectStates {
  fetching: boolean;
  project: ProjectEntity | null;
}

export type TProjectActionType = 'SET_PROJECT_FETCHING' | 'REMOVE_PROJECT' | 'SET_PROJECT_BY_CLASS';

export type TProjectSetFetchAction = BaseAction<
  TProjectActionType,
  'SET_PROJECT_FETCHING',
  boolean
>;
export type TProjectSetByClassAction = BaseAction<
  TProjectActionType,
  'SET_PROJECT_BY_CLASS',
  ProjectEntity
>;
export type TProjectRemoveAction = BaseAction<TProjectActionType, 'REMOVE_PROJECT', undefined>;

export type TProjectActions =
  | TProjectSetFetchAction
  | TProjectSetByClassAction
  | TProjectRemoveAction;

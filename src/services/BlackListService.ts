import { create } from 'apisauce';

class BlackListService {
  private readonly _baseUrl = 'https://3krjkubmjk.execute-api.ap-southeast-3.amazonaws.com/v1';
  private readonly _baseAxios = create({
    baseURL: this._baseUrl,
    headers: {
      'x-api-key': 'NqWjTj6qpI7LK8JUKvkQz3isRY9NGU7faeWwv2ff',
    },
  });

  public async check(phoneNumber: string) {
    const get = await this._baseAxios.get('/user/blacklist-phone-number/' + phoneNumber);
    if (!get.ok) {
      throw get;
    } else {
      return get.data;
    }
  }

  public async blacklist(phoneNumbers: string[]) {
    const params = {
      type: 'blacklist',
      source: 'ideal',
      phone_numbers: phoneNumbers.map((value) => {
        return {
          phone: value,
          reason: 'Autotrimitra Ideal',
        };
      }),
    };

    const post = await this._baseAxios.post('/user/blacklist-multi-number', params);
    if (!post.ok) {
      throw post;
    } else {
      return post.data;
    }
  }

  public async removeBlacklist(phoneNumbers: string[]) {
    const params = {
      type: 'user',
      source: 'ideal',
      phone_numbers: phoneNumbers.map((value) => {
        return {
          phone: value,
          reason: 'Autotrimitra Ideal',
        };
      }),
    };

    const post = await this._baseAxios.post('/user/remove-blacklist-multi-number', params);
    if (!post.ok) {
      throw post;
    } else {
      return post.data;
    }
  }
}

export const blacklistServices = new BlackListService();

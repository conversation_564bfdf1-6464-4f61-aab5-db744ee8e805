import { create } from 'apisauce';
import {
  AddConversationFlowParams,
  BaseSuccessResponse,
  IAddNewAdminParams,
  IAddNewOwnedVehicleParams,
  IAdminUpdateActiveStatusParams,
  ICreatePriceListParams,
  IFreeLeadsParams,
  ILogSendPriceListParams,
  IMessageSendV2Params,
  IUpdateCustomerDecisionParams,
  UpdateConversationFlowParams,
} from './types/mainApiService/mainApiService.types';
import { TCaptureTarget } from '../Components/ImageCapture/types/capture-types';
import { IPlaceToStayCaptureFields } from '../Components/ImageCapture/PlaceToStayCapture';
import { IPlaceOfBusinessFields } from '../Components/ImageCapture/PlaceOfBusinessCapture';
import { IBusinessDocumentFields } from '../Components/ImageCapture/BusinessDocumentCapture';
import { IIncomeDocFields } from '../Components/ImageCapture/IncomeDocCapture';
import { IOtherDocumentFields } from '../Components/ImageCapture/OtherDocumentCapture';
import { IProfileInformationFields } from '../Components/NewClientInformation/ProfileInformation';
import { DocumentReference } from 'firebase/firestore';
import qs from 'query-string';
import authServices from './firebase/AuthServices';
import {
  AdListingRequest as ITrimobiAddNewListingParams,
  TrimobiBuyNewPlanRequest,
} from './types/mainApiService/mainApiService.trimobi.types';
import {
  AdsPackage,
  PlanCheckoutStatusItem,
} from './types/mainApiService/mainApiService.amartavip.types';

class MainApiServices {
  public setTokenInterval = setInterval(() => {
    if (authServices.currentUser) {
      authServices.currentUser?.getIdToken(true).then((value) => {
        this.setToken(value);
      });
    }
  }, 120 * 1000);

  public baseApi = create({
    baseURL: 'https://asia-southeast2-ideal-trimitra.cloudfunctions.net/ideal-backend/',
    // baseURL: 'http://localhost:8000/',
  });

  public setToken(token: string): void {
    this.baseApi.setHeaders({
      Authorization: 'Bearer ' + token,
    });
  }

  public async sendTemplateInitMessage(params: {
    templateName: string;
    phoneNumber: string;
    variables: string[];
    projectPath: string;
  }) {
    let body = Object.assign({}, params);
    const send = await this.baseApi.post<any>('/message/send-template-init-message', body);
    if (send.ok) {
      return send.data;
    } else {
      throw Object.assign(new Error(send.originalError?.message), send.originalError);
    }
  }

  public async sendMessageTemplateInternal(params: {
    roomPath: string;
    adminSessionPath: string;
    phoneNumber: string;
    templatePath: string;
  }) {
    return this.baseApi.post('/message/send-template-internal', params);
  }

  public async sendMessageV2(params: IMessageSendV2Params) {
    const formData = new FormData();
    formData.append('roomPath', params.roomPath);
    formData.append('phoneNumber', params.phoneNumber);
    formData.append('adminSessionPath', params.adminSessionPath);
    if (params.text) formData.append('text', params.text);
    if (params.media) formData.append('media', params.media);
    if (params.fileUrl) formData.append('fileUrl', params.fileUrl);
    if (params.fileType) formData.append('fileType', params.fileType);
    if (params.filename) formData.append('filename', params.filename);
    if (params.fileMediaId) formData.append('fileMediaId', params.fileMediaId);
    return this.baseApi.post('/message/send-v2', formData);
  }

  public async pinMessage(params: {
    mode: 'pin' | 'unpin';
    chatRoomPath: string; // Firestore Path
    messagePath?: string | null; // Firestore Path
  }) {
    const pin = await this.baseApi.post('/message/pin-message', params);
    if (pin.ok) {
      return pin.data;
    } else {
      throw pin.originalError;
    }
  }

  public async setPhoneNumberAs(params: {
    clientRef: DocumentReference;
    phoneNumber: string;
    type: string;
  }) {
    const update = await this.baseApi.post('client/profile/set-phone-number-as', {
      clientRefPath: params.clientRef.path,
      phoneNumber: params.phoneNumber,
      type: params.type,
    });

    if (!update.ok) {
      throw update.originalError;
    } else {
      return update.data;
    }
  }

  public async setPhoneNumber(params: {
    clientRef: DocumentReference;
    phoneNumberOwner: string;
    phoneNumberGuarantor: string;
    phoneNumberOrderMaker: string;
  }) {
    const update = await this.baseApi.post('client/profile/set-phone-number', {
      clientRefPath: params.clientRef.path,
      phoneNumberOwner: params.phoneNumberOwner,
      phoneNumberGuarantor: params.phoneNumberGuarantor,
      phoneNumberOrderMaker: params.phoneNumberOrderMaker,
    });
    if (!update.ok) {
      throw update.originalError;
    } else {
      return update.data;
    }
  }

  public async setProfile(
    fields: IProfileInformationFields,
    clientRef: DocumentReference,
    roomRef: DocumentReference,
  ) {
    const update = await this.baseApi.post('client/profile/set-profile', {
      clientRef: clientRef.path,
      roomRef: roomRef.path,
      phoneNumber: fields.phoneNumber,
      contactName: fields.contactName,
      area: fields.area,
      organization: fields.organization,
      organization_group: fields.organization_group,
    });
    if (!update.ok) {
      throw update.originalError;
    } else {
      return update.data;
    }
  }

  public async setClientIdCardV2(fields: any, type: TCaptureTarget, clientRef: DocumentReference) {
    const formData = new FormData();

    formData.append('clientRef', clientRef.path);
    formData.append('type', type);

    Object.keys(fields).forEach((key) => {
      let val = fields[key as keyof typeof fields];
      if (val) {
        if (
          [
            'province',
            'city',
            'district',
            'subDistrict',
            'domicileProvince',
            'domicileCity',
            'domicileDistrict',
            'domicileSubDistrict',
          ].indexOf(key) >= 0
        ) {
          formData.append(
            `${key}[code]`,
            fields[key as 'province' | 'city' | 'district' | 'subDistrict']!.code,
          );
          formData.append(
            `${key}[name]`,
            fields[key as 'province' | 'city' | 'district' | 'subDistrict']!.name,
          );
        } else {
          formData.append(key, val as any);
        }
      }
    });

    const update = await this.baseApi.post('client/document/v2/set-id-card', formData);
    if (!update.ok) {
      throw update.originalError;
    }
  }

  public async setFamilyRegisterV2(fields: any, clientRef: DocumentReference) {
    const formData = new FormData();
    formData.append('clientRef', clientRef.path);
    if (fields.image) formData.append('image', fields.image);
    formData.append('familyRegisterNumber', fields.familyRegisterNumber);
    const update = await this.baseApi.post('client/document/v2/set-family-register', formData);
    if (!update.ok) {
      throw update.originalError;
    }
  }

  public async setSelfieV2(fields: any, clientRef: DocumentReference) {
    const formData = new FormData();

    formData.append('clientRef', clientRef.path);

    Object.keys(fields).forEach((value) => {
      let val = fields[value as keyof typeof fields] as string | Blob;
      if (val) {
        formData.append(value, (fields[value as keyof typeof fields] as string | Blob) ?? '');
      }
    });

    const set = await this.baseApi.post('client/document/v2/set-selfie', formData);
    if (!set.ok) {
      throw set.originalError;
    }
  }

  public async setPlaceToStay(
    fields: IPlaceToStayCaptureFields & { imageUrl?: string },
    clientRef: DocumentReference,
  ) {
    const formData = new FormData();

    formData.append('clientRef', clientRef.path);

    Object.keys(fields).forEach((value) => {
      let val = fields[value as keyof typeof fields] as string | Blob;
      if (val) {
        formData.append(value, (fields[value as keyof typeof fields] as string | Blob) ?? '');
      }
    });

    const set = await this.baseApi.post('client/document/set-place-to-stay', formData);
    if (!set.ok) {
      throw set.originalError;
    }
  }

  public async setPlaceOfBusiness(
    fields: IPlaceOfBusinessFields & { imageUrl?: string },
    clientRef: DocumentReference,
  ) {
    const formData = new FormData();

    formData.append('clientRef', clientRef.path);

    Object.keys(fields).forEach((value) => {
      let val = fields[value as keyof typeof fields] as string | Blob;
      if (val) {
        formData.append(value, (fields[value as keyof typeof fields] as string | Blob) ?? '');
      }
    });

    const set = await this.baseApi.post('client/document/set-place-of-business', formData);
    if (!set.ok) {
      throw set.originalError;
    }
  }

  public async setBusinessDocument(
    fields: IBusinessDocumentFields & { imageUrl?: string },
    clientRef: DocumentReference,
  ) {
    const formData = new FormData();

    formData.append('clientRef', clientRef.path);

    Object.keys(fields).forEach((value) => {
      let val = fields[value as keyof typeof fields] as string | Blob;
      if (val) {
        formData.append(value, (fields[value as keyof typeof fields] as string | Blob) ?? '');
      }
    });

    const set = await this.baseApi.post('client/document/set-business-document', formData);
    if (!set.ok) {
      throw set.originalError;
    }
  }

  public async setIncomeDocument(
    fields: IIncomeDocFields & { imageUrl?: string },
    clientRef: DocumentReference,
  ) {
    const formData = new FormData();

    formData.append('clientRef', clientRef.path);

    Object.keys(fields).forEach((value) => {
      let val = fields[value as keyof typeof fields] as string | Blob;
      if (val) {
        formData.append(value, (fields[value as keyof typeof fields] as string | Blob) ?? '');
      }
    });

    const set = await this.baseApi.post('client/document/set-income-document', formData);
    if (!set.ok) {
      throw set.originalError;
    }
  }

  public async setOtherDocument(
    fields: IOtherDocumentFields & { imageUrl?: string },
    clientRef: DocumentReference,
  ) {
    const formData = new FormData();

    formData.append('clientRef', clientRef.path);

    Object.keys(fields).forEach((value) => {
      let val = fields[value as keyof typeof fields] as string | Blob;
      if (val) {
        formData.append(value, (fields[value as keyof typeof fields] as string | Blob) ?? '');
      }
    });

    const set = await this.baseApi.post('client/document/set-other-document', formData);
    if (!set.ok) {
      throw set.originalError;
    }
  }

  public getImageUrl(chatRef: DocumentReference) {
    return this.baseApi.getBaseURL() + 'message/image?path=' + chatRef.path;
  }

  public getMediaUrl(chatRef: DocumentReference, fileName: string) {
    return this.baseApi.getBaseURL() + `message/media/${fileName}?path=` + chatRef.path;
  }

  public async updateDreamVehicle(
    params: {
      area: string;
      condition: string;
      license_plate: string;
      brand_name: string;
      brand_uuid: string;
      model_name: string;
      model_uuid: string;
      model_category: string;
      variant_code: string;
      variant_name: string;
      variant_uuid: string;
      variant_free_text: string;
      color_name: string;
      color_code: string;
      year: string;
      price: number;
      mileage: string;
    },
    clientRef: DocumentReference,
    chatRoomRef: DocumentReference,
  ) {
    const update = await this.baseApi.post('account/survey/update-dream-vehicle', {
      ...params,
      _clientPath: clientRef.path,
      _chatRoomPath: chatRoomRef.path,
    });
    if (!update.ok) {
      throw update;
    }

    return update;
  }

  public async updateCreditScheme(
    params: {
      tenor: number;
      installment: number;
      downPayment: number;
      surveyTime: string;
      surveyGmapUrl: string | null;
      discountInstallment: number;
      discountTenor: number;
      priceListSource: 'b2b' | 'dealCode';
      dealCode: string;
      selectedLeasingCode: string;
      leasingAdminId: string;
    },
    clientRef: DocumentReference,
  ) {
    const update = await this.baseApi.post(
      'account/survey/update-credit-scheme',
      qs.stringify({
        ...params,
        _clientPath: clientRef.path,
      } as any),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      },
    );
    if (!update.ok) {
      throw update.originalError;
    }

    return update;
  }

  public async updateLoanScheme(params: { clientRefPath: string; tenor: number; amount: number }) {
    const update = await this.baseApi.post('/account/survey/update-loan-scheme', params);

    if (!update.ok) {
      throw update.originalError;
    }

    return update.data;
  }

  public async addFreeLeads(params: IFreeLeadsParams) {
    const add = await this.baseApi.post<
      BaseSuccessResponse<{
        alreadyExists: boolean;
      }>
    >(
      '/leads/add-free-leads',
      {
        ...params,
      },
      {
        headers: {
          Authorization: 'Basic bGVhZHNDbGllbnRBcHA6cER5PEEyVC5zKGYzYDZIWg==',
        },
      },
    );
    if (!add.ok) {
      throw add.originalError;
    }

    return add;
  }

  createPriceList = async (params: ICreatePriceListParams) => {
    const create = await this.baseApi.post<BaseSuccessResponse<string>>(
      '/message/generate-price-list',
      {
        ...params,
      },
    );

    if (create.ok) {
      return create.data?.success.data;
    } else {
      throw create.originalError;
    }
  };

  logSendPriceList = async (params: ILogSendPriceListParams) => {
    const insertLog = await this.baseApi.post<BaseSuccessResponse<any>>(
      '/message/pricelist-bq-log',
      params,
    );
    if (insertLog.ok) {
      return insertLog.data?.success;
    } else {
      throw insertLog.originalError;
    }
  };

  saveImageToGoogleBucket = async (path: string) => {
    const insert = await this.baseApi.post('/message/image/save', {
      path: path,
    });

    if (insert.ok) {
      return insert.data;
    } else {
      throw insert.originalError;
    }
  };

  updateCustomerDecision = async (params: IUpdateCustomerDecisionParams) => {
    const update = await this.baseApi.post('/client/profile/set-customer-decision', {
      ...params,
    });

    if (update.ok) {
      return update.data;
    } else {
      throw update.originalError;
    }
  };

  addNewAdmin = async (params: IAddNewAdminParams) => {
    const create = await this.baseApi.post('/admin/add-new-admin', {
      ...params,
    });

    if (create.ok) {
      return create.data;
    } else {
      throw create.originalError;
    }
  };

  updateAdminStatus = async (params: IAdminUpdateActiveStatusParams) => {
    const update = await this.baseApi.post('/admin/update-admin-status', {
      ...params,
    });

    if (update.ok) {
      return update.data;
    } else {
      throw update.originalError;
    }
  };

  addConversationFlow = async (params: AddConversationFlowParams) => {
    const add = await this.baseApi.post('/conversation-flow/add-new-flow', {
      ...params,
    });

    if (add.ok) {
      return add.data;
    } else {
      throw add.originalError;
    }
  };

  updateConversationFlow = async (params: UpdateConversationFlowParams) => {
    const flow = await this.baseApi.post('/conversation-flow/update-flow', {
      ...params,
    });

    if (flow.ok) {
      return flow.data;
    } else {
      throw flow.originalError;
    }
  };

  adminAcquisitionAsLeads = async (params: {
    organization: string;
    clientRefPath: string;
    projectRefPath: string;
    chatRoomRefPath: string;
  }) => {
    const post = await this.baseApi.post('/client/profile/acquisition', {
      ...params,
    });

    if (post.ok) {
      return post.data;
    } else {
      throw post.originalError;
    }
  };

  addOwnedVehicle = async (params: IAddNewOwnedVehicleParams) => {
    const post = await this.baseApi.post('/client/document/add-owned-vehicle', {
      ...params,
    });

    if (post.ok) {
      return post.data;
    } else {
      throw post.originalError;
    }
  };

  trimobiAddNewListing = async (params: ITrimobiAddNewListingParams) => {
    const listing = await this.baseApi.post<BaseSuccessResponse<{ trimobiUrl: string }>>(
      '/client/trimobi/add-new-listing',
      {
        ...params,
      },
    );

    if (listing.ok) {
      return listing.data;
    } else {
      throw listing.originalError;
    }
  };

  trimobiBuyNewPlan = async (params: TrimobiBuyNewPlanRequest) => {
    const post = await this.baseApi.post('/client/trimobi/buy-new-plan', {
      ...params,
    });

    if (post.ok) {
      return post.data;
    } else {
      throw post.originalError;
    }
  };

  trimobiUpgradePlan = async (params: Omit<TrimobiBuyNewPlanRequest, 'vehicleType'>) => {
    const post = await this.baseApi.post<BaseSuccessResponse<{ qrisUrl: string; checkout: any }>>(
      '/client/trimobi/upgrade-plan',
      {
        ...params,
      },
    );

    if (post.ok) {
      return post.data;
    } else {
      throw post.originalError;
    }
  };

  trimobiGetPurchasedPlan = async () => {
    const get = await this.baseApi.get<BaseSuccessResponse<AdsPackage[]>>(
      '/client/trimobi/get-purchased-plan',
    );

    if (get.ok) {
      return get.data?.success!;
    } else {
      throw get.originalError;
    }
  };

  trimobiGetPlanCheckoutStatus = async (params: { condition: string; transaction_id: string }) => {
    const get = await this.baseApi.get<BaseSuccessResponse<PlanCheckoutStatusItem[]>>(
      '/client/trimobi/get-plan-checkout-status',
      {
        ...params,
      },
    );

    if (get.ok) {
      return get.data?.success!;
    } else {
      throw get.originalError;
    }
  };

  toggleAgentAiReply = async (params: {
    chatRoomRefPath: string;
    status: boolean;
    source: string;
  }) => {
    const toggle = await this.baseApi.post<BaseSuccessResponse<any>>(
      '/client/profile/toggle-agent-ai-reply',
      {
        ...params,
      },
    );

    if (toggle.ok) {
      return toggle.data;
    } else {
      throw toggle.originalError;
    }
  };
}

export const mainApiServices = new MainApiServices();

import { create } from 'apisauce';
import {
  GetFeedResponse,
  GetMediaByArtCodeResponse,
  FeedEventRequest,
} from './types/amartaArt.services.types';

class AmartaArtServices {
  private baseApi = create({
    baseURL: 'https://2dkeeytpe6.execute-api.ap-southeast-1.amazonaws.com/v1',
    headers: {
      'x-api-key': 'sM2TXcm8pW3iDwLAqQJ1t4l6ip8ggTwZ4mwUNdK4',
    },
  });

  public async getFeed(params: {
    art_category: 'feed' | 'feed_promo';
    brand_uid: string;
    start?: number;
    length?: number;
  }): Promise<GetFeedResponse> {
    const response = await this.baseApi.get<GetFeedResponse>('/feed/content-curation', {
      ...params,
    });

    if (!response.ok) {
      throw response.originalError;
    }

    return response.data!;
  }

  public async getMediaByArtCode(artCode: string): Promise<GetMediaByArtCodeResponse> {
    const response = await this.baseApi.get<GetMediaByArtCodeResponse>(`/public/${artCode}`);

    if (!response.ok) {
      throw response.originalError;
    }

    return response.data!;
  }

  public async feedEvent(params: FeedEventRequest) {
    const response = await this.baseApi.post('/feed/webhook', params);

    if (!response.ok) {
      throw response.originalError;
    }

    return response.data;
  }

  public async extendMediaValidity(params: {
    type: string;
    art_class: string;
    art_code: string;
    project_id: string;
    url_file: string;
    provider: string;
    project_name: string;
    project_phone_number: string;
    expired: string;
  }) {
    const response = await this.baseApi.put(
      `/image`,
      {
        ...params,
      },
      {
        headers: {
          Authorization: 'Bearer dummyToken',
        },
      },
    );

    if (!response.ok) {
      throw response.originalError;
    }

    return response.data;
  }
}

export const amartaArtServices = new AmartaArtServices();

import * as apisauce from 'apisauce';
import {
  AutotrimitraBaseResponse,
  IBrandModel,
  IVariant,
  IVehicleModel,
} from './types/autotrimitra-services-types';
import { IProvince } from './types/provinceTypes';
import { ICity } from './types/cityTypes';
import { IDistrict } from './types/districtTypes';
import { ISubDistrict } from './types/subDistrictTypes';
import { create } from 'apisauce';

class AutotrimitraServices {
  private apisauceInstance = create({
    baseURL: 'https://au-api-trimitra-get-65q45htc.ts.gateway.dev/',
  });
  private key = 'AIzaSyAFTemJSnp4h16lYQfITqLD8Ryp9fGNsVg';

  public async getVehicleBrand() {
    const get = await this.apisauceInstance.get<AutotrimitraBaseResponse<IBrandModel[]>>(
      '/vehicle/brand',
      {
        key: this.key,
      },
    );

    if (!get.ok) {
      throw get;
    } else {
      return get.data;
    }
  }

  public async getVehicleModelBrand(params?: {
    brandUuid?: string;
    modelUuid?: string;
    category?: string;
  }) {
    const queries = {
      ...(params?.brandUuid && {
        brand_uuid: params.brandUuid,
      }),
      ...(params?.modelUuid && {
        model_uuid: params.modelUuid,
      }),
      ...(params?.category && {
        category: params.category,
      }),
    };
    const get = await this.apisauceInstance.get<AutotrimitraBaseResponse<IVehicleModel[]>>(
      '/vehicle/model',
      {
        key: this.key,
        ...queries,
      },
    );

    if (!get.ok) {
      throw get;
    } else {
      return get.data;
    }
  }

  public async getVehicleVariantModel(params?: {
    variantUuid?: string;
    modelUuid?: string;
    code?: string;
  }) {
    const queries = {
      ...(params?.variantUuid && {
        variant_uuid: params.variantUuid,
      }),
      ...(params?.modelUuid && {
        model_uuid: params.modelUuid,
      }),
      ...(params?.code && {
        code: params.code,
      }),
    };
    const get = await this.apisauceInstance.get<AutotrimitraBaseResponse<IVariant[]>>(
      '/vehicle/variant',
      {
        key: this.key,
        ...queries,
      },
    );

    if (!get.ok) {
      throw get;
    } else {
      return get.data;
    }
  }

  public async getProvince(queries?: { province_code?: string }) {
    const q: any = {};

    if (queries?.province_code) q['province_code'] = queries.province_code;

    const get = await this.apisauceInstance.get<AutotrimitraBaseResponse<IProvince[]>>(
      '/geo-area/province',
      {
        ...q,
        key: this.key,
      },
    );

    if (!get.ok) {
      throw get;
    } else {
      return get.data;
    }
  }

  public async getCities(provinceCode: string) {
    const get = await this.apisauceInstance.get<AutotrimitraBaseResponse<ICity[]>>(
      '/geo-area/city',
      {
        province_code: provinceCode,
        key: this.key,
      },
    );

    if (!get.ok) {
      throw get;
    } else {
      return get.data;
    }
  }

  public async getDistricts(cityCode: string) {
    const get = await this.apisauceInstance.get<AutotrimitraBaseResponse<IDistrict[]>>(
      '/geo-area/district',
      {
        city_code: cityCode,
        key: this.key,
      },
    );

    if (!get.ok) {
      throw get;
    } else {
      return get.data;
    }
  }

  public async getSubDistricts(districtCode: string) {
    const get = await this.apisauceInstance.get<AutotrimitraBaseResponse<ISubDistrict[]>>(
      '/geo-area/subdistrict',
      {
        district_code: districtCode,
        key: this.key,
      },
    );

    if (!get.ok) {
      throw get;
    } else {
      return get.data;
    }
  }
}

export const autotrimitraServices = new AutotrimitraServices();

export interface IReqBodyB2bLoan {
  source: string;

  name: string;
  email?: string;
  phoneNumber: string;
  vehicle: {
    brand: {
      name: string;
      uuid: string;
    };
    model: {
      name: string;
      uuid: string;
      category: string;
    };
    variant?: {
      name: string;
      uuid: string;
      code: string;
    };
    variant_free_text: string;
    condition: 'new' | 'used';
    year: string;
    mileage: string;
    license_plate: string;
  };
  scheme: {
    amount: number;
    tenor: number;
  };
  id_card: {
    id_card_image?: string;
    id_card_number: string;
    full_name?: string;
    full_address?: string;
    birth_date?: string;
    birth_place?: string;
    marital_status_code?: string;
    marital_status?: string;
    occupation_code?: string;
    occupation?: string;
  };
  id_card_address?: IAddressLoan | null;
  domicile_address?: IAddressLoan | null;
  family_register?: {
    family_register_image: string;
    family_register_number: string;
  };

  vehicle_registration?: {
    vehicle_registration_number: string;
    vehicle_registration_image: string;
  };

  notice?: {
    notice_number: string;
    notice_image: string;
  };

  vehicle_ownership_document?: {
    vehicle_document_number: string;
    vehicle_document_image: string;
  };
}

export interface IAddressLoan {
  full_address?: string | null;
  province: {
    code: string;
    name: string;
  };
  city: {
    code: string;
    name: string;
  };
  district?: {
    code: string;
    name: string;
  } | null;
  sub_district?: {
    code: string;
    name: string;
  } | null;
  zip_code?: string | null;
  neighbourhood?: string | null;
  hamlet?: string | null;
}

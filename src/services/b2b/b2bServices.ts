import { create } from 'apisauce';
import { IPriceListJsonData } from '../types/TabularPriceList.types';
import { ISuccessResponseGetSurveyByOfferCode } from './b2bServices.types';
import { IReqBodyB2bLoan } from './b2bLoan.types';

class B2bServices {
  private baseUrl = 'https://asia-southeast2-amh-b2b.cloudfunctions.net/api-b2b';
  // private baseUrl = "http://localhost:8000"
  private apisauceBase = create({
    baseURL: this.baseUrl,
  });

  async getPriceList(params: { cityGroup: string; modelName: string; variantCode: string }) {
    const get = await this.apisauceBase.get<{ data: IPriceListJsonData }>('/price-list', {
      leasingCode: '_GLOBAL_',
      // leasingCode: "BCAF",
      ...params,
    });

    if (get.ok) {
      return get.data!;
    }

    throw get.originalError;
  }

  async getStatusSurveyByOfferCode(offerCode: string) {
    const get = await this.apisauceBase.get<{
      data: ISuccessResponseGetSurveyByOfferCode;
    }>('/public/get-survey/offer-code/' + offerCode);

    if (get.ok) {
      return get.data!;
    }

    throw get.originalError;
  }

  createSurveyOrder = (bodyPayload: any) => {
    return this.apisauceBase.post<{ data: { offerCode: string } }>(
      '/public/create-survey-order',
      {
        ...bodyPayload,
      },
      {
        headers: {
          'Api-Key': 'j11I6kdaVbXZHfQPqZKy',
        },
      },
    );
  };

  createSurveyOrderLoan = (bodyPayload: IReqBodyB2bLoan) => {
    return this.apisauceBase.post<{ data: { offerCode: string } }>(
      '/public/create-loan-survey-order',
      {
        ...bodyPayload,
      },
      {
        headers: {
          'Api-Key': 'qHVzIyC5A1w9gtywjbGH',
        },
      },
    );
  };

  acquisition = async (params: {
    poNumber: string;
    offerCodeInOtodis: string;
    offerCodeInB2b: string;
    adminId: string;
    adminName: string;
  }) => {
    const submit = await this.apisauceBase.post('/public/acquisition-from-ideal', { ...params });

    if (submit.ok) {
      return submit.data;
    }

    throw submit.originalError;
  };

  changeFincoAdmin = (params: { offerCode: string; adminRefId: string }) => {
    return this.apisauceBase.post('/public/change-survey-order-admin-from-ideal', { ...params });
  };
}

const b2bServices = new B2bServices();

export default b2bServices;

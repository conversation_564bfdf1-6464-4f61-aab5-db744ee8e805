export type TAdiraWebhookStatus = 'NVER' | 'REJC' | 'BTPO' | 'CANC' | 'APPR';
export interface IAdiraWebhook<TIMESTAMP> {
  request_id: string;
  request_time_stamp: TIMESTAMP;
  leasing_code: string;
  spk_no: string;
  order_id: string;
  aplicationNo: string;
  status_code: TAdiraWebhookStatus;
  status_desc: string;
  keterangan: string;
  status_date: TIMESTAMP;
  no_po: string;
  telp_number: string;
  dom_address: string;
  otr: number;
  down_payment: number;
  tenor: number;
  instalment: number;
  imagePO: string | null;
}

export interface IAdiraSubmitResponse {
  Status: number;
  Message: string;
  Data: IDataAdiraSubmitResponse[];
}

export interface IDataAdiraSubmitResponse {
  SPK_No: string;
  KTP_No: string;
  NamaPemohon: string;
  OrderNo: string;
  BranchID: string;
  nik_cmo: string;
  cmo_name: string;
  survey_date: Date;
  status_code: string;
  status_desc: string;
}

export interface ISuccessResponseGetSurveyByOfferCode {
  survey: {
    adira_host_2_host: null | {
      success: boolean;
      status: IAdiraWebhook<string> | null;
      image_po?: string | null;

      submitOriginalResponse: IAdiraSubmitResponse | null;
      submitDataResponse: IDataAdiraSubmitResponse | null;

      error: any;

      payload: any;
    };
    status: string;
    admin_leasing_target: {
      settings: {
        city_group: string[];
        area_scopes: string[];
        already_in_firebase_auth: boolean;
        telegram_chat_id: null | string;
        is_adira_cmo: boolean;
        adira_host_2_host: {
          dlccode: string;
          nikcmo: string;
        };
      };
      leasing_company: {
        name: string;
        code: string;
        ref: string;
      };
      full_name: string;
      email: string;
      phone_number: string;
      role: string;
      b2b_telegram_token: null | string;
      ref: string;
    };
    area: string;
    created_at: string;
    credit_scheme: {
      installment: number;
      tenor: number;
      down_payment: number;
    };
    guarantor_address: {
      neighbourhood: string;
      district: {
        code: string;
        name: string;
      };
      province: {
        code: string;
        name: string;
      };
      full_address: string;
      hamlet: string;
      sub_district: {
        name: string;
        code: string;
      };
      zip_code: string;
      city: {
        code: string;
        name: string;
      };
    };
    guarantor_id_card: {
      full_address: string;
      occupation: string;
      last_education_code: string;
      id_card_number: string;
      id_card_image: string;
      marital_status: string;
      marital_status_code: string;
      birth_place: string;
      last_education_name: string;
      mother_birth: string;
      occupation_code: string;
      birth_date: string;
      full_name: string;
    };
    offer_code: string;
    order_make_id_card: {
      last_education_name: string;
      birth_place: string;
      occupation: string;
      id_card_image: string;
      mother_birth: string;
      marital_status_code: string;
      marital_status: string;
      birth_date: string;
      last_education_code: string;
      id_card_number: string;
      occupation_code: string;
      full_address: string;
      full_name: string;
    };
    order_maker_address: {
      district: {
        name: string;
        code: string;
      };
      full_address: string;
      city: {
        name: string;
        code: string;
      };
      neighbourhood: string;
      zip_code: string;
      sub_district: {
        code: string;
        name: string;
      };
      hamlet: string;
      province: {
        code: string;
        name: string;
      };
    };
    owner_address: {
      neighbourhood: string;
      district: {
        code: string;
        name: string;
      };
      full_address: string;
      zip_code: string;
      city: {
        name: string;
        code: string;
      };
      hamlet: string;
      sub_district: {
        name: string;
        code: string;
      };
      province: {
        name: string;
        code: string;
      };
    };
    owner_id_card: {
      id_card_number: string;
      birth_place: string;
      full_address: string;
      id_card_image: string;
      marital_status: null;
      marital_status_code: null;
      occupation_code: null;
      last_education_name: null;
      last_education_code: null;
      occupation: null;
      birth_date: string;
      mother_birth: null;
      full_name: string;
    };
    phone_number_guarantor: string;
    phone_number_order_maker: string;
    phone_number_owner: string;
    vehicle: {
      brand: {
        uuid: string;
        name: string;
      };
      color_code: string;
      color: string;
      year: string;
      price: number;
      variant: {
        name: string;
        code: string;
        uuid: string;
      };
      model: {
        name: string;
        uuid: string;
      };
      alternative_color: {
        name: string;
        code: string;
      };
    };
    already_in_offer: {
      exists: boolean;
      offer_code: string;
    } | null;
  };
  purchaseOrder: null;
  status: string;
}

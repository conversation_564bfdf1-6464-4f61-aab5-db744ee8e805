import { create } from 'apisauce';
import {
  CatalogueBaseResponse,
  GetAvailability,
  GetAvailabilityMultiCityGroup,
  GetAvailableAreaResponse,
  GetAvailableModelResponse,
  GetAvailableVariant,
  GetDealerDataResponse,
  GetDetailProductResponse,
  Model,
  VariantProduct,
} from '../types/catalaogueTypes';
import { DealCodeResponse } from '../types/offerServiceTypes';
import { AxiosError } from 'axios';

class CatalogueServices {
  private readonly _baseUrl = 'https://zvu1c5uoue.execute-api.ap-southeast-1.amazonaws.com/v1';
  private readonly _baseAxios = create({
    baseURL: this._baseUrl,
    headers: {
      'x-api-key': 'kHhfCAPtSu34ObnDWjI9S4K9nA086VBg2eXchdQW',
    },
  });

  public getAvailableCityGroup(query?: { name?: string; company?: string }) {
    let company = query?.company || 'amarta';
    let queries: any = {};

    if (query?.name) queries.name = query.name; // Jika di isi hanya menampilkan satu city group

    try {
      return this._baseAxios.get<GetAvailableAreaResponse>(`/area/${company}/city_group`, {
        ...queries,
      });
    } catch (e: any) {
      throw new Error(e);
    }
  }

  public getAvailableCityByCityGroup(query: { cityGroup: string; company?: string }) {
    let queries: any = {};

    let company = query?.company || 'amarta';
    queries.name = query.cityGroup; // Jika di isi hanya menampilkan kota mana saja yang tergabung dalam citygroup

    try {
      return this._baseAxios.get<GetAvailableAreaResponse>(`/area/${company}/city`, {
        ...queries,
      });
    } catch (e: any) {
      throw new Error(e);
    }
  }

  public getAvailableModel(params: { city: string; company?: string }) {
    let company = params?.company || 'amarta';
    try {
      return this._baseAxios.get<GetAvailableModelResponse>(
        `products/${company}/city/${params.city}/model`,
      );
    } catch (e: any) {
      throw new Error(e);
    }
  }

  public getAvailableVariant(params: { model: string; area: string; company?: string }) {
    let company = params?.company || 'amarta';
    try {
      return this._baseAxios.get<GetAvailableVariant>(
        `products/${company}/area/${params.area}/variant`,
        {
          model_name: params.model,
        },
      );
    } catch (e: any) {
      throw new Error(e);
    }
  }

  public productVariantArea(params: { area: string; variantCode: string; company?: string }) {
    let company = params?.company || 'amarta';
    try {
      return this._baseAxios.get<CatalogueBaseResponse<VariantProduct[]>>(
        `products/${company}/area/${params.area}/variant`,
        {
          code: params.variantCode,
        },
      );
    } catch (e: any) {
      throw new Error(e);
    }
  }

  public detailProduct(params: { variantCode: string; company?: string }) {
    let company = params?.company || 'amarta';
    try {
      return this._baseAxios.get<GetDetailProductResponse>(`detail-products/${company}`, {
        code: params.variantCode,
      });
    } catch (e: any) {
      throw new Error(e);
    }
  }

  public async getUsedVehicle(params: { area: string; licensePlate?: string; company?: string }) {
    let queries: { license_plate?: string } = {};
    let company = params?.company || 'amarta';
    if (params.licensePlate) queries.license_plate = params.licensePlate;

    const get = await this._baseAxios.get<CatalogueBaseResponse<VariantProduct[]>>(
      `/products/${company}/citygroup/${params.area}/used`,
      {
        ...queries,
      },
    );
    if (!get.ok) {
      throw get;
    } else {
      return get.data;
    }
  }

  public async getVariantByAreaAMH<RETURN = VariantProduct[]>(params: {
    area: string;
    variantCode?: string;
    modelName?: string;
    company?: string;
  }) {
    let queries: { code?: string; model_name?: string } = {};

    if (params.variantCode) queries.code = params.variantCode;
    if (params.modelName) queries.model_name = params.modelName;
    let company = params?.company || 'amarta';

    const get = await this._baseAxios.get<CatalogueBaseResponse<RETURN>>(
      `/products/${company}/citygroup/${params.area}/variant`,
      {
        ...queries,
      },
    );
    if (!get.ok) {
      throw get;
    } else {
      return get.data;
    }
  }

  public async getModelByCityGroup(params: { area: string; company?: string }) {
    let company = params?.company || 'amarta';
    const get = await this._baseAxios.get<CatalogueBaseResponse<Model[]>>(
      `/products/${company}/citygroup/${params.area}/model`,
    );
    if (!get.ok) {
      throw get;
    } else {
      return get.data;
    }
  }

  public async checkAvailability(params: { area: string; company?: string }) {
    let company = params?.company || 'amarta';
    const get = await this._baseAxios.get<GetAvailability>(`/stock/${company}/${params.area}`);
    if (!get.ok) {
      throw get;
    } else {
      return get.data;
    }
  }

  public async checkAvailabilityMultiDealer(query: {
    cityCode: string;
    variantCode: string;
    company?: string;
  }) {
    let company = query?.company || 'amarta';
    const get = await this._baseAxios.get<GetAvailabilityMultiCityGroup>(
      `/stock/multiple-dealer/${company}`,
      {
        city_code: query.cityCode,
        code: query.variantCode,
      },
    );
    if (!get.ok) {
      throw get;
    } else {
      return get.data;
    }
  }

  public async getDealCode(dealCode: string, company?: string) {
    let _company = company || 'amarta';
    const get = await this._baseAxios.get<DealCodeResponse>(`/deal/${_company}/${dealCode}`);
    if (!get.ok) {
      const originalError: AxiosError<any> = get.originalError;
      throw Object.assign(
        new Error(originalError.response?.data?.error?.message),
        originalError.response?.data,
      );
    }

    const { data } = get;
    if (!data) {
      throw new Error('data not found');
    }

    return data.data;
  }

  public getDealerData(company: string) {
    return this._baseAxios.get<GetDealerDataResponse>(`/project/${company}`);
  }
}

export const catalogueServices = new CatalogueServices();

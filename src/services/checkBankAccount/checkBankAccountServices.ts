import apisauce, { ApiErrorResponse } from 'apisauce';

interface BaseResponse {
  statusCode: string;
  data: string;
}

interface CheckBankAccountResponse {
  responseCode: string;
  responseMessage: string;
  referenceNo: string;
  partnerReferenceNo: string;
  beneficiaryAccountName: string;
  beneficiaryAccountNo: string;
}

class CheckBankAccountServices {
  private apisauceInstance = apisauce.create({
    baseURL: 'https://api.robofin.com/util/id',
  });

  public async checkBankAccount(params: { swiftCode: string; accountNo: string }) {
    // IF BANK BCA
    if (params.swiftCode === 'CENAIDJA') {
      return this.getBankAccountBca(params.accountNo);
    } else {
      return this.getBankAccountBySwiftCode(params.accountNo, params.swiftCode);
    }
  }

  private async getBankAccountBca(accountNumber: string) {
    try {
      const post = await this.apisauceInstance.post<BaseResponse>('/bca-acc-inq', {
        accountNo: accountNumber,
      });
      const encode: CheckBankAccountResponse = JSON.parse(post.data!.data);
      return encode;
    } catch (e) {
      const error = e as ApiErrorResponse<any>;
      throw error.data;
    }
  }

  private async getBankAccountBySwiftCode(accountNumber: string, swiftCode: string) {
    try {
      const post = await this.apisauceInstance.post<BaseResponse>('/bank-acc-inq/1', {
        accountNo: accountNumber,
        bankCode: swiftCode,
      });
      const encode: CheckBankAccountResponse = JSON.parse(post.data!.data);
      return encode;
    } catch (e) {
      const error = e as ApiErrorResponse<any>;
      throw error.data;
    }
  }
}

const checkBankAccountServices = new CheckBankAccountServices();

export default checkBankAccountServices;

import axios from 'axios';
import { IResponseAvailableTenorItem, IResponseInstallment } from './types/credit_simulation_types';

export class CreditSimulationServices {
  public static async getRate(params: { cityGroup: string; model: string }) {
    const { cityGroup, model } = params;
    try {
      const get = await axios.get<{ data: IResponseAvailableTenorItem[] }>(
        `https://zvu1c5uoue.execute-api.ap-southeast-1.amazonaws.com/v1/credit-simulation/config/amarta/global/${cityGroup}`,
        {
          params: {
            vehicle_model: model,
          },
          headers: {
            'x-api-key': 'kHhfCAPtSu34ObnDWjI9S4K9nA086VBg2eXchdQW',
          },
        },
      );

      return get.data.data;
    } catch (e: any) {
      throw e;
    }
  }

  public static async simulate(params: {
    price: number;
    down_payment: number;
    rate: number;
    tenor: number;
  }) {
    try {
      const get = await axios.get<{ data: IResponseInstallment }>(
        `https://fh63sf7bye.execute-api.ap-southeast-1.amazonaws.com/v1/credit-simulation/effective-rate`,
        {
          params: {
            ...params,
            insurance: 0,
            payment: 0,
          },
        },
      );
      return get.data.data;
    } catch (e: any) {
      throw e;
    }
  }
}

export const creditSimulation = new CreditSimulationServices();

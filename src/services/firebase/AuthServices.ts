import 'firebase/auth';
import {
  EmailAuthProvider,
  onAuthStateChanged,
  reauthenticateWithCredential,
  signInWithCredential,
  signInWithEmailAndPassword,
  signOut,
  updatePassword,
  User,
} from 'firebase/auth';
import 'firebase/firestore';
import { myAuthVer9 } from '../myFirebase';
import { mainApiServices } from '../MainApiServices';
import { Unsubscribe } from 'firebase/firestore';

class AuthServices {
  private _currentUser: User | null = null;

  public stateChangeLogin?: Unsubscribe = onAuthStateChanged(myAuthVer9, async (user) => {
    if (user) {
      mainApiServices.setToken(await user.getIdToken());
      this._currentUser = user;
    }
  });

  get currentUser(): User | null {
    return this._currentUser;
  }

  public login = async (email: string, password: string) => {
    try {
      const login = await signInWithEmailAndPassword(myAuthVer9, email, password);
      return {
        success: true,
        data: login.user,
      };
    } catch (e: any) {
      let message = '';
      if ('message' in e) {
        message = e.message;
      }

      return {
        success: false,
        message,
      };
    }
  };

  public checkAuth = async (): Promise<User | false> => {
    await new Promise((r) => {
      setTimeout((args) => {
        r(true);
      }, 1000);
    });

    if (this._currentUser) {
      return this._currentUser;
    } else {
      return false;
    }
  };

  public changePassword = async (
    oldPassword: string,
    newPassword: string,
  ): Promise<{
    success: boolean;
    message?: string;
  }> => {
    if (this._currentUser) {
      try {
        const credential = EmailAuthProvider.credential(this._currentUser.email!, oldPassword);
        const sign = await signInWithCredential(myAuthVer9, credential);
        await reauthenticateWithCredential(sign.user, credential);

        await updatePassword(this._currentUser, newPassword);
        return {
          success: true,
        };
      } catch (e: any) {
        let message = '';
        if ('message' in e) {
          message = e.message;
        }
        return {
          success: false,
          message,
        };
      }
    } else {
      return {
        success: false,
        message: "There's no authenticated user",
      };
    }
  };

  public signOut = async () => {
    try {
      await signOut(myAuthVer9);
      this._currentUser = null;
      return true;
    } catch (e: any) {
      return false;
    }
  };
}

const authService = new AuthServices();

export default authService;

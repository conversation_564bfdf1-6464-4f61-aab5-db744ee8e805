import 'firebase/firestore';
import ChatRoomEntity from '../../entities/ChatRoomEntity';
import firestore, {
  collection,
  getDocs,
  limit,
  orderBy,
  query,
  Query,
  where,
} from 'firebase/firestore';
import { firestoreIdealVer9 } from '../myFirebase';
import MessageEntity from '../../entities/MessageEntity';
import { ChatRoomStatesDepartment, ChatRoomStatesLabel } from '../../redux/types/recent-chat-types';

/**
 * Interface for chat room query filter parameters
 */
interface ChatRoomQueryFilters {
  label?: ChatRoomStatesLabel;
  department?: ChatRoomStatesDepartment;
  phoneNumber?: string;
  limit?: number;
}

/**
 * Service class for handling Firestore database operations related to chat rooms
 * and messages.
 */
class FirestoreServices {
  /**
   * Queries chat rooms with various filtering options
   * @param params - Query parameters containing project reference and filters
   * @param params.projectRef - Firestore document reference for the project
   * @param params.filters - Optional filters for the query
   * @returns Query<ChatRoomEntity> - Firestore query object
   */
  public queryGetChatRoomVer2(params: {
    projectRef: firestore.DocumentReference;
    filters?: ChatRoomQueryFilters;
  }): Query<ChatRoomEntity> {
    // Initialize chat room collection with converter
    const chatRoomCollection = collection(
      firestoreIdealVer9,
      params.projectRef.path,
      'chat_rooms',
    ).withConverter(ChatRoomEntity.converter);

    // Handle phone number specific queries
    if (params.filters?.phoneNumber) {
      return query(
        chatRoomCollection,
        where('contacts', 'array-contains', params.filters.phoneNumber),
      );
    }

    // Build query with default sorting
    let baseQuery = query(chatRoomCollection, orderBy('recent_chat.timestamp', 'desc'));

    // Apply limit filter if specified
    if (params.filters?.limit) {
      baseQuery = query(baseQuery, limit(params.filters.limit));
    }

    // Apply label filter
    if (params.filters?.label) {
      baseQuery = this.applyLabelFilter(baseQuery, params.filters.label);
    }

    // Apply department filter
    if (params.filters?.department) {
      baseQuery = this.applyDepartmentFilter(baseQuery, params.filters.department);
    }

    return baseQuery;
  }

  /**
   * Applies label filter to the query
   * @private
   * @param baseQuery - Base query to extend
   * @param label - Label filter to apply
   * @returns Query with label filter applied
   */
  private applyLabelFilter(
    baseQuery: Query<ChatRoomEntity>,
    label: ChatRoomStatesLabel,
  ): Query<ChatRoomEntity> {
    if (label === 'ALL_LABELS') {
      return baseQuery;
    }

    if (label === 'NO_LABEL') {
      return query(baseQuery, where('label', '==', null));
    }

    return query(baseQuery, where('label', '==', label.ref));
  }

  /**
   * Applies department filter to the query
   * @private
   * @param baseQuery - Base query to extend
   * @param department - Department filter to apply
   * @returns Query with department filter applied
   */
  private applyDepartmentFilter(
    baseQuery: Query<ChatRoomEntity>,
    department: ChatRoomStatesDepartment,
  ): Query<ChatRoomEntity> {
    if (department === 'ALL_DEPARTMENTS') {
      return baseQuery;
    }

    if (department === 'NO_DEPARTMENT') {
      return query(baseQuery, where('doc_department', '==', null));
    }

    return query(baseQuery, where('doc_department', '==', department.ref));
  }

  /**
   * Retrieves room conversations for a specific chat room
   * @param chatRoomEntity - Chat room entity to get conversations from
   * @returns Object containing methods and references for accessing conversations
   */
  public roomConversations(chatRoomEntity: ChatRoomEntity) {
    const chatCollectionRef = collection(chatRoomEntity.ref, 'chats');
    const conversationQuery = query(
      chatCollectionRef.withConverter(MessageEntity.converter),
      orderBy('message.unixtime', 'asc'),
    );

    return {
      get: () => getDocs(conversationQuery),
      query: conversationQuery,
      collectionRef: chatCollectionRef,
    };
  }
}

// Single instance of FirestoreServices
const firestoreServices = new FirestoreServices();
export default firestoreServices;

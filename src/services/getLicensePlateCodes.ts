import apisauce from 'apisauce';
import { ILicensePlateRegistrationCode } from '../Components/LicensePlate/LicensePlateSelectCityCode';

const getLicensePlateCodes = async () => {
  const base = apisauce.create({
    baseURL: 'https://mospnswbue3nmegqxkv4ymtube0ehbof.lambda-url.ap-southeast-1.on.aws',
  });

  const get = await base.get<ILicensePlateRegistrationCode[]>('/license-plates');

  if (get.ok) {
    return get.data || [];
  } else {
    throw get;
  }
};

export default getLicensePlateCodes;

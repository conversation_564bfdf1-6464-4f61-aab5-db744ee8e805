import apisauce, { create } from 'apisauce';
import {
  ResponseBodyBbn,
  ResponseGetOrderPolicy,
  ResponseGetShipmentCost,
} from './logisticService.types';

class LogisticService {
  private base = create({
    baseURL: 'https://v0b1z99035.execute-api.ap-southeast-1.amazonaws.com/v1',
    headers: {
      'x-api-key': 'rXTFzVZPH45GwqKvybxJg98cOQ0deR7z3vlIo5Aw',
    },
  });

  async getShippingCost(params: { dealer_code: string; city_code: string }) {
    const get = await this.base.get<{ data: ResponseGetShipmentCost }>('/config/shipping-cost', {
      ...params,
    });

    if (get.ok) {
      return get.data!;
    } else {
      throw get.originalError;
    }
  }

  async getBbnByEngineNumber(engineNumber: string) {
    const get = await this.base.get<ResponseBodyBbn>('public/order/assign-service-bureau', {
      vehicle_engine_number: engineNumber,
    });

    if (get.ok) {
      return get.data!;
    } else {
      throw get;
    }
  }

  async getOrderPolicy(params: { vehicle_model: string }) {
    const get = await this.base.get<{ data: ResponseGetOrderPolicy }>(
      '/config/order-policy/amarta',
      {
        ...params,
      },
    );

    if (get.ok) {
      return get.data!;
    } else {
      throw get.originalError;
    }
  }
}

const logisticService = new LogisticService();

export default logisticService;

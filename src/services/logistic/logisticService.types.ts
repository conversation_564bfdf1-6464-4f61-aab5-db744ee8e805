export interface ResponseGetShipmentCost {
  city_code: string;
  city_name: string;
  pelwil: number;
  pdi: number;
  bpkon: number;
  bpdead: number;
  bpdeab: number;
  bureau_fee: number;
  tol: number;
}

export interface ResponseBodyBbn {
  success: {
    total_data: number;
    data: OrderDataBbn[];
  };
}

export interface OrderDataBbn {
  order_code: string;
  customer_doc_name: string;
  service_bureau_code: string;
  service_bureau_name: string;
  service_bureau_account_number: string;
  invoice_number: string;
  notice_cost: number;
  process_cost: number;
  progressive_cost: number;
  service_cost: number;
  others_cost: number;
  transfer_title_cost: number;
  penalty: number;
  master_notice_cost: number;
  master_process_cost: number;
  transfer_title_status: string;
  transfer_title_date: string;
  vehicle_engine_number: string;
  vehicle_chassis_number: string;
  vehicle_variant_name: string;
  vehicle_notice_number: string;
  vehicle_notice_image: string;
  vehicle_notice_entry_date: string;
  vehicle_notice_submission_date: string;
  vehicle_license_plate: string;
  vehicle_license_entry_date: string;
  vehicle_license_submission_date: string;
  vehicle_certificate_of_ownership_number: string;
  vehicle_certificate_of_ownership_entry_date: string;
  vehicle_certificate_of_ownership_submission_date: string;
  vehicle_certificate_of_ownership_image: string[];
  vehicle_registration_certificate_number: string;
  vehicle_registration_certificate_image: string;
  vehicle_registration_certificate_entry_date: string;
  vehicle_registration_certificate_submission_date: string;
  paid_notice_cost: boolean;
  paid_notice_cost_date: string;
  paid_process_cost: boolean;
  paid_process_cost_date: string;
  paid_bill_pkb: boolean;
  daya_id_order: string;
  daya_invoice_stnk: string;
  daya_invoice_date: string;
  daya_bast_date: string;
  daya_receiver_name: string;
  daya_input_time: string;
  flag_has_outstanding: string;
  payment_approved: string;
  create_date: string;
  handover_delivery_date: string;
  order_closing_date: string;
  update_date: string;
}

export interface ResponseGetOrderPolicy {
  data: {
    vehicle_model: string;
    cash_type: string;
    cash_book_fee: number;
    credit_type: string;
    credit_book_fee: number;
    indent_code: boolean;
  };
}

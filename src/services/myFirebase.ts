import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';

const firebaseConfigIdeal = {
  apiKey: 'AIzaSyC9DHA34xIktbUNF-Oq6Iw6wpSAm-4d_Rk',
  authDomain: 'ideal-trimitra.firebaseapp.com',
  databaseURL: 'https://ideal-trimitra-default-rtdb.firebaseio.com',
  projectId: 'ideal-trimitra',
  storageBucket: 'ideal-trimitra.appspot.com',
  messagingSenderId: '153502006543',
  appId: '1:153502006543:web:04c2fb769754cf730a127a',
  measurementId: 'G-57XTYX6RDV',
};

const firebaseConfigB2B = {
  apiKey: 'AIzaSyCEivx6movF0B1N6qJJnIpVz9F6o0QKMwE',
  authDomain: 'amh-b2b.firebaseapp.com',
  projectId: 'amh-b2b',
  storageBucket: 'amh-b2b.appspot.com',
  messagingSenderId: '1068414348304',
  appId: '1:1068414348304:web:5c9a5840597ad0c17d6e95',
  measurementId: 'G-T1F05NYF49',
};

const firebaseAppIdeal = initializeApp(firebaseConfigIdeal, {
  name: 'ideal',
});
export const firestoreIdealVer9 = getFirestore(firebaseAppIdeal);

const firebaseAppB2B = initializeApp(firebaseConfigB2B, {
  name: 'b2b',
});
export const firestoreB2BVer9 = getFirestore(firebaseAppB2B);

export const myAuthVer9 = getAuth(firebaseAppIdeal);

import { create } from 'apisauce';
import { IOcrResponseSuccessGetIdCardData } from './ocrService.types';

class OcrServices {
  private base = create({
    baseURL:
      'https://asia-southeast1-autotrimitra.cloudfunctions.net/api-cdn-trimitra-biz__ocr-document',
    headers: {
      SecretKey: 'Mn3zrubT3AM5NA8UFXZO6BgV6Rd69YrH',
    },
  });

  public getDataFromIdCard = (url: string) => {
    return this.base.post<{ data: IOcrResponseSuccessGetIdCardData }>('/ocr/id-card', {
      image_url: url,
    });
  };
}

const ocrServices = new OcrServices();
export default ocrServices;

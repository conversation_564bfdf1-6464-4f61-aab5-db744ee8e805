import { create } from 'apisauce';
import {
  ICreateOfferParams,
  ICreateOfferSuccessResponse,
  IGetOfferSuccessResponse,
} from './types/offerServiceTypes';
import {
  ErrorResponseUpdateDataToOtodis,
  ParamsUpdateDataToOtodisCreateBill,
  ParamsUpdateDataToOtodisIdCard,
  ParamsUpdateDataToOtodisPromoCode,
  ParamsUpdateDataToOtodisShippingAddress,
} from './types/offerServices.types';

class OfferServices {
  private key = '9bekwohY878MgiMsRL0Wk2Xsgv4QsxtW4jEIuBqb';
  private basicKeyAuth = 'cHVibGljOnB1YmxpYy03ODM3aGRJSzhlNjdxOGFKNzZmZDllSks=';
  private baseApi = create({
    baseURL: 'https://42cjbxpaa8.execute-api.ap-southeast-1.amazonaws.com/v1',
    headers: {
      'x-api-key': this.key,
      Authorization: 'Basic ' + this.basicKeyAuth,
    },
  });

  public async createOffer(params: ICreateOfferParams) {
    const create = await this.baseApi.post<ICreateOfferSuccessResponse>('/create-offer', {
      ...params,
    });

    if (create.ok) {
      return create.data;
    } else {
      throw create.originalError;
    }
  }

  public async getDataByOfferCode(params: string) {
    const get = await this.baseApi.get<IGetOfferSuccessResponse>(
      `/offer/public/${params}?company=amarta`,
    );
    if (get.ok) {
      return get.data;
    } else {
      throw get.originalError;
    }
  }

  public async getDataByPoNumber(poNumber: string) {
    const get = await this.baseApi.get<IGetOfferSuccessResponse>(
      `/offer/public/${poNumber}?company=amarta&po_search=true`,
    );
    if (get.ok) {
      return get.data;
    } else {
      throw get.originalError;
    }
  }

  public async updateIdCard(params: ParamsUpdateDataToOtodisIdCard) {
    const update = await this.baseApi.put<{ data: string }, ErrorResponseUpdateDataToOtodis>(
      `/offer/public`,
      {
        ...params,
      },
    );

    if (update.ok) {
      return update.data;
    } else {
      throw update.originalError;
    }
  }

  public async updateShippingAddress(params: ParamsUpdateDataToOtodisShippingAddress) {
    const update = await this.baseApi.put<{ data: string }, ErrorResponseUpdateDataToOtodis>(
      `/offer/public`,
      {
        ...params,
      },
    );

    if (update.ok) {
      return update.data;
    } else {
      throw update.originalError;
    }
  }

  public async updatePromoCode(params: ParamsUpdateDataToOtodisPromoCode) {
    const update = await this.baseApi.put<{ data: string }, ErrorResponseUpdateDataToOtodis>(
      `/offer/public`,
      {
        ...params,
      },
    );

    if (update.ok) {
      return update.data;
    } else {
      throw update.originalError;
    }
  }

  public async createBill(params: ParamsUpdateDataToOtodisCreateBill) {
    const update = await this.baseApi.put<{ data: any }, ErrorResponseUpdateDataToOtodis>(
      `/offer/public`,
      {
        ...params,
      },
    );

    if (update.ok) {
      return update.data;
    } else {
      throw update.originalError;
    }
  }
}

export const offerServices = new OfferServices();

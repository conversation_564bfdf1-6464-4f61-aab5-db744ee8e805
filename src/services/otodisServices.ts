import apisauce, { create } from 'apisauce';
import {
  OtodisCheckStockParams,
  OtodisCheckStockResponseLite,
  OtodisDealer,
} from './types/otodisServiceTypes';

class OtodisServices {
  private baseApi = create({
    baseURL: 'https://c1dbgj7gv9.execute-api.ap-southeast-1.amazonaws.com/otodis',
  });

  public async getDealer() {
    const getDealer = await this.baseApi.get<{ data: OtodisDealer[] }>('/dealer');

    if (getDealer.ok) {
      return getDealer.data;
    } else {
      throw getDealer.originalError;
    }
  }

  public async getStock(params: OtodisCheckStockParams) {
    const getDealer = await this.baseApi.get<{
      data: OtodisCheckStockResponseLite[];
    }>('/stock', { ...params });

    if (getDealer.ok) {
      return getDealer.data;
    } else {
      throw getDealer.originalError;
    }
  }
}

export const otodisServices = new OtodisServices();

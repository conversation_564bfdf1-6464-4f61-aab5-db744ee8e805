import apisauce from 'apisauce';

const paymentReceiptServices = async (params: {
  offerCode: string;
  company: string;
  adminName: string;
}) => {
  const baseUrl =
    'https://asia-southeast1-autotrimitra.cloudfunctions.net/api-offer-otodis--create-pdf';
  const base = apisauce.create({
    baseURL: baseUrl,
  });

  const get = await base.get('/spk/generate-payment', {
    company: params.company,
    offer_code: params.offerCode,
    admin_name: params.adminName,
    force_download: true,
  });

  if (get.ok) {
    return `${baseUrl}/spk/generate-payment?company=${params.company}&offer_code=${params.offerCode}&admin_name=${params.adminName}&force_download=true`;
  } else {
    return false;
  }
};

export default paymentReceiptServices;

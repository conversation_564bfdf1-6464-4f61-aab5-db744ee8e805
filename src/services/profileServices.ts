import { create } from 'apisauce';
import { TProfileResponse } from './types/profile_service_types';

class ProfileServices {
  private profileInstance = create({
    baseURL: 'https://xqfrf2v5m0.execute-api.ap-southeast-1.amazonaws.com/v1',
    headers: {
      'X-api-key': 'lgDRR2ltg45JOyuvcWxWe8NRdNnJP4YS30xonWNh',
    },
  });

  public async getProfile(phoneNumber: string) {
    const get = await this.profileInstance.get<TProfileResponse[]>('/customer/' + phoneNumber);

    if (!get.ok) {
      throw get;
    } else {
      return get.data;
    }
  }
}

export const profileServices = new ProfileServices();

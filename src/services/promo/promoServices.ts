import apisauce, { create } from 'apisauce';
import { IGetPromoParams, IPromoCode } from '../types/promoServiceTypes';

class PromoServices {
  private key = 'dIIuNWGdbC4cV96OjqaXR24bYGJLumWx1RI8moty';
  private baseApi = create({
    baseURL: ' https://80pj23grqd.execute-api.ap-southeast-1.amazonaws.com/v1',
    headers: {
      'x-api-key': this.key,
    },
  });

  public async getPromo(params: Partial<IGetPromoParams>) {
    const payload = {
      promo_type: 'new_vehicle',
      city_group: params.city_group,
      purchase_method: params.purchase_method,
      vehicle_brand: 'honda',
      vehicle_model: params.vehicle_model,
      vehicle_variant: params.vehicle_variant,
      tenor: params.tenor,
      vehicle_variant_color: params.vehicle_variant_color,
      alternative_color: 'SAME_AS_ORDER',
      transaction_amount: params.transaction_amount,
      finco_code: params.finco_code,
      allow_agent: params.allow_agent,
    };
    const get = await this.baseApi.get<{ data: IPromoCode[] }>('/promo/amarta', payload);
    if (get.ok) {
      return get.data;
    } else {
      throw get.originalError.response?.data;
    }
  }

  public async getDetailPromo(promoCode: string, params: IGetPromoParams) {
    const payload = { ...params };
    const get = await this.baseApi.get<{ data: IPromoCode }>('/promo/amarta/' + promoCode, payload);
    if (get.ok) {
      return get.data;
    } else {
      throw get.originalError.response?.data;
    }
  }
}

export const promoService = new PromoServices();

import { CommonRegionProps } from '../../Components/SelectRegion/SelectRegion';

export interface ResponseBodyGetPlan {
  success: boolean;
  data: Package[];
  meta: null | Record<string, unknown>;
}

export interface Package {
  code: string;
  condition: string[];
  category: string[];
  active: boolean;
  disable: boolean;
  name: string;
  caption: string;
  information: string[];
  days: number;
  price_type: string;
  ads_option: AdsOption[];
}

export interface AdsOption {
  ads: number;
  price: number;
}

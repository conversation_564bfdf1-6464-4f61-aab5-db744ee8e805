import apisauce from 'apisauce';
import { ResponseBodyGetPlan } from './trimobi.getPlan.response.types';

class TrimobiServices {
  private baseUrl = 'https://zvu1c5uoue.execute-api.ap-southeast-1.amazonaws.com/v1';
  private apiKey = '3YLfRdqaaPaASF0MzmqH89U1n2SNx5K92kkV6rf8';

  private baseApiSauce = apisauce.create({
    baseURL: this.baseUrl,
    headers: {
      'Content-Type': 'application/json',
      'x-api-key': this.apiKey,
    },
  });

  async getPlan(params: {
    company: string;
    category?: string;
    condition: string;
    internal: boolean;
  }) {
    const response = await this.baseApiSauce.get<ResponseBodyGetPlan>(`/plan`, {
      ...params,
    });
    return response.data;
  }
}

const trimobiServices = new TrimobiServices();

export default trimobiServices;

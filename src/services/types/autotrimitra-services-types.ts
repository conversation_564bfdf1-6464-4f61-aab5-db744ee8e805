export interface AutotrimitraBaseResponse<DATA = any> {
  data: DATA;
}

export interface AutotrimitrayBaseError {
  message: string;
  code: number;
}

export interface Fuel {
  gasoline: boolean;
}

export interface Transmission {
  manual: boolean;
  automatic?: boolean;
}

export interface IBrandModel {
  brand_uuid: string;
  brand_name: string;
}

export interface IVehicleModel {
  model_uuid: string;
  model_name: string;
  brand_uuid: string;
  brand_name: string;
  category: string;
  class: string;
  engine_capacity: number[];
  fuel: Fuel;
  transmission: Transmission;
  url_image: string;
  url_info: string;
  production_year: number[];
}

export type IModelRequired = Pick<IVehicleModel, 'model_uuid' | 'model_name'>;

export interface IVariant {
  variant_name: string;
  variant_uuid: string;
  distributor_code: string;
  code: string;
  code_vin: string;
  color: string[];
  price?: number;
  engine_capacity?: number;
  engine_code?: any;
  engine_cylinder?: number;
  engine_hp?: number;
  engine_torque?: number;
  engine_valve?: number;
  engine_valve_config: string;
  fuel: string;
  fuel_supply_system: string;
  transmission: string;
  transmission_speed?: any;
  transmission_type?: any;
  url_image: string;
  url_info?: any;
  brand_uuid: string;
  brand_name: string;
  model_uuid: string;
  model_name: string;
  category: string;
  class: string;
  first_production_year?: number;
  latest_production_year?: number;
  generation?: number;
}

export interface CatalogueBaseResponse<T> {
  data: T;
}

export interface CatalogueCity {
  name: string;
  code: string;
}

export interface Area {
  area: string;
  city_name: string;
  city_code: string;
  city_group: string;
  province_name: string;
  province_code: string;
}

export interface Model {
  model_name: string;
  brand_name: string;
  class: string;
  price: number;
  price_max: number;
  price_min: number;
  url_image: string;
  last_update: string;
}

export interface VariantProduct {
  variant_code: string;
  variant_name: string;
  color_code: string;
  color_name: string;
  model_name: string;
  class: string;
  price: number;
  price_strikethrough: number;
  url_image: string;
  url_video: string;
  url_brochure: string;
  url_specification: string;
  last_update: string;
  active: boolean;
  registration_year?: string;
  license_plate?: string;
}

export interface MediaDetailItem {
  caption: string;
  description: '';
  url_thumbnail: string;
  url: string;
}

export interface DetailProduct {
  entity_type: string;
  variant_code: string;
  variant_name: string;
  model_category: string;
  model_class: string;
  model_name: string;
  class: string;
  detail_image: MediaDetailItem[];
  detail_social_media: [];
  detail_video: MediaDetailItem[];
  detail_document: MediaDetailItem[];
  active: true;
}

export interface IStock {
  color: {
    [key: string]: number;
  }[];
  stock_qty: number;
  variant_name: string;
  model_name: string;
  custom_availability: null;
}

interface StockColor {
  [key: string]: number | undefined;
}

interface StockData {
  color: StockColor;
  stock_qty: number;
  variant_name: string;
  model_name: string;
  custom_availability: any; // You might want to replace 'any' with the appropriate type if known
}

interface StockMeta {
  availability_limit: number;
}

interface Stock {
  data: StockData;
  meta: StockMeta;
}

export interface GetAvailabilityMultiCityGroupResponse {
  company: string;
  area: string;
  city_group: string;
  stock: Stock;
  shipment_cost: number;
}

// Interface untuk koordinat lokasi dealer
interface ICoordinate {
  lng: string;
  lat: string;
}

// Interface untuk jam operasional harian
interface IOperationalHours {
  open: string;
  close: string;
}

// Interface untuk jam operasional mingguan
interface IWeeklyOperationalHours {
  mon?: IOperationalHours;
  tue?: IOperationalHours;
  wed?: IOperationalHours;
  thu?: IOperationalHours;
  fri?: IOperationalHours;
  sat?: IOperationalHours;
  sun?: IOperationalHours;
}

// Interface untuk media sosial
interface ISocialMedia {
  x?: string;
  youtube?: string;
  instagram?: string;
  tiktok?: string;
  facebook?: string;
}

// Interface untuk alamat dealer individual
interface IDealerAddress {
  whatsapp: string;
  address: string;
  coordinate: ICoordinate;
  name: string;
  operational_hours: IWeeklyOperationalHours;
  phone_number: string;
  social_media: ISocialMedia;
}

// Interface untuk halaman utama
interface IHomePage {
  cover: string;
}

// Interface utama untuk response data dealer
export interface IGetDealerDataResponse {
  code: string;
  name: string;
  legal_name: string;
  logo: string;
  description: string;
  address: string[];
  addresses: IDealerAddress[];
  phone_number: string;
  whatsapp: string;
  social_media: ISocialMedia;
  default_city_group: string;
  home_page: IHomePage;
}

export type GetAvailableAreaResponse = CatalogueBaseResponse<Area[]>;
export type GetDetailProductResponse = CatalogueBaseResponse<DetailProduct[]>;
export type GetAvailableModelResponse = CatalogueBaseResponse<Model[]>;
export type GetAvailableVariant = CatalogueBaseResponse<VariantProduct[]>;
export type GetAvailability = CatalogueBaseResponse<{ [key: string]: IStock }>;
export type GetAvailabilityMultiCityGroup = CatalogueBaseResponse<
  GetAvailabilityMultiCityGroupResponse[]
>;
export type GetDealerDataResponse = CatalogueBaseResponse<IGetDealerDataResponse>;

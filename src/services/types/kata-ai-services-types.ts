import {
  EMessageContext,
  IFileContext,
  TextContext,
} from '../../entities/types/message-entities-types';

export interface ILoginResponse {
  access_token: string;
  token_type: 'bearer';
  expires_in: number;
}

export interface ISendMessageParams {
  to: string;
  text: string;
  file?: File;
}

export interface ISendMessageSuccess {
  messages: {
    id: string;
  }[];
  meta: {
    api_status: 'stable';
    version: '2.29.3';
  };
}

export interface IUploadMediaSuccess {
  media: {
    id: string;
  }[];
}

export interface ISendMessage {
  to: string;
  recipient_type: 'individual' | 'group';
  type: EMessageContext;
  preview_url: boolean;
  text?: TextContext;
  image?: Pick<IFileContext, 'caption' | 'id' | 'mime_type' | 'link'>;
}

export interface IKataAiServiceReturn<SUCCESS = any, FAILED = any> {
  success: boolean;
  data?: SUCCESS;
  failed?: FAILED;
}

export interface IKataAiServiceDataError {
  code: string;
  message: string;

  [key: string]: string;
}

export interface IRecallParams {
  command: () => Promise<any>;
}

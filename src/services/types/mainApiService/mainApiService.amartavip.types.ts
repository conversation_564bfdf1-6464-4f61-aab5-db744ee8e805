export interface AdsPackage {
  source: string;
  code: string;
  ads_quantity: number;
  ads_used: number;
  created_time: string;
  days: number;
  price_type: string;
  condition: string[];
  category: string[];
  name: string;
  caption: string;
}

export interface PlanCheckoutStatusItem {
  transaction_id: string;
  bill_id: string;
  bill_reference_id: string;
  bill_amount: number;
  bill_data: string;
  user_uid: string;
  type: string;
  payload: {
    source: string;
    code: string;
    notes: string;
    ads_option_qty: number;
    payment_issuer: string;
    payment_method: string;
  };
  created_time: string;
  status: string;
}

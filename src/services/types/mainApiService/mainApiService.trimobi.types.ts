interface Location {
  code: string;
  name: string;
}

interface Brand {
  name: string;
  uuid: string;
}

interface Model {
  uuid: string;
  category: string;
  name: string;
}

interface Image {
  uuid: string;
  url: string;
}

export interface AdListingRequest {
  planCode: string;
  vehicleType: string;
  fullName: string;
  phoneNumber: string;
  province: Location;
  city: Location;
  district: Location;
  subDistrict: Location;
  address: string;
  postalCode: string;
  brand: Brand;
  model: Model;
  variant: string;
  licensePlate: string;
  fuelType: string;
  transmission: string;
  engineCapacity: string;
  year: string;
  mileage: string;
  completeVehicleDocuments: boolean;
  activeVehicleDocuments: boolean;
  ownerNameMatchDocuments: boolean;
  allowBuyerPriceOffer: boolean;
  title: string;
  description: string;
  price: number;
  images: Image[];
}

export interface TrimobiBuyNewPlanRequest {
  projectId: string;
  planCode: string;
  adsQuantity: number;
  customerPhoneNumber: string;
  vehicleType: string;
}

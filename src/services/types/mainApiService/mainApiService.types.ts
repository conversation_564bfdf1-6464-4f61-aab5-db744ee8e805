import {
  EFamilyEducation,
  EFamilyRelation,
} from '../../../entities/types/family-register-entity-types';
import {
  IConversationFlowMessage,
  IConversationFlowStartAt,
} from '../../../redux/add-conversation-flow/addConversationFlow.slice';
import { ILicensePlateRegistrationCode } from '../../../Components/LicensePlate/LicensePlateSelectCityCode';

export interface IMessageSendV2Params {
  roomPath: string;
  phoneNumber: string;
  adminSessionPath: string;
  text?: string;
  media?: File;
  fileUrl?: string;
  fileType?: string;
  filename?: string;
  fileMediaId?: string;
}

export interface IUpdateClientIdCardParams {
  idCardNumber: string;
  name: string;
  placeOfBirth: string;
  dateOfBirth: string | Date;
  gender: 'male' | 'female';
  address: string;
  occupation: string;
  maritalStatus: string;
  guarantorIdCardNumber: string;
  guarantorIdCardName: string;
}

export interface IFreeLeadsParams {
  externalId: string;
  area: string;
  title: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  email: string;
  provinceName: string;
  provinceCode: string;
  cityName: string;
  cityCode: string;
  vehicleUsage: 'individual' | 'shared' | 'corporate' | null;
  paymentPlan: 'cash' | 'credit' | null;
  hasVehicleLoan: boolean;
  vehicleOptions:
    | {
        brand: {
          name: string;
        };
        model: {
          name: string;
        };
        variant: {
          code: string;
          name: string;
        };
        color: {
          code: string;
          name: string;
        };
      }[]
    | null;
  organization: string;
  source: string;
  purchasePlan: 'firstVehicle' | 'vehicleReplacement' | 'vehicleAddition' | null;
  nextTotalVehicleOwnerShip: string;

  price: number | null;
  notes: string;

  idCard_number: string | null;
  driverLicense_number: string | null;

  ideal: {
    chat_room_ref: string | null;
  } | null;
}

export interface IUpdateClientFamilyRegister {
  familyRegisterNumber: string;
  lastEducation: EFamilyEducation | null;
  statusOfIntraGroupRelation: EFamilyRelation | null;
}

export interface ISetImageDocumentParams {
  type: 'idCard' | 'familyRegistration' | 'orderMakerIdCard';
  file: File | Blob;
}

export interface ILabelDataResponse {
  ref: string;
  name: string;
  active: boolean;
  description: null | string;
}

export interface BaseSuccessResponse<T> {
  success: {
    type: string;
    data: T;
  };
}

export interface PriceListRow {
  downPayment: number;
  tenor: number;
  installment: number;
  tenorDiscount: number;
  installmentDiscount: number;
}

export interface ICreatePriceListParams {
  title: string;
  admin: string;
  source: string;
  subTitle: string;
  phoneNumber: string;
  expiredAt: Date | null;
  variantCode: string;
  cityGroup: string;
  priceListNormal: PriceListRow[];
  priceListHighLight: PriceListRow[];
}

interface BigQueryRecord {
  down_payment?: number | null;
  tenor?: number | null;
  installment?: number | null;
  discount_down_payment?: number | null;
  discount_tenor?: number | null;
  discount_installment?: number | null;
}

export interface ILogSendPriceListParams {
  event: string;
  phone_number?: string | null;
  name?: string | null;
  city_group?: string | null;
  vehicle?: {
    model_name?: string | null;
    variant_name?: string | null;
    variant_code?: string | null;
    variant_color_code?: string | null;
    variant_color_name?: string | null;
  } | null;
  discount_promo?: number | null;
  otr?: number | null;
  pricelists?: BigQueryRecord[] | null;
  admin_id?: string | null;
  credit?: {
    offer_code?: string | null;
    down_payment?: number | null;
    discount_down_payment?: number | null;
    tenor?: number | null;
    discount_tenor?: number | null;
    installment?: number | null;
    discount_installment?: number | null;
  } | null;
}

export interface IUpdateCustomerDecisionParams {
  clientRefPath: string;
  finalResult: string;
  followUp: {
    date: string;
    time: string;
    message: {
      template: string;
      templateRef: string;
      variables: string[];
    };
  };
}

export interface IAddNewAdminParams {
  name: string;
  email: string;
  password: string;
  departmentPath: string | null;
  projectPath: string;
  role: string;
}

export interface IAdminUpdateActiveStatusParams {
  status: 'enable' | 'disable';
  adminPath: string;
}

export interface AddConversationFlowParams {
  topicName: string;
  startAt: IConversationFlowStartAt;
  messages: IConversationFlowMessage[];
  referralSourceId?: {
    dealCode: string;
    sourceId: string;
  };
  projectId: string;
}

export interface UpdateConversationFlowParams {
  pathConversationFlow: string;
  messages: IConversationFlowMessage[];
  active: boolean;
  referralSourceId?: {
    dealCode: string;
    sourceId: string;
  };
  projectId: string;
}

export interface IAddNewOwnedVehicleParams {
  _clientPath: string;

  brandUuid: string;
  brandName: string;
  modelUuid: string;
  modelName: string;
  modelCategory: string;
  variantFreeText: string;
  mileage: string;
  year: string;

  licensePlateCode: string;
  licensePlateNumber: string;
  licensePlateSeries: string;
  licensePlateCodeDetails: ILicensePlateRegistrationCode;
}

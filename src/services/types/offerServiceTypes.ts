export interface ICreateOfferIdCard {
  birth_date: string | Date;
  birth_place: string;
  full_address: string;
  full_name: string;
  id_card_image: string;
  id_card_number: string;
  marital_status: string;
  occupation_code: string;
  occupation?: string;
  sex?: string;
}

export interface ICreateOfferAddress {
  full_address: string;
  province_name: string;
  province_code: string;
  city_name: string;
  city_code: string;
  district_name: string;
  district_code: string;
  sub_district_name: string;
  sub_district_code: string;
  zip_code: string;
}

export interface ICreateOfferParams {
  company: 'amarta';
  source: string;
  purchase_method: string;
  area: string;
  take_vehicle_in_dealer: boolean;
  agent_code: string;
  phone_number_owner: string;
  phone_number_wa_available_owner?: boolean;
  phone_number_alternative_owner?: string;
  phone_number_order_maker: string;
  phone_number_wa_available_order_maker?: boolean;
  phone_number_alternative_order_maker?: string;
  home_ownership_status?: string;
  home_ownership_status_code: string;
  promo_code?: string;
  notes: string;
  offer_broker?: boolean;
  id_card_owner: ICreateOfferIdCard;
  id_card_order_maker: ICreateOfferIdCard;
  id_card_guarantor: Pick<ICreateOfferIdCard, 'full_name' | 'id_card_number'>;
  family_register_owner: {
    family_register_image: string;
    family_register_number: string;
    last_education_owner?: string;
    last_education_order_maker?: string;
    relation_owner?: string;
    relation_order_maker?: string;
  };
  address_owner: ICreateOfferAddress;
  address_order_maker: ICreateOfferAddress;
  address_shipping: ICreateOfferAddress;
  vehicle: {
    brand_name: string;
    brand_uuid: string;
    model_name: string;
    model_uuid: string;
    variant_name: string;
    variant_uuid: string;
    variant_code: string;
    color: string;
    color_code: string;
  };
  mediator_code: string;
  mediator_name: string;
  indent_code?: string;
  indent_date?: string;
}

export interface ICreateOfferSuccessResponse {
  data: {
    offer_code: string;
    bill_code: string;
  };
}

export interface IGetOfferData {
  transaction_code: string;
  transaction_time: string;
  purchase_method: string;
  offer_status: string;
  qc_status: boolean;
  area: string;
  contacts: {
    phone_number_owner: string;
    phone_number_alternative_owner: string | null;
    phone_number_order_maker: string | null;
    phone_number_alternative_order_maker: string | null;
    phone_number_guarantor: string | null;
  };
  id_card_order_maker: {
    id_card_image: string;
    marital_status: string;
    full_name: string;
    occupation: string;
    birth_date: string;
    occupation_code: string;
    sex: string;
    birth_place: string;
    id_card_number: string;
    full_address: string;
    religion: string;
  };
  id_card_guarantor: {
    id_card_image: string;
    marital_status: string;
    full_name: string;
    occupation: string;
    birth_date: string;
    occupation_code: string;
    sex: string;
    birth_place: string;
    id_card_number: string;
    full_address: string;
    religion: string;
  };
  id_card_owner: {
    id_card_image: string;
    marital_status: string;
    full_name: string;
    occupation: string;
    birth_date: string;
    occupation_code: string;
    sex: string;
    birth_place: string;
    id_card_number: string;
    full_address: string;
    religion: string;
  };
  contact: {
    phone_number_wa_available_guarantor: boolean;
    phone_number_wa_available_order_maker: boolean;
    phone_number_owner: string;
    phone_number_wa_available_owner: boolean;
    phone_number_alternative_owner: string;
    email_owner: string;
    phone_number_alternative_order_maker: string;
    phone_number_guarantor: string;
    phone_number_order_maker: string;
    email_order_maker: string;
  };
  detail_promo: {
    discount: number;
    tenor: number;
    code: string;
    type: string;
    percentage: number;
    target: string;
  }[];
  credit: {
    po_code: string;
    dp_amount: number;
    installment_amount: number;
    finco_name: string;
    finco_code: string;
    po_data: {};
    po_track_number: string;
    po_expire_poilicy: number;
    credit_note: string;
    tenor: number;
    po_phone_number: string;
    po_expired_at: string;
    po_image: string;
    finco_branch: string;
    po_time: string;
  };
  vehicle: {
    model_name: string;
    alternative_color: {
      name: string;
      code: string;
    };
    year: string;
    variant_uuid: string;
    model_uuid: string;
    brand_name: string;
    variant_name: string;
    color_name: string;
    variant_code: string;
    color_code: string;
    brand_uuid: string;
  };
  order?: {
    code: string | null;
    time: string | null;
    product: string | null;
  };
  price_and_bill: {
    total_bill: number;
    otr: number;
  };
}

export interface IGetOfferSuccessResponse {
  success: boolean;
  data: IGetOfferData;
}

interface DefaultAddressDealDetail {
  city_code: string;
  city_name: string;
  province_code: string;
  province_name: string;
}

interface VariantDealDetail {
  code: string;
  custom_thumb_image: string;
  custom_image: string;
  variant_name: string;
  variant_color_code: string;
  variant_code: string;
  variant_color_name: string;
}

interface VehicleDealDetail {
  variant_custom: VariantDealDetail[];
  variant_alternative_color: {
    name: string;
    code: string;
  };
  brand_name: string;
  model_name: string;
  models_name: string[];
  brand_uuid: string;
}

interface CreditOptionDealDetail {
  dp_amount: number;
  tenor: string[];
  installment_amount: number;
  finco_name: string;
  finco_branch: string;
  finco_code: string;
  otr: number;
}

interface DataEntryOptionDealDetail {
  name: string;
  show: boolean;
  require: boolean;
  type: string;
  validation: boolean;
}
interface EventPointDealDetail {
  name: string;
  key: string;
  point: number;
}

export interface DealCodeResponse {
  success: boolean;
  data: {
    company: string;
    deal_code: string;
    url_image: string;
    url_thumb_image: string;
    start_period: string;
    end_period: string;
    active: boolean;
    show: boolean;
    caption: string;
    notes: string;
    purchase_method: string;
    area: string[];
    default_address: DefaultAddressDealDetail;
    custom_price: number;
    custom_price_range_up: number;
    custom_price_range_down: number;
    take_vehicle_in_dealer: string;
    agent_code: string;
    promo_codes: string[];
    total_promo_discount: number;
    vehicle: VehicleDealDetail;
    credit: CreditOptionDealDetail[];
    data_entry_option: DataEntryOptionDealDetail[];
    image_campaign_custom: string[];
    event_points: EventPointDealDetail[];
    default_finco_id: string;
    default_dealer_code: string;
    default_dealer_name: string;
  };
}

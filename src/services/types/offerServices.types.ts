export interface ErrorResponseUpdateDataToOtodis {
  error: {
    code: number;
    type: string;
    message: string;
  };
}
interface IdCardOwner {
  id_card_number: string;
  id_card_image: string;
  full_name: string;
  full_address: string;
  birth_place: string;
  birth_date: string;
  marital_status_code: string;
  marital_status: string;
  occupation_code: string;
}

interface AddressOwner {
  full_address: string;
  province_code: string;
  province_name: string;
  city_code: string;
  city_name: string;
  district_code: string;
  district_name: string;
  sub_district_code: string;
  sub_district_name: string;
  zip_code: string;
}

interface LocationPoint {
  point: string;
  lat: string;
  lng: string;
}

interface AddressShipping {
  full_address: string;
  province_code: string;
  province_name: string;
  city_code: string;
  city_name: string;
  district_code: string;
  district_name: string;
  sub_district_code: string;
  sub_district_name: string;
  zip_code: string;
  location_point: LocationPoint;
}

interface DataShipping {
  mediator_name: string;
  mediator_phone_number: string;
  mediator_credit_amount: number;
  customer_cash_on_delivery: number;
  shipping_date: string;
  shipping_credit_payment_date: string;
}

export interface ParamsUpdateDataToOtodisBasic<UPDATE_TYPE> {
  update_type: UPDATE_TYPE;
  company: 'AMARTA';
  offer_code: string;
  admin_id: string;
  admin_name: string;
}

export interface ParamsUpdateDataToOtodisIdCard
  extends ParamsUpdateDataToOtodisBasic<'id-card-owner'> {
  id_card_owner: IdCardOwner;
  address_owner: AddressOwner;
}

export interface ParamsUpdateDataToOtodisShippingAddress
  extends ParamsUpdateDataToOtodisBasic<'address-shipping'> {
  address_shipping: AddressShipping;
  data_shipping: DataShipping;
}

export interface ParamsUpdateDataToOtodisPromoCode
  extends ParamsUpdateDataToOtodisBasic<'promo-codes'> {
  promo_codes: string[];
}
export interface ParamsUpdateDataToOtodisCreateBill
  extends ParamsUpdateDataToOtodisBasic<'create-bill-offer'> {}

export interface OtodisDealer {
  dealer_code: string;
  dealer_name: string;
  active: boolean;
}

export interface OtodisCheckStockParams {
  dealer_code: string;
  vehicle_variant_code: string;
  vehicle_color_code: string;
  vehicle_year: string;
  lite_respone: boolean;
  status_stock?: 'ACTIVE';
}

export interface OtodisCheckStockResponseLite {
  dealer_code: string;
  dealer_name: string;
  warehouse_name: string;
  stock_unit_code: string;
  stock_status: string;
  vehicle_variant_code: string;
  vehicle_variant_name: string;
  vehicle_color_code: string;
  vehicle_color_name: string;
  vehicle_engine_number: string;
  vehicle_chassis_number: string;
  vehicle_license_plate: string;
  vehicle_year: number;
  vehicle_engine_document: string;
  stock_age_day: number;
}

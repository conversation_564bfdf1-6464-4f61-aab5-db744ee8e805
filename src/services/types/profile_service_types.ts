import { EFamilyRelation } from '../../entities/types/family-register-entity-types';
import { EMaritalStatus } from '../../Components/ImageCapture/types/marital-status-types';
import { EBusinessDocument } from '../../Components/ImageCapture/types/business-document-types';
import { EIncomeType } from '../../Components/ImageCapture/types/income_document_types';
import { EPrivateDocument } from '../../Components/ImageCapture/types/private-document-types';
import { EPlaceToStayStatus } from '../../Components/ImageCapture/types/place_to_stay_types';
import {
  EBusinessSize,
  EBusinessType,
} from '../../Components/ImageCapture/types/place_of_business_types';

export interface IOwnedVehicle {
  id: string;
  Brand: {
    Uuid: string;
    Name: string;
  };
  Model: {
    Uuid: string;
    Name: string;
  };
  Variant: {
    Code: string;
    Uuid: string;
    Name: string;
  };
  Year: string;
}

export interface CommonRegionFields {
  Code: string;
  Name: string;
}

export interface FamilyMember {
  IdCardNumber: string;
  FullName: string;
  FamilyRelation: EFamilyRelation;
  MaritalStatus: EMaritalStatus;
}

export interface IdCardFields {
  IdCardNumber: string;
  FullName: string;
  BirthMother: string;
  PlaceOfBirth: string;
  DateOfBirth: string;
  Occupation: string;
  MaritalStatus: string;
  LastEducation: string;

  FullAddress: string;
  ZipCode: string;
  Province: CommonRegionFields;
  City: CommonRegionFields;
  District: CommonRegionFields;
  SubDistrict: CommonRegionFields;
  Hamlet: string;
  Neighbourhood: string;

  DomicileFullAddress: string;
  DomicileZipCode: string;
  DomicileProvince: CommonRegionFields;
  DomicileCity: CommonRegionFields;
  DomicileDistrict: CommonRegionFields;
  DomicileSubDistrict: CommonRegionFields;
  DomicileHamlet: string;
  DomicileNeighbourhood: string;

  IdCardImage?: string;
}

export interface IFamilyRegisterFields {
  FamilyRegisterNumber: string;
  FullAddress: string;
  ZipCode: string;

  Province: CommonRegionFields;
  City: CommonRegionFields;
  District: CommonRegionFields;
  SubDistrict: CommonRegionFields;

  FamilyRegisterImage?: string | null;

  Members: FamilyMember[];
}

export interface IBusinessDocumentFields {
  DocumentTypes: EBusinessDocument;
  DocumentNumber: string;
  ExpiredDate: string | Date | null;
  BusinessDocumentImage?: string;
}

export interface IIncomeDocumentFields {
  IncomeType: EIncomeType;
  DocumentType: EPrivateDocument;
  IncomeAmount: string;
  AdditionalInfo: string;
  BankName: string;
  BankAccountNumber: string;
  ImageIncomeDocument?: string;
}

export interface IPlaceToStayFields {
  FullAddress: string;
  Latitude: string;
  Longitude: string;
  PlaceToStayImage?: string;
  PlaceToStayStatus: EPlaceToStayStatus;
}

export interface IPlaceOfBusinessFields {
  FullAddress: string;
  Latitude: string;
  Longitude: string;
  BusinessSize: EBusinessSize;
  BusinessType: EBusinessType;
  PlaceOfBusinessImage?: string;
}

export interface ISelfieFields {
  FullAddress: string | null;
  Latitude: string | null;
  Longitude: string | null;
  Description: string | null;
  SelfieImage?: string;
}

export interface IOtherDocumentFields {
  DocumentName: string;
  ExpiredDate: Date | string | null;
  Notes: string;
  ImageOtherDocument?: string;
}

export interface IUntouchableResponse {
  TrackingId: string;
  Source: string;
  SK: string;
  PK: string;
  CreatedAt: string;
  UpdatedAt: string;
  Status: string;
}

export interface IDocumentsResponse {
  IdCard: IdCardFields;
  IdCardSpouse: IdCardFields | null;
  FamilyRegister: IFamilyRegisterFields;
  IncomeDocument?: IIncomeDocumentFields | null;
  BusinessDocument?: IBusinessDocumentFields | null;
  PlaceToStay: IPlaceToStayFields | null;
  PlaceOfBusiness: IPlaceOfBusinessFields | null;
  Selfie: ISelfieFields | null;
  OtherDocument: IOtherDocumentFields | null;
}

export interface IProfileResponse {
  Phone: string;
  CustomerName: string;
  CityGroup?: string;
  OwnedVehicles?: IOwnedVehicle[];
}

export type TProfileResponse = IProfileResponse &
  Partial<IDocumentsResponse> &
  IUntouchableResponse;

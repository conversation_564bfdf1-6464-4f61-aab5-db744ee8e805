export interface IGetPromoParams {
  promo_type: string;
  city_group: string;
  purchase_method: string;
  vehicle_brand: 'honda';
  vehicle_model: string;
  vehicle_variant?: string;

  tenor?: number;
  vehicle_variant_color?: string;
  alternative_color?: string;

  finco_code?: string;
  transaction_amount?: number;

  allow_agent?: boolean;
}

export interface IPromoCode {
  allow_agent: boolean;
  promo_code: string;
  discount_type: 'nominal' | 'percent';
  discount_value: number;
  target_tenor: string[];
  purchase_method: string[];
}

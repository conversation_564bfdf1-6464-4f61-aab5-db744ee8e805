import { Coordinate, RegionType } from './provinceTypes';

export interface ISubDistrict {
  type?: RegionType.SUBDISTRICT;
  code: string;
  name: string;
  name_alias?: null;
  coordinate?: Coordinate;
  province_code?: string;
  province_name?: string;
  city_code?: string;
  city_administrative_type?: string;
  city_name?: string;
  district_code?: string;
  district_name?: string;
  postal_code?: string[];
}

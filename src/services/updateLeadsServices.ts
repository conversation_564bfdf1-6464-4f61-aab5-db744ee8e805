import { IParamsUpdateLeads, IParamsUpdateLeadsOffer } from './types/updateLeads_types';
import apisauce, { create } from 'apisauce';

class UpdateLeadsServices {
  private baseUrl = create({
    baseURL: `https://order.amartahonda.com/rest/index.php/api/amarta`,
  });

  public async updateLeads(params: IParamsUpdateLeads) {
    return await this.baseUrl.put('/simpan_leads', {
      ...params,
    });
  }

  public async updateLeadsOfferCode(params: IParamsUpdateLeadsOffer) {
    return await this.baseUrl.put('/leads_offer', {
      ...params,
    });
  }
}

const updateLeadsService = new UpdateLeadsServices();

export default updateLeadsService;
